"""
集成示例 - 展示如何在 aipet 项目中集成 VCPChat 的优秀功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import Qt, pyqtSlot
from aipet.modules.selection_assistant import SelectionAssistant
from aipet.modules.input_enhancer import InputEnhancer
from aipet.ui.input_area import ModernInputArea

class EnhancedAiPetDemo(QMainWindow):
    """增强版 AiPet 演示"""
    
    def __init__(self):
        super().__init__()
        self.selection_assistant = None
        self.input_enhancer = None
        self.setup_ui()
        self.setup_enhanced_features()
        
    def setup_ui(self):
        """设置基础UI"""
        self.setWindowTitle("增强版 AiPet - 集成 VCPChat 功能")
        self.setGeometry(100, 100, 800, 600)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("AiPet 功能增强演示")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 功能说明
        info_label = QLabel("""
        新增功能：
        1. 📝 划词助手 - 全局文本选择，悬浮工具条
        2. 🎯 智能输入 - 拖拽文件、智能粘贴、长文本处理
        3. 🎨 增强渲染 - 动画效果、特殊样式
        4. 🎭 主题升级 - CSS变量、动态切换
        """)
        info_label.setStyleSheet("background: #f0f0f0; padding: 15px; border-radius: 8px; margin: 10px;")
        layout.addWidget(info_label)
        
        # 输入区域
        self.input_area = ModernInputArea(self)
        layout.addWidget(self.input_area)
        
        # 控制按钮
        self.setup_control_buttons(layout)
        
    def setup_control_buttons(self, layout):
        """设置控制按钮"""
        button_layout = QVBoxLayout()
        
        # 划词助手控制
        self.selection_toggle_btn = QPushButton("启动划词助手")
        self.selection_toggle_btn.clicked.connect(self.toggle_selection_assistant)
        self.selection_toggle_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        button_layout.addWidget(self.selection_toggle_btn)
        
        # 测试按钮
        test_btn = QPushButton("测试增强输入功能")
        test_btn.clicked.connect(self.test_enhanced_input)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        button_layout.addWidget(test_btn)
        
        # 状态显示
        self.status_label = QLabel("状态: 就绪")
        self.status_label.setStyleSheet("color: #666; margin: 10px; font-size: 12px;")
        button_layout.addWidget(self.status_label)
        
        layout.addLayout(button_layout)
    
    def setup_enhanced_features(self):
        """设置增强功能"""
        try:
            # 设置划词助手
            self.selection_assistant = SelectionAssistant(self)
            
            # 设置输入增强器
            self.input_enhancer = InputEnhancer(self.input_area, self)
            self.input_enhancer.setup_enhanced_input()
            
            self.update_status("增强功能已初始化")
            
        except Exception as e:
            self.update_status(f"初始化失败: {str(e)}")
    
    @pyqtSlot()
    def toggle_selection_assistant(self):
        """切换划词助手状态"""
        if not self.selection_assistant:
            self.update_status("划词助手未初始化")
            return
            
        try:
            if self.selection_assistant.is_enabled():
                self.selection_assistant.stop()
                self.selection_toggle_btn.setText("启动划词助手")
                self.selection_toggle_btn.setStyleSheet(
                    self.selection_toggle_btn.styleSheet().replace("#4CAF50", "#f44336")
                )
                self.update_status("划词助手已停止")
            else:
                self.selection_assistant.start()
                self.selection_toggle_btn.setText("停止划词助手")
                self.selection_toggle_btn.setStyleSheet(
                    self.selection_toggle_btn.styleSheet().replace("#f44336", "#4CAF50")
                )
                self.update_status("划词助手已启动 - 请选择任意文本试试")
                
        except Exception as e:
            self.update_status(f"切换失败: {str(e)}")
    
    @pyqtSlot()
    def test_enhanced_input(self):
        """测试增强输入功能"""
        test_text = """
        这是一个测试文本，用于演示增强输入功能：
        
        1. 拖拽文件到输入框试试
        2. 复制图片然后粘贴
        3. 粘贴长文本（超过2000字符）
        4. 拖拽文本到输入框
        
        增强功能包括：
        - 智能文件类型识别
        - 自动长文本处理
        - 图片剪贴板支持
        - 拖拽视觉反馈
        """
        
        if hasattr(self.input_area, 'input_field'):
            self.input_area.input_field.setPlainText(test_text)
            self.update_status("已插入测试文本，请尝试拖拽文件或粘贴内容")
    
    def send_message(self, message: str):
        """模拟发送消息（供划词助手调用）"""
        self.update_status(f"收到消息: {message[:50]}...")
        
        # 这里可以集成到实际的消息发送逻辑
        if hasattr(self.input_area, 'input_field'):
            current_text = self.input_area.input_field.toPlainText()
            new_text = current_text + "\n\n" + message
            self.input_area.input_field.setPlainText(new_text)
    
    def update_status(self, message: str):
        """更新状态显示"""
        self.status_label.setText(f"状态: {message}")
        print(f"[状态] {message}")  # 同时输出到控制台
    
    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 清理资源
            if self.selection_assistant and self.selection_assistant.is_enabled():
                self.selection_assistant.stop()
                
            if self.input_enhancer:
                self.input_enhancer.cleanup_temp_files()
                
            self.update_status("正在清理资源...")
            
        except Exception as e:
            print(f"清理资源时出错: {e}")
        
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("Enhanced AiPet Demo")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AiPet Team")
    
    # 创建主窗口
    window = EnhancedAiPetDemo()
    window.show()
    
    # 显示启动信息
    print("=" * 50)
    print("增强版 AiPet 演示程序")
    print("=" * 50)
    print("功能说明：")
    print("1. 点击'启动划词助手'后，选择任意文本会显示悬浮工具条")
    print("2. 在输入框中拖拽文件或粘贴内容测试增强输入功能")
    print("3. 支持图片、文本文件的智能处理")
    print("4. 长文本会自动提示转换为文件附件")
    print("=" * 50)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
