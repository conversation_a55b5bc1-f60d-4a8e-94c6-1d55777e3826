AI-Agent  桌面宠物设置
常规
宠物形象
删除角色
翻译工具
成人模式
语音模型
试听
水印
角色透明度
开关
开关列表
录屏兼容
语音识别
AI语音
在线搜索
翻译
高级开关
媒体理解
全局鼠标穿透
关闭
下次启动时
左击底部
左击顶部
右击底部
右击顶部
任务栏锁定
人工智能
推理
云端推理
阿里云 API-Key
本地推理
文本模型
云端
本地
保存设置
扩展工具
源Python文件路径
编译进程序中
动画绑定
模型
参数
动作组
动作名
保存
表情
音频
播放方式
动画
规则
AI 桌宠相关说明
进行对话
切换角色
自动眨眼
自动呼吸
自动拖拽
文本输出
语音识别
聊天
鼠标会透过桌宠（无法对桌宠进行操作！）
当移动桌宠时，桌宠始终不会超过任务栏
格式化说明
1. {year}表当前年份
2. {ip}表你的设备IP
无模型输入
绑定的动作文件名字，从motions文件夹从上之下顺序
保留模型，无法删除
开始录入：你需要在您的角色上点击录入。点击两次，第一次为最小值，第二次为最大值
编译完成
确认？
等待您的聊天……
兼容模型
模型可能不支持Live2D Cubism 2.0 Core。如果是2.0 Core则可能使用64位应用程序建模。不支持32位应用程序
您的模型不支持Live2D Cubism 2.0 Core，程序内置2.0 Core的模型，是否启用？
需要重启程序，以启用兼容模型
文件夹路径
运行
终止
错误
函数入口
人工智能 - ai
通义千问 - tongyi
爬虫翻译 - spider
必应翻译 - bing
添加角色
使桌宠可以被录屏捕捉（会关闭不高于任务栏功能）
使桌宠可以看懂图片/视频（包含主动Agent）
绅士懂得都懂~
保存成功！
您需要重启以应用更新~
使桌宠可以听懂你说话
让你的桌宠发声
桌宠可以在网上冲浪
说话（AI语音）翻译成日文
图片处理
讯飞ID
讯飞Key
讯飞Secret
文本输出参数
语音参数
播放失败
开发者工具
检查更新
待检查
好耶！你是最新版本！！
好吧，看来你不是最新版{latest}！
完🥚，检查更新看来出问题了！
检查中……
终止失败
终止成功！
运行
终止
程序增强
独立程序
自动选择
没有文件
导入文件
优化建议
代码中存在死循环！
用户开启了安全检查，您的插件因违反安全检查规则无法执行！
L1 - 轻微保护
L2 - 轻度保护
L3 - 中度保护
L4 - 高度保护
L5 - 重度保护
插件的安全检查功能
安全检查
文本输出API
语音合成API
设置
关于
语言
插件
绑定设置
选择文件夹
选择文件
导入音频
正在思考
音频可视化
会根据音量在桌宠上方显示不同高度的方块
添加失败
非法的模型结构
另存为
移动到
打开文件
在资源管理器中打开
邮箱
密码
登录
注册
验证码
获取验证码
下次自动登录!
录入
局域网URL：
语音识别采样
完全平均（sum(s) / len(s)）
平均加小（sum(s) / len(s) + min(s)）
平均加大（sum(s) / len(s) + max(s)）
倍增平均（sum(s) / len(s) * 2）
采样需要在一个安静的环境下进行，将会对环境进行录音并计算音量。采样数据会根据算法（Algorithm）进行计算
兼容架构
您导入的模型架构当前支持的，您需要重启以重载架构！
架构版本不和引擎版本相同，如果多次载入可能导致着色器崩溃
人设绑定
人设保存成功！
系统权限集只能有一个
请选中用户权限集后尝试
添加系统权限集
添加用户权限集
添加智能体权限集
删除当前行
获取智能体回复
保存并应用
撤销！
触发词语
执行词条
添加
删除
克隆角色
删除此克隆体
可爱的克隆角色——{name}
天空一声巨响，{name}闪亮登场！
物理模拟
物理演算
开启物理演算 (重力加速度、弹性势能)
弹性势能
在高处往下移动触底时会弹起
阻尼抛物线运动
抛出时依据阻力考虑减少动能（抛物线运动）
角色大小
克隆
删除所有克隆体
准备编译
Step.1 选择图标
Step.2 输入名称
请输入名称
Live2D 部分为网上模型，如有侵权请联系 <EMAIL> 进行删除
开启 Realtime(实时) API 选项
用于独立程序API接口 (http://127.0.0.1:8210)
关闭鼠标穿透
下载
模型下载
下载中……
下载失败
已拥有
云商店
正在下载
{name} 正在下载，请稍候……
下载完成！
插件下载
存储空间