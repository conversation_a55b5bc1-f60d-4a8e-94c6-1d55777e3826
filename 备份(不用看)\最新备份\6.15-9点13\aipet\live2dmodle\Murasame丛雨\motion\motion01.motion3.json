{"Version": 3, "Meta": {"Duration": 8, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 88, "TotalSegmentCount": 10115, "TotalPointCount": 10203, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 8, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.033, -0.004, 2, 0.067, -0.016, 2, 0.1, -0.036, 2, 0.133, -0.064, 2, 0.167, -0.099, 2, 0.2, -0.14, 2, 0.233, -0.188, 2, 0.267, -0.243, 2, 0.3, -0.303, 2, 0.333, -0.37, 2, 0.367, -0.443, 2, 0.4, -0.52, 2, 0.433, -0.603, 2, 0.467, -0.69, 2, 0.5, -0.781, 2, 0.533, -0.877, 2, 0.567, -0.976, 2, 0.6, -1.08, 2, 0.633, -1.187, 2, 0.667, -1.297, 2, 0.7, -1.409, 2, 0.733, -1.524, 2, 0.767, -1.641, 2, 0.8, -1.759, 2, 0.833, -1.88, 2, 0.867, -2.003, 2, 0.9, -2.126, 2, 0.933, -2.251, 2, 0.967, -2.376, 2, 1, -2.5, 2, 1.033, -2.624, 2, 1.067, -2.749, 2, 1.1, -2.874, 2, 1.133, -2.997, 2, 1.167, -3.12, 2, 1.2, -3.241, 2, 1.233, -3.359, 2, 1.267, -3.476, 2, 1.3, -3.591, 2, 1.333, -3.703, 2, 1.367, -3.813, 2, 1.4, -3.92, 2, 1.433, -4.024, 2, 1.467, -4.123, 2, 1.5, -4.219, 2, 1.533, -4.31, 2, 1.567, -4.397, 2, 1.6, -4.48, 2, 1.633, -4.557, 2, 1.667, -4.63, 2, 1.7, -4.697, 2, 1.733, -4.757, 2, 1.767, -4.812, 2, 1.8, -4.86, 2, 1.833, -4.901, 2, 1.867, -4.936, 2, 1.9, -4.964, 2, 1.933, -4.984, 2, 1.967, -4.996, 2, 2, -5, 2, 2.033, -4.999, 2, 2.067, -4.994, 2, 2.1, -4.987, 2, 2.133, -4.978, 2, 2.167, -4.965, 2, 2.2, -4.95, 2, 2.233, -4.932, 2, 2.267, -4.911, 2, 2.3, -4.888, 2, 2.333, -4.861, 2, 2.367, -4.832, 2, 2.4, -4.8, 2, 2.433, -4.765, 2, 2.467, -4.728, 2, 2.5, -4.688, 2, 2.533, -4.645, 2, 2.567, -4.599, 2, 2.6, -4.55, 2, 2.633, -4.499, 2, 2.667, -4.444, 2, 2.7, -4.387, 2, 2.733, -4.328, 2, 2.767, -4.265, 2, 2.8, -4.2, 2, 2.833, -4.133, 2, 2.867, -4.061, 2, 2.9, -3.988, 2, 2.933, -3.911, 2, 2.967, -3.832, 2, 3, -3.75, 2, 3.033, -3.666, 2, 3.067, -3.578, 2, 3.1, -3.487, 2, 3.133, -3.395, 2, 3.167, -3.298, 2, 3.2, -3.199, 2, 3.233, -3.098, 2, 3.267, -2.995, 2, 3.3, -2.888, 2, 3.333, -2.779, 2, 3.367, -2.665, 2, 3.4, -2.55, 2, 3.433, -2.431, 2, 3.467, -2.311, 2, 3.5, -2.188, 2, 3.533, -2.062, 2, 3.567, -1.933, 2, 3.6, -1.8, 2, 3.633, -1.665, 2, 3.667, -1.526, 2, 3.7, -1.387, 2, 3.733, -1.244, 2, 3.767, -1.099, 2, 3.8, -0.951, 2, 3.833, -0.8, 2, 3.867, -0.644, 2, 3.9, -0.488, 2, 3.933, -0.327, 2, 3.967, -0.165, 2, 4, 0, 2, 4.033, 0.165, 2, 4.067, 0.327, 2, 4.1, 0.488, 2, 4.133, 0.644, 2, 4.167, 0.8, 2, 4.2, 0.951, 2, 4.233, 1.099, 2, 4.267, 1.244, 2, 4.3, 1.387, 2, 4.333, 1.526, 2, 4.367, 1.665, 2, 4.4, 1.8, 2, 4.433, 1.933, 2, 4.467, 2.062, 2, 4.5, 2.188, 2, 4.533, 2.311, 2, 4.567, 2.431, 2, 4.6, 2.55, 2, 4.633, 2.665, 2, 4.667, 2.779, 2, 4.7, 2.888, 2, 4.733, 2.995, 2, 4.767, 3.098, 2, 4.8, 3.199, 2, 4.833, 3.298, 2, 4.867, 3.395, 2, 4.9, 3.487, 2, 4.933, 3.578, 2, 4.967, 3.666, 2, 5, 3.75, 2, 5.033, 3.832, 2, 5.067, 3.911, 2, 5.1, 3.988, 2, 5.133, 4.061, 2, 5.167, 4.133, 2, 5.2, 4.2, 2, 5.233, 4.265, 2, 5.267, 4.328, 2, 5.3, 4.387, 2, 5.333, 4.444, 2, 5.367, 4.499, 2, 5.4, 4.55, 2, 5.433, 4.599, 2, 5.467, 4.645, 2, 5.5, 4.688, 2, 5.533, 4.728, 2, 5.567, 4.765, 2, 5.6, 4.8, 2, 5.633, 4.832, 2, 5.667, 4.861, 2, 5.7, 4.888, 2, 5.733, 4.911, 2, 5.767, 4.932, 2, 5.8, 4.95, 2, 5.833, 4.965, 2, 5.867, 4.978, 2, 5.9, 4.987, 2, 5.933, 4.994, 2, 5.967, 4.999, 2, 6, 5, 2, 6.033, 4.996, 2, 6.067, 4.984, 2, 6.1, 4.964, 2, 6.133, 4.936, 2, 6.167, 4.901, 2, 6.2, 4.86, 2, 6.233, 4.812, 2, 6.267, 4.757, 2, 6.3, 4.697, 2, 6.333, 4.63, 2, 6.367, 4.557, 2, 6.4, 4.48, 2, 6.433, 4.397, 2, 6.467, 4.31, 2, 6.5, 4.219, 2, 6.533, 4.123, 2, 6.567, 4.024, 2, 6.6, 3.92, 2, 6.633, 3.813, 2, 6.667, 3.703, 2, 6.7, 3.591, 2, 6.733, 3.476, 2, 6.767, 3.359, 2, 6.8, 3.241, 2, 6.833, 3.12, 2, 6.867, 2.997, 2, 6.9, 2.874, 2, 6.933, 2.749, 2, 6.967, 2.624, 2, 7, 2.5, 2, 7.033, 2.376, 2, 7.067, 2.251, 2, 7.1, 2.126, 2, 7.133, 2.003, 2, 7.167, 1.88, 2, 7.2, 1.759, 2, 7.233, 1.641, 2, 7.267, 1.524, 2, 7.3, 1.409, 2, 7.333, 1.297, 2, 7.367, 1.187, 2, 7.4, 1.08, 2, 7.433, 0.976, 2, 7.467, 0.877, 2, 7.5, 0.781, 2, 7.533, 0.69, 2, 7.567, 0.603, 2, 7.6, 0.52, 2, 7.633, 0.443, 2, 7.667, 0.37, 2, 7.7, 0.303, 2, 7.733, 0.243, 2, 7.767, 0.188, 2, 7.8, 0.14, 2, 7.833, 0.099, 2, 7.867, 0.064, 2, 7.9, 0.036, 2, 7.933, 0.016, 2, 7.967, 0.004, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.033, 0.008, 2, 0.067, 0.032, 2, 0.1, 0.073, 2, 0.133, 0.127, 2, 0.167, 0.198, 2, 0.2, 0.281, 2, 0.233, 0.377, 2, 0.267, 0.486, 2, 0.3, 0.607, 2, 0.333, 0.739, 2, 0.367, 0.885, 2, 0.4, 1.04, 2, 0.433, 1.206, 2, 0.467, 1.38, 2, 0.5, 1.562, 2, 0.533, 1.753, 2, 0.567, 1.952, 2, 0.6, 2.161, 2, 0.633, 2.373, 2, 0.667, 2.595, 2, 0.7, 2.819, 2, 0.733, 3.048, 2, 0.767, 3.281, 2, 0.8, 3.519, 2, 0.833, 3.759, 2, 0.867, 4.006, 2, 0.9, 4.252, 2, 0.933, 4.503, 2, 0.967, 4.751, 2, 1, 5, 2, 1.033, 5.249, 2, 1.067, 5.497, 2, 1.1, 5.748, 2, 1.133, 5.994, 2, 1.167, 6.241, 2, 1.2, 6.481, 2, 1.233, 6.719, 2, 1.267, 6.952, 2, 1.3, 7.181, 2, 1.333, 7.405, 2, 1.367, 7.627, 2, 1.4, 7.839, 2, 1.433, 8.048, 2, 1.467, 8.247, 2, 1.5, 8.438, 2, 1.533, 8.62, 2, 1.567, 8.794, 2, 1.6, 8.96, 2, 1.633, 9.115, 2, 1.667, 9.261, 2, 1.7, 9.393, 2, 1.733, 9.514, 2, 1.767, 9.623, 2, 1.8, 9.719, 2, 1.833, 9.802, 2, 1.867, 9.873, 2, 1.9, 9.927, 2, 1.933, 9.968, 2, 1.967, 9.992, 2, 2, 10, 2, 2.033, 9.992, 2, 2.067, 9.968, 2, 2.1, 9.927, 2, 2.133, 9.873, 2, 2.167, 9.802, 2, 2.2, 9.719, 2, 2.233, 9.623, 2, 2.267, 9.514, 2, 2.3, 9.393, 2, 2.333, 9.261, 2, 2.367, 9.115, 2, 2.4, 8.96, 2, 2.433, 8.794, 2, 2.467, 8.62, 2, 2.5, 8.438, 2, 2.533, 8.247, 2, 2.567, 8.048, 2, 2.6, 7.839, 2, 2.633, 7.627, 2, 2.667, 7.405, 2, 2.7, 7.181, 2, 2.733, 6.952, 2, 2.767, 6.719, 2, 2.8, 6.481, 2, 2.833, 6.241, 2, 2.867, 5.994, 2, 2.9, 5.748, 2, 2.933, 5.497, 2, 2.967, 5.249, 2, 3, 5, 2, 3.033, 4.751, 2, 3.067, 4.503, 2, 3.1, 4.252, 2, 3.133, 4.006, 2, 3.167, 3.759, 2, 3.2, 3.519, 2, 3.233, 3.281, 2, 3.267, 3.048, 2, 3.3, 2.819, 2, 3.333, 2.595, 2, 3.367, 2.373, 2, 3.4, 2.161, 2, 3.433, 1.952, 2, 3.467, 1.753, 2, 3.5, 1.562, 2, 3.533, 1.38, 2, 3.567, 1.206, 2, 3.6, 1.04, 2, 3.633, 0.885, 2, 3.667, 0.739, 2, 3.7, 0.607, 2, 3.733, 0.486, 2, 3.767, 0.377, 2, 3.8, 0.281, 2, 3.833, 0.198, 2, 3.867, 0.127, 2, 3.9, 0.073, 2, 3.933, 0.032, 2, 3.967, 0.008, 2, 4, 0, 2, 4.033, 0.008, 2, 4.067, 0.032, 2, 4.1, 0.073, 2, 4.133, 0.127, 2, 4.167, 0.198, 2, 4.2, 0.281, 2, 4.233, 0.377, 2, 4.267, 0.486, 2, 4.3, 0.607, 2, 4.333, 0.739, 2, 4.367, 0.885, 2, 4.4, 1.04, 2, 4.433, 1.206, 2, 4.467, 1.38, 2, 4.5, 1.562, 2, 4.533, 1.753, 2, 4.567, 1.952, 2, 4.6, 2.161, 2, 4.633, 2.373, 2, 4.667, 2.595, 2, 4.7, 2.819, 2, 4.733, 3.048, 2, 4.767, 3.281, 2, 4.8, 3.519, 2, 4.833, 3.759, 2, 4.867, 4.006, 2, 4.9, 4.252, 2, 4.933, 4.503, 2, 4.967, 4.751, 2, 5, 5, 2, 5.033, 5.249, 2, 5.067, 5.497, 2, 5.1, 5.748, 2, 5.133, 5.994, 2, 5.167, 6.241, 2, 5.2, 6.481, 2, 5.233, 6.719, 2, 5.267, 6.952, 2, 5.3, 7.181, 2, 5.333, 7.405, 2, 5.367, 7.627, 2, 5.4, 7.839, 2, 5.433, 8.048, 2, 5.467, 8.247, 2, 5.5, 8.438, 2, 5.533, 8.62, 2, 5.567, 8.794, 2, 5.6, 8.96, 2, 5.633, 9.115, 2, 5.667, 9.261, 2, 5.7, 9.393, 2, 5.733, 9.514, 2, 5.767, 9.623, 2, 5.8, 9.719, 2, 5.833, 9.802, 2, 5.867, 9.873, 2, 5.9, 9.927, 2, 5.933, 9.968, 2, 5.967, 9.992, 2, 6, 10, 2, 6.033, 9.992, 2, 6.067, 9.968, 2, 6.1, 9.927, 2, 6.133, 9.873, 2, 6.167, 9.802, 2, 6.2, 9.719, 2, 6.233, 9.623, 2, 6.267, 9.514, 2, 6.3, 9.393, 2, 6.333, 9.261, 2, 6.367, 9.115, 2, 6.4, 8.96, 2, 6.433, 8.794, 2, 6.467, 8.62, 2, 6.5, 8.438, 2, 6.533, 8.247, 2, 6.567, 8.048, 2, 6.6, 7.839, 2, 6.633, 7.627, 2, 6.667, 7.405, 2, 6.7, 7.181, 2, 6.733, 6.952, 2, 6.767, 6.719, 2, 6.8, 6.481, 2, 6.833, 6.241, 2, 6.867, 5.994, 2, 6.9, 5.748, 2, 6.933, 5.497, 2, 6.967, 5.249, 2, 7, 5, 2, 7.033, 4.751, 2, 7.067, 4.503, 2, 7.1, 4.252, 2, 7.133, 4.006, 2, 7.167, 3.759, 2, 7.2, 3.519, 2, 7.233, 3.281, 2, 7.267, 3.048, 2, 7.3, 2.819, 2, 7.333, 2.595, 2, 7.367, 2.373, 2, 7.4, 2.161, 2, 7.433, 1.952, 2, 7.467, 1.753, 2, 7.5, 1.562, 2, 7.533, 1.38, 2, 7.567, 1.206, 2, 7.6, 1.04, 2, 7.633, 0.885, 2, 7.667, 0.739, 2, 7.7, 0.607, 2, 7.733, 0.486, 2, 7.767, 0.377, 2, 7.8, 0.281, 2, 7.833, 0.198, 2, 7.867, 0.127, 2, 7.9, 0.073, 2, 7.933, 0.032, 2, 7.967, 0.008, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.667, 0.844, 2, 0.7, 0.5, 2, 0.733, 0.156, 2, 0.767, 0, 2, 0.8, 0.156, 2, 0.833, 0.5, 2, 0.867, 0.844, 2, 0.9, 1, 2, 1.033, 0.844, 2, 1.067, 0.5, 2, 1.1, 0.156, 2, 1.133, 0, 2, 1.167, 0.156, 2, 1.2, 0.5, 2, 1.233, 0.844, 2, 1.267, 1, 2, 3.467, 0.844, 2, 3.5, 0.5, 2, 3.533, 0.156, 2, 3.567, 0, 2, 3.6, 0.156, 2, 3.633, 0.5, 2, 3.667, 0.844, 2, 3.7, 1, 2, 5.933, 0.844, 2, 5.967, 0.5, 2, 6, 0.156, 2, 6.033, 0, 2, 6.067, 0.156, 2, 6.1, 0.5, 2, 6.133, 0.844, 2, 6.167, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.667, 0.844, 2, 0.7, 0.5, 2, 0.733, 0.156, 2, 0.767, 0, 2, 0.8, 0.156, 2, 0.833, 0.5, 2, 0.867, 0.844, 2, 0.9, 1, 2, 1.033, 0.844, 2, 1.067, 0.5, 2, 1.1, 0.156, 2, 1.133, 0, 2, 1.167, 0.156, 2, 1.2, 0.5, 2, 1.233, 0.844, 2, 1.267, 1, 2, 3.467, 0.844, 2, 3.5, 0.5, 2, 3.533, 0.156, 2, 3.567, 0, 2, 3.6, 0.156, 2, 3.633, 0.5, 2, 3.667, 0.844, 2, 3.7, 1, 2, 5.933, 0.844, 2, 5.967, 0.5, 2, 6, 0.156, 2, 6.033, 0, 2, 6.067, 0.156, 2, 6.1, 0.5, 2, 6.133, 0.844, 2, 6.167, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0.001, 2, 0.1, 0.001, 2, 0.133, 0.003, 2, 0.167, 0.004, 2, 0.2, 0.006, 2, 0.233, 0.008, 2, 0.267, 0.01, 2, 0.3, 0.012, 2, 0.333, 0.015, 2, 0.367, 0.018, 2, 0.4, 0.021, 2, 0.433, 0.024, 2, 0.467, 0.028, 2, 0.5, 0.031, 2, 0.533, 0.035, 2, 0.567, 0.039, 2, 0.6, 0.043, 2, 0.633, 0.047, 2, 0.667, 0.052, 2, 0.7, 0.056, 2, 0.733, 0.061, 2, 0.767, 0.066, 2, 0.8, 0.07, 2, 0.833, 0.075, 2, 0.867, 0.08, 2, 0.9, 0.085, 2, 0.933, 0.09, 2, 0.967, 0.095, 2, 1, 0.1, 2, 1.033, 0.105, 2, 1.067, 0.11, 2, 1.1, 0.115, 2, 1.133, 0.12, 2, 1.167, 0.125, 2, 1.2, 0.13, 2, 1.233, 0.134, 2, 1.267, 0.139, 2, 1.3, 0.144, 2, 1.333, 0.148, 2, 1.367, 0.153, 2, 1.4, 0.157, 2, 1.433, 0.161, 2, 1.467, 0.165, 2, 1.5, 0.169, 2, 1.533, 0.172, 2, 1.567, 0.176, 2, 1.6, 0.179, 2, 1.633, 0.182, 2, 1.667, 0.185, 2, 1.7, 0.188, 2, 1.733, 0.19, 2, 1.767, 0.192, 2, 1.8, 0.194, 2, 1.833, 0.196, 2, 1.867, 0.197, 2, 1.9, 0.199, 2, 1.933, 0.199, 2, 1.967, 0.2, 2, 2, 0.2, 2, 2.033, 0.2, 2, 2.067, 0.2, 2, 2.1, 0.199, 2, 2.133, 0.199, 2, 2.167, 0.199, 2, 2.2, 0.198, 2, 2.233, 0.197, 2, 2.267, 0.196, 2, 2.3, 0.196, 2, 2.333, 0.194, 2, 2.367, 0.193, 2, 2.4, 0.192, 2, 2.433, 0.191, 2, 2.467, 0.189, 2, 2.5, 0.188, 2, 2.533, 0.186, 2, 2.567, 0.184, 2, 2.6, 0.182, 2, 2.633, 0.18, 2, 2.667, 0.178, 2, 2.7, 0.175, 2, 2.733, 0.173, 2, 2.767, 0.171, 2, 2.8, 0.168, 2, 2.833, 0.165, 2, 2.867, 0.162, 2, 2.9, 0.16, 2, 2.933, 0.156, 2, 2.967, 0.153, 2, 3, 0.15, 2, 3.033, 0.147, 2, 3.067, 0.143, 2, 3.1, 0.139, 2, 3.133, 0.136, 2, 3.167, 0.132, 2, 3.2, 0.128, 2, 3.233, 0.124, 2, 3.267, 0.12, 2, 3.3, 0.116, 2, 3.333, 0.111, 2, 3.367, 0.107, 2, 3.4, 0.102, 2, 3.433, 0.097, 2, 3.467, 0.092, 2, 3.5, 0.087, 2, 3.533, 0.082, 2, 3.567, 0.077, 2, 3.6, 0.072, 2, 3.633, 0.067, 2, 3.667, 0.061, 2, 3.7, 0.055, 2, 3.733, 0.05, 2, 3.767, 0.044, 2, 3.8, 0.038, 2, 3.833, 0.032, 2, 3.867, 0.026, 2, 3.9, 0.02, 2, 3.933, 0.013, 2, 3.967, 0.007, 2, 4, 0, 2, 4.033, -0.007, 2, 4.067, -0.013, 2, 4.1, -0.02, 2, 4.133, -0.026, 2, 4.167, -0.032, 2, 4.2, -0.038, 2, 4.233, -0.044, 2, 4.267, -0.05, 2, 4.3, -0.055, 2, 4.333, -0.061, 2, 4.367, -0.067, 2, 4.4, -0.072, 2, 4.433, -0.077, 2, 4.467, -0.082, 2, 4.5, -0.088, 2, 4.533, -0.092, 2, 4.567, -0.097, 2, 4.6, -0.102, 2, 4.633, -0.107, 2, 4.667, -0.111, 2, 4.7, -0.116, 2, 4.733, -0.12, 2, 4.767, -0.124, 2, 4.8, -0.128, 2, 4.833, -0.132, 2, 4.867, -0.136, 2, 4.9, -0.139, 2, 4.933, -0.143, 2, 4.967, -0.147, 2, 5, -0.15, 2, 5.033, -0.153, 2, 5.067, -0.156, 2, 5.1, -0.16, 2, 5.133, -0.162, 2, 5.167, -0.165, 2, 5.2, -0.168, 2, 5.233, -0.171, 2, 5.267, -0.173, 2, 5.3, -0.175, 2, 5.333, -0.178, 2, 5.367, -0.18, 2, 5.4, -0.182, 2, 5.433, -0.184, 2, 5.467, -0.186, 2, 5.5, -0.188, 2, 5.533, -0.189, 2, 5.567, -0.191, 2, 5.6, -0.192, 2, 5.633, -0.193, 2, 5.667, -0.194, 2, 5.7, -0.196, 2, 5.733, -0.196, 2, 5.767, -0.197, 2, 5.8, -0.198, 2, 5.833, -0.199, 2, 5.867, -0.199, 2, 5.9, -0.199, 2, 5.933, -0.2, 2, 5.967, -0.2, 2, 6, -0.2, 2, 6.033, -0.2, 2, 6.067, -0.199, 2, 6.1, -0.199, 2, 6.133, -0.197, 2, 6.167, -0.196, 2, 6.2, -0.194, 2, 6.233, -0.192, 2, 6.267, -0.19, 2, 6.3, -0.188, 2, 6.333, -0.185, 2, 6.367, -0.182, 2, 6.4, -0.179, 2, 6.433, -0.176, 2, 6.467, -0.172, 2, 6.5, -0.169, 2, 6.533, -0.165, 2, 6.567, -0.161, 2, 6.6, -0.157, 2, 6.633, -0.153, 2, 6.667, -0.148, 2, 6.7, -0.144, 2, 6.733, -0.139, 2, 6.767, -0.134, 2, 6.8, -0.13, 2, 6.833, -0.125, 2, 6.867, -0.12, 2, 6.9, -0.115, 2, 6.933, -0.11, 2, 6.967, -0.105, 2, 7, -0.1, 2, 7.033, -0.095, 2, 7.067, -0.09, 2, 7.1, -0.085, 2, 7.133, -0.08, 2, 7.167, -0.075, 2, 7.2, -0.07, 2, 7.233, -0.066, 2, 7.267, -0.061, 2, 7.3, -0.056, 2, 7.333, -0.052, 2, 7.367, -0.047, 2, 7.4, -0.043, 2, 7.433, -0.039, 2, 7.467, -0.035, 2, 7.5, -0.031, 2, 7.533, -0.028, 2, 7.567, -0.024, 2, 7.6, -0.021, 2, 7.633, -0.018, 2, 7.667, -0.015, 2, 7.7, -0.012, 2, 7.733, -0.01, 2, 7.767, -0.008, 2, 7.8, -0.006, 2, 7.833, -0.004, 2, 7.867, -0.003, 2, 7.9, -0.001, 2, 7.933, -0.001, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, -0.002, 2, 0.1, -0.004, 2, 0.133, -0.006, 2, 0.167, -0.01, 2, 0.2, -0.014, 2, 0.233, -0.019, 2, 0.267, -0.024, 2, 0.3, -0.03, 2, 0.333, -0.037, 2, 0.367, -0.044, 2, 0.4, -0.052, 2, 0.433, -0.06, 2, 0.467, -0.069, 2, 0.5, -0.078, 2, 0.533, -0.088, 2, 0.567, -0.098, 2, 0.6, -0.108, 2, 0.633, -0.119, 2, 0.667, -0.13, 2, 0.7, -0.141, 2, 0.733, -0.152, 2, 0.767, -0.164, 2, 0.8, -0.176, 2, 0.833, -0.188, 2, 0.867, -0.2, 2, 0.9, -0.213, 2, 0.933, -0.225, 2, 0.967, -0.238, 2, 1, -0.25, 2, 1.033, -0.262, 2, 1.067, -0.275, 2, 1.1, -0.287, 2, 1.133, -0.3, 2, 1.167, -0.312, 2, 1.2, -0.324, 2, 1.233, -0.336, 2, 1.267, -0.348, 2, 1.3, -0.359, 2, 1.333, -0.37, 2, 1.367, -0.381, 2, 1.4, -0.392, 2, 1.433, -0.402, 2, 1.467, -0.412, 2, 1.5, -0.422, 2, 1.533, -0.431, 2, 1.567, -0.44, 2, 1.6, -0.448, 2, 1.633, -0.456, 2, 1.667, -0.463, 2, 1.7, -0.47, 2, 1.733, -0.476, 2, 1.767, -0.481, 2, 1.8, -0.486, 2, 1.833, -0.49, 2, 1.867, -0.494, 2, 1.9, -0.496, 2, 1.933, -0.498, 2, 1.967, -0.5, 2, 2, -0.5, 2, 2.033, -0.5, 2, 2.067, -0.498, 2, 2.1, -0.496, 2, 2.133, -0.494, 2, 2.167, -0.49, 2, 2.2, -0.486, 2, 2.233, -0.481, 2, 2.267, -0.476, 2, 2.3, -0.47, 2, 2.333, -0.463, 2, 2.367, -0.456, 2, 2.4, -0.448, 2, 2.433, -0.44, 2, 2.467, -0.431, 2, 2.5, -0.422, 2, 2.533, -0.412, 2, 2.567, -0.402, 2, 2.6, -0.392, 2, 2.633, -0.381, 2, 2.667, -0.37, 2, 2.7, -0.359, 2, 2.733, -0.348, 2, 2.767, -0.336, 2, 2.8, -0.324, 2, 2.833, -0.312, 2, 2.867, -0.3, 2, 2.9, -0.287, 2, 2.933, -0.275, 2, 2.967, -0.262, 2, 3, -0.25, 2, 3.033, -0.238, 2, 3.067, -0.225, 2, 3.1, -0.213, 2, 3.133, -0.2, 2, 3.167, -0.188, 2, 3.2, -0.176, 2, 3.233, -0.164, 2, 3.267, -0.152, 2, 3.3, -0.141, 2, 3.333, -0.13, 2, 3.367, -0.119, 2, 3.4, -0.108, 2, 3.433, -0.098, 2, 3.467, -0.088, 2, 3.5, -0.078, 2, 3.533, -0.069, 2, 3.567, -0.06, 2, 3.6, -0.052, 2, 3.633, -0.044, 2, 3.667, -0.037, 2, 3.7, -0.03, 2, 3.733, -0.024, 2, 3.767, -0.019, 2, 3.8, -0.014, 2, 3.833, -0.01, 2, 3.867, -0.006, 2, 3.9, -0.004, 2, 3.933, -0.002, 2, 3.967, 0, 2, 4, 0, 2, 4.033, 0, 2, 4.067, -0.002, 2, 4.1, -0.004, 2, 4.133, -0.006, 2, 4.167, -0.01, 2, 4.2, -0.014, 2, 4.233, -0.019, 2, 4.267, -0.024, 2, 4.3, -0.03, 2, 4.333, -0.037, 2, 4.367, -0.044, 2, 4.4, -0.052, 2, 4.433, -0.06, 2, 4.467, -0.069, 2, 4.5, -0.078, 2, 4.533, -0.088, 2, 4.567, -0.098, 2, 4.6, -0.108, 2, 4.633, -0.119, 2, 4.667, -0.13, 2, 4.7, -0.141, 2, 4.733, -0.152, 2, 4.767, -0.164, 2, 4.8, -0.176, 2, 4.833, -0.188, 2, 4.867, -0.2, 2, 4.9, -0.213, 2, 4.933, -0.225, 2, 4.967, -0.238, 2, 5, -0.25, 2, 5.033, -0.262, 2, 5.067, -0.275, 2, 5.1, -0.287, 2, 5.133, -0.3, 2, 5.167, -0.312, 2, 5.2, -0.324, 2, 5.233, -0.336, 2, 5.267, -0.348, 2, 5.3, -0.359, 2, 5.333, -0.37, 2, 5.367, -0.381, 2, 5.4, -0.392, 2, 5.433, -0.402, 2, 5.467, -0.412, 2, 5.5, -0.422, 2, 5.533, -0.431, 2, 5.567, -0.44, 2, 5.6, -0.448, 2, 5.633, -0.456, 2, 5.667, -0.463, 2, 5.7, -0.47, 2, 5.733, -0.476, 2, 5.767, -0.481, 2, 5.8, -0.486, 2, 5.833, -0.49, 2, 5.867, -0.494, 2, 5.9, -0.496, 2, 5.933, -0.498, 2, 5.967, -0.5, 2, 6, -0.5, 2, 6.033, -0.5, 2, 6.067, -0.498, 2, 6.1, -0.496, 2, 6.133, -0.494, 2, 6.167, -0.49, 2, 6.2, -0.486, 2, 6.233, -0.481, 2, 6.267, -0.476, 2, 6.3, -0.47, 2, 6.333, -0.463, 2, 6.367, -0.456, 2, 6.4, -0.448, 2, 6.433, -0.44, 2, 6.467, -0.431, 2, 6.5, -0.422, 2, 6.533, -0.412, 2, 6.567, -0.402, 2, 6.6, -0.392, 2, 6.633, -0.381, 2, 6.667, -0.37, 2, 6.7, -0.359, 2, 6.733, -0.348, 2, 6.767, -0.336, 2, 6.8, -0.324, 2, 6.833, -0.312, 2, 6.867, -0.3, 2, 6.9, -0.287, 2, 6.933, -0.275, 2, 6.967, -0.262, 2, 7, -0.25, 2, 7.033, -0.238, 2, 7.067, -0.225, 2, 7.1, -0.213, 2, 7.133, -0.2, 2, 7.167, -0.188, 2, 7.2, -0.176, 2, 7.233, -0.164, 2, 7.267, -0.152, 2, 7.3, -0.141, 2, 7.333, -0.13, 2, 7.367, -0.119, 2, 7.4, -0.108, 2, 7.433, -0.098, 2, 7.467, -0.088, 2, 7.5, -0.078, 2, 7.533, -0.069, 2, 7.567, -0.06, 2, 7.6, -0.052, 2, 7.633, -0.044, 2, 7.667, -0.037, 2, 7.7, -0.03, 2, 7.733, -0.024, 2, 7.767, -0.019, 2, 7.8, -0.014, 2, 7.833, -0.01, 2, 7.867, -0.006, 2, 7.9, -0.004, 2, 7.933, -0.002, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangL", "Segments": [0, 0, 2, 0.667, 0.222, 2, 0.7, 0.623, 2, 0.733, 0.844, 2, 0.767, 0.393, 2, 0.8, -0.423, 2, 0.833, -0.875, 2, 0.867, -0.63, 2, 0.9, -0.091, 2, 0.933, 0.448, 2, 0.967, 0.693, 2, 1, 0.625, 2, 1.033, 0.689, 2, 1.067, 0.753, 2, 1.1, 0.583, 2, 1.133, 0.176, 2, 1.167, -0.32, 2, 1.2, -0.727, 2, 1.233, -0.898, 2, 1.267, -0.482, 2, 1.3, 0.27, 2, 1.333, 0.686, 2, 1.367, 0.618, 2, 1.4, 0.454, 2, 1.433, 0.235, 2, 1.467, 0.016, 2, 1.5, -0.149, 2, 1.533, -0.217, 2, 1.567, -0.2, 2, 1.6, -0.156, 2, 1.633, -0.096, 2, 1.667, -0.032, 2, 1.7, 0.028, 2, 1.733, 0.072, 2, 1.767, 0.089, 2, 1.8, 0.079, 2, 1.833, 0.056, 2, 1.867, 0.025, 2, 1.9, -0.006, 2, 1.933, -0.029, 2, 1.967, -0.038, 2, 2, -0.034, 2, 2.033, -0.024, 2, 2.067, -0.011, 2, 2.1, 0.002, 2, 2.133, 0.012, 2, 2.167, 0.017, 2, 2.2, 0.015, 2, 2.233, 0.01, 2, 2.267, 0.005, 2, 2.3, -0.001, 2, 2.333, -0.005, 2, 2.367, -0.007, 2, 2.4, -0.006, 2, 2.433, -0.004, 2, 2.467, -0.002, 2, 2.5, 0, 2, 2.533, 0.002, 2, 2.567, 0.003, 2, 2.6, 0.003, 2, 2.633, 0.002, 2, 2.667, 0.001, 2, 2.7, 0, 2, 2.733, -0.001, 2, 2.767, -0.001, 2, 2.8, -0.001, 2, 2.833, -0.001, 2, 2.867, 0, 2, 2.9, 0, 2, 2.933, 0, 2, 2.967, 0.001, 2, 3, 0.001, 2, 3.033, 0, 2, 3.067, 0, 2, 3.1, 0, 2, 3.133, 0, 2, 3.267, 0, 2, 3.3, 0, 2, 3.333, 0, 2, 3.467, 0.222, 2, 3.5, 0.623, 2, 3.533, 0.844, 2, 3.567, 0.393, 2, 3.6, -0.423, 2, 3.633, -0.875, 2, 3.667, -0.63, 2, 3.7, -0.091, 2, 3.733, 0.448, 2, 3.767, 0.693, 2, 3.8, 0.643, 2, 3.833, 0.514, 2, 3.867, 0.334, 2, 3.9, 0.144, 2, 3.933, -0.036, 2, 3.967, -0.166, 2, 4, -0.216, 2, 4.033, -0.193, 2, 4.067, -0.137, 2, 4.1, -0.063, 2, 4.133, 0.012, 2, 4.167, 0.068, 2, 4.2, 0.091, 2, 4.233, 0.081, 2, 4.267, 0.057, 2, 4.3, 0.026, 2, 4.333, -0.006, 2, 4.367, -0.029, 2, 4.4, -0.039, 2, 4.433, -0.035, 2, 4.467, -0.025, 2, 4.5, -0.011, 2, 4.533, 0.002, 2, 4.567, 0.013, 2, 4.6, 0.017, 2, 4.633, 0.015, 2, 4.667, 0.011, 2, 4.7, 0.005, 2, 4.733, -0.001, 2, 4.767, -0.005, 2, 4.8, -0.007, 2, 4.833, -0.006, 2, 4.867, -0.005, 2, 4.9, -0.002, 2, 4.933, 0, 2, 4.967, 0.002, 2, 5, 0.003, 2, 5.033, 0.003, 2, 5.067, 0.002, 2, 5.1, 0.001, 2, 5.133, 0, 2, 5.167, -0.001, 2, 5.2, -0.001, 2, 5.233, -0.001, 2, 5.267, -0.001, 2, 5.3, 0, 2, 5.333, 0, 2, 5.367, 0, 2, 5.4, 0.001, 2, 5.433, 0.001, 2, 5.467, 0, 2, 5.5, 0, 2, 5.533, 0, 2, 5.567, 0, 2, 5.7, 0, 2, 5.733, 0, 2, 5.767, 0, 2, 5.9, 0, 2, 5.933, 0.222, 2, 5.967, 0.623, 2, 6, 0.844, 2, 6.033, 0.393, 2, 6.067, -0.423, 2, 6.1, -0.875, 2, 6.133, -0.63, 2, 6.167, -0.091, 2, 6.2, 0.448, 2, 6.233, 0.693, 2, 6.267, 0.643, 2, 6.3, 0.514, 2, 6.333, 0.334, 2, 6.367, 0.144, 2, 6.4, -0.036, 2, 6.433, -0.166, 2, 6.467, -0.216, 2, 6.5, -0.193, 2, 6.533, -0.137, 2, 6.567, -0.063, 2, 6.6, 0.012, 2, 6.633, 0.068, 2, 6.667, 0.091, 2, 6.7, 0.081, 2, 6.733, 0.057, 2, 6.767, 0.026, 2, 6.8, -0.006, 2, 6.833, -0.029, 2, 6.867, -0.039, 2, 6.9, -0.035, 2, 6.933, -0.025, 2, 6.967, -0.011, 2, 7, 0.002, 2, 7.033, 0.013, 2, 7.067, 0.017, 2, 7.1, 0.015, 2, 7.133, 0.011, 2, 7.167, 0.005, 2, 7.2, -0.001, 2, 7.233, -0.005, 2, 7.267, -0.007, 2, 7.3, -0.006, 2, 7.333, -0.005, 2, 7.367, -0.002, 2, 7.4, 0, 2, 7.433, 0.002, 2, 7.467, 0.003, 2, 7.5, 0.003, 2, 7.533, 0.002, 2, 7.567, 0.001, 2, 7.6, 0, 2, 7.633, -0.001, 2, 7.667, -0.001, 2, 7.7, -0.001, 2, 7.733, -0.001, 2, 7.767, 0, 2, 7.8, 0, 2, 7.833, 0, 2, 7.867, 0.001, 2, 7.9, 0.001, 2, 7.933, 0, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangR", "Segments": [0, 0, 2, 0.667, 0.222, 2, 0.7, 0.623, 2, 0.733, 0.844, 2, 0.767, 0.393, 2, 0.8, -0.423, 2, 0.833, -0.875, 2, 0.867, -0.63, 2, 0.9, -0.091, 2, 0.933, 0.448, 2, 0.967, 0.693, 2, 1, 0.625, 2, 1.033, 0.689, 2, 1.067, 0.753, 2, 1.1, 0.583, 2, 1.133, 0.176, 2, 1.167, -0.32, 2, 1.2, -0.727, 2, 1.233, -0.898, 2, 1.267, -0.482, 2, 1.3, 0.27, 2, 1.333, 0.686, 2, 1.367, 0.618, 2, 1.4, 0.454, 2, 1.433, 0.235, 2, 1.467, 0.016, 2, 1.5, -0.149, 2, 1.533, -0.217, 2, 1.567, -0.2, 2, 1.6, -0.156, 2, 1.633, -0.096, 2, 1.667, -0.032, 2, 1.7, 0.028, 2, 1.733, 0.072, 2, 1.767, 0.089, 2, 1.8, 0.079, 2, 1.833, 0.056, 2, 1.867, 0.025, 2, 1.9, -0.006, 2, 1.933, -0.029, 2, 1.967, -0.038, 2, 2, -0.034, 2, 2.033, -0.024, 2, 2.067, -0.011, 2, 2.1, 0.002, 2, 2.133, 0.012, 2, 2.167, 0.017, 2, 2.2, 0.015, 2, 2.233, 0.01, 2, 2.267, 0.005, 2, 2.3, -0.001, 2, 2.333, -0.005, 2, 2.367, -0.007, 2, 2.4, -0.006, 2, 2.433, -0.004, 2, 2.467, -0.002, 2, 2.5, 0, 2, 2.533, 0.002, 2, 2.567, 0.003, 2, 2.6, 0.003, 2, 2.633, 0.002, 2, 2.667, 0.001, 2, 2.7, 0, 2, 2.733, -0.001, 2, 2.767, -0.001, 2, 2.8, -0.001, 2, 2.833, -0.001, 2, 2.867, 0, 2, 2.9, 0, 2, 2.933, 0, 2, 2.967, 0.001, 2, 3, 0.001, 2, 3.033, 0, 2, 3.067, 0, 2, 3.1, 0, 2, 3.133, 0, 2, 3.267, 0, 2, 3.3, 0, 2, 3.333, 0, 2, 3.467, 0.222, 2, 3.5, 0.623, 2, 3.533, 0.844, 2, 3.567, 0.393, 2, 3.6, -0.423, 2, 3.633, -0.875, 2, 3.667, -0.63, 2, 3.7, -0.091, 2, 3.733, 0.448, 2, 3.767, 0.693, 2, 3.8, 0.643, 2, 3.833, 0.514, 2, 3.867, 0.334, 2, 3.9, 0.144, 2, 3.933, -0.036, 2, 3.967, -0.166, 2, 4, -0.216, 2, 4.033, -0.193, 2, 4.067, -0.137, 2, 4.1, -0.063, 2, 4.133, 0.012, 2, 4.167, 0.068, 2, 4.2, 0.091, 2, 4.233, 0.081, 2, 4.267, 0.057, 2, 4.3, 0.026, 2, 4.333, -0.006, 2, 4.367, -0.029, 2, 4.4, -0.039, 2, 4.433, -0.035, 2, 4.467, -0.025, 2, 4.5, -0.011, 2, 4.533, 0.002, 2, 4.567, 0.013, 2, 4.6, 0.017, 2, 4.633, 0.015, 2, 4.667, 0.011, 2, 4.7, 0.005, 2, 4.733, -0.001, 2, 4.767, -0.005, 2, 4.8, -0.007, 2, 4.833, -0.006, 2, 4.867, -0.005, 2, 4.9, -0.002, 2, 4.933, 0, 2, 4.967, 0.002, 2, 5, 0.003, 2, 5.033, 0.003, 2, 5.067, 0.002, 2, 5.1, 0.001, 2, 5.133, 0, 2, 5.167, -0.001, 2, 5.2, -0.001, 2, 5.233, -0.001, 2, 5.267, -0.001, 2, 5.3, 0, 2, 5.333, 0, 2, 5.367, 0, 2, 5.4, 0.001, 2, 5.433, 0.001, 2, 5.467, 0, 2, 5.5, 0, 2, 5.533, 0, 2, 5.567, 0, 2, 5.7, 0, 2, 5.733, 0, 2, 5.767, 0, 2, 5.9, 0, 2, 5.933, 0.222, 2, 5.967, 0.623, 2, 6, 0.844, 2, 6.033, 0.393, 2, 6.067, -0.423, 2, 6.1, -0.875, 2, 6.133, -0.63, 2, 6.167, -0.091, 2, 6.2, 0.448, 2, 6.233, 0.693, 2, 6.267, 0.643, 2, 6.3, 0.514, 2, 6.333, 0.334, 2, 6.367, 0.144, 2, 6.4, -0.036, 2, 6.433, -0.166, 2, 6.467, -0.216, 2, 6.5, -0.193, 2, 6.533, -0.137, 2, 6.567, -0.063, 2, 6.6, 0.012, 2, 6.633, 0.068, 2, 6.667, 0.091, 2, 6.7, 0.081, 2, 6.733, 0.057, 2, 6.767, 0.026, 2, 6.8, -0.006, 2, 6.833, -0.029, 2, 6.867, -0.039, 2, 6.9, -0.035, 2, 6.933, -0.025, 2, 6.967, -0.011, 2, 7, 0.002, 2, 7.033, 0.013, 2, 7.067, 0.017, 2, 7.1, 0.015, 2, 7.133, 0.011, 2, 7.167, 0.005, 2, 7.2, -0.001, 2, 7.233, -0.005, 2, 7.267, -0.007, 2, 7.3, -0.006, 2, 7.333, -0.005, 2, 7.367, -0.002, 2, 7.4, 0, 2, 7.433, 0.002, 2, 7.467, 0.003, 2, 7.5, 0.003, 2, 7.533, 0.002, 2, 7.567, 0.001, 2, 7.6, 0, 2, 7.633, -0.001, 2, 7.667, -0.001, 2, 7.7, -0.001, 2, 7.733, -0.001, 2, 7.767, 0, 2, 7.8, 0, 2, 7.833, 0, 2, 7.867, 0.001, 2, 7.9, 0.001, 2, 7.933, 0, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamGaoGguangL", "Segments": [0, 0, 2, 0.667, 0.222, 2, 0.7, 0.623, 2, 0.733, 0.844, 2, 0.767, 0.393, 2, 0.8, -0.423, 2, 0.833, -0.875, 2, 0.867, -0.63, 2, 0.9, -0.091, 2, 0.933, 0.448, 2, 0.967, 0.693, 2, 1, 0.625, 2, 1.033, 0.689, 2, 1.067, 0.753, 2, 1.1, 0.583, 2, 1.133, 0.176, 2, 1.167, -0.32, 2, 1.2, -0.727, 2, 1.233, -0.898, 2, 1.267, -0.482, 2, 1.3, 0.27, 2, 1.333, 0.686, 2, 1.367, 0.618, 2, 1.4, 0.454, 2, 1.433, 0.235, 2, 1.467, 0.016, 2, 1.5, -0.149, 2, 1.533, -0.217, 2, 1.567, -0.2, 2, 1.6, -0.156, 2, 1.633, -0.096, 2, 1.667, -0.032, 2, 1.7, 0.028, 2, 1.733, 0.072, 2, 1.767, 0.089, 2, 1.8, 0.079, 2, 1.833, 0.056, 2, 1.867, 0.025, 2, 1.9, -0.006, 2, 1.933, -0.029, 2, 1.967, -0.038, 2, 2, -0.034, 2, 2.033, -0.024, 2, 2.067, -0.011, 2, 2.1, 0.002, 2, 2.133, 0.012, 2, 2.167, 0.017, 2, 2.2, 0.015, 2, 2.233, 0.01, 2, 2.267, 0.005, 2, 2.3, -0.001, 2, 2.333, -0.005, 2, 2.367, -0.007, 2, 2.4, -0.006, 2, 2.433, -0.004, 2, 2.467, -0.002, 2, 2.5, 0, 2, 2.533, 0.002, 2, 2.567, 0.003, 2, 2.6, 0.003, 2, 2.633, 0.002, 2, 2.667, 0.001, 2, 2.7, 0, 2, 2.733, -0.001, 2, 2.767, -0.001, 2, 2.8, -0.001, 2, 2.833, -0.001, 2, 2.867, 0, 2, 2.9, 0, 2, 2.933, 0, 2, 2.967, 0.001, 2, 3, 0.001, 2, 3.033, 0, 2, 3.067, 0, 2, 3.1, 0, 2, 3.133, 0, 2, 3.267, 0, 2, 3.3, 0, 2, 3.333, 0, 2, 3.467, 0.222, 2, 3.5, 0.623, 2, 3.533, 0.844, 2, 3.567, 0.393, 2, 3.6, -0.423, 2, 3.633, -0.875, 2, 3.667, -0.63, 2, 3.7, -0.091, 2, 3.733, 0.448, 2, 3.767, 0.693, 2, 3.8, 0.643, 2, 3.833, 0.514, 2, 3.867, 0.334, 2, 3.9, 0.144, 2, 3.933, -0.036, 2, 3.967, -0.166, 2, 4, -0.216, 2, 4.033, -0.193, 2, 4.067, -0.137, 2, 4.1, -0.063, 2, 4.133, 0.012, 2, 4.167, 0.068, 2, 4.2, 0.091, 2, 4.233, 0.081, 2, 4.267, 0.057, 2, 4.3, 0.026, 2, 4.333, -0.006, 2, 4.367, -0.029, 2, 4.4, -0.039, 2, 4.433, -0.035, 2, 4.467, -0.025, 2, 4.5, -0.011, 2, 4.533, 0.002, 2, 4.567, 0.013, 2, 4.6, 0.017, 2, 4.633, 0.015, 2, 4.667, 0.011, 2, 4.7, 0.005, 2, 4.733, -0.001, 2, 4.767, -0.005, 2, 4.8, -0.007, 2, 4.833, -0.006, 2, 4.867, -0.005, 2, 4.9, -0.002, 2, 4.933, 0, 2, 4.967, 0.002, 2, 5, 0.003, 2, 5.033, 0.003, 2, 5.067, 0.002, 2, 5.1, 0.001, 2, 5.133, 0, 2, 5.167, -0.001, 2, 5.2, -0.001, 2, 5.233, -0.001, 2, 5.267, -0.001, 2, 5.3, 0, 2, 5.333, 0, 2, 5.367, 0, 2, 5.4, 0.001, 2, 5.433, 0.001, 2, 5.467, 0, 2, 5.5, 0, 2, 5.533, 0, 2, 5.567, 0, 2, 5.7, 0, 2, 5.733, 0, 2, 5.767, 0, 2, 5.9, 0, 2, 5.933, 0.222, 2, 5.967, 0.623, 2, 6, 0.844, 2, 6.033, 0.393, 2, 6.067, -0.423, 2, 6.1, -0.875, 2, 6.133, -0.63, 2, 6.167, -0.091, 2, 6.2, 0.448, 2, 6.233, 0.693, 2, 6.267, 0.643, 2, 6.3, 0.514, 2, 6.333, 0.334, 2, 6.367, 0.144, 2, 6.4, -0.036, 2, 6.433, -0.166, 2, 6.467, -0.216, 2, 6.5, -0.193, 2, 6.533, -0.137, 2, 6.567, -0.063, 2, 6.6, 0.012, 2, 6.633, 0.068, 2, 6.667, 0.091, 2, 6.7, 0.081, 2, 6.733, 0.057, 2, 6.767, 0.026, 2, 6.8, -0.006, 2, 6.833, -0.029, 2, 6.867, -0.039, 2, 6.9, -0.035, 2, 6.933, -0.025, 2, 6.967, -0.011, 2, 7, 0.002, 2, 7.033, 0.013, 2, 7.067, 0.017, 2, 7.1, 0.015, 2, 7.133, 0.011, 2, 7.167, 0.005, 2, 7.2, -0.001, 2, 7.233, -0.005, 2, 7.267, -0.007, 2, 7.3, -0.006, 2, 7.333, -0.005, 2, 7.367, -0.002, 2, 7.4, 0, 2, 7.433, 0.002, 2, 7.467, 0.003, 2, 7.5, 0.003, 2, 7.533, 0.002, 2, 7.567, 0.001, 2, 7.6, 0, 2, 7.633, -0.001, 2, 7.667, -0.001, 2, 7.7, -0.001, 2, 7.733, -0.001, 2, 7.767, 0, 2, 7.8, 0, 2, 7.833, 0, 2, 7.867, 0.001, 2, 7.9, 0.001, 2, 7.933, 0, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamGaoGuangR", "Segments": [0, 0, 2, 0.667, 0.222, 2, 0.7, 0.623, 2, 0.733, 0.844, 2, 0.767, 0.393, 2, 0.8, -0.423, 2, 0.833, -0.875, 2, 0.867, -0.63, 2, 0.9, -0.091, 2, 0.933, 0.448, 2, 0.967, 0.693, 2, 1, 0.625, 2, 1.033, 0.689, 2, 1.067, 0.753, 2, 1.1, 0.583, 2, 1.133, 0.176, 2, 1.167, -0.32, 2, 1.2, -0.727, 2, 1.233, -0.898, 2, 1.267, -0.482, 2, 1.3, 0.27, 2, 1.333, 0.686, 2, 1.367, 0.618, 2, 1.4, 0.454, 2, 1.433, 0.235, 2, 1.467, 0.016, 2, 1.5, -0.149, 2, 1.533, -0.217, 2, 1.567, -0.2, 2, 1.6, -0.156, 2, 1.633, -0.096, 2, 1.667, -0.032, 2, 1.7, 0.028, 2, 1.733, 0.072, 2, 1.767, 0.089, 2, 1.8, 0.079, 2, 1.833, 0.056, 2, 1.867, 0.025, 2, 1.9, -0.006, 2, 1.933, -0.029, 2, 1.967, -0.038, 2, 2, -0.034, 2, 2.033, -0.024, 2, 2.067, -0.011, 2, 2.1, 0.002, 2, 2.133, 0.012, 2, 2.167, 0.017, 2, 2.2, 0.015, 2, 2.233, 0.01, 2, 2.267, 0.005, 2, 2.3, -0.001, 2, 2.333, -0.005, 2, 2.367, -0.007, 2, 2.4, -0.006, 2, 2.433, -0.004, 2, 2.467, -0.002, 2, 2.5, 0, 2, 2.533, 0.002, 2, 2.567, 0.003, 2, 2.6, 0.003, 2, 2.633, 0.002, 2, 2.667, 0.001, 2, 2.7, 0, 2, 2.733, -0.001, 2, 2.767, -0.001, 2, 2.8, -0.001, 2, 2.833, -0.001, 2, 2.867, 0, 2, 2.9, 0, 2, 2.933, 0, 2, 2.967, 0.001, 2, 3, 0.001, 2, 3.033, 0, 2, 3.067, 0, 2, 3.1, 0, 2, 3.133, 0, 2, 3.267, 0, 2, 3.3, 0, 2, 3.333, 0, 2, 3.467, 0.222, 2, 3.5, 0.623, 2, 3.533, 0.844, 2, 3.567, 0.393, 2, 3.6, -0.423, 2, 3.633, -0.875, 2, 3.667, -0.63, 2, 3.7, -0.091, 2, 3.733, 0.448, 2, 3.767, 0.693, 2, 3.8, 0.643, 2, 3.833, 0.514, 2, 3.867, 0.334, 2, 3.9, 0.144, 2, 3.933, -0.036, 2, 3.967, -0.166, 2, 4, -0.216, 2, 4.033, -0.193, 2, 4.067, -0.137, 2, 4.1, -0.063, 2, 4.133, 0.012, 2, 4.167, 0.068, 2, 4.2, 0.091, 2, 4.233, 0.081, 2, 4.267, 0.057, 2, 4.3, 0.026, 2, 4.333, -0.006, 2, 4.367, -0.029, 2, 4.4, -0.039, 2, 4.433, -0.035, 2, 4.467, -0.025, 2, 4.5, -0.011, 2, 4.533, 0.002, 2, 4.567, 0.013, 2, 4.6, 0.017, 2, 4.633, 0.015, 2, 4.667, 0.011, 2, 4.7, 0.005, 2, 4.733, -0.001, 2, 4.767, -0.005, 2, 4.8, -0.007, 2, 4.833, -0.006, 2, 4.867, -0.005, 2, 4.9, -0.002, 2, 4.933, 0, 2, 4.967, 0.002, 2, 5, 0.003, 2, 5.033, 0.003, 2, 5.067, 0.002, 2, 5.1, 0.001, 2, 5.133, 0, 2, 5.167, -0.001, 2, 5.2, -0.001, 2, 5.233, -0.001, 2, 5.267, -0.001, 2, 5.3, 0, 2, 5.333, 0, 2, 5.367, 0, 2, 5.4, 0.001, 2, 5.433, 0.001, 2, 5.467, 0, 2, 5.5, 0, 2, 5.533, 0, 2, 5.567, 0, 2, 5.7, 0, 2, 5.733, 0, 2, 5.767, 0, 2, 5.9, 0, 2, 5.933, 0.222, 2, 5.967, 0.623, 2, 6, 0.844, 2, 6.033, 0.393, 2, 6.067, -0.423, 2, 6.1, -0.875, 2, 6.133, -0.63, 2, 6.167, -0.091, 2, 6.2, 0.448, 2, 6.233, 0.693, 2, 6.267, 0.643, 2, 6.3, 0.514, 2, 6.333, 0.334, 2, 6.367, 0.144, 2, 6.4, -0.036, 2, 6.433, -0.166, 2, 6.467, -0.216, 2, 6.5, -0.193, 2, 6.533, -0.137, 2, 6.567, -0.063, 2, 6.6, 0.012, 2, 6.633, 0.068, 2, 6.667, 0.091, 2, 6.7, 0.081, 2, 6.733, 0.057, 2, 6.767, 0.026, 2, 6.8, -0.006, 2, 6.833, -0.029, 2, 6.867, -0.039, 2, 6.9, -0.035, 2, 6.933, -0.025, 2, 6.967, -0.011, 2, 7, 0.002, 2, 7.033, 0.013, 2, 7.067, 0.017, 2, 7.1, 0.015, 2, 7.133, 0.011, 2, 7.167, 0.005, 2, 7.2, -0.001, 2, 7.233, -0.005, 2, 7.267, -0.007, 2, 7.3, -0.006, 2, 7.333, -0.005, 2, 7.367, -0.002, 2, 7.4, 0, 2, 7.433, 0.002, 2, 7.467, 0.003, 2, 7.5, 0.003, 2, 7.533, 0.002, 2, 7.567, 0.001, 2, 7.6, 0, 2, 7.633, -0.001, 2, 7.667, -0.001, 2, 7.7, -0.001, 2, 7.733, -0.001, 2, 7.767, 0, 2, 7.8, 0, 2, 7.833, 0, 2, 7.867, 0.001, 2, 7.9, 0.001, 2, 7.933, 0, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.033, 0.001, 2, 0.067, 0.003, 2, 0.1, 0.007, 2, 0.133, 0.013, 2, 0.167, 0.02, 2, 0.2, 0.028, 2, 0.233, 0.038, 2, 0.267, 0.049, 2, 0.3, 0.061, 2, 0.333, 0.074, 2, 0.367, 0.089, 2, 0.4, 0.104, 2, 0.433, 0.121, 2, 0.467, 0.138, 2, 0.5, 0.156, 2, 0.533, 0.175, 2, 0.567, 0.195, 2, 0.6, 0.216, 2, 0.633, 0.237, 2, 0.667, 0.259, 2, 0.7, 0.282, 2, 0.733, 0.305, 2, 0.767, 0.328, 2, 0.8, 0.352, 2, 0.833, 0.376, 2, 0.867, 0.401, 2, 0.9, 0.425, 2, 0.933, 0.45, 2, 0.967, 0.475, 2, 1, 0.5, 2, 1.033, 0.525, 2, 1.067, 0.55, 2, 1.1, 0.575, 2, 1.133, 0.599, 2, 1.167, 0.624, 2, 1.2, 0.648, 2, 1.233, 0.672, 2, 1.267, 0.695, 2, 1.3, 0.718, 2, 1.333, 0.741, 2, 1.367, 0.763, 2, 1.4, 0.784, 2, 1.433, 0.805, 2, 1.467, 0.825, 2, 1.5, 0.844, 2, 1.533, 0.862, 2, 1.567, 0.879, 2, 1.6, 0.896, 2, 1.633, 0.911, 2, 1.667, 0.926, 2, 1.7, 0.939, 2, 1.733, 0.951, 2, 1.767, 0.962, 2, 1.8, 0.972, 2, 1.833, 0.98, 2, 1.867, 0.987, 2, 1.9, 0.993, 2, 1.933, 0.997, 2, 1.967, 0.999, 2, 2, 1, 2, 2.033, 0.999, 2, 2.067, 0.997, 2, 2.1, 0.993, 2, 2.133, 0.987, 2, 2.167, 0.98, 2, 2.2, 0.972, 2, 2.233, 0.962, 2, 2.267, 0.951, 2, 2.3, 0.939, 2, 2.333, 0.926, 2, 2.367, 0.911, 2, 2.4, 0.896, 2, 2.433, 0.879, 2, 2.467, 0.862, 2, 2.5, 0.844, 2, 2.533, 0.825, 2, 2.567, 0.805, 2, 2.6, 0.784, 2, 2.633, 0.763, 2, 2.667, 0.741, 2, 2.7, 0.718, 2, 2.733, 0.695, 2, 2.767, 0.672, 2, 2.8, 0.648, 2, 2.833, 0.624, 2, 2.867, 0.599, 2, 2.9, 0.575, 2, 2.933, 0.55, 2, 2.967, 0.525, 2, 3, 0.5, 2, 3.033, 0.475, 2, 3.067, 0.45, 2, 3.1, 0.425, 2, 3.133, 0.401, 2, 3.167, 0.376, 2, 3.2, 0.352, 2, 3.233, 0.328, 2, 3.267, 0.305, 2, 3.3, 0.282, 2, 3.333, 0.259, 2, 3.367, 0.237, 2, 3.4, 0.216, 2, 3.433, 0.195, 2, 3.467, 0.175, 2, 3.5, 0.156, 2, 3.533, 0.138, 2, 3.567, 0.121, 2, 3.6, 0.104, 2, 3.633, 0.089, 2, 3.667, 0.074, 2, 3.7, 0.061, 2, 3.733, 0.049, 2, 3.767, 0.038, 2, 3.8, 0.028, 2, 3.833, 0.02, 2, 3.867, 0.013, 2, 3.9, 0.007, 2, 3.933, 0.003, 2, 3.967, 0.001, 2, 4, 0, 2, 4.033, 0.001, 2, 4.067, 0.003, 2, 4.1, 0.007, 2, 4.133, 0.013, 2, 4.167, 0.02, 2, 4.2, 0.028, 2, 4.233, 0.038, 2, 4.267, 0.049, 2, 4.3, 0.061, 2, 4.333, 0.074, 2, 4.367, 0.089, 2, 4.4, 0.104, 2, 4.433, 0.121, 2, 4.467, 0.138, 2, 4.5, 0.156, 2, 4.533, 0.175, 2, 4.567, 0.195, 2, 4.6, 0.216, 2, 4.633, 0.237, 2, 4.667, 0.259, 2, 4.7, 0.282, 2, 4.733, 0.305, 2, 4.767, 0.328, 2, 4.8, 0.352, 2, 4.833, 0.376, 2, 4.867, 0.401, 2, 4.9, 0.425, 2, 4.933, 0.45, 2, 4.967, 0.475, 2, 5, 0.5, 2, 5.033, 0.525, 2, 5.067, 0.55, 2, 5.1, 0.575, 2, 5.133, 0.599, 2, 5.167, 0.624, 2, 5.2, 0.648, 2, 5.233, 0.672, 2, 5.267, 0.695, 2, 5.3, 0.718, 2, 5.333, 0.741, 2, 5.367, 0.763, 2, 5.4, 0.784, 2, 5.433, 0.805, 2, 5.467, 0.825, 2, 5.5, 0.844, 2, 5.533, 0.862, 2, 5.567, 0.879, 2, 5.6, 0.896, 2, 5.633, 0.911, 2, 5.667, 0.926, 2, 5.7, 0.939, 2, 5.733, 0.951, 2, 5.767, 0.962, 2, 5.8, 0.972, 2, 5.833, 0.98, 2, 5.867, 0.987, 2, 5.9, 0.993, 2, 5.933, 0.997, 2, 5.967, 0.999, 2, 6, 1, 2, 6.033, 0.999, 2, 6.067, 0.997, 2, 6.1, 0.993, 2, 6.133, 0.987, 2, 6.167, 0.98, 2, 6.2, 0.972, 2, 6.233, 0.962, 2, 6.267, 0.951, 2, 6.3, 0.939, 2, 6.333, 0.926, 2, 6.367, 0.911, 2, 6.4, 0.896, 2, 6.433, 0.879, 2, 6.467, 0.862, 2, 6.5, 0.844, 2, 6.533, 0.825, 2, 6.567, 0.805, 2, 6.6, 0.784, 2, 6.633, 0.763, 2, 6.667, 0.741, 2, 6.7, 0.718, 2, 6.733, 0.695, 2, 6.767, 0.672, 2, 6.8, 0.648, 2, 6.833, 0.624, 2, 6.867, 0.599, 2, 6.9, 0.575, 2, 6.933, 0.55, 2, 6.967, 0.525, 2, 7, 0.5, 2, 7.033, 0.475, 2, 7.067, 0.45, 2, 7.1, 0.425, 2, 7.133, 0.401, 2, 7.167, 0.376, 2, 7.2, 0.352, 2, 7.233, 0.328, 2, 7.267, 0.305, 2, 7.3, 0.282, 2, 7.333, 0.259, 2, 7.367, 0.237, 2, 7.4, 0.216, 2, 7.433, 0.195, 2, 7.467, 0.175, 2, 7.5, 0.156, 2, 7.533, 0.138, 2, 7.567, 0.121, 2, 7.6, 0.104, 2, 7.633, 0.089, 2, 7.667, 0.074, 2, 7.7, 0.061, 2, 7.733, 0.049, 2, 7.767, 0.038, 2, 7.8, 0.028, 2, 7.833, 0.02, 2, 7.867, 0.013, 2, 7.9, 0.007, 2, 7.933, 0.003, 2, 7.967, 0.001, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.033, -0.003, 2, 0.067, -0.013, 2, 0.1, -0.029, 2, 0.133, -0.051, 2, 0.167, -0.079, 2, 0.2, -0.112, 2, 0.233, -0.151, 2, 0.267, -0.194, 2, 0.3, -0.243, 2, 0.333, -0.296, 2, 0.367, -0.354, 2, 0.4, -0.416, 2, 0.433, -0.482, 2, 0.467, -0.552, 2, 0.5, -0.625, 2, 0.533, -0.701, 2, 0.567, -0.781, 2, 0.6, -0.864, 2, 0.633, -0.949, 2, 0.667, -1.038, 2, 0.7, -1.128, 2, 0.733, -1.219, 2, 0.767, -1.313, 2, 0.8, -1.407, 2, 0.833, -1.504, 2, 0.867, -1.602, 2, 0.9, -1.701, 2, 0.933, -1.801, 2, 0.967, -1.9, 2, 1, -2, 2, 1.033, -2.1, 2, 1.067, -2.199, 2, 1.1, -2.299, 2, 1.133, -2.398, 2, 1.167, -2.496, 2, 1.2, -2.593, 2, 1.233, -2.687, 2, 1.267, -2.781, 2, 1.3, -2.872, 2, 1.333, -2.962, 2, 1.367, -3.051, 2, 1.4, -3.136, 2, 1.433, -3.219, 2, 1.467, -3.299, 2, 1.5, -3.375, 2, 1.533, -3.448, 2, 1.567, -3.518, 2, 1.6, -3.584, 2, 1.633, -3.646, 2, 1.667, -3.704, 2, 1.7, -3.757, 2, 1.733, -3.806, 2, 1.767, -3.849, 2, 1.8, -3.888, 2, 1.833, -3.921, 2, 1.867, -3.949, 2, 1.9, -3.971, 2, 1.933, -3.987, 2, 1.967, -3.997, 2, 2, -4, 2, 2.033, -3.999, 2, 2.067, -3.996, 2, 2.1, -3.99, 2, 2.133, -3.982, 2, 2.167, -3.972, 2, 2.2, -3.96, 2, 2.233, -3.946, 2, 2.267, -3.929, 2, 2.3, -3.91, 2, 2.333, -3.889, 2, 2.367, -3.866, 2, 2.4, -3.84, 2, 2.433, -3.812, 2, 2.467, -3.782, 2, 2.5, -3.75, 2, 2.533, -3.716, 2, 2.567, -3.679, 2, 2.6, -3.64, 2, 2.633, -3.599, 2, 2.667, -3.555, 2, 2.7, -3.51, 2, 2.733, -3.462, 2, 2.767, -3.412, 2, 2.8, -3.36, 2, 2.833, -3.306, 2, 2.867, -3.249, 2, 2.9, -3.19, 2, 2.933, -3.128, 2, 2.967, -3.065, 2, 3, -3, 2, 3.033, -2.932, 2, 3.067, -2.863, 2, 3.1, -2.79, 2, 3.133, -2.716, 2, 3.167, -2.638, 2, 3.2, -2.56, 2, 3.233, -2.479, 2, 3.267, -2.396, 2, 3.3, -2.311, 2, 3.333, -2.223, 2, 3.367, -2.132, 2, 3.4, -2.04, 2, 3.433, -1.945, 2, 3.467, -1.849, 2, 3.5, -1.75, 2, 3.533, -1.649, 2, 3.567, -1.546, 2, 3.6, -1.44, 2, 3.633, -1.332, 2, 3.667, -1.221, 2, 3.7, -1.109, 2, 3.733, -0.995, 2, 3.767, -0.879, 2, 3.8, -0.761, 2, 3.833, -0.64, 2, 3.867, -0.515, 2, 3.9, -0.39, 2, 3.933, -0.261, 2, 3.967, -0.132, 2, 4, 0, 2, 4.033, 0.132, 2, 4.067, 0.261, 2, 4.1, 0.39, 2, 4.133, 0.515, 2, 4.167, 0.64, 2, 4.2, 0.761, 2, 4.233, 0.879, 2, 4.267, 0.995, 2, 4.3, 1.109, 2, 4.333, 1.221, 2, 4.367, 1.332, 2, 4.4, 1.44, 2, 4.433, 1.546, 2, 4.467, 1.649, 2, 4.5, 1.75, 2, 4.533, 1.849, 2, 4.567, 1.945, 2, 4.6, 2.04, 2, 4.633, 2.132, 2, 4.667, 2.223, 2, 4.7, 2.311, 2, 4.733, 2.396, 2, 4.767, 2.479, 2, 4.8, 2.56, 2, 4.833, 2.638, 2, 4.867, 2.716, 2, 4.9, 2.79, 2, 4.933, 2.863, 2, 4.967, 2.932, 2, 5, 3, 2, 5.033, 3.065, 2, 5.067, 3.128, 2, 5.1, 3.19, 2, 5.133, 3.249, 2, 5.167, 3.306, 2, 5.2, 3.36, 2, 5.233, 3.412, 2, 5.267, 3.462, 2, 5.3, 3.51, 2, 5.333, 3.555, 2, 5.367, 3.599, 2, 5.4, 3.64, 2, 5.433, 3.679, 2, 5.467, 3.716, 2, 5.5, 3.75, 2, 5.533, 3.782, 2, 5.567, 3.812, 2, 5.6, 3.84, 2, 5.633, 3.866, 2, 5.667, 3.889, 2, 5.7, 3.91, 2, 5.733, 3.929, 2, 5.767, 3.946, 2, 5.8, 3.96, 2, 5.833, 3.972, 2, 5.867, 3.982, 2, 5.9, 3.99, 2, 5.933, 3.996, 2, 5.967, 3.999, 2, 6, 4, 2, 6.033, 3.997, 2, 6.067, 3.987, 2, 6.1, 3.971, 2, 6.133, 3.949, 2, 6.167, 3.921, 2, 6.2, 3.888, 2, 6.233, 3.849, 2, 6.267, 3.806, 2, 6.3, 3.757, 2, 6.333, 3.704, 2, 6.367, 3.646, 2, 6.4, 3.584, 2, 6.433, 3.518, 2, 6.467, 3.448, 2, 6.5, 3.375, 2, 6.533, 3.299, 2, 6.567, 3.219, 2, 6.6, 3.136, 2, 6.633, 3.051, 2, 6.667, 2.962, 2, 6.7, 2.872, 2, 6.733, 2.781, 2, 6.767, 2.687, 2, 6.8, 2.593, 2, 6.833, 2.496, 2, 6.867, 2.398, 2, 6.9, 2.299, 2, 6.933, 2.199, 2, 6.967, 2.1, 2, 7, 2, 2, 7.033, 1.9, 2, 7.067, 1.801, 2, 7.1, 1.701, 2, 7.133, 1.602, 2, 7.167, 1.504, 2, 7.2, 1.407, 2, 7.233, 1.313, 2, 7.267, 1.219, 2, 7.3, 1.128, 2, 7.333, 1.038, 2, 7.367, 0.949, 2, 7.4, 0.864, 2, 7.433, 0.781, 2, 7.467, 0.701, 2, 7.5, 0.625, 2, 7.533, 0.552, 2, 7.567, 0.482, 2, 7.6, 0.416, 2, 7.633, 0.354, 2, 7.667, 0.296, 2, 7.7, 0.243, 2, 7.733, 0.194, 2, 7.767, 0.151, 2, 7.8, 0.112, 2, 7.833, 0.079, 2, 7.867, 0.051, 2, 7.9, 0.029, 2, 7.933, 0.013, 2, 7.967, 0.003, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.033, 0.002, 2, 0.067, 0.01, 2, 0.1, 0.022, 2, 0.133, 0.038, 2, 0.167, 0.059, 2, 0.2, 0.084, 2, 0.233, 0.113, 2, 0.267, 0.146, 2, 0.3, 0.182, 2, 0.333, 0.222, 2, 0.367, 0.266, 2, 0.4, 0.312, 2, 0.433, 0.362, 2, 0.467, 0.414, 2, 0.5, 0.469, 2, 0.533, 0.526, 2, 0.567, 0.586, 2, 0.6, 0.648, 2, 0.633, 0.712, 2, 0.667, 0.778, 2, 0.7, 0.846, 2, 0.733, 0.914, 2, 0.767, 0.984, 2, 0.8, 1.056, 2, 0.833, 1.128, 2, 0.867, 1.202, 2, 0.9, 1.276, 2, 0.933, 1.351, 2, 0.967, 1.425, 2, 1, 1.5, 2, 1.033, 1.575, 2, 1.067, 1.649, 2, 1.1, 1.724, 2, 1.133, 1.798, 2, 1.167, 1.872, 2, 1.2, 1.944, 2, 1.233, 2.016, 2, 1.267, 2.086, 2, 1.3, 2.154, 2, 1.333, 2.222, 2, 1.367, 2.288, 2, 1.4, 2.352, 2, 1.433, 2.414, 2, 1.467, 2.474, 2, 1.5, 2.531, 2, 1.533, 2.586, 2, 1.567, 2.638, 2, 1.6, 2.688, 2, 1.633, 2.734, 2, 1.667, 2.778, 2, 1.7, 2.818, 2, 1.733, 2.854, 2, 1.767, 2.887, 2, 1.8, 2.916, 2, 1.833, 2.941, 2, 1.867, 2.962, 2, 1.9, 2.978, 2, 1.933, 2.99, 2, 1.967, 2.998, 2, 2, 3, 2, 2.033, 2.998, 2, 2.067, 2.99, 2, 2.1, 2.978, 2, 2.133, 2.962, 2, 2.167, 2.941, 2, 2.2, 2.916, 2, 2.233, 2.887, 2, 2.267, 2.854, 2, 2.3, 2.818, 2, 2.333, 2.778, 2, 2.367, 2.734, 2, 2.4, 2.688, 2, 2.433, 2.638, 2, 2.467, 2.586, 2, 2.5, 2.531, 2, 2.533, 2.474, 2, 2.567, 2.414, 2, 2.6, 2.352, 2, 2.633, 2.288, 2, 2.667, 2.222, 2, 2.7, 2.154, 2, 2.733, 2.086, 2, 2.767, 2.016, 2, 2.8, 1.944, 2, 2.833, 1.872, 2, 2.867, 1.798, 2, 2.9, 1.724, 2, 2.933, 1.649, 2, 2.967, 1.575, 2, 3, 1.5, 2, 3.033, 1.425, 2, 3.067, 1.351, 2, 3.1, 1.276, 2, 3.133, 1.202, 2, 3.167, 1.128, 2, 3.2, 1.056, 2, 3.233, 0.984, 2, 3.267, 0.914, 2, 3.3, 0.846, 2, 3.333, 0.778, 2, 3.367, 0.712, 2, 3.4, 0.648, 2, 3.433, 0.586, 2, 3.467, 0.526, 2, 3.5, 0.469, 2, 3.533, 0.414, 2, 3.567, 0.362, 2, 3.6, 0.312, 2, 3.633, 0.266, 2, 3.667, 0.222, 2, 3.7, 0.182, 2, 3.733, 0.146, 2, 3.767, 0.113, 2, 3.8, 0.084, 2, 3.833, 0.059, 2, 3.867, 0.038, 2, 3.9, 0.022, 2, 3.933, 0.01, 2, 3.967, 0.002, 2, 4, 0, 2, 4.033, 0.002, 2, 4.067, 0.01, 2, 4.1, 0.022, 2, 4.133, 0.038, 2, 4.167, 0.059, 2, 4.2, 0.084, 2, 4.233, 0.113, 2, 4.267, 0.146, 2, 4.3, 0.182, 2, 4.333, 0.222, 2, 4.367, 0.266, 2, 4.4, 0.312, 2, 4.433, 0.362, 2, 4.467, 0.414, 2, 4.5, 0.469, 2, 4.533, 0.526, 2, 4.567, 0.586, 2, 4.6, 0.648, 2, 4.633, 0.712, 2, 4.667, 0.778, 2, 4.7, 0.846, 2, 4.733, 0.914, 2, 4.767, 0.984, 2, 4.8, 1.056, 2, 4.833, 1.128, 2, 4.867, 1.202, 2, 4.9, 1.276, 2, 4.933, 1.351, 2, 4.967, 1.425, 2, 5, 1.5, 2, 5.033, 1.575, 2, 5.067, 1.649, 2, 5.1, 1.724, 2, 5.133, 1.798, 2, 5.167, 1.872, 2, 5.2, 1.944, 2, 5.233, 2.016, 2, 5.267, 2.086, 2, 5.3, 2.154, 2, 5.333, 2.222, 2, 5.367, 2.288, 2, 5.4, 2.352, 2, 5.433, 2.414, 2, 5.467, 2.474, 2, 5.5, 2.531, 2, 5.533, 2.586, 2, 5.567, 2.638, 2, 5.6, 2.688, 2, 5.633, 2.734, 2, 5.667, 2.778, 2, 5.7, 2.818, 2, 5.733, 2.854, 2, 5.767, 2.887, 2, 5.8, 2.916, 2, 5.833, 2.941, 2, 5.867, 2.962, 2, 5.9, 2.978, 2, 5.933, 2.99, 2, 5.967, 2.998, 2, 6, 3, 2, 6.033, 2.998, 2, 6.067, 2.99, 2, 6.1, 2.978, 2, 6.133, 2.962, 2, 6.167, 2.941, 2, 6.2, 2.916, 2, 6.233, 2.887, 2, 6.267, 2.854, 2, 6.3, 2.818, 2, 6.333, 2.778, 2, 6.367, 2.734, 2, 6.4, 2.688, 2, 6.433, 2.638, 2, 6.467, 2.586, 2, 6.5, 2.531, 2, 6.533, 2.474, 2, 6.567, 2.414, 2, 6.6, 2.352, 2, 6.633, 2.288, 2, 6.667, 2.222, 2, 6.7, 2.154, 2, 6.733, 2.086, 2, 6.767, 2.016, 2, 6.8, 1.944, 2, 6.833, 1.872, 2, 6.867, 1.798, 2, 6.9, 1.724, 2, 6.933, 1.649, 2, 6.967, 1.575, 2, 7, 1.5, 2, 7.033, 1.425, 2, 7.067, 1.351, 2, 7.1, 1.276, 2, 7.133, 1.202, 2, 7.167, 1.128, 2, 7.2, 1.056, 2, 7.233, 0.984, 2, 7.267, 0.914, 2, 7.3, 0.846, 2, 7.333, 0.778, 2, 7.367, 0.712, 2, 7.4, 0.648, 2, 7.433, 0.586, 2, 7.467, 0.526, 2, 7.5, 0.469, 2, 7.533, 0.414, 2, 7.567, 0.362, 2, 7.6, 0.312, 2, 7.633, 0.266, 2, 7.667, 0.222, 2, 7.7, 0.182, 2, 7.733, 0.146, 2, 7.767, 0.113, 2, 7.8, 0.084, 2, 7.833, 0.059, 2, 7.867, 0.038, 2, 7.9, 0.022, 2, 7.933, 0.01, 2, 7.967, 0.002, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.033, -0.001, 2, 0.067, -0.003, 2, 0.1, -0.007, 2, 0.133, -0.013, 2, 0.167, -0.02, 2, 0.2, -0.028, 2, 0.233, -0.038, 2, 0.267, -0.049, 2, 0.3, -0.061, 2, 0.333, -0.074, 2, 0.367, -0.089, 2, 0.4, -0.104, 2, 0.433, -0.121, 2, 0.467, -0.138, 2, 0.5, -0.156, 2, 0.533, -0.175, 2, 0.567, -0.195, 2, 0.6, -0.216, 2, 0.633, -0.237, 2, 0.667, -0.259, 2, 0.7, -0.282, 2, 0.733, -0.305, 2, 0.767, -0.328, 2, 0.8, -0.352, 2, 0.833, -0.376, 2, 0.867, -0.401, 2, 0.9, -0.425, 2, 0.933, -0.45, 2, 0.967, -0.475, 2, 1, -0.5, 2, 1.033, -0.525, 2, 1.067, -0.55, 2, 1.1, -0.575, 2, 1.133, -0.599, 2, 1.167, -0.624, 2, 1.2, -0.648, 2, 1.233, -0.672, 2, 1.267, -0.695, 2, 1.3, -0.718, 2, 1.333, -0.741, 2, 1.367, -0.763, 2, 1.4, -0.784, 2, 1.433, -0.805, 2, 1.467, -0.825, 2, 1.5, -0.844, 2, 1.533, -0.862, 2, 1.567, -0.879, 2, 1.6, -0.896, 2, 1.633, -0.911, 2, 1.667, -0.926, 2, 1.7, -0.939, 2, 1.733, -0.951, 2, 1.767, -0.962, 2, 1.8, -0.972, 2, 1.833, -0.98, 2, 1.867, -0.987, 2, 1.9, -0.993, 2, 1.933, -0.997, 2, 1.967, -0.999, 2, 2, -1, 2, 2.033, -1, 2, 2.067, -0.999, 2, 2.1, -0.997, 2, 2.133, -0.996, 2, 2.167, -0.993, 2, 2.2, -0.99, 2, 2.233, -0.986, 2, 2.267, -0.982, 2, 2.3, -0.978, 2, 2.333, -0.972, 2, 2.367, -0.966, 2, 2.4, -0.96, 2, 2.433, -0.953, 2, 2.467, -0.946, 2, 2.5, -0.938, 2, 2.533, -0.929, 2, 2.567, -0.92, 2, 2.6, -0.91, 2, 2.633, -0.9, 2, 2.667, -0.889, 2, 2.7, -0.877, 2, 2.733, -0.866, 2, 2.767, -0.853, 2, 2.8, -0.84, 2, 2.833, -0.827, 2, 2.867, -0.812, 2, 2.9, -0.798, 2, 2.933, -0.782, 2, 2.967, -0.766, 2, 3, -0.75, 2, 3.033, -0.733, 2, 3.067, -0.716, 2, 3.1, -0.697, 2, 3.133, -0.679, 2, 3.167, -0.66, 2, 3.2, -0.64, 2, 3.233, -0.62, 2, 3.267, -0.599, 2, 3.3, -0.578, 2, 3.333, -0.556, 2, 3.367, -0.533, 2, 3.4, -0.51, 2, 3.433, -0.486, 2, 3.467, -0.462, 2, 3.5, -0.438, 2, 3.533, -0.412, 2, 3.567, -0.387, 2, 3.6, -0.36, 2, 3.633, -0.333, 2, 3.667, -0.305, 2, 3.7, -0.277, 2, 3.733, -0.249, 2, 3.767, -0.22, 2, 3.8, -0.19, 2, 3.833, -0.16, 2, 3.867, -0.129, 2, 3.9, -0.098, 2, 3.933, -0.065, 2, 3.967, -0.033, 2, 4, 0, 2, 4.033, 0.033, 2, 4.067, 0.065, 2, 4.1, 0.098, 2, 4.133, 0.129, 2, 4.167, 0.16, 2, 4.2, 0.19, 2, 4.233, 0.22, 2, 4.267, 0.249, 2, 4.3, 0.277, 2, 4.333, 0.305, 2, 4.367, 0.333, 2, 4.4, 0.36, 2, 4.433, 0.387, 2, 4.467, 0.412, 2, 4.5, 0.438, 2, 4.533, 0.462, 2, 4.567, 0.486, 2, 4.6, 0.51, 2, 4.633, 0.533, 2, 4.667, 0.556, 2, 4.7, 0.578, 2, 4.733, 0.599, 2, 4.767, 0.62, 2, 4.8, 0.64, 2, 4.833, 0.66, 2, 4.867, 0.679, 2, 4.9, 0.697, 2, 4.933, 0.716, 2, 4.967, 0.733, 2, 5, 0.75, 2, 5.033, 0.766, 2, 5.067, 0.782, 2, 5.1, 0.798, 2, 5.133, 0.812, 2, 5.167, 0.827, 2, 5.2, 0.84, 2, 5.233, 0.853, 2, 5.267, 0.866, 2, 5.3, 0.877, 2, 5.333, 0.889, 2, 5.367, 0.9, 2, 5.4, 0.91, 2, 5.433, 0.92, 2, 5.467, 0.929, 2, 5.5, 0.938, 2, 5.533, 0.946, 2, 5.567, 0.953, 2, 5.6, 0.96, 2, 5.633, 0.966, 2, 5.667, 0.972, 2, 5.7, 0.978, 2, 5.733, 0.982, 2, 5.767, 0.986, 2, 5.8, 0.99, 2, 5.833, 0.993, 2, 5.867, 0.996, 2, 5.9, 0.997, 2, 5.933, 0.999, 2, 5.967, 1, 2, 6, 1, 2, 6.033, 0.999, 2, 6.067, 0.997, 2, 6.1, 0.993, 2, 6.133, 0.987, 2, 6.167, 0.98, 2, 6.2, 0.972, 2, 6.233, 0.962, 2, 6.267, 0.951, 2, 6.3, 0.939, 2, 6.333, 0.926, 2, 6.367, 0.911, 2, 6.4, 0.896, 2, 6.433, 0.879, 2, 6.467, 0.862, 2, 6.5, 0.844, 2, 6.533, 0.825, 2, 6.567, 0.805, 2, 6.6, 0.784, 2, 6.633, 0.763, 2, 6.667, 0.741, 2, 6.7, 0.718, 2, 6.733, 0.695, 2, 6.767, 0.672, 2, 6.8, 0.648, 2, 6.833, 0.624, 2, 6.867, 0.599, 2, 6.9, 0.575, 2, 6.933, 0.55, 2, 6.967, 0.525, 2, 7, 0.5, 2, 7.033, 0.475, 2, 7.067, 0.45, 2, 7.1, 0.425, 2, 7.133, 0.401, 2, 7.167, 0.376, 2, 7.2, 0.352, 2, 7.233, 0.328, 2, 7.267, 0.305, 2, 7.3, 0.282, 2, 7.333, 0.259, 2, 7.367, 0.237, 2, 7.4, 0.216, 2, 7.433, 0.195, 2, 7.467, 0.175, 2, 7.5, 0.156, 2, 7.533, 0.138, 2, 7.567, 0.121, 2, 7.6, 0.104, 2, 7.633, 0.089, 2, 7.667, 0.074, 2, 7.7, 0.061, 2, 7.733, 0.049, 2, 7.767, 0.038, 2, 7.8, 0.028, 2, 7.833, 0.02, 2, 7.867, 0.013, 2, 7.9, 0.007, 2, 7.933, 0.003, 2, 7.967, 0.001, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamShenTiQianHou", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, -0.002, 2, 0.067, -0.006, 2, 0.1, -0.015, 2, 0.133, -0.025, 2, 0.167, -0.04, 2, 0.2, -0.056, 2, 0.233, -0.075, 2, 0.267, -0.097, 2, 0.3, -0.121, 2, 0.333, -0.148, 2, 0.367, -0.177, 2, 0.4, -0.208, 2, 0.433, -0.241, 2, 0.467, -0.276, 2, 0.5, -0.312, 2, 0.533, -0.351, 2, 0.567, -0.39, 2, 0.6, -0.432, 2, 0.633, -0.475, 2, 0.667, -0.519, 2, 0.7, -0.564, 2, 0.733, -0.61, 2, 0.767, -0.656, 2, 0.8, -0.704, 2, 0.833, -0.752, 2, 0.867, -0.801, 2, 0.9, -0.85, 2, 0.933, -0.901, 2, 0.967, -0.95, 2, 1, -1, 2, 1.033, -1.05, 2, 1.067, -1.099, 2, 1.1, -1.15, 2, 1.133, -1.199, 2, 1.167, -1.248, 2, 1.2, -1.296, 2, 1.233, -1.344, 2, 1.267, -1.39, 2, 1.3, -1.436, 2, 1.333, -1.481, 2, 1.367, -1.525, 2, 1.4, -1.568, 2, 1.433, -1.61, 2, 1.467, -1.649, 2, 1.5, -1.688, 2, 1.533, -1.724, 2, 1.567, -1.759, 2, 1.6, -1.792, 2, 1.633, -1.823, 2, 1.667, -1.852, 2, 1.7, -1.879, 2, 1.733, -1.903, 2, 1.767, -1.925, 2, 1.8, -1.944, 2, 1.833, -1.96, 2, 1.867, -1.975, 2, 1.9, -1.985, 2, 1.933, -1.994, 2, 1.967, -1.998, 2, 2, -2, 2, 2.033, -1.998, 2, 2.067, -1.994, 2, 2.1, -1.985, 2, 2.133, -1.975, 2, 2.167, -1.96, 2, 2.2, -1.944, 2, 2.233, -1.925, 2, 2.267, -1.903, 2, 2.3, -1.879, 2, 2.333, -1.852, 2, 2.367, -1.823, 2, 2.4, -1.792, 2, 2.433, -1.759, 2, 2.467, -1.724, 2, 2.5, -1.688, 2, 2.533, -1.649, 2, 2.567, -1.61, 2, 2.6, -1.568, 2, 2.633, -1.525, 2, 2.667, -1.481, 2, 2.7, -1.436, 2, 2.733, -1.39, 2, 2.767, -1.344, 2, 2.8, -1.296, 2, 2.833, -1.248, 2, 2.867, -1.199, 2, 2.9, -1.15, 2, 2.933, -1.099, 2, 2.967, -1.05, 2, 3, -1, 2, 3.033, -0.95, 2, 3.067, -0.901, 2, 3.1, -0.85, 2, 3.133, -0.801, 2, 3.167, -0.752, 2, 3.2, -0.704, 2, 3.233, -0.656, 2, 3.267, -0.61, 2, 3.3, -0.564, 2, 3.333, -0.519, 2, 3.367, -0.475, 2, 3.4, -0.432, 2, 3.433, -0.39, 2, 3.467, -0.351, 2, 3.5, -0.312, 2, 3.533, -0.276, 2, 3.567, -0.241, 2, 3.6, -0.208, 2, 3.633, -0.177, 2, 3.667, -0.148, 2, 3.7, -0.121, 2, 3.733, -0.097, 2, 3.767, -0.075, 2, 3.8, -0.056, 2, 3.833, -0.04, 2, 3.867, -0.025, 2, 3.9, -0.015, 2, 3.933, -0.006, 2, 3.967, -0.002, 2, 4, 0, 2, 4.033, -0.002, 2, 4.067, -0.006, 2, 4.1, -0.015, 2, 4.133, -0.025, 2, 4.167, -0.04, 2, 4.2, -0.056, 2, 4.233, -0.075, 2, 4.267, -0.097, 2, 4.3, -0.121, 2, 4.333, -0.148, 2, 4.367, -0.177, 2, 4.4, -0.208, 2, 4.433, -0.241, 2, 4.467, -0.276, 2, 4.5, -0.312, 2, 4.533, -0.351, 2, 4.567, -0.39, 2, 4.6, -0.432, 2, 4.633, -0.475, 2, 4.667, -0.519, 2, 4.7, -0.564, 2, 4.733, -0.61, 2, 4.767, -0.656, 2, 4.8, -0.704, 2, 4.833, -0.752, 2, 4.867, -0.801, 2, 4.9, -0.85, 2, 4.933, -0.901, 2, 4.967, -0.95, 2, 5, -1, 2, 5.033, -1.05, 2, 5.067, -1.099, 2, 5.1, -1.15, 2, 5.133, -1.199, 2, 5.167, -1.248, 2, 5.2, -1.296, 2, 5.233, -1.344, 2, 5.267, -1.39, 2, 5.3, -1.436, 2, 5.333, -1.481, 2, 5.367, -1.525, 2, 5.4, -1.568, 2, 5.433, -1.61, 2, 5.467, -1.649, 2, 5.5, -1.688, 2, 5.533, -1.724, 2, 5.567, -1.759, 2, 5.6, -1.792, 2, 5.633, -1.823, 2, 5.667, -1.852, 2, 5.7, -1.879, 2, 5.733, -1.903, 2, 5.767, -1.925, 2, 5.8, -1.944, 2, 5.833, -1.96, 2, 5.867, -1.975, 2, 5.9, -1.985, 2, 5.933, -1.994, 2, 5.967, -1.998, 2, 6, -2, 2, 6.033, -1.998, 2, 6.067, -1.994, 2, 6.1, -1.985, 2, 6.133, -1.975, 2, 6.167, -1.96, 2, 6.2, -1.944, 2, 6.233, -1.925, 2, 6.267, -1.903, 2, 6.3, -1.879, 2, 6.333, -1.852, 2, 6.367, -1.823, 2, 6.4, -1.792, 2, 6.433, -1.759, 2, 6.467, -1.724, 2, 6.5, -1.688, 2, 6.533, -1.649, 2, 6.567, -1.61, 2, 6.6, -1.568, 2, 6.633, -1.525, 2, 6.667, -1.481, 2, 6.7, -1.436, 2, 6.733, -1.39, 2, 6.767, -1.344, 2, 6.8, -1.296, 2, 6.833, -1.248, 2, 6.867, -1.199, 2, 6.9, -1.15, 2, 6.933, -1.099, 2, 6.967, -1.05, 2, 7, -1, 2, 7.033, -0.95, 2, 7.067, -0.901, 2, 7.1, -0.85, 2, 7.133, -0.801, 2, 7.167, -0.752, 2, 7.2, -0.704, 2, 7.233, -0.656, 2, 7.267, -0.61, 2, 7.3, -0.564, 2, 7.333, -0.519, 2, 7.367, -0.475, 2, 7.4, -0.432, 2, 7.433, -0.39, 2, 7.467, -0.351, 2, 7.5, -0.312, 2, 7.533, -0.276, 2, 7.567, -0.241, 2, 7.6, -0.208, 2, 7.633, -0.177, 2, 7.667, -0.148, 2, 7.7, -0.121, 2, 7.733, -0.097, 2, 7.767, -0.075, 2, 7.8, -0.056, 2, 7.833, -0.04, 2, 7.867, -0.025, 2, 7.9, -0.015, 2, 7.933, -0.006, 2, 7.967, -0.002, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.002, 2, 0.067, 0.006, 2, 0.1, 0.015, 2, 0.133, 0.025, 2, 0.167, 0.04, 2, 0.2, 0.056, 2, 0.233, 0.075, 2, 0.267, 0.097, 2, 0.3, 0.121, 2, 0.333, 0.148, 2, 0.367, 0.177, 2, 0.4, 0.208, 2, 0.433, 0.241, 2, 0.467, 0.276, 2, 0.5, 0.312, 2, 0.533, 0.351, 2, 0.567, 0.39, 2, 0.6, 0.432, 2, 0.633, 0.475, 2, 0.667, 0.519, 2, 0.7, 0.564, 2, 0.733, 0.61, 2, 0.767, 0.656, 2, 0.8, 0.704, 2, 0.833, 0.752, 2, 0.867, 0.801, 2, 0.9, 0.85, 2, 0.933, 0.901, 2, 0.967, 0.95, 2, 1, 1, 2, 1.033, 1.05, 2, 1.067, 1.099, 2, 1.1, 1.15, 2, 1.133, 1.199, 2, 1.167, 1.248, 2, 1.2, 1.296, 2, 1.233, 1.344, 2, 1.267, 1.39, 2, 1.3, 1.436, 2, 1.333, 1.481, 2, 1.367, 1.525, 2, 1.4, 1.568, 2, 1.433, 1.61, 2, 1.467, 1.649, 2, 1.5, 1.688, 2, 1.533, 1.724, 2, 1.567, 1.759, 2, 1.6, 1.792, 2, 1.633, 1.823, 2, 1.667, 1.852, 2, 1.7, 1.879, 2, 1.733, 1.903, 2, 1.767, 1.925, 2, 1.8, 1.944, 2, 1.833, 1.96, 2, 1.867, 1.975, 2, 1.9, 1.985, 2, 1.933, 1.994, 2, 1.967, 1.998, 2, 2, 2, 2, 2.033, 1.998, 2, 2.067, 1.994, 2, 2.1, 1.985, 2, 2.133, 1.975, 2, 2.167, 1.96, 2, 2.2, 1.944, 2, 2.233, 1.925, 2, 2.267, 1.903, 2, 2.3, 1.879, 2, 2.333, 1.852, 2, 2.367, 1.823, 2, 2.4, 1.792, 2, 2.433, 1.759, 2, 2.467, 1.724, 2, 2.5, 1.688, 2, 2.533, 1.649, 2, 2.567, 1.61, 2, 2.6, 1.568, 2, 2.633, 1.525, 2, 2.667, 1.481, 2, 2.7, 1.436, 2, 2.733, 1.39, 2, 2.767, 1.344, 2, 2.8, 1.296, 2, 2.833, 1.248, 2, 2.867, 1.199, 2, 2.9, 1.15, 2, 2.933, 1.099, 2, 2.967, 1.05, 2, 3, 1, 2, 3.033, 0.95, 2, 3.067, 0.901, 2, 3.1, 0.85, 2, 3.133, 0.801, 2, 3.167, 0.752, 2, 3.2, 0.704, 2, 3.233, 0.656, 2, 3.267, 0.61, 2, 3.3, 0.564, 2, 3.333, 0.519, 2, 3.367, 0.475, 2, 3.4, 0.432, 2, 3.433, 0.39, 2, 3.467, 0.351, 2, 3.5, 0.312, 2, 3.533, 0.276, 2, 3.567, 0.241, 2, 3.6, 0.208, 2, 3.633, 0.177, 2, 3.667, 0.148, 2, 3.7, 0.121, 2, 3.733, 0.097, 2, 3.767, 0.075, 2, 3.8, 0.056, 2, 3.833, 0.04, 2, 3.867, 0.025, 2, 3.9, 0.015, 2, 3.933, 0.006, 2, 3.967, 0.002, 2, 4, 0, 2, 4.033, 0.002, 2, 4.067, 0.006, 2, 4.1, 0.015, 2, 4.133, 0.025, 2, 4.167, 0.04, 2, 4.2, 0.056, 2, 4.233, 0.075, 2, 4.267, 0.097, 2, 4.3, 0.121, 2, 4.333, 0.148, 2, 4.367, 0.177, 2, 4.4, 0.208, 2, 4.433, 0.241, 2, 4.467, 0.276, 2, 4.5, 0.312, 2, 4.533, 0.351, 2, 4.567, 0.39, 2, 4.6, 0.432, 2, 4.633, 0.475, 2, 4.667, 0.519, 2, 4.7, 0.564, 2, 4.733, 0.61, 2, 4.767, 0.656, 2, 4.8, 0.704, 2, 4.833, 0.752, 2, 4.867, 0.801, 2, 4.9, 0.85, 2, 4.933, 0.901, 2, 4.967, 0.95, 2, 5, 1, 2, 5.033, 1.05, 2, 5.067, 1.099, 2, 5.1, 1.15, 2, 5.133, 1.199, 2, 5.167, 1.248, 2, 5.2, 1.296, 2, 5.233, 1.344, 2, 5.267, 1.39, 2, 5.3, 1.436, 2, 5.333, 1.481, 2, 5.367, 1.525, 2, 5.4, 1.568, 2, 5.433, 1.61, 2, 5.467, 1.649, 2, 5.5, 1.688, 2, 5.533, 1.724, 2, 5.567, 1.759, 2, 5.6, 1.792, 2, 5.633, 1.823, 2, 5.667, 1.852, 2, 5.7, 1.879, 2, 5.733, 1.903, 2, 5.767, 1.925, 2, 5.8, 1.944, 2, 5.833, 1.96, 2, 5.867, 1.975, 2, 5.9, 1.985, 2, 5.933, 1.994, 2, 5.967, 1.998, 2, 6, 2, 2, 6.033, 1.998, 2, 6.067, 1.994, 2, 6.1, 1.985, 2, 6.133, 1.975, 2, 6.167, 1.96, 2, 6.2, 1.944, 2, 6.233, 1.925, 2, 6.267, 1.903, 2, 6.3, 1.879, 2, 6.333, 1.852, 2, 6.367, 1.823, 2, 6.4, 1.792, 2, 6.433, 1.759, 2, 6.467, 1.724, 2, 6.5, 1.688, 2, 6.533, 1.649, 2, 6.567, 1.61, 2, 6.6, 1.568, 2, 6.633, 1.525, 2, 6.667, 1.481, 2, 6.7, 1.436, 2, 6.733, 1.39, 2, 6.767, 1.344, 2, 6.8, 1.296, 2, 6.833, 1.248, 2, 6.867, 1.199, 2, 6.9, 1.15, 2, 6.933, 1.099, 2, 6.967, 1.05, 2, 7, 1, 2, 7.033, 0.95, 2, 7.067, 0.901, 2, 7.1, 0.85, 2, 7.133, 0.801, 2, 7.167, 0.752, 2, 7.2, 0.704, 2, 7.233, 0.656, 2, 7.267, 0.61, 2, 7.3, 0.564, 2, 7.333, 0.519, 2, 7.367, 0.475, 2, 7.4, 0.432, 2, 7.433, 0.39, 2, 7.467, 0.351, 2, 7.5, 0.312, 2, 7.533, 0.276, 2, 7.567, 0.241, 2, 7.6, 0.208, 2, 7.633, 0.177, 2, 7.667, 0.148, 2, 7.7, 0.121, 2, 7.733, 0.097, 2, 7.767, 0.075, 2, 7.8, 0.056, 2, 7.833, 0.04, 2, 7.867, 0.025, 2, 7.9, 0.015, 2, 7.933, 0.006, 2, 7.967, 0.002, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.002, 2, 0.067, 0.006, 2, 0.1, 0.015, 2, 0.133, 0.025, 2, 0.167, 0.04, 2, 0.2, 0.056, 2, 0.233, 0.075, 2, 0.267, 0.097, 2, 0.3, 0.121, 2, 0.333, 0.148, 2, 0.367, 0.177, 2, 0.4, 0.208, 2, 0.433, 0.241, 2, 0.467, 0.276, 2, 0.5, 0.312, 2, 0.533, 0.351, 2, 0.567, 0.39, 2, 0.6, 0.432, 2, 0.633, 0.475, 2, 0.667, 0.519, 2, 0.7, 0.564, 2, 0.733, 0.61, 2, 0.767, 0.656, 2, 0.8, 0.704, 2, 0.833, 0.752, 2, 0.867, 0.801, 2, 0.9, 0.85, 2, 0.933, 0.901, 2, 0.967, 0.95, 2, 1, 1, 2, 1.033, 1.05, 2, 1.067, 1.099, 2, 1.1, 1.15, 2, 1.133, 1.199, 2, 1.167, 1.248, 2, 1.2, 1.296, 2, 1.233, 1.344, 2, 1.267, 1.39, 2, 1.3, 1.436, 2, 1.333, 1.481, 2, 1.367, 1.525, 2, 1.4, 1.568, 2, 1.433, 1.61, 2, 1.467, 1.649, 2, 1.5, 1.688, 2, 1.533, 1.724, 2, 1.567, 1.759, 2, 1.6, 1.792, 2, 1.633, 1.823, 2, 1.667, 1.852, 2, 1.7, 1.879, 2, 1.733, 1.903, 2, 1.767, 1.925, 2, 1.8, 1.944, 2, 1.833, 1.96, 2, 1.867, 1.975, 2, 1.9, 1.985, 2, 1.933, 1.994, 2, 1.967, 1.998, 2, 2, 2, 2, 2.033, 1.998, 2, 2.067, 1.994, 2, 2.1, 1.985, 2, 2.133, 1.975, 2, 2.167, 1.96, 2, 2.2, 1.944, 2, 2.233, 1.925, 2, 2.267, 1.903, 2, 2.3, 1.879, 2, 2.333, 1.852, 2, 2.367, 1.823, 2, 2.4, 1.792, 2, 2.433, 1.759, 2, 2.467, 1.724, 2, 2.5, 1.688, 2, 2.533, 1.649, 2, 2.567, 1.61, 2, 2.6, 1.568, 2, 2.633, 1.525, 2, 2.667, 1.481, 2, 2.7, 1.436, 2, 2.733, 1.39, 2, 2.767, 1.344, 2, 2.8, 1.296, 2, 2.833, 1.248, 2, 2.867, 1.199, 2, 2.9, 1.15, 2, 2.933, 1.099, 2, 2.967, 1.05, 2, 3, 1, 2, 3.033, 0.95, 2, 3.067, 0.901, 2, 3.1, 0.85, 2, 3.133, 0.801, 2, 3.167, 0.752, 2, 3.2, 0.704, 2, 3.233, 0.656, 2, 3.267, 0.61, 2, 3.3, 0.564, 2, 3.333, 0.519, 2, 3.367, 0.475, 2, 3.4, 0.432, 2, 3.433, 0.39, 2, 3.467, 0.351, 2, 3.5, 0.312, 2, 3.533, 0.276, 2, 3.567, 0.241, 2, 3.6, 0.208, 2, 3.633, 0.177, 2, 3.667, 0.148, 2, 3.7, 0.121, 2, 3.733, 0.097, 2, 3.767, 0.075, 2, 3.8, 0.056, 2, 3.833, 0.04, 2, 3.867, 0.025, 2, 3.9, 0.015, 2, 3.933, 0.006, 2, 3.967, 0.002, 2, 4, 0, 2, 4.033, 0.002, 2, 4.067, 0.006, 2, 4.1, 0.015, 2, 4.133, 0.025, 2, 4.167, 0.04, 2, 4.2, 0.056, 2, 4.233, 0.075, 2, 4.267, 0.097, 2, 4.3, 0.121, 2, 4.333, 0.148, 2, 4.367, 0.177, 2, 4.4, 0.208, 2, 4.433, 0.241, 2, 4.467, 0.276, 2, 4.5, 0.312, 2, 4.533, 0.351, 2, 4.567, 0.39, 2, 4.6, 0.432, 2, 4.633, 0.475, 2, 4.667, 0.519, 2, 4.7, 0.564, 2, 4.733, 0.61, 2, 4.767, 0.656, 2, 4.8, 0.704, 2, 4.833, 0.752, 2, 4.867, 0.801, 2, 4.9, 0.85, 2, 4.933, 0.901, 2, 4.967, 0.95, 2, 5, 1, 2, 5.033, 1.05, 2, 5.067, 1.099, 2, 5.1, 1.15, 2, 5.133, 1.199, 2, 5.167, 1.248, 2, 5.2, 1.296, 2, 5.233, 1.344, 2, 5.267, 1.39, 2, 5.3, 1.436, 2, 5.333, 1.481, 2, 5.367, 1.525, 2, 5.4, 1.568, 2, 5.433, 1.61, 2, 5.467, 1.649, 2, 5.5, 1.688, 2, 5.533, 1.724, 2, 5.567, 1.759, 2, 5.6, 1.792, 2, 5.633, 1.823, 2, 5.667, 1.852, 2, 5.7, 1.879, 2, 5.733, 1.903, 2, 5.767, 1.925, 2, 5.8, 1.944, 2, 5.833, 1.96, 2, 5.867, 1.975, 2, 5.9, 1.985, 2, 5.933, 1.994, 2, 5.967, 1.998, 2, 6, 2, 2, 6.033, 1.998, 2, 6.067, 1.994, 2, 6.1, 1.985, 2, 6.133, 1.975, 2, 6.167, 1.96, 2, 6.2, 1.944, 2, 6.233, 1.925, 2, 6.267, 1.903, 2, 6.3, 1.879, 2, 6.333, 1.852, 2, 6.367, 1.823, 2, 6.4, 1.792, 2, 6.433, 1.759, 2, 6.467, 1.724, 2, 6.5, 1.688, 2, 6.533, 1.649, 2, 6.567, 1.61, 2, 6.6, 1.568, 2, 6.633, 1.525, 2, 6.667, 1.481, 2, 6.7, 1.436, 2, 6.733, 1.39, 2, 6.767, 1.344, 2, 6.8, 1.296, 2, 6.833, 1.248, 2, 6.867, 1.199, 2, 6.9, 1.15, 2, 6.933, 1.099, 2, 6.967, 1.05, 2, 7, 1, 2, 7.033, 0.95, 2, 7.067, 0.901, 2, 7.1, 0.85, 2, 7.133, 0.801, 2, 7.167, 0.752, 2, 7.2, 0.704, 2, 7.233, 0.656, 2, 7.267, 0.61, 2, 7.3, 0.564, 2, 7.333, 0.519, 2, 7.367, 0.475, 2, 7.4, 0.432, 2, 7.433, 0.39, 2, 7.467, 0.351, 2, 7.5, 0.312, 2, 7.533, 0.276, 2, 7.567, 0.241, 2, 7.6, 0.208, 2, 7.633, 0.177, 2, 7.667, 0.148, 2, 7.7, 0.121, 2, 7.733, 0.097, 2, 7.767, 0.075, 2, 7.8, 0.056, 2, 7.833, 0.04, 2, 7.867, 0.025, 2, 7.9, 0.015, 2, 7.933, 0.006, 2, 7.967, 0.002, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, -0.002, 2, 0.067, -0.006, 2, 0.1, -0.015, 2, 0.133, -0.025, 2, 0.167, -0.04, 2, 0.2, -0.056, 2, 0.233, -0.075, 2, 0.267, -0.097, 2, 0.3, -0.121, 2, 0.333, -0.148, 2, 0.367, -0.177, 2, 0.4, -0.208, 2, 0.433, -0.241, 2, 0.467, -0.276, 2, 0.5, -0.312, 2, 0.533, -0.351, 2, 0.567, -0.39, 2, 0.6, -0.432, 2, 0.633, -0.475, 2, 0.667, -0.519, 2, 0.7, -0.564, 2, 0.733, -0.61, 2, 0.767, -0.656, 2, 0.8, -0.704, 2, 0.833, -0.752, 2, 0.867, -0.801, 2, 0.9, -0.85, 2, 0.933, -0.901, 2, 0.967, -0.95, 2, 1, -1, 2, 1.033, -1.05, 2, 1.067, -1.099, 2, 1.1, -1.15, 2, 1.133, -1.199, 2, 1.167, -1.248, 2, 1.2, -1.296, 2, 1.233, -1.344, 2, 1.267, -1.39, 2, 1.3, -1.436, 2, 1.333, -1.481, 2, 1.367, -1.525, 2, 1.4, -1.568, 2, 1.433, -1.61, 2, 1.467, -1.649, 2, 1.5, -1.688, 2, 1.533, -1.724, 2, 1.567, -1.759, 2, 1.6, -1.792, 2, 1.633, -1.823, 2, 1.667, -1.852, 2, 1.7, -1.879, 2, 1.733, -1.903, 2, 1.767, -1.925, 2, 1.8, -1.944, 2, 1.833, -1.96, 2, 1.867, -1.975, 2, 1.9, -1.985, 2, 1.933, -1.994, 2, 1.967, -1.998, 2, 2, -2, 2, 2.033, -1.998, 2, 2.067, -1.994, 2, 2.1, -1.985, 2, 2.133, -1.975, 2, 2.167, -1.96, 2, 2.2, -1.944, 2, 2.233, -1.925, 2, 2.267, -1.903, 2, 2.3, -1.879, 2, 2.333, -1.852, 2, 2.367, -1.823, 2, 2.4, -1.792, 2, 2.433, -1.759, 2, 2.467, -1.724, 2, 2.5, -1.688, 2, 2.533, -1.649, 2, 2.567, -1.61, 2, 2.6, -1.568, 2, 2.633, -1.525, 2, 2.667, -1.481, 2, 2.7, -1.436, 2, 2.733, -1.39, 2, 2.767, -1.344, 2, 2.8, -1.296, 2, 2.833, -1.248, 2, 2.867, -1.199, 2, 2.9, -1.15, 2, 2.933, -1.099, 2, 2.967, -1.05, 2, 3, -1, 2, 3.033, -0.95, 2, 3.067, -0.901, 2, 3.1, -0.85, 2, 3.133, -0.801, 2, 3.167, -0.752, 2, 3.2, -0.704, 2, 3.233, -0.656, 2, 3.267, -0.61, 2, 3.3, -0.564, 2, 3.333, -0.519, 2, 3.367, -0.475, 2, 3.4, -0.432, 2, 3.433, -0.39, 2, 3.467, -0.351, 2, 3.5, -0.312, 2, 3.533, -0.276, 2, 3.567, -0.241, 2, 3.6, -0.208, 2, 3.633, -0.177, 2, 3.667, -0.148, 2, 3.7, -0.121, 2, 3.733, -0.097, 2, 3.767, -0.075, 2, 3.8, -0.056, 2, 3.833, -0.04, 2, 3.867, -0.025, 2, 3.9, -0.015, 2, 3.933, -0.006, 2, 3.967, -0.002, 2, 4, 0, 2, 4.033, -0.002, 2, 4.067, -0.006, 2, 4.1, -0.015, 2, 4.133, -0.025, 2, 4.167, -0.04, 2, 4.2, -0.056, 2, 4.233, -0.075, 2, 4.267, -0.097, 2, 4.3, -0.121, 2, 4.333, -0.148, 2, 4.367, -0.177, 2, 4.4, -0.208, 2, 4.433, -0.241, 2, 4.467, -0.276, 2, 4.5, -0.312, 2, 4.533, -0.351, 2, 4.567, -0.39, 2, 4.6, -0.432, 2, 4.633, -0.475, 2, 4.667, -0.519, 2, 4.7, -0.564, 2, 4.733, -0.61, 2, 4.767, -0.656, 2, 4.8, -0.704, 2, 4.833, -0.752, 2, 4.867, -0.801, 2, 4.9, -0.85, 2, 4.933, -0.901, 2, 4.967, -0.95, 2, 5, -1, 2, 5.033, -1.05, 2, 5.067, -1.099, 2, 5.1, -1.15, 2, 5.133, -1.199, 2, 5.167, -1.248, 2, 5.2, -1.296, 2, 5.233, -1.344, 2, 5.267, -1.39, 2, 5.3, -1.436, 2, 5.333, -1.481, 2, 5.367, -1.525, 2, 5.4, -1.568, 2, 5.433, -1.61, 2, 5.467, -1.649, 2, 5.5, -1.688, 2, 5.533, -1.724, 2, 5.567, -1.759, 2, 5.6, -1.792, 2, 5.633, -1.823, 2, 5.667, -1.852, 2, 5.7, -1.879, 2, 5.733, -1.903, 2, 5.767, -1.925, 2, 5.8, -1.944, 2, 5.833, -1.96, 2, 5.867, -1.975, 2, 5.9, -1.985, 2, 5.933, -1.994, 2, 5.967, -1.998, 2, 6, -2, 2, 6.033, -1.998, 2, 6.067, -1.994, 2, 6.1, -1.985, 2, 6.133, -1.975, 2, 6.167, -1.96, 2, 6.2, -1.944, 2, 6.233, -1.925, 2, 6.267, -1.903, 2, 6.3, -1.879, 2, 6.333, -1.852, 2, 6.367, -1.823, 2, 6.4, -1.792, 2, 6.433, -1.759, 2, 6.467, -1.724, 2, 6.5, -1.688, 2, 6.533, -1.649, 2, 6.567, -1.61, 2, 6.6, -1.568, 2, 6.633, -1.525, 2, 6.667, -1.481, 2, 6.7, -1.436, 2, 6.733, -1.39, 2, 6.767, -1.344, 2, 6.8, -1.296, 2, 6.833, -1.248, 2, 6.867, -1.199, 2, 6.9, -1.15, 2, 6.933, -1.099, 2, 6.967, -1.05, 2, 7, -1, 2, 7.033, -0.95, 2, 7.067, -0.901, 2, 7.1, -0.85, 2, 7.133, -0.801, 2, 7.167, -0.752, 2, 7.2, -0.704, 2, 7.233, -0.656, 2, 7.267, -0.61, 2, 7.3, -0.564, 2, 7.333, -0.519, 2, 7.367, -0.475, 2, 7.4, -0.432, 2, 7.433, -0.39, 2, 7.467, -0.351, 2, 7.5, -0.312, 2, 7.533, -0.276, 2, 7.567, -0.241, 2, 7.6, -0.208, 2, 7.633, -0.177, 2, 7.667, -0.148, 2, 7.7, -0.121, 2, 7.733, -0.097, 2, 7.767, -0.075, 2, 7.8, -0.056, 2, 7.833, -0.04, 2, 7.867, -0.025, 2, 7.9, -0.015, 2, 7.933, -0.006, 2, 7.967, -0.002, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Paramzuodatui", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0.001, 2, 0.1, 0.002, 2, 0.133, 0.003, 2, 0.167, 0.004, 2, 0.2, 0.005, 2, 0.233, 0.006, 2, 0.267, 0.007, 2, 0.3, 0.007, 2, 0.367, 0.007, 2, 0.4, 0.007, 2, 0.433, 0.006, 2, 0.467, 0.006, 2, 0.5, 0.005, 2, 0.533, 0.005, 2, 0.567, 0.004, 2, 0.6, 0.004, 2, 0.633, 0.003, 2, 0.667, 0.003, 2, 0.7, 0.002, 2, 0.733, 0.002, 2, 0.767, 0.002, 2, 0.833, 0.002, 2, 0.867, 0.002, 2, 0.9, 0.002, 2, 0.933, 0.002, 2, 0.967, 0.002, 2, 1, 0.001, 2, 1.033, 0.001, 2, 1.067, 0.001, 2, 1.1, 0, 2, 1.133, 0, 2, 1.167, 0, 2, 1.2, -0.001, 2, 1.233, -0.001, 2, 1.267, -0.002, 2, 1.3, -0.002, 2, 1.333, -0.003, 2, 1.367, -0.003, 2, 1.4, -0.004, 2, 1.433, -0.005, 2, 1.467, -0.005, 2, 1.5, -0.006, 2, 1.533, -0.006, 2, 1.567, -0.007, 2, 1.6, -0.007, 2, 1.633, -0.008, 2, 1.667, -0.008, 2, 1.7, -0.009, 2, 1.733, -0.009, 2, 1.767, -0.01, 2, 1.8, -0.01, 2, 1.833, -0.011, 2, 1.867, -0.011, 2, 1.9, -0.011, 2, 1.933, -0.011, 2, 1.967, -0.011, 2, 2, -0.012, 2, 2.033, -0.012, 2, 2.067, -0.011, 2, 2.1, -0.011, 2, 2.133, -0.011, 2, 2.167, -0.01, 2, 2.2, -0.009, 2, 2.233, -0.009, 2, 2.267, -0.008, 2, 2.3, -0.008, 2, 2.333, -0.008, 2, 2.367, -0.008, 2, 2.4, -0.008, 2, 2.433, -0.008, 2, 2.467, -0.009, 2, 2.5, -0.009, 2, 2.533, -0.009, 2, 2.567, -0.01, 2, 2.6, -0.01, 2, 2.633, -0.01, 2, 2.667, -0.01, 2, 2.7, -0.01, 2, 2.733, -0.01, 2, 2.767, -0.01, 2, 2.8, -0.01, 2, 2.833, -0.009, 2, 2.867, -0.009, 2, 2.9, -0.009, 2, 2.933, -0.009, 2, 2.967, -0.009, 2, 3, -0.009, 2, 3.167, -0.009, 2, 3.233, -0.009, 2, 3.3, -0.009, 2, 3.333, -0.009, 2, 3.4, -0.009, 2, 3.433, -0.008, 2, 3.467, -0.008, 2, 3.5, -0.008, 2, 3.533, -0.008, 2, 3.567, -0.008, 2, 3.633, -0.008, 2, 3.7, -0.008, 2, 3.733, -0.007, 2, 3.767, -0.007, 2, 3.8, -0.007, 2, 3.833, -0.007, 2, 3.9, -0.007, 2, 3.967, -0.007, 2, 4, -0.006, 2, 4.033, -0.006, 2, 4.067, -0.005, 2, 4.1, -0.004, 2, 4.133, -0.003, 2, 4.167, -0.003, 2, 4.2, -0.002, 2, 4.233, -0.001, 2, 4.267, 0, 2, 4.3, 0.001, 2, 4.333, 0.001, 2, 4.367, 0.001, 2, 4.4, 0.002, 2, 4.433, 0.001, 2, 4.5, 0.001, 2, 4.6, 0.001, 2, 4.633, 0.001, 2, 4.667, 0.001, 2, 4.7, 0.002, 2, 4.733, 0.002, 2, 4.767, 0.002, 2, 4.8, 0.003, 2, 4.833, 0.003, 2, 4.867, 0.004, 2, 4.9, 0.004, 2, 4.933, 0.004, 2, 4.967, 0.005, 2, 5, 0.005, 2, 5.033, 0.005, 2, 5.067, 0.005, 2, 5.133, 0.005, 2, 5.2, 0.005, 2, 5.233, 0.005, 2, 5.267, 0.005, 2, 5.3, 0.006, 2, 5.333, 0.006, 2, 5.367, 0.006, 2, 5.4, 0.006, 2, 5.433, 0.006, 2, 5.467, 0.006, 2, 5.5, 0.007, 2, 5.533, 0.007, 2, 5.567, 0.007, 2, 5.6, 0.007, 2, 5.633, 0.007, 2, 5.667, 0.008, 2, 5.7, 0.008, 2, 5.733, 0.008, 2, 5.767, 0.008, 2, 5.8, 0.008, 2, 5.833, 0.008, 2, 5.867, 0.008, 2, 5.933, 0.008, 2, 5.967, 0.009, 2, 6, 0.009, 2, 6.033, 0.01, 2, 6.067, 0.01, 2, 6.1, 0.011, 2, 6.133, 0.011, 2, 6.167, 0.012, 2, 6.2, 0.013, 2, 6.233, 0.013, 2, 6.267, 0.013, 2, 6.3, 0.013, 2, 6.333, 0.013, 2, 6.367, 0.013, 2, 6.4, 0.013, 2, 6.433, 0.013, 2, 6.467, 0.012, 2, 6.5, 0.012, 2, 6.533, 0.011, 2, 6.567, 0.011, 2, 6.6, 0.011, 2, 6.633, 0.01, 2, 6.667, 0.01, 2, 6.7, 0.009, 2, 6.733, 0.009, 2, 6.767, 0.009, 2, 6.8, 0.009, 2, 6.833, 0.009, 2, 6.9, 0.009, 2, 6.933, 0.009, 2, 6.967, 0.008, 2, 7, 0.008, 2, 7.033, 0.008, 2, 7.067, 0.008, 2, 7.1, 0.007, 2, 7.133, 0.007, 2, 7.167, 0.007, 2, 7.2, 0.006, 2, 7.233, 0.006, 2, 7.267, 0.005, 2, 7.3, 0.005, 2, 7.333, 0.004, 2, 7.367, 0.004, 2, 7.4, 0.003, 2, 7.433, 0.002, 2, 7.467, 0.002, 2, 7.5, 0.001, 2, 7.533, 0.001, 2, 7.567, 0, 2, 7.6, 0, 2, 7.633, -0.001, 2, 7.667, -0.001, 2, 7.7, -0.002, 2, 7.733, -0.002, 2, 7.767, -0.003, 2, 7.8, -0.003, 2, 7.833, -0.003, 2, 7.867, -0.004, 2, 7.9, -0.004, 2, 7.933, -0.004, 2, 7.967, -0.004, 2, 8, -0.004]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0.001, 2, 0.1, 0.002, 2, 0.133, 0.004, 2, 0.167, 0.005, 2, 0.2, 0.006, 2, 0.233, 0.007, 2, 0.267, 0.007, 2, 0.333, 0.007, 2, 0.367, 0.007, 2, 0.4, 0.007, 2, 0.433, 0.007, 2, 0.467, 0.007, 2, 0.5, 0.006, 2, 0.533, 0.006, 2, 0.567, 0.006, 2, 0.6, 0.005, 2, 0.633, 0.005, 2, 0.667, 0.005, 2, 0.7, 0.004, 2, 0.733, 0.004, 2, 0.767, 0.003, 2, 0.8, 0.003, 2, 0.833, 0.002, 2, 0.867, 0.002, 2, 0.9, 0.001, 2, 0.933, 0, 2, 0.967, 0, 2, 1, -0.001, 2, 1.033, -0.001, 2, 1.067, -0.002, 2, 1.1, -0.003, 2, 1.133, -0.003, 2, 1.167, -0.004, 2, 1.2, -0.005, 2, 1.233, -0.005, 2, 1.267, -0.006, 2, 1.3, -0.007, 2, 1.333, -0.007, 2, 1.367, -0.008, 2, 1.4, -0.008, 2, 1.433, -0.009, 2, 1.467, -0.01, 2, 1.5, -0.01, 2, 1.533, -0.011, 2, 1.567, -0.011, 2, 1.6, -0.012, 2, 1.633, -0.012, 2, 1.667, -0.013, 2, 1.7, -0.013, 2, 1.733, -0.013, 2, 1.767, -0.014, 2, 1.8, -0.014, 2, 1.833, -0.014, 2, 1.867, -0.015, 2, 1.9, -0.015, 2, 1.933, -0.015, 2, 1.967, -0.015, 2, 2, -0.015, 2, 2.033, -0.015, 2, 2.067, -0.015, 2, 2.1, -0.015, 2, 2.133, -0.014, 2, 2.167, -0.013, 2, 2.2, -0.012, 2, 2.233, -0.012, 2, 2.267, -0.011, 2, 2.3, -0.011, 2, 2.367, -0.011, 2, 2.4, -0.012, 2, 2.433, -0.012, 2, 2.467, -0.012, 2, 2.5, -0.013, 2, 2.533, -0.013, 2, 2.567, -0.013, 2, 2.6, -0.013, 2, 2.7, -0.013, 2, 2.733, -0.013, 2, 2.767, -0.013, 2, 2.8, -0.012, 2, 2.833, -0.012, 2, 2.867, -0.012, 2, 2.9, -0.012, 2, 2.967, -0.012, 2, 3, -0.012, 2, 3.067, -0.012, 2, 3.1, -0.012, 2, 3.133, -0.012, 2, 3.2, -0.011, 2, 3.233, -0.011, 2, 3.267, -0.011, 2, 3.3, -0.011, 2, 3.333, -0.011, 2, 3.4, -0.011, 2, 3.433, -0.01, 2, 3.467, -0.01, 2, 3.5, -0.01, 2, 3.533, -0.01, 2, 3.567, -0.009, 2, 3.6, -0.009, 2, 3.633, -0.009, 2, 3.667, -0.008, 2, 3.7, -0.008, 2, 3.733, -0.008, 2, 3.767, -0.008, 2, 3.8, -0.008, 2, 3.833, -0.008, 2, 3.9, -0.007, 2, 3.933, -0.007, 2, 3.967, -0.006, 2, 4, -0.006, 2, 4.033, -0.005, 2, 4.067, -0.004, 2, 4.1, -0.003, 2, 4.133, -0.002, 2, 4.167, -0.001, 2, 4.2, 0, 2, 4.233, 0.001, 2, 4.267, 0.002, 2, 4.3, 0.002, 2, 4.333, 0.002, 2, 4.433, 0.002, 2, 4.5, 0.002, 2, 4.533, 0.002, 2, 4.567, 0.002, 2, 4.6, 0.003, 2, 4.633, 0.003, 2, 4.667, 0.003, 2, 4.7, 0.003, 2, 4.733, 0.003, 2, 4.767, 0.003, 2, 4.8, 0.004, 2, 4.833, 0.004, 2, 4.867, 0.004, 2, 4.9, 0.004, 2, 4.933, 0.005, 2, 4.967, 0.005, 2, 5, 0.005, 2, 5.033, 0.006, 2, 5.067, 0.006, 2, 5.1, 0.006, 2, 5.133, 0.007, 2, 5.167, 0.007, 2, 5.2, 0.008, 2, 5.233, 0.008, 2, 5.267, 0.008, 2, 5.3, 0.009, 2, 5.333, 0.009, 2, 5.367, 0.01, 2, 5.4, 0.01, 2, 5.433, 0.01, 2, 5.467, 0.011, 2, 5.5, 0.011, 2, 5.533, 0.012, 2, 5.567, 0.012, 2, 5.6, 0.012, 2, 5.633, 0.013, 2, 5.667, 0.013, 2, 5.7, 0.014, 2, 5.733, 0.014, 2, 5.767, 0.014, 2, 5.8, 0.015, 2, 5.833, 0.015, 2, 5.867, 0.015, 2, 5.9, 0.015, 2, 5.933, 0.016, 2, 5.967, 0.016, 2, 6, 0.016, 2, 6.033, 0.016, 2, 6.067, 0.016, 2, 6.1, 0.017, 2, 6.133, 0.017, 2, 6.167, 0.017, 2, 6.2, 0.017, 2, 6.233, 0.017, 2, 6.267, 0.017, 2, 6.333, 0.017, 2, 6.367, 0.017, 2, 6.4, 0.017, 2, 6.433, 0.017, 2, 6.467, 0.016, 2, 6.5, 0.016, 2, 6.533, 0.016, 2, 6.567, 0.016, 2, 6.6, 0.015, 2, 6.633, 0.015, 2, 6.667, 0.015, 2, 6.7, 0.014, 2, 6.733, 0.014, 2, 6.767, 0.013, 2, 6.8, 0.013, 2, 6.833, 0.012, 2, 6.867, 0.012, 2, 6.9, 0.011, 2, 6.933, 0.01, 2, 6.967, 0.01, 2, 7, 0.009, 2, 7.033, 0.009, 2, 7.067, 0.008, 2, 7.1, 0.007, 2, 7.133, 0.007, 2, 7.167, 0.006, 2, 7.2, 0.006, 2, 7.233, 0.005, 2, 7.267, 0.004, 2, 7.3, 0.004, 2, 7.333, 0.003, 2, 7.367, 0.002, 2, 7.4, 0.002, 2, 7.433, 0.001, 2, 7.467, 0.001, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, -0.001, 2, 7.6, -0.001, 2, 7.633, -0.002, 2, 7.667, -0.002, 2, 7.7, -0.003, 2, 7.733, -0.003, 2, 7.767, -0.003, 2, 7.8, -0.004, 2, 7.833, -0.004, 2, 7.867, -0.004, 2, 7.9, -0.004, 2, 7.933, -0.004, 2, 7.967, -0.005, 2, 8, -0.005]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0.001, 2, 0.1, 0.001, 2, 0.133, 0.002, 2, 0.167, 0.003, 2, 0.2, 0.004, 2, 0.233, 0.005, 2, 0.267, 0.005, 2, 0.3, 0.006, 2, 0.333, 0.006, 2, 0.367, 0.006, 2, 0.4, 0.006, 2, 0.433, 0.005, 2, 0.467, 0.005, 2, 0.5, 0.004, 2, 0.533, 0.004, 2, 0.567, 0.003, 2, 0.6, 0.002, 2, 0.633, 0.002, 2, 0.667, 0.001, 2, 0.7, 0, 2, 0.733, 0, 2, 0.767, 0, 2, 0.8, -0.001, 2, 0.833, -0.001, 2, 0.9, -0.001, 2, 0.933, -0.001, 2, 0.967, -0.001, 2, 1, -0.001, 2, 1.033, -0.001, 2, 1.067, -0.002, 2, 1.1, -0.002, 2, 1.133, -0.003, 2, 1.167, -0.003, 2, 1.2, -0.003, 2, 1.233, -0.004, 2, 1.267, -0.004, 2, 1.3, -0.005, 2, 1.333, -0.006, 2, 1.367, -0.006, 2, 1.4, -0.007, 2, 1.433, -0.007, 2, 1.467, -0.008, 2, 1.5, -0.009, 2, 1.533, -0.009, 2, 1.567, -0.01, 2, 1.6, -0.01, 2, 1.633, -0.011, 2, 1.667, -0.011, 2, 1.7, -0.012, 2, 1.733, -0.012, 2, 1.767, -0.013, 2, 1.8, -0.013, 2, 1.833, -0.014, 2, 1.867, -0.014, 2, 1.9, -0.014, 2, 1.933, -0.014, 2, 1.967, -0.014, 2, 2, -0.015, 2, 2.033, -0.015, 2, 2.067, -0.014, 2, 2.1, -0.014, 2, 2.133, -0.014, 2, 2.167, -0.013, 2, 2.2, -0.013, 2, 2.233, -0.012, 2, 2.267, -0.012, 2, 2.3, -0.011, 2, 2.333, -0.011, 2, 2.367, -0.011, 2, 2.433, -0.011, 2, 2.467, -0.011, 2, 2.5, -0.011, 2, 2.533, -0.012, 2, 2.567, -0.012, 2, 2.6, -0.012, 2, 2.633, -0.012, 2, 2.667, -0.013, 2, 2.733, -0.012, 2, 2.767, -0.012, 2, 2.8, -0.012, 2, 2.833, -0.012, 2, 2.867, -0.012, 2, 2.9, -0.012, 2, 2.933, -0.011, 2, 2.967, -0.011, 2, 3, -0.011, 2, 3.033, -0.011, 2, 3.067, -0.011, 2, 3.1, -0.01, 2, 3.133, -0.01, 2, 3.2, -0.01, 2, 3.233, -0.01, 2, 3.267, -0.01, 2, 3.3, -0.01, 2, 3.333, -0.01, 2, 3.367, -0.01, 2, 3.4, -0.009, 2, 3.433, -0.009, 2, 3.467, -0.009, 2, 3.5, -0.008, 2, 3.533, -0.008, 2, 3.567, -0.007, 2, 3.6, -0.007, 2, 3.633, -0.006, 2, 3.667, -0.006, 2, 3.7, -0.005, 2, 3.733, -0.005, 2, 3.767, -0.004, 2, 3.8, -0.004, 2, 3.833, -0.003, 2, 3.867, -0.003, 2, 3.9, -0.002, 2, 3.933, -0.002, 2, 3.967, -0.001, 2, 4, -0.001, 2, 4.033, 0, 2, 4.067, 0, 2, 4.1, 0.001, 2, 4.133, 0.001, 2, 4.167, 0.002, 2, 4.2, 0.002, 2, 4.233, 0.002, 2, 4.267, 0.003, 2, 4.3, 0.003, 2, 4.333, 0.003, 2, 4.367, 0.003, 2, 4.4, 0.003, 2, 4.433, 0.004, 2, 4.467, 0.004, 2, 4.533, 0.004, 2, 4.633, 0.004, 2, 4.667, 0.004, 2, 4.7, 0.004, 2, 4.733, 0.004, 2, 4.767, 0.004, 2, 4.8, 0.004, 2, 4.833, 0.004, 2, 4.867, 0.005, 2, 4.9, 0.005, 2, 4.933, 0.005, 2, 4.967, 0.005, 2, 5, 0.006, 2, 5.033, 0.006, 2, 5.067, 0.006, 2, 5.1, 0.006, 2, 5.133, 0.007, 2, 5.167, 0.007, 2, 5.2, 0.007, 2, 5.233, 0.008, 2, 5.267, 0.008, 2, 5.3, 0.008, 2, 5.333, 0.008, 2, 5.367, 0.009, 2, 5.4, 0.009, 2, 5.433, 0.009, 2, 5.467, 0.01, 2, 5.5, 0.01, 2, 5.533, 0.01, 2, 5.567, 0.01, 2, 5.6, 0.011, 2, 5.633, 0.011, 2, 5.667, 0.011, 2, 5.7, 0.011, 2, 5.733, 0.011, 2, 5.767, 0.011, 2, 5.8, 0.012, 2, 5.833, 0.012, 2, 5.867, 0.012, 2, 5.9, 0.012, 2, 5.967, 0.012, 2, 6.033, 0.012, 2, 6.067, 0.012, 2, 6.1, 0.013, 2, 6.133, 0.013, 2, 6.167, 0.014, 2, 6.2, 0.015, 2, 6.233, 0.015, 2, 6.267, 0.016, 2, 6.3, 0.016, 2, 6.367, 0.016, 2, 6.4, 0.016, 2, 6.433, 0.016, 2, 6.467, 0.015, 2, 6.5, 0.015, 2, 6.533, 0.015, 2, 6.567, 0.015, 2, 6.6, 0.014, 2, 6.633, 0.014, 2, 6.667, 0.014, 2, 6.7, 0.013, 2, 6.733, 0.013, 2, 6.767, 0.013, 2, 6.8, 0.012, 2, 6.833, 0.012, 2, 6.867, 0.011, 2, 6.9, 0.011, 2, 6.933, 0.01, 2, 6.967, 0.01, 2, 7, 0.009, 2, 7.033, 0.008, 2, 7.067, 0.008, 2, 7.1, 0.007, 2, 7.133, 0.007, 2, 7.167, 0.006, 2, 7.2, 0.006, 2, 7.233, 0.005, 2, 7.267, 0.004, 2, 7.3, 0.004, 2, 7.333, 0.003, 2, 7.367, 0.003, 2, 7.4, 0.002, 2, 7.433, 0.002, 2, 7.467, 0.001, 2, 7.5, 0.001, 2, 7.533, 0, 2, 7.567, 0, 2, 7.6, -0.001, 2, 7.633, -0.001, 2, 7.667, -0.002, 2, 7.7, -0.002, 2, 7.733, -0.002, 2, 7.767, -0.003, 2, 7.8, -0.003, 2, 7.833, -0.003, 2, 7.867, -0.003, 2, 7.9, -0.004, 2, 7.933, -0.004, 2, 7.967, -0.004, 2, 8, -0.004]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.001, 2, 0.067, 0.002, 2, 0.1, 0.004, 2, 0.133, 0.007, 2, 0.167, 0.01, 2, 0.2, 0.012, 2, 0.233, 0.013, 2, 0.267, 0.014, 2, 0.3, 0.014, 2, 0.333, 0.013, 2, 0.367, 0.012, 2, 0.4, 0.01, 2, 0.433, 0.009, 2, 0.467, 0.008, 2, 0.5, 0.007, 2, 0.533, 0.006, 2, 0.567, 0.005, 2, 0.6, 0.006, 2, 0.633, 0.005, 2, 0.667, 0.006, 2, 0.733, 0.006, 2, 0.767, 0.005, 2, 0.8, 0.005, 2, 0.833, 0.005, 2, 0.867, 0.004, 2, 0.9, 0.004, 2, 0.933, 0.003, 2, 0.967, 0.003, 2, 1, 0.002, 2, 1.033, 0.001, 2, 1.067, 0, 2, 1.1, 0, 2, 1.133, -0.001, 2, 1.167, -0.002, 2, 1.2, -0.003, 2, 1.233, -0.004, 2, 1.267, -0.005, 2, 1.3, -0.006, 2, 1.333, -0.008, 2, 1.367, -0.009, 2, 1.4, -0.01, 2, 1.433, -0.011, 2, 1.467, -0.012, 2, 1.5, -0.013, 2, 1.533, -0.014, 2, 1.567, -0.015, 2, 1.6, -0.016, 2, 1.633, -0.017, 2, 1.667, -0.018, 2, 1.7, -0.018, 2, 1.733, -0.019, 2, 1.767, -0.02, 2, 1.8, -0.021, 2, 1.833, -0.021, 2, 1.867, -0.022, 2, 1.9, -0.022, 2, 1.933, -0.022, 2, 1.967, -0.023, 2, 2, -0.023, 2, 2.033, -0.023, 2, 2.067, -0.023, 2, 2.1, -0.022, 2, 2.133, -0.021, 2, 2.167, -0.019, 2, 2.2, -0.018, 2, 2.233, -0.017, 2, 2.267, -0.016, 2, 2.3, -0.016, 2, 2.333, -0.016, 2, 2.367, -0.016, 2, 2.4, -0.017, 2, 2.433, -0.017, 2, 2.467, -0.018, 2, 2.5, -0.019, 2, 2.533, -0.019, 2, 2.567, -0.02, 2, 2.6, -0.02, 2, 2.633, -0.02, 2, 2.667, -0.019, 2, 2.7, -0.019, 2, 2.733, -0.019, 2, 2.767, -0.018, 2, 2.8, -0.018, 2, 2.833, -0.018, 2, 2.867, -0.018, 2, 2.9, -0.018, 2, 2.933, -0.018, 2, 3, -0.018, 2, 3.067, -0.018, 2, 3.1, -0.018, 2, 3.133, -0.018, 2, 3.2, -0.018, 2, 3.233, -0.018, 2, 3.267, -0.017, 2, 3.3, -0.017, 2, 3.333, -0.017, 2, 3.367, -0.017, 2, 3.4, -0.017, 2, 3.433, -0.017, 2, 3.467, -0.017, 2, 3.5, -0.016, 2, 3.533, -0.016, 2, 3.567, -0.016, 2, 3.6, -0.016, 2, 3.633, -0.015, 2, 3.667, -0.015, 2, 3.7, -0.015, 2, 3.733, -0.015, 2, 3.767, -0.014, 2, 3.8, -0.014, 2, 3.833, -0.014, 2, 3.867, -0.014, 2, 3.9, -0.013, 2, 3.967, -0.013, 2, 4, -0.012, 2, 4.033, -0.011, 2, 4.067, -0.009, 2, 4.1, -0.008, 2, 4.133, -0.006, 2, 4.167, -0.004, 2, 4.2, -0.002, 2, 4.233, 0, 2, 4.267, 0.001, 2, 4.3, 0.002, 2, 4.333, 0.002, 2, 4.367, 0.002, 2, 4.4, 0.001, 2, 4.433, 0.001, 2, 4.5, 0.001, 2, 4.533, 0.001, 2, 4.567, 0.002, 2, 4.6, 0.003, 2, 4.633, 0.003, 2, 4.667, 0.004, 2, 4.7, 0.005, 2, 4.733, 0.006, 2, 4.767, 0.006, 2, 4.8, 0.007, 2, 4.833, 0.008, 2, 4.867, 0.008, 2, 4.9, 0.008, 2, 4.967, 0.008, 2, 5, 0.009, 2, 5.033, 0.009, 2, 5.067, 0.01, 2, 5.1, 0.01, 2, 5.133, 0.01, 2, 5.167, 0.008, 2, 5.2, 0.005, 2, 5.233, 0.004, 2, 5.267, 0.004, 2, 5.3, 0.005, 2, 5.333, 0.007, 2, 5.367, 0.009, 2, 5.4, 0.012, 2, 5.433, 0.014, 2, 5.467, 0.016, 2, 5.5, 0.017, 2, 5.533, 0.017, 2, 5.567, 0.017, 2, 5.6, 0.017, 2, 5.633, 0.016, 2, 5.667, 0.016, 2, 5.7, 0.015, 2, 5.733, 0.015, 2, 5.767, 0.014, 2, 5.8, 0.015, 2, 5.833, 0.015, 2, 5.867, 0.016, 2, 5.9, 0.017, 2, 5.933, 0.018, 2, 5.967, 0.019, 2, 6, 0.02, 2, 6.033, 0.021, 2, 6.067, 0.022, 2, 6.1, 0.023, 2, 6.133, 0.024, 2, 6.167, 0.025, 2, 6.2, 0.026, 2, 6.233, 0.026, 2, 6.267, 0.026, 2, 6.3, 0.026, 2, 6.333, 0.026, 2, 6.367, 0.025, 2, 6.4, 0.024, 2, 6.433, 0.023, 2, 6.467, 0.022, 2, 6.5, 0.021, 2, 6.533, 0.02, 2, 6.567, 0.02, 2, 6.633, 0.02, 2, 6.667, 0.02, 2, 6.7, 0.02, 2, 6.733, 0.02, 2, 6.767, 0.02, 2, 6.8, 0.019, 2, 6.833, 0.019, 2, 6.867, 0.018, 2, 6.9, 0.018, 2, 6.933, 0.017, 2, 6.967, 0.017, 2, 7, 0.016, 2, 7.033, 0.015, 2, 7.067, 0.014, 2, 7.1, 0.013, 2, 7.133, 0.012, 2, 7.167, 0.011, 2, 7.2, 0.01, 2, 7.233, 0.009, 2, 7.267, 0.008, 2, 7.3, 0.007, 2, 7.333, 0.006, 2, 7.367, 0.005, 2, 7.4, 0.004, 2, 7.433, 0.003, 2, 7.467, 0.002, 2, 7.5, 0.001, 2, 7.533, 0, 2, 7.567, -0.001, 2, 7.6, -0.002, 2, 7.633, -0.003, 2, 7.667, -0.004, 2, 7.7, -0.004, 2, 7.733, -0.005, 2, 7.767, -0.006, 2, 7.8, -0.006, 2, 7.833, -0.007, 2, 7.867, -0.008, 2, 7.9, -0.008, 2, 7.933, -0.008, 2, 7.967, -0.009, 2, 8, -0.009]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0, 2, 0.1, -0.001, 2, 0.133, -0.001, 2, 0.167, -0.002, 2, 0.2, -0.003, 2, 0.233, -0.003, 2, 0.267, -0.004, 2, 0.3, -0.004, 2, 0.333, -0.005, 2, 0.367, -0.005, 2, 0.4, -0.005, 2, 0.433, -0.005, 2, 0.467, -0.004, 2, 0.5, -0.004, 2, 0.533, -0.004, 2, 0.567, -0.003, 2, 0.6, -0.003, 2, 0.633, -0.002, 2, 0.667, -0.002, 2, 0.7, -0.001, 2, 0.733, -0.001, 2, 0.767, -0.001, 2, 0.8, -0.001, 2, 0.833, 0, 2, 0.9, 0, 2, 0.967, 0, 2, 1, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.1, 0, 2, 1.133, 0, 2, 1.167, 0.001, 2, 1.2, 0.001, 2, 1.233, 0.001, 2, 1.267, 0.001, 2, 1.3, 0.002, 2, 1.333, 0.002, 2, 1.367, 0.002, 2, 1.4, 0.003, 2, 1.433, 0.003, 2, 1.467, 0.004, 2, 1.5, 0.004, 2, 1.533, 0.004, 2, 1.567, 0.005, 2, 1.6, 0.005, 2, 1.633, 0.005, 2, 1.667, 0.006, 2, 1.7, 0.006, 2, 1.733, 0.006, 2, 1.767, 0.007, 2, 1.8, 0.007, 2, 1.833, 0.007, 2, 1.867, 0.007, 2, 1.9, 0.008, 2, 1.933, 0.008, 2, 1.967, 0.008, 2, 2, 0.008, 2, 2.033, 0.008, 2, 2.1, 0.008, 2, 2.133, 0.008, 2, 2.167, 0.007, 2, 2.2, 0.007, 2, 2.233, 0.007, 2, 2.267, 0.006, 2, 2.3, 0.006, 2, 2.333, 0.006, 2, 2.367, 0.005, 2, 2.4, 0.006, 2, 2.433, 0.005, 2, 2.5, 0.006, 2, 2.533, 0.006, 2, 2.567, 0.006, 2, 2.6, 0.006, 2, 2.633, 0.006, 2, 2.667, 0.007, 2, 2.7, 0.007, 2, 2.733, 0.007, 2, 2.767, 0.007, 2, 2.833, 0.007, 2, 2.9, 0.007, 2, 2.933, 0.007, 2, 2.967, 0.006, 2, 3, 0.006, 2, 3.033, 0.006, 2, 3.067, 0.006, 2, 3.133, 0.006, 2, 3.167, 0.006, 2, 3.233, 0.006, 2, 3.467, 0.006, 2, 3.533, 0.006, 2, 3.567, 0.005, 2, 3.633, 0.005, 2, 3.667, 0.005, 2, 3.7, 0.005, 2, 3.733, 0.005, 2, 3.767, 0.005, 2, 3.8, 0.005, 2, 3.833, 0.004, 2, 3.867, 0.004, 2, 3.9, 0.004, 2, 3.933, 0.003, 2, 3.967, 0.003, 2, 4, 0.003, 2, 4.033, 0.002, 2, 4.067, 0.002, 2, 4.1, 0.001, 2, 4.133, 0.001, 2, 4.167, 0.001, 2, 4.2, 0, 2, 4.233, 0, 2, 4.267, 0, 2, 4.3, -0.001, 2, 4.333, -0.001, 2, 4.367, -0.001, 2, 4.4, -0.001, 2, 4.433, -0.001, 2, 4.467, -0.002, 2, 4.5, -0.002, 2, 4.533, -0.002, 2, 4.6, -0.001, 2, 4.733, -0.001, 2, 4.767, -0.002, 2, 4.8, -0.002, 2, 4.833, -0.002, 2, 4.867, -0.002, 2, 4.9, -0.003, 2, 4.933, -0.003, 2, 4.967, -0.003, 2, 5, -0.003, 2, 5.033, -0.004, 2, 5.067, -0.004, 2, 5.1, -0.004, 2, 5.133, -0.004, 2, 5.167, -0.003, 2, 5.2, -0.003, 2, 5.3, -0.003, 2, 5.333, -0.003, 2, 5.367, -0.004, 2, 5.4, -0.004, 2, 5.433, -0.004, 2, 5.467, -0.005, 2, 5.5, -0.005, 2, 5.533, -0.005, 2, 5.567, -0.005, 2, 5.6, -0.006, 2, 5.633, -0.006, 2, 5.667, -0.006, 2, 5.733, -0.006, 2, 5.9, -0.006, 2, 5.967, -0.006, 2, 6.033, -0.006, 2, 6.067, -0.006, 2, 6.1, -0.006, 2, 6.133, -0.007, 2, 6.167, -0.007, 2, 6.2, -0.008, 2, 6.233, -0.008, 2, 6.267, -0.009, 2, 6.3, -0.009, 2, 6.333, -0.009, 2, 6.367, -0.009, 2, 6.4, -0.009, 2, 6.433, -0.009, 2, 6.467, -0.009, 2, 6.5, -0.009, 2, 6.533, -0.008, 2, 6.567, -0.008, 2, 6.6, -0.008, 2, 6.633, -0.007, 2, 6.667, -0.007, 2, 6.7, -0.007, 2, 6.733, -0.006, 2, 6.767, -0.006, 2, 6.8, -0.006, 2, 6.833, -0.006, 2, 6.867, -0.005, 2, 6.9, -0.005, 2, 6.967, -0.005, 2, 7, -0.005, 2, 7.033, -0.005, 2, 7.067, -0.005, 2, 7.1, -0.005, 2, 7.133, -0.005, 2, 7.167, -0.004, 2, 7.2, -0.004, 2, 7.233, -0.004, 2, 7.267, -0.004, 2, 7.3, -0.003, 2, 7.333, -0.003, 2, 7.367, -0.002, 2, 7.4, -0.002, 2, 7.433, -0.002, 2, 7.467, -0.001, 2, 7.5, -0.001, 2, 7.533, -0.001, 2, 7.567, 0, 2, 7.6, 0, 2, 7.633, 0.001, 2, 7.667, 0.001, 2, 7.7, 0.001, 2, 7.733, 0.002, 2, 7.767, 0.002, 2, 7.8, 0.002, 2, 7.833, 0.002, 2, 7.867, 0.003, 2, 7.9, 0.003, 2, 7.933, 0.003, 2, 7.967, 0.003, 2, 8, 0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh0", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh0", "Segments": [0, 0, 2, 0.033, 0.002, 2, 0.067, 0.007, 2, 0.1, 0.015, 2, 0.133, 0.025, 2, 0.167, 0.036, 2, 0.2, 0.048, 2, 0.233, 0.061, 2, 0.267, 0.073, 2, 0.3, 0.085, 2, 0.333, 0.095, 2, 0.367, 0.102, 2, 0.4, 0.108, 2, 0.433, 0.11, 2, 0.467, 0.109, 2, 0.5, 0.107, 2, 0.533, 0.105, 2, 0.567, 0.104, 2, 0.6, 0.105, 2, 0.633, 0.104, 2, 0.667, 0.105, 2, 0.7, 0.104, 2, 0.733, 0.103, 2, 0.767, 0.101, 2, 0.8, 0.098, 2, 0.833, 0.095, 2, 0.867, 0.091, 2, 0.9, 0.086, 2, 0.933, 0.081, 2, 0.967, 0.075, 2, 1, 0.069, 2, 1.033, 0.062, 2, 1.067, 0.055, 2, 1.1, 0.047, 2, 1.133, 0.039, 2, 1.167, 0.031, 2, 1.2, 0.023, 2, 1.233, 0.014, 2, 1.267, 0.005, 2, 1.3, -0.004, 2, 1.333, -0.013, 2, 1.367, -0.022, 2, 1.4, -0.031, 2, 1.433, -0.04, 2, 1.467, -0.049, 2, 1.5, -0.057, 2, 1.533, -0.066, 2, 1.567, -0.075, 2, 1.6, -0.083, 2, 1.633, -0.091, 2, 1.667, -0.098, 2, 1.7, -0.105, 2, 1.733, -0.112, 2, 1.767, -0.118, 2, 1.8, -0.124, 2, 1.833, -0.13, 2, 1.867, -0.134, 2, 1.9, -0.138, 2, 1.933, -0.142, 2, 1.967, -0.145, 2, 2, -0.147, 2, 2.033, -0.148, 2, 2.067, -0.148, 2, 2.1, -0.148, 2, 2.133, -0.146, 2, 2.167, -0.143, 2, 2.2, -0.14, 2, 2.233, -0.138, 2, 2.267, -0.136, 2, 2.3, -0.135, 2, 2.333, -0.135, 2, 2.367, -0.136, 2, 2.4, -0.137, 2, 2.433, -0.137, 2, 2.467, -0.139, 2, 2.5, -0.14, 2, 2.533, -0.141, 2, 2.567, -0.143, 2, 2.6, -0.145, 2, 2.633, -0.147, 2, 2.667, -0.149, 2, 2.7, -0.151, 2, 2.733, -0.153, 2, 2.767, -0.156, 2, 2.8, -0.158, 2, 2.833, -0.16, 2, 2.867, -0.163, 2, 2.9, -0.165, 2, 2.933, -0.168, 2, 2.967, -0.17, 2, 3, -0.172, 2, 3.033, -0.175, 2, 3.067, -0.177, 2, 3.1, -0.179, 2, 3.133, -0.181, 2, 3.167, -0.183, 2, 3.2, -0.184, 2, 3.233, -0.186, 2, 3.267, -0.187, 2, 3.3, -0.188, 2, 3.333, -0.189, 2, 3.367, -0.19, 2, 3.4, -0.19, 2, 3.433, -0.19, 2, 3.467, -0.19, 2, 3.5, -0.19, 2, 3.533, -0.189, 2, 3.567, -0.189, 2, 3.6, -0.191, 2, 3.633, -0.191, 2, 3.667, -0.192, 2, 3.7, -0.192, 2, 3.733, -0.191, 2, 3.767, -0.19, 2, 3.8, -0.189, 2, 3.833, -0.188, 2, 3.867, -0.19, 2, 3.9, -0.189, 2, 3.933, -0.191, 2, 3.967, -0.19, 2, 4, -0.19, 2, 4.033, -0.189, 2, 4.067, -0.187, 2, 4.1, -0.185, 2, 4.133, -0.183, 2, 4.167, -0.181, 2, 4.2, -0.178, 2, 4.233, -0.174, 2, 4.267, -0.171, 2, 4.3, -0.167, 2, 4.333, -0.163, 2, 4.367, -0.158, 2, 4.4, -0.154, 2, 4.433, -0.149, 2, 4.467, -0.143, 2, 4.5, -0.138, 2, 4.533, -0.132, 2, 4.567, -0.126, 2, 4.6, -0.12, 2, 4.633, -0.113, 2, 4.667, -0.107, 2, 4.7, -0.1, 2, 4.733, -0.093, 2, 4.767, -0.086, 2, 4.8, -0.079, 2, 4.833, -0.071, 2, 4.867, -0.064, 2, 4.9, -0.056, 2, 4.933, -0.049, 2, 4.967, -0.041, 2, 5, -0.033, 2, 5.033, -0.025, 2, 5.067, -0.017, 2, 5.1, -0.009, 2, 5.133, -0.001, 2, 5.167, 0.007, 2, 5.2, 0.015, 2, 5.233, 0.023, 2, 5.267, 0.031, 2, 5.3, 0.039, 2, 5.333, 0.047, 2, 5.367, 0.055, 2, 5.4, 0.063, 2, 5.433, 0.07, 2, 5.467, 0.078, 2, 5.5, 0.086, 2, 5.533, 0.093, 2, 5.567, 0.1, 2, 5.6, 0.108, 2, 5.633, 0.115, 2, 5.667, 0.122, 2, 5.7, 0.128, 2, 5.733, 0.135, 2, 5.767, 0.142, 2, 5.8, 0.148, 2, 5.833, 0.154, 2, 5.867, 0.159, 2, 5.9, 0.165, 2, 5.933, 0.17, 2, 5.967, 0.175, 2, 6, 0.18, 2, 6.033, 0.185, 2, 6.067, 0.189, 2, 6.1, 0.193, 2, 6.133, 0.196, 2, 6.167, 0.199, 2, 6.2, 0.202, 2, 6.233, 0.205, 2, 6.267, 0.207, 2, 6.3, 0.209, 2, 6.333, 0.21, 2, 6.367, 0.211, 2, 6.4, 0.212, 2, 6.433, 0.212, 2, 6.467, 0.212, 2, 6.5, 0.211, 2, 6.533, 0.21, 2, 6.567, 0.209, 2, 6.6, 0.21, 2, 6.633, 0.21, 2, 6.667, 0.21, 2, 6.7, 0.21, 2, 6.733, 0.209, 2, 6.767, 0.207, 2, 6.8, 0.204, 2, 6.833, 0.2, 2, 6.867, 0.196, 2, 6.9, 0.191, 2, 6.933, 0.186, 2, 6.967, 0.18, 2, 7, 0.173, 2, 7.033, 0.166, 2, 7.067, 0.159, 2, 7.1, 0.151, 2, 7.133, 0.143, 2, 7.167, 0.135, 2, 7.2, 0.126, 2, 7.233, 0.117, 2, 7.267, 0.108, 2, 7.3, 0.099, 2, 7.333, 0.09, 2, 7.367, 0.081, 2, 7.4, 0.072, 2, 7.433, 0.063, 2, 7.467, 0.054, 2, 7.5, 0.045, 2, 7.533, 0.036, 2, 7.567, 0.028, 2, 7.6, 0.02, 2, 7.633, 0.012, 2, 7.667, 0.005, 2, 7.7, -0.002, 2, 7.733, -0.008, 2, 7.767, -0.014, 2, 7.8, -0.02, 2, 7.833, -0.025, 2, 7.867, -0.029, 2, 7.9, -0.033, 2, 7.933, -0.035, 2, 7.967, -0.038, 2, 8, -0.039]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh0", "Segments": [0, 0, 2, 0.033, -0.003, 2, 0.067, -0.012, 2, 0.1, -0.022, 2, 0.133, -0.03, 2, 0.167, -0.033, 2, 0.2, -0.031, 2, 0.233, -0.027, 2, 0.267, -0.02, 2, 0.3, -0.011, 2, 0.333, -0.003, 2, 0.367, 0.006, 2, 0.4, 0.013, 2, 0.433, 0.017, 2, 0.467, 0.019, 2, 0.5, 0.018, 2, 0.533, 0.015, 2, 0.567, 0.012, 2, 0.6, 0.007, 2, 0.633, 0.003, 2, 0.667, -0.001, 2, 0.7, -0.004, 2, 0.733, -0.005, 2, 0.767, -0.004, 2, 0.8, -0.003, 2, 0.833, -0.002, 2, 0.867, 0, 2, 0.9, 0.003, 2, 0.933, 0.005, 2, 0.967, 0.007, 2, 1, 0.008, 2, 1.033, 0.009, 2, 1.067, 0.01, 2, 1.1, 0.008, 2, 1.167, 0.007, 2, 1.2, 0.007, 2, 1.233, 0.007, 2, 1.267, 0.008, 2, 1.3, 0.008, 2, 1.333, 0.007, 2, 1.367, 0.006, 2, 1.4, 0.007, 2, 1.433, 0.006, 2, 1.467, 0.007, 2, 1.5, 0.008, 2, 1.533, 0.01, 2, 1.567, 0.01, 2, 1.6, 0.009, 2, 1.633, 0.01, 2, 1.667, 0.01, 2, 1.7, 0.01, 2, 1.733, 0.01, 2, 1.767, 0.011, 2, 1.833, 0.011, 2, 1.867, 0.01, 2, 1.9, 0.01, 2, 1.933, 0.01, 2, 1.967, 0.01, 2, 2, 0.011, 2, 2.033, 0.009, 2, 2.067, 0.004, 2, 2.1, -0.003, 2, 2.133, -0.01, 2, 2.167, -0.015, 2, 2.2, -0.017, 2, 2.233, -0.016, 2, 2.267, -0.012, 2, 2.3, -0.007, 2, 2.333, -0.002, 2, 2.367, 0.004, 2, 2.4, 0.009, 2, 2.433, 0.012, 2, 2.467, 0.013, 2, 2.5, 0.013, 2, 2.533, 0.011, 2, 2.567, 0.009, 2, 2.6, 0.006, 2, 2.633, 0.003, 2, 2.667, 0.001, 2, 2.7, -0.002, 2, 2.733, -0.003, 2, 2.767, -0.004, 2, 2.8, -0.004, 2, 2.833, -0.003, 2, 2.867, -0.001, 2, 2.9, 0, 2, 2.933, 0.001, 2, 2.967, 0.002, 2, 3, 0.002, 2, 3.033, 0.002, 2, 3.067, 0.002, 2, 3.1, 0.003, 2, 3.133, 0.002, 2, 3.167, 0.002, 2, 3.2, 0.002, 2, 3.233, 0.001, 2, 3.267, 0, 2, 3.3, -0.001, 2, 3.333, -0.001, 2, 3.367, 0, 2, 3.4, 0, 2, 3.433, 0.001, 2, 3.467, 0.001, 2, 3.5, 0, 2, 3.533, -0.001, 2, 3.567, -0.001, 2, 3.6, 0, 2, 3.633, 0, 2, 3.667, 0.001, 2, 3.7, 0.001, 2, 3.733, 0, 2, 3.767, -0.001, 2, 3.8, -0.002, 2, 3.867, 0, 2, 3.9, 0, 2, 3.933, 0.001, 2, 3.967, 0, 2, 4, -0.003, 2, 4.033, -0.007, 2, 4.067, -0.012, 2, 4.1, -0.017, 2, 4.133, -0.021, 2, 4.167, -0.025, 2, 4.2, -0.026, 2, 4.233, -0.024, 2, 4.267, -0.019, 2, 4.3, -0.012, 2, 4.333, -0.006, 2, 4.367, 0.001, 2, 4.4, 0.006, 2, 4.433, 0.008, 2, 4.5, 0.007, 2, 4.533, 0.005, 2, 4.567, 0.003, 2, 4.6, 0, 2, 4.633, -0.003, 2, 4.667, -0.006, 2, 4.7, -0.009, 2, 4.733, -0.01, 2, 4.767, -0.011, 2, 4.8, -0.011, 2, 4.833, -0.009, 2, 4.867, -0.008, 2, 4.9, -0.006, 2, 4.933, -0.005, 2, 4.967, -0.003, 2, 5, -0.003, 2, 5.033, -0.003, 2, 5.067, -0.003, 2, 5.1, -0.003, 2, 5.133, -0.003, 2, 5.167, -0.003, 2, 5.2, -0.003, 2, 5.233, -0.004, 2, 5.267, -0.005, 2, 5.3, -0.005, 2, 5.333, -0.005, 2, 5.367, -0.004, 2, 5.4, -0.004, 2, 5.433, -0.003, 2, 5.467, -0.004, 2, 5.5, -0.004, 2, 5.533, -0.004, 2, 5.567, -0.004, 2, 5.6, -0.003, 2, 5.667, -0.003, 2, 5.7, -0.003, 2, 5.733, -0.003, 2, 5.767, -0.003, 2, 5.8, -0.003, 2, 5.833, -0.003, 2, 5.867, -0.003, 2, 5.9, -0.003, 2, 5.933, -0.003, 2, 6, -0.003, 2, 6.033, -0.005, 2, 6.067, -0.01, 2, 6.1, -0.016, 2, 6.133, -0.022, 2, 6.167, -0.024, 2, 6.2, -0.023, 2, 6.233, -0.019, 2, 6.267, -0.014, 2, 6.3, -0.008, 2, 6.333, -0.002, 2, 6.367, 0.003, 2, 6.4, 0.008, 2, 6.433, 0.012, 2, 6.467, 0.013, 2, 6.5, 0.012, 2, 6.533, 0.01, 2, 6.567, 0.007, 2, 6.6, 0.004, 2, 6.633, 0, 2, 6.667, -0.002, 2, 6.7, -0.003, 2, 6.733, -0.002, 2, 6.767, -0.002, 2, 6.8, 0, 2, 6.833, 0.001, 2, 6.867, 0.002, 2, 6.9, 0.004, 2, 6.933, 0.005, 2, 6.967, 0.007, 2, 7, 0.008, 2, 7.033, 0.009, 2, 7.067, 0.009, 2, 7.1, 0.007, 2, 7.133, 0.008, 2, 7.167, 0.006, 2, 7.2, 0.007, 2, 7.233, 0.007, 2, 7.267, 0.008, 2, 7.3, 0.008, 2, 7.367, 0.007, 2, 7.4, 0.007, 2, 7.433, 0.007, 2, 7.467, 0.007, 2, 7.5, 0.008, 2, 7.533, 0.01, 2, 7.567, 0.01, 2, 7.6, 0.009, 2, 7.633, 0.01, 2, 7.667, 0.009, 2, 7.7, 0.01, 2, 7.733, 0.01, 2, 7.767, 0.011, 2, 7.8, 0.011, 2, 7.833, 0.011, 2, 7.867, 0.01, 2, 7.9, 0.01, 2, 7.933, 0.01, 2, 7.967, 0.01, 2, 8, 0.011]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh0", "Segments": [0, 0, 2, 0.033, -0.001, 2, 0.067, -0.005, 2, 0.1, -0.011, 2, 0.133, -0.017, 2, 0.167, -0.023, 2, 0.2, -0.028, 2, 0.233, -0.032, 2, 0.267, -0.034, 2, 0.3, -0.032, 2, 0.333, -0.026, 2, 0.367, -0.018, 2, 0.4, -0.008, 2, 0.433, 0.002, 2, 0.467, 0.012, 2, 0.5, 0.021, 2, 0.533, 0.026, 2, 0.567, 0.029, 2, 0.6, 0.027, 2, 0.633, 0.023, 2, 0.667, 0.018, 2, 0.7, 0.012, 2, 0.733, 0.005, 2, 0.767, -0.001, 2, 0.8, -0.006, 2, 0.833, -0.01, 2, 0.867, -0.011, 2, 0.9, -0.01, 2, 0.933, -0.008, 2, 0.967, -0.005, 2, 1, -0.001, 2, 1.033, 0.003, 2, 1.067, 0.007, 2, 1.1, 0.01, 2, 1.133, 0.013, 2, 1.167, 0.013, 2, 1.2, 0.013, 2, 1.233, 0.012, 2, 1.267, 0.011, 2, 1.3, 0.01, 2, 1.333, 0.009, 2, 1.367, 0.007, 2, 1.4, 0.006, 2, 1.433, 0.006, 2, 1.467, 0.005, 2, 1.5, 0.005, 2, 1.533, 0.006, 2, 1.567, 0.007, 2, 1.6, 0.007, 2, 1.633, 0.008, 2, 1.667, 0.009, 2, 1.7, 0.01, 2, 1.733, 0.011, 2, 1.767, 0.011, 2, 1.8, 0.011, 2, 1.833, 0.011, 2, 1.867, 0.01, 2, 1.9, 0.008, 2, 1.933, 0.006, 2, 1.967, 0.004, 2, 2, 0.001, 2, 2.033, -0.002, 2, 2.067, -0.005, 2, 2.1, -0.008, 2, 2.133, -0.011, 2, 2.167, -0.013, 2, 2.2, -0.016, 2, 2.233, -0.017, 2, 2.267, -0.018, 2, 2.3, -0.019, 2, 2.333, -0.018, 2, 2.367, -0.014, 2, 2.4, -0.009, 2, 2.433, -0.003, 2, 2.467, 0.003, 2, 2.5, 0.009, 2, 2.533, 0.014, 2, 2.567, 0.017, 2, 2.6, 0.019, 2, 2.633, 0.018, 2, 2.667, 0.014, 2, 2.7, 0.01, 2, 2.733, 0.005, 2, 2.767, 0, 2, 2.8, -0.004, 2, 2.833, -0.007, 2, 2.867, -0.008, 2, 2.933, -0.008, 2, 2.967, -0.006, 2, 3, -0.004, 2, 3.033, -0.002, 2, 3.067, 0.001, 2, 3.1, 0.003, 2, 3.133, 0.004, 2, 3.167, 0.005, 2, 3.2, 0.005, 2, 3.233, 0.004, 2, 3.267, 0.003, 2, 3.3, 0.002, 2, 3.333, 0.001, 2, 3.367, 0, 2, 3.4, -0.001, 2, 3.433, -0.001, 2, 3.467, -0.001, 2, 3.5, -0.001, 2, 3.533, -0.001, 2, 3.567, 0, 2, 3.6, 0, 2, 3.633, 0, 2, 3.667, 0, 2, 3.7, 0, 2, 3.767, 0, 2, 3.8, 0, 2, 3.833, -0.001, 2, 3.867, -0.001, 2, 3.933, -0.001, 2, 3.967, 0, 2, 4, 0, 2, 4.033, -0.001, 2, 4.067, -0.004, 2, 4.1, -0.007, 2, 4.133, -0.012, 2, 4.167, -0.016, 2, 4.2, -0.02, 2, 4.233, -0.024, 2, 4.267, -0.026, 2, 4.3, -0.027, 2, 4.333, -0.025, 2, 4.367, -0.021, 2, 4.4, -0.015, 2, 4.433, -0.007, 2, 4.467, 0, 2, 4.5, 0.007, 2, 4.533, 0.011, 2, 4.567, 0.013, 2, 4.6, 0.012, 2, 4.633, 0.009, 2, 4.667, 0.005, 2, 4.7, 0.001, 2, 4.733, -0.004, 2, 4.767, -0.008, 2, 4.8, -0.012, 2, 4.833, -0.015, 2, 4.867, -0.015, 2, 4.9, -0.015, 2, 4.933, -0.013, 2, 4.967, -0.011, 2, 5, -0.009, 2, 5.033, -0.006, 2, 5.067, -0.004, 2, 5.1, -0.002, 2, 5.133, 0, 2, 5.167, 0, 2, 5.2, 0, 2, 5.233, -0.001, 2, 5.267, -0.002, 2, 5.3, -0.003, 2, 5.333, -0.004, 2, 5.367, -0.005, 2, 5.4, -0.006, 2, 5.433, -0.006, 2, 5.467, -0.006, 2, 5.5, -0.005, 2, 5.533, -0.005, 2, 5.567, -0.005, 2, 5.6, -0.004, 2, 5.633, -0.004, 2, 5.667, -0.003, 2, 5.7, -0.003, 2, 5.733, -0.003, 2, 5.8, -0.003, 2, 5.833, -0.003, 2, 5.867, -0.003, 2, 5.9, -0.003, 2, 5.967, -0.003, 2, 6.033, -0.004, 2, 6.067, -0.006, 2, 6.1, -0.009, 2, 6.133, -0.013, 2, 6.167, -0.017, 2, 6.2, -0.02, 2, 6.233, -0.023, 2, 6.267, -0.024, 2, 6.3, -0.022, 2, 6.333, -0.018, 2, 6.367, -0.012, 2, 6.4, -0.005, 2, 6.433, 0.002, 2, 6.467, 0.008, 2, 6.5, 0.014, 2, 6.533, 0.018, 2, 6.567, 0.02, 2, 6.6, 0.018, 2, 6.633, 0.016, 2, 6.667, 0.011, 2, 6.7, 0.007, 2, 6.733, 0.002, 2, 6.767, -0.002, 2, 6.8, -0.005, 2, 6.833, -0.006, 2, 6.867, -0.006, 2, 6.9, -0.004, 2, 6.933, -0.003, 2, 6.967, 0, 2, 7, 0.002, 2, 7.033, 0.005, 2, 7.067, 0.007, 2, 7.1, 0.009, 2, 7.133, 0.011, 2, 7.167, 0.011, 2, 7.2, 0.011, 2, 7.233, 0.011, 2, 7.267, 0.01, 2, 7.3, 0.009, 2, 7.333, 0.008, 2, 7.367, 0.008, 2, 7.4, 0.007, 2, 7.433, 0.006, 2, 7.467, 0.006, 2, 7.5, 0.006, 2, 7.533, 0.007, 2, 7.567, 0.007, 2, 7.6, 0.008, 2, 7.633, 0.008, 2, 7.667, 0.009, 2, 7.7, 0.01, 2, 7.733, 0.01, 2, 7.767, 0.011, 2, 7.8, 0.011, 2, 7.833, 0.011, 2, 7.867, 0.011, 2, 7.9, 0.009, 2, 7.933, 0.006, 2, 7.967, 0.003, 2, 8, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh0", "Segments": [0, 0, 2, 0.033, -0.001, 2, 0.067, -0.003, 2, 0.1, -0.007, 2, 0.133, -0.011, 2, 0.167, -0.015, 2, 0.2, -0.02, 2, 0.233, -0.025, 2, 0.267, -0.029, 2, 0.3, -0.033, 2, 0.333, -0.035, 2, 0.367, -0.036, 2, 0.4, -0.033, 2, 0.433, -0.026, 2, 0.467, -0.016, 2, 0.5, -0.005, 2, 0.533, 0.008, 2, 0.567, 0.019, 2, 0.6, 0.029, 2, 0.633, 0.036, 2, 0.667, 0.039, 2, 0.7, 0.037, 2, 0.733, 0.031, 2, 0.767, 0.023, 2, 0.8, 0.014, 2, 0.833, 0.004, 2, 0.867, -0.005, 2, 0.9, -0.013, 2, 0.933, -0.019, 2, 0.967, -0.021, 2, 1, -0.019, 2, 1.033, -0.015, 2, 1.067, -0.01, 2, 1.1, -0.003, 2, 1.133, 0.003, 2, 1.167, 0.01, 2, 1.2, 0.015, 2, 1.233, 0.019, 2, 1.267, 0.021, 2, 1.3, 0.02, 2, 1.333, 0.018, 2, 1.367, 0.016, 2, 1.4, 0.013, 2, 1.433, 0.01, 2, 1.467, 0.007, 2, 1.5, 0.004, 2, 1.533, 0.003, 2, 1.567, 0.002, 2, 1.6, 0.002, 2, 1.633, 0.004, 2, 1.667, 0.005, 2, 1.7, 0.007, 2, 1.733, 0.009, 2, 1.767, 0.011, 2, 1.8, 0.012, 2, 1.833, 0.013, 2, 1.867, 0.013, 2, 1.933, 0.013, 2, 1.967, 0.012, 2, 2, 0.01, 2, 2.033, 0.007, 2, 2.067, 0.004, 2, 2.1, 0.001, 2, 2.133, -0.002, 2, 2.167, -0.006, 2, 2.2, -0.009, 2, 2.233, -0.012, 2, 2.267, -0.015, 2, 2.3, -0.018, 2, 2.333, -0.02, 2, 2.367, -0.021, 2, 2.4, -0.021, 2, 2.433, -0.02, 2, 2.467, -0.015, 2, 2.5, -0.009, 2, 2.533, -0.002, 2, 2.567, 0.006, 2, 2.6, 0.013, 2, 2.633, 0.019, 2, 2.667, 0.024, 2, 2.7, 0.025, 2, 2.733, 0.024, 2, 2.767, 0.02, 2, 2.8, 0.015, 2, 2.833, 0.009, 2, 2.867, 0.002, 2, 2.9, -0.004, 2, 2.933, -0.009, 2, 2.967, -0.013, 2, 3, -0.015, 2, 3.033, -0.013, 2, 3.067, -0.011, 2, 3.1, -0.007, 2, 3.133, -0.003, 2, 3.167, 0.002, 2, 3.2, 0.005, 2, 3.233, 0.008, 2, 3.267, 0.009, 2, 3.3, 0.009, 2, 3.333, 0.007, 2, 3.367, 0.006, 2, 3.4, 0.004, 2, 3.433, 0.002, 2, 3.467, 0, 2, 3.5, -0.002, 2, 3.533, -0.003, 2, 3.567, -0.004, 2, 3.6, -0.003, 2, 3.633, -0.003, 2, 3.667, -0.002, 2, 3.7, -0.001, 2, 3.733, 0, 2, 3.767, 0, 2, 3.8, 0.001, 2, 3.833, 0.001, 2, 3.867, 0.001, 2, 3.9, 0, 2, 3.933, -0.002, 2, 3.967, -0.004, 2, 4, -0.006, 2, 4.033, -0.008, 2, 4.067, -0.011, 2, 4.1, -0.014, 2, 4.133, -0.017, 2, 4.167, -0.02, 2, 4.2, -0.022, 2, 4.233, -0.024, 2, 4.267, -0.026, 2, 4.3, -0.028, 2, 4.333, -0.029, 2, 4.367, -0.029, 2, 4.4, -0.027, 2, 4.433, -0.023, 2, 4.467, -0.016, 2, 4.5, -0.009, 2, 4.533, -0.001, 2, 4.567, 0.007, 2, 4.6, 0.013, 2, 4.633, 0.018, 2, 4.667, 0.019, 2, 4.7, 0.018, 2, 4.733, 0.014, 2, 4.767, 0.009, 2, 4.8, 0.002, 2, 4.833, -0.005, 2, 4.867, -0.011, 2, 4.9, -0.017, 2, 4.933, -0.02, 2, 4.967, -0.022, 2, 5, -0.021, 2, 5.033, -0.018, 2, 5.067, -0.015, 2, 5.1, -0.011, 2, 5.133, -0.006, 2, 5.167, -0.002, 2, 5.2, 0.002, 2, 5.233, 0.004, 2, 5.267, 0.005, 2, 5.3, 0.004, 2, 5.333, 0.003, 2, 5.367, 0.001, 2, 5.4, -0.001, 2, 5.433, -0.004, 2, 5.467, -0.006, 2, 5.5, -0.007, 2, 5.533, -0.008, 2, 5.6, -0.008, 2, 5.633, -0.007, 2, 5.667, -0.006, 2, 5.7, -0.005, 2, 5.733, -0.004, 2, 5.767, -0.003, 2, 5.8, -0.002, 2, 5.833, -0.002, 2, 5.9, -0.002, 2, 5.933, -0.003, 2, 5.967, -0.004, 2, 6, -0.005, 2, 6.033, -0.007, 2, 6.067, -0.009, 2, 6.1, -0.012, 2, 6.133, -0.014, 2, 6.167, -0.016, 2, 6.2, -0.018, 2, 6.233, -0.02, 2, 6.267, -0.022, 2, 6.3, -0.023, 2, 6.333, -0.024, 2, 6.367, -0.024, 2, 6.4, -0.022, 2, 6.433, -0.018, 2, 6.467, -0.011, 2, 6.5, -0.003, 2, 6.533, 0.005, 2, 6.567, 0.013, 2, 6.6, 0.02, 2, 6.633, 0.025, 2, 6.667, 0.026, 2, 6.7, 0.025, 2, 6.733, 0.022, 2, 6.767, 0.016, 2, 6.8, 0.01, 2, 6.833, 0.004, 2, 6.867, -0.002, 2, 6.9, -0.008, 2, 6.933, -0.011, 2, 6.967, -0.013, 2, 7, -0.012, 2, 7.033, -0.009, 2, 7.067, -0.005, 2, 7.1, 0, 2, 7.133, 0.004, 2, 7.167, 0.009, 2, 7.2, 0.013, 2, 7.233, 0.015, 2, 7.267, 0.016, 2, 7.3, 0.016, 2, 7.333, 0.015, 2, 7.367, 0.013, 2, 7.4, 0.011, 2, 7.433, 0.009, 2, 7.467, 0.007, 2, 7.5, 0.006, 2, 7.533, 0.005, 2, 7.567, 0.004, 2, 7.6, 0.004, 2, 7.633, 0.005, 2, 7.667, 0.006, 2, 7.7, 0.008, 2, 7.733, 0.009, 2, 7.767, 0.01, 2, 7.8, 0.012, 2, 7.833, 0.012, 2, 7.867, 0.013, 2, 7.933, 0.012, 2, 7.967, 0.01, 2, 8, 0.008]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1", "Segments": [0, 0, 2, 0.033, 0.002, 2, 0.067, 0.007, 2, 0.1, 0.015, 2, 0.133, 0.025, 2, 0.167, 0.036, 2, 0.2, 0.048, 2, 0.233, 0.061, 2, 0.267, 0.073, 2, 0.3, 0.085, 2, 0.333, 0.095, 2, 0.367, 0.102, 2, 0.4, 0.108, 2, 0.433, 0.11, 2, 0.467, 0.109, 2, 0.5, 0.107, 2, 0.533, 0.105, 2, 0.567, 0.104, 2, 0.6, 0.105, 2, 0.633, 0.104, 2, 0.667, 0.105, 2, 0.7, 0.104, 2, 0.733, 0.103, 2, 0.767, 0.101, 2, 0.8, 0.098, 2, 0.833, 0.095, 2, 0.867, 0.091, 2, 0.9, 0.086, 2, 0.933, 0.081, 2, 0.967, 0.075, 2, 1, 0.069, 2, 1.033, 0.062, 2, 1.067, 0.055, 2, 1.1, 0.047, 2, 1.133, 0.039, 2, 1.167, 0.031, 2, 1.2, 0.023, 2, 1.233, 0.014, 2, 1.267, 0.005, 2, 1.3, -0.004, 2, 1.333, -0.013, 2, 1.367, -0.022, 2, 1.4, -0.031, 2, 1.433, -0.04, 2, 1.467, -0.049, 2, 1.5, -0.057, 2, 1.533, -0.066, 2, 1.567, -0.075, 2, 1.6, -0.083, 2, 1.633, -0.091, 2, 1.667, -0.098, 2, 1.7, -0.105, 2, 1.733, -0.112, 2, 1.767, -0.118, 2, 1.8, -0.124, 2, 1.833, -0.13, 2, 1.867, -0.134, 2, 1.9, -0.138, 2, 1.933, -0.142, 2, 1.967, -0.145, 2, 2, -0.147, 2, 2.033, -0.148, 2, 2.067, -0.148, 2, 2.1, -0.148, 2, 2.133, -0.146, 2, 2.167, -0.143, 2, 2.2, -0.14, 2, 2.233, -0.138, 2, 2.267, -0.136, 2, 2.3, -0.135, 2, 2.333, -0.135, 2, 2.367, -0.136, 2, 2.4, -0.137, 2, 2.433, -0.137, 2, 2.467, -0.139, 2, 2.5, -0.14, 2, 2.533, -0.141, 2, 2.567, -0.143, 2, 2.6, -0.145, 2, 2.633, -0.147, 2, 2.667, -0.149, 2, 2.7, -0.151, 2, 2.733, -0.153, 2, 2.767, -0.156, 2, 2.8, -0.158, 2, 2.833, -0.16, 2, 2.867, -0.163, 2, 2.9, -0.165, 2, 2.933, -0.168, 2, 2.967, -0.17, 2, 3, -0.172, 2, 3.033, -0.175, 2, 3.067, -0.177, 2, 3.1, -0.179, 2, 3.133, -0.181, 2, 3.167, -0.183, 2, 3.2, -0.184, 2, 3.233, -0.186, 2, 3.267, -0.187, 2, 3.3, -0.188, 2, 3.333, -0.189, 2, 3.367, -0.19, 2, 3.4, -0.19, 2, 3.433, -0.19, 2, 3.467, -0.19, 2, 3.5, -0.19, 2, 3.533, -0.189, 2, 3.567, -0.189, 2, 3.6, -0.191, 2, 3.633, -0.191, 2, 3.667, -0.192, 2, 3.7, -0.192, 2, 3.733, -0.191, 2, 3.767, -0.19, 2, 3.8, -0.189, 2, 3.833, -0.188, 2, 3.867, -0.19, 2, 3.9, -0.189, 2, 3.933, -0.191, 2, 3.967, -0.19, 2, 4, -0.19, 2, 4.033, -0.189, 2, 4.067, -0.187, 2, 4.1, -0.185, 2, 4.133, -0.183, 2, 4.167, -0.181, 2, 4.2, -0.178, 2, 4.233, -0.174, 2, 4.267, -0.171, 2, 4.3, -0.167, 2, 4.333, -0.163, 2, 4.367, -0.158, 2, 4.4, -0.154, 2, 4.433, -0.149, 2, 4.467, -0.143, 2, 4.5, -0.138, 2, 4.533, -0.132, 2, 4.567, -0.126, 2, 4.6, -0.12, 2, 4.633, -0.113, 2, 4.667, -0.107, 2, 4.7, -0.1, 2, 4.733, -0.093, 2, 4.767, -0.086, 2, 4.8, -0.079, 2, 4.833, -0.071, 2, 4.867, -0.064, 2, 4.9, -0.056, 2, 4.933, -0.049, 2, 4.967, -0.041, 2, 5, -0.033, 2, 5.033, -0.025, 2, 5.067, -0.017, 2, 5.1, -0.009, 2, 5.133, -0.001, 2, 5.167, 0.007, 2, 5.2, 0.015, 2, 5.233, 0.023, 2, 5.267, 0.031, 2, 5.3, 0.039, 2, 5.333, 0.047, 2, 5.367, 0.055, 2, 5.4, 0.063, 2, 5.433, 0.07, 2, 5.467, 0.078, 2, 5.5, 0.086, 2, 5.533, 0.093, 2, 5.567, 0.1, 2, 5.6, 0.108, 2, 5.633, 0.115, 2, 5.667, 0.122, 2, 5.7, 0.128, 2, 5.733, 0.135, 2, 5.767, 0.142, 2, 5.8, 0.148, 2, 5.833, 0.154, 2, 5.867, 0.159, 2, 5.9, 0.165, 2, 5.933, 0.17, 2, 5.967, 0.175, 2, 6, 0.18, 2, 6.033, 0.185, 2, 6.067, 0.189, 2, 6.1, 0.193, 2, 6.133, 0.196, 2, 6.167, 0.199, 2, 6.2, 0.202, 2, 6.233, 0.205, 2, 6.267, 0.207, 2, 6.3, 0.209, 2, 6.333, 0.21, 2, 6.367, 0.211, 2, 6.4, 0.212, 2, 6.433, 0.212, 2, 6.467, 0.212, 2, 6.5, 0.211, 2, 6.533, 0.21, 2, 6.567, 0.209, 2, 6.6, 0.21, 2, 6.633, 0.21, 2, 6.667, 0.21, 2, 6.7, 0.21, 2, 6.733, 0.209, 2, 6.767, 0.207, 2, 6.8, 0.204, 2, 6.833, 0.2, 2, 6.867, 0.196, 2, 6.9, 0.191, 2, 6.933, 0.186, 2, 6.967, 0.18, 2, 7, 0.173, 2, 7.033, 0.166, 2, 7.067, 0.159, 2, 7.1, 0.151, 2, 7.133, 0.143, 2, 7.167, 0.135, 2, 7.2, 0.126, 2, 7.233, 0.117, 2, 7.267, 0.108, 2, 7.3, 0.099, 2, 7.333, 0.09, 2, 7.367, 0.081, 2, 7.4, 0.072, 2, 7.433, 0.063, 2, 7.467, 0.054, 2, 7.5, 0.045, 2, 7.533, 0.036, 2, 7.567, 0.028, 2, 7.6, 0.02, 2, 7.633, 0.012, 2, 7.667, 0.005, 2, 7.7, -0.002, 2, 7.733, -0.008, 2, 7.767, -0.014, 2, 7.8, -0.02, 2, 7.833, -0.025, 2, 7.867, -0.029, 2, 7.9, -0.033, 2, 7.933, -0.035, 2, 7.967, -0.038, 2, 8, -0.039]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1", "Segments": [0, 0, 2, 0.033, -0.003, 2, 0.067, -0.012, 2, 0.1, -0.022, 2, 0.133, -0.03, 2, 0.167, -0.033, 2, 0.2, -0.031, 2, 0.233, -0.027, 2, 0.267, -0.02, 2, 0.3, -0.011, 2, 0.333, -0.003, 2, 0.367, 0.006, 2, 0.4, 0.013, 2, 0.433, 0.017, 2, 0.467, 0.019, 2, 0.5, 0.018, 2, 0.533, 0.015, 2, 0.567, 0.012, 2, 0.6, 0.007, 2, 0.633, 0.003, 2, 0.667, -0.001, 2, 0.7, -0.004, 2, 0.733, -0.005, 2, 0.767, -0.004, 2, 0.8, -0.003, 2, 0.833, -0.002, 2, 0.867, 0, 2, 0.9, 0.003, 2, 0.933, 0.005, 2, 0.967, 0.007, 2, 1, 0.008, 2, 1.033, 0.009, 2, 1.067, 0.01, 2, 1.1, 0.008, 2, 1.167, 0.007, 2, 1.2, 0.007, 2, 1.233, 0.007, 2, 1.267, 0.008, 2, 1.3, 0.008, 2, 1.333, 0.007, 2, 1.367, 0.006, 2, 1.4, 0.007, 2, 1.433, 0.006, 2, 1.467, 0.007, 2, 1.5, 0.008, 2, 1.533, 0.01, 2, 1.567, 0.01, 2, 1.6, 0.009, 2, 1.633, 0.01, 2, 1.667, 0.01, 2, 1.7, 0.01, 2, 1.733, 0.01, 2, 1.767, 0.011, 2, 1.833, 0.011, 2, 1.867, 0.01, 2, 1.9, 0.01, 2, 1.933, 0.01, 2, 1.967, 0.01, 2, 2, 0.011, 2, 2.033, 0.009, 2, 2.067, 0.004, 2, 2.1, -0.003, 2, 2.133, -0.01, 2, 2.167, -0.015, 2, 2.2, -0.017, 2, 2.233, -0.016, 2, 2.267, -0.012, 2, 2.3, -0.007, 2, 2.333, -0.002, 2, 2.367, 0.004, 2, 2.4, 0.009, 2, 2.433, 0.012, 2, 2.467, 0.013, 2, 2.5, 0.013, 2, 2.533, 0.011, 2, 2.567, 0.009, 2, 2.6, 0.006, 2, 2.633, 0.003, 2, 2.667, 0.001, 2, 2.7, -0.002, 2, 2.733, -0.003, 2, 2.767, -0.004, 2, 2.8, -0.004, 2, 2.833, -0.003, 2, 2.867, -0.001, 2, 2.9, 0, 2, 2.933, 0.001, 2, 2.967, 0.002, 2, 3, 0.002, 2, 3.033, 0.002, 2, 3.067, 0.002, 2, 3.1, 0.003, 2, 3.133, 0.002, 2, 3.167, 0.002, 2, 3.2, 0.002, 2, 3.233, 0.001, 2, 3.267, 0, 2, 3.3, -0.001, 2, 3.333, -0.001, 2, 3.367, 0, 2, 3.4, 0, 2, 3.433, 0.001, 2, 3.467, 0.001, 2, 3.5, 0, 2, 3.533, -0.001, 2, 3.567, -0.001, 2, 3.6, 0, 2, 3.633, 0, 2, 3.667, 0.001, 2, 3.7, 0.001, 2, 3.733, 0, 2, 3.767, -0.001, 2, 3.8, -0.002, 2, 3.867, 0, 2, 3.9, 0, 2, 3.933, 0.001, 2, 3.967, 0, 2, 4, -0.003, 2, 4.033, -0.007, 2, 4.067, -0.012, 2, 4.1, -0.017, 2, 4.133, -0.021, 2, 4.167, -0.025, 2, 4.2, -0.026, 2, 4.233, -0.024, 2, 4.267, -0.019, 2, 4.3, -0.012, 2, 4.333, -0.006, 2, 4.367, 0.001, 2, 4.4, 0.006, 2, 4.433, 0.008, 2, 4.5, 0.007, 2, 4.533, 0.005, 2, 4.567, 0.003, 2, 4.6, 0, 2, 4.633, -0.003, 2, 4.667, -0.006, 2, 4.7, -0.009, 2, 4.733, -0.01, 2, 4.767, -0.011, 2, 4.8, -0.011, 2, 4.833, -0.009, 2, 4.867, -0.008, 2, 4.9, -0.006, 2, 4.933, -0.005, 2, 4.967, -0.003, 2, 5, -0.003, 2, 5.033, -0.003, 2, 5.067, -0.003, 2, 5.1, -0.003, 2, 5.133, -0.003, 2, 5.167, -0.003, 2, 5.2, -0.003, 2, 5.233, -0.004, 2, 5.267, -0.005, 2, 5.3, -0.005, 2, 5.333, -0.005, 2, 5.367, -0.004, 2, 5.4, -0.004, 2, 5.433, -0.003, 2, 5.467, -0.004, 2, 5.5, -0.004, 2, 5.533, -0.004, 2, 5.567, -0.004, 2, 5.6, -0.003, 2, 5.667, -0.003, 2, 5.7, -0.003, 2, 5.733, -0.003, 2, 5.767, -0.003, 2, 5.8, -0.003, 2, 5.833, -0.003, 2, 5.867, -0.003, 2, 5.9, -0.003, 2, 5.933, -0.003, 2, 6, -0.003, 2, 6.033, -0.005, 2, 6.067, -0.01, 2, 6.1, -0.016, 2, 6.133, -0.022, 2, 6.167, -0.024, 2, 6.2, -0.023, 2, 6.233, -0.019, 2, 6.267, -0.014, 2, 6.3, -0.008, 2, 6.333, -0.002, 2, 6.367, 0.003, 2, 6.4, 0.008, 2, 6.433, 0.012, 2, 6.467, 0.013, 2, 6.5, 0.012, 2, 6.533, 0.01, 2, 6.567, 0.007, 2, 6.6, 0.004, 2, 6.633, 0, 2, 6.667, -0.002, 2, 6.7, -0.003, 2, 6.733, -0.002, 2, 6.767, -0.002, 2, 6.8, 0, 2, 6.833, 0.001, 2, 6.867, 0.002, 2, 6.9, 0.004, 2, 6.933, 0.005, 2, 6.967, 0.007, 2, 7, 0.008, 2, 7.033, 0.009, 2, 7.067, 0.009, 2, 7.1, 0.007, 2, 7.133, 0.008, 2, 7.167, 0.006, 2, 7.2, 0.007, 2, 7.233, 0.007, 2, 7.267, 0.008, 2, 7.3, 0.008, 2, 7.367, 0.007, 2, 7.4, 0.007, 2, 7.433, 0.007, 2, 7.467, 0.007, 2, 7.5, 0.008, 2, 7.533, 0.01, 2, 7.567, 0.01, 2, 7.6, 0.009, 2, 7.633, 0.01, 2, 7.667, 0.009, 2, 7.7, 0.01, 2, 7.733, 0.01, 2, 7.767, 0.011, 2, 7.8, 0.011, 2, 7.833, 0.011, 2, 7.867, 0.01, 2, 7.9, 0.01, 2, 7.933, 0.01, 2, 7.967, 0.01, 2, 8, 0.011]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1", "Segments": [0, 0, 2, 0.033, -0.001, 2, 0.067, -0.005, 2, 0.1, -0.011, 2, 0.133, -0.017, 2, 0.167, -0.023, 2, 0.2, -0.028, 2, 0.233, -0.032, 2, 0.267, -0.034, 2, 0.3, -0.032, 2, 0.333, -0.026, 2, 0.367, -0.018, 2, 0.4, -0.008, 2, 0.433, 0.002, 2, 0.467, 0.012, 2, 0.5, 0.021, 2, 0.533, 0.026, 2, 0.567, 0.029, 2, 0.6, 0.027, 2, 0.633, 0.023, 2, 0.667, 0.018, 2, 0.7, 0.012, 2, 0.733, 0.005, 2, 0.767, -0.001, 2, 0.8, -0.006, 2, 0.833, -0.01, 2, 0.867, -0.011, 2, 0.9, -0.01, 2, 0.933, -0.008, 2, 0.967, -0.005, 2, 1, -0.001, 2, 1.033, 0.003, 2, 1.067, 0.007, 2, 1.1, 0.01, 2, 1.133, 0.013, 2, 1.167, 0.013, 2, 1.2, 0.013, 2, 1.233, 0.012, 2, 1.267, 0.011, 2, 1.3, 0.01, 2, 1.333, 0.009, 2, 1.367, 0.007, 2, 1.4, 0.006, 2, 1.433, 0.006, 2, 1.467, 0.005, 2, 1.5, 0.005, 2, 1.533, 0.006, 2, 1.567, 0.007, 2, 1.6, 0.007, 2, 1.633, 0.008, 2, 1.667, 0.009, 2, 1.7, 0.01, 2, 1.733, 0.011, 2, 1.767, 0.011, 2, 1.8, 0.011, 2, 1.833, 0.011, 2, 1.867, 0.01, 2, 1.9, 0.008, 2, 1.933, 0.006, 2, 1.967, 0.004, 2, 2, 0.001, 2, 2.033, -0.002, 2, 2.067, -0.005, 2, 2.1, -0.008, 2, 2.133, -0.011, 2, 2.167, -0.013, 2, 2.2, -0.016, 2, 2.233, -0.017, 2, 2.267, -0.018, 2, 2.3, -0.019, 2, 2.333, -0.018, 2, 2.367, -0.014, 2, 2.4, -0.009, 2, 2.433, -0.003, 2, 2.467, 0.003, 2, 2.5, 0.009, 2, 2.533, 0.014, 2, 2.567, 0.017, 2, 2.6, 0.019, 2, 2.633, 0.018, 2, 2.667, 0.014, 2, 2.7, 0.01, 2, 2.733, 0.005, 2, 2.767, 0, 2, 2.8, -0.004, 2, 2.833, -0.007, 2, 2.867, -0.008, 2, 2.933, -0.008, 2, 2.967, -0.006, 2, 3, -0.004, 2, 3.033, -0.002, 2, 3.067, 0.001, 2, 3.1, 0.003, 2, 3.133, 0.004, 2, 3.167, 0.005, 2, 3.2, 0.005, 2, 3.233, 0.004, 2, 3.267, 0.003, 2, 3.3, 0.002, 2, 3.333, 0.001, 2, 3.367, 0, 2, 3.4, -0.001, 2, 3.433, -0.001, 2, 3.467, -0.001, 2, 3.5, -0.001, 2, 3.533, -0.001, 2, 3.567, 0, 2, 3.6, 0, 2, 3.633, 0, 2, 3.667, 0, 2, 3.7, 0, 2, 3.767, 0, 2, 3.8, 0, 2, 3.833, -0.001, 2, 3.867, -0.001, 2, 3.933, -0.001, 2, 3.967, 0, 2, 4, 0, 2, 4.033, -0.001, 2, 4.067, -0.004, 2, 4.1, -0.007, 2, 4.133, -0.012, 2, 4.167, -0.016, 2, 4.2, -0.02, 2, 4.233, -0.024, 2, 4.267, -0.026, 2, 4.3, -0.027, 2, 4.333, -0.025, 2, 4.367, -0.021, 2, 4.4, -0.015, 2, 4.433, -0.007, 2, 4.467, 0, 2, 4.5, 0.007, 2, 4.533, 0.011, 2, 4.567, 0.013, 2, 4.6, 0.012, 2, 4.633, 0.009, 2, 4.667, 0.005, 2, 4.7, 0.001, 2, 4.733, -0.004, 2, 4.767, -0.008, 2, 4.8, -0.012, 2, 4.833, -0.015, 2, 4.867, -0.015, 2, 4.9, -0.015, 2, 4.933, -0.013, 2, 4.967, -0.011, 2, 5, -0.009, 2, 5.033, -0.006, 2, 5.067, -0.004, 2, 5.1, -0.002, 2, 5.133, 0, 2, 5.167, 0, 2, 5.2, 0, 2, 5.233, -0.001, 2, 5.267, -0.002, 2, 5.3, -0.003, 2, 5.333, -0.004, 2, 5.367, -0.005, 2, 5.4, -0.006, 2, 5.433, -0.006, 2, 5.467, -0.006, 2, 5.5, -0.005, 2, 5.533, -0.005, 2, 5.567, -0.005, 2, 5.6, -0.004, 2, 5.633, -0.004, 2, 5.667, -0.003, 2, 5.7, -0.003, 2, 5.733, -0.003, 2, 5.8, -0.003, 2, 5.833, -0.003, 2, 5.867, -0.003, 2, 5.9, -0.003, 2, 5.967, -0.003, 2, 6.033, -0.004, 2, 6.067, -0.006, 2, 6.1, -0.009, 2, 6.133, -0.013, 2, 6.167, -0.017, 2, 6.2, -0.02, 2, 6.233, -0.023, 2, 6.267, -0.024, 2, 6.3, -0.022, 2, 6.333, -0.018, 2, 6.367, -0.012, 2, 6.4, -0.005, 2, 6.433, 0.002, 2, 6.467, 0.008, 2, 6.5, 0.014, 2, 6.533, 0.018, 2, 6.567, 0.02, 2, 6.6, 0.018, 2, 6.633, 0.016, 2, 6.667, 0.011, 2, 6.7, 0.007, 2, 6.733, 0.002, 2, 6.767, -0.002, 2, 6.8, -0.005, 2, 6.833, -0.006, 2, 6.867, -0.006, 2, 6.9, -0.004, 2, 6.933, -0.003, 2, 6.967, 0, 2, 7, 0.002, 2, 7.033, 0.005, 2, 7.067, 0.007, 2, 7.1, 0.009, 2, 7.133, 0.011, 2, 7.167, 0.011, 2, 7.2, 0.011, 2, 7.233, 0.011, 2, 7.267, 0.01, 2, 7.3, 0.009, 2, 7.333, 0.008, 2, 7.367, 0.008, 2, 7.4, 0.007, 2, 7.433, 0.006, 2, 7.467, 0.006, 2, 7.5, 0.006, 2, 7.533, 0.007, 2, 7.567, 0.007, 2, 7.6, 0.008, 2, 7.633, 0.008, 2, 7.667, 0.009, 2, 7.7, 0.01, 2, 7.733, 0.01, 2, 7.767, 0.011, 2, 7.8, 0.011, 2, 7.833, 0.011, 2, 7.867, 0.011, 2, 7.9, 0.009, 2, 7.933, 0.006, 2, 7.967, 0.003, 2, 8, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1", "Segments": [0, 0, 2, 0.033, -0.001, 2, 0.067, -0.003, 2, 0.1, -0.007, 2, 0.133, -0.011, 2, 0.167, -0.015, 2, 0.2, -0.02, 2, 0.233, -0.025, 2, 0.267, -0.029, 2, 0.3, -0.033, 2, 0.333, -0.035, 2, 0.367, -0.036, 2, 0.4, -0.033, 2, 0.433, -0.026, 2, 0.467, -0.016, 2, 0.5, -0.005, 2, 0.533, 0.008, 2, 0.567, 0.019, 2, 0.6, 0.029, 2, 0.633, 0.036, 2, 0.667, 0.039, 2, 0.7, 0.037, 2, 0.733, 0.031, 2, 0.767, 0.023, 2, 0.8, 0.014, 2, 0.833, 0.004, 2, 0.867, -0.005, 2, 0.9, -0.013, 2, 0.933, -0.019, 2, 0.967, -0.021, 2, 1, -0.019, 2, 1.033, -0.015, 2, 1.067, -0.01, 2, 1.1, -0.003, 2, 1.133, 0.003, 2, 1.167, 0.01, 2, 1.2, 0.015, 2, 1.233, 0.019, 2, 1.267, 0.021, 2, 1.3, 0.02, 2, 1.333, 0.018, 2, 1.367, 0.016, 2, 1.4, 0.013, 2, 1.433, 0.01, 2, 1.467, 0.007, 2, 1.5, 0.004, 2, 1.533, 0.003, 2, 1.567, 0.002, 2, 1.6, 0.002, 2, 1.633, 0.004, 2, 1.667, 0.005, 2, 1.7, 0.007, 2, 1.733, 0.009, 2, 1.767, 0.011, 2, 1.8, 0.012, 2, 1.833, 0.013, 2, 1.867, 0.013, 2, 1.933, 0.013, 2, 1.967, 0.012, 2, 2, 0.01, 2, 2.033, 0.007, 2, 2.067, 0.004, 2, 2.1, 0.001, 2, 2.133, -0.002, 2, 2.167, -0.006, 2, 2.2, -0.009, 2, 2.233, -0.012, 2, 2.267, -0.015, 2, 2.3, -0.018, 2, 2.333, -0.02, 2, 2.367, -0.021, 2, 2.4, -0.021, 2, 2.433, -0.02, 2, 2.467, -0.015, 2, 2.5, -0.009, 2, 2.533, -0.002, 2, 2.567, 0.006, 2, 2.6, 0.013, 2, 2.633, 0.019, 2, 2.667, 0.024, 2, 2.7, 0.025, 2, 2.733, 0.024, 2, 2.767, 0.02, 2, 2.8, 0.015, 2, 2.833, 0.009, 2, 2.867, 0.002, 2, 2.9, -0.004, 2, 2.933, -0.009, 2, 2.967, -0.013, 2, 3, -0.015, 2, 3.033, -0.013, 2, 3.067, -0.011, 2, 3.1, -0.007, 2, 3.133, -0.003, 2, 3.167, 0.002, 2, 3.2, 0.005, 2, 3.233, 0.008, 2, 3.267, 0.009, 2, 3.3, 0.009, 2, 3.333, 0.007, 2, 3.367, 0.006, 2, 3.4, 0.004, 2, 3.433, 0.002, 2, 3.467, 0, 2, 3.5, -0.002, 2, 3.533, -0.003, 2, 3.567, -0.004, 2, 3.6, -0.003, 2, 3.633, -0.003, 2, 3.667, -0.002, 2, 3.7, -0.001, 2, 3.733, 0, 2, 3.767, 0, 2, 3.8, 0.001, 2, 3.833, 0.001, 2, 3.867, 0.001, 2, 3.9, 0, 2, 3.933, -0.002, 2, 3.967, -0.004, 2, 4, -0.006, 2, 4.033, -0.008, 2, 4.067, -0.011, 2, 4.1, -0.014, 2, 4.133, -0.017, 2, 4.167, -0.02, 2, 4.2, -0.022, 2, 4.233, -0.024, 2, 4.267, -0.026, 2, 4.3, -0.028, 2, 4.333, -0.029, 2, 4.367, -0.029, 2, 4.4, -0.027, 2, 4.433, -0.023, 2, 4.467, -0.016, 2, 4.5, -0.009, 2, 4.533, -0.001, 2, 4.567, 0.007, 2, 4.6, 0.013, 2, 4.633, 0.018, 2, 4.667, 0.019, 2, 4.7, 0.018, 2, 4.733, 0.014, 2, 4.767, 0.009, 2, 4.8, 0.002, 2, 4.833, -0.005, 2, 4.867, -0.011, 2, 4.9, -0.017, 2, 4.933, -0.02, 2, 4.967, -0.022, 2, 5, -0.021, 2, 5.033, -0.018, 2, 5.067, -0.015, 2, 5.1, -0.011, 2, 5.133, -0.006, 2, 5.167, -0.002, 2, 5.2, 0.002, 2, 5.233, 0.004, 2, 5.267, 0.005, 2, 5.3, 0.004, 2, 5.333, 0.003, 2, 5.367, 0.001, 2, 5.4, -0.001, 2, 5.433, -0.004, 2, 5.467, -0.006, 2, 5.5, -0.007, 2, 5.533, -0.008, 2, 5.6, -0.008, 2, 5.633, -0.007, 2, 5.667, -0.006, 2, 5.7, -0.005, 2, 5.733, -0.004, 2, 5.767, -0.003, 2, 5.8, -0.002, 2, 5.833, -0.002, 2, 5.9, -0.002, 2, 5.933, -0.003, 2, 5.967, -0.004, 2, 6, -0.005, 2, 6.033, -0.007, 2, 6.067, -0.009, 2, 6.1, -0.012, 2, 6.133, -0.014, 2, 6.167, -0.016, 2, 6.2, -0.018, 2, 6.233, -0.02, 2, 6.267, -0.022, 2, 6.3, -0.023, 2, 6.333, -0.024, 2, 6.367, -0.024, 2, 6.4, -0.022, 2, 6.433, -0.018, 2, 6.467, -0.011, 2, 6.5, -0.003, 2, 6.533, 0.005, 2, 6.567, 0.013, 2, 6.6, 0.02, 2, 6.633, 0.025, 2, 6.667, 0.026, 2, 6.7, 0.025, 2, 6.733, 0.022, 2, 6.767, 0.016, 2, 6.8, 0.01, 2, 6.833, 0.004, 2, 6.867, -0.002, 2, 6.9, -0.008, 2, 6.933, -0.011, 2, 6.967, -0.013, 2, 7, -0.012, 2, 7.033, -0.009, 2, 7.067, -0.005, 2, 7.1, 0, 2, 7.133, 0.004, 2, 7.167, 0.009, 2, 7.2, 0.013, 2, 7.233, 0.015, 2, 7.267, 0.016, 2, 7.3, 0.016, 2, 7.333, 0.015, 2, 7.367, 0.013, 2, 7.4, 0.011, 2, 7.433, 0.009, 2, 7.467, 0.007, 2, 7.5, 0.006, 2, 7.533, 0.005, 2, 7.567, 0.004, 2, 7.6, 0.004, 2, 7.633, 0.005, 2, 7.667, 0.006, 2, 7.7, 0.008, 2, 7.733, 0.009, 2, 7.767, 0.01, 2, 7.8, 0.012, 2, 7.833, 0.012, 2, 7.867, 0.013, 2, 7.933, 0.012, 2, 7.967, 0.01, 2, 8, 0.008]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh2", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh2", "Segments": [0, 0, 2, 0.033, 0.002, 2, 0.067, 0.008, 2, 0.1, 0.016, 2, 0.133, 0.028, 2, 0.167, 0.04, 2, 0.2, 0.054, 2, 0.233, 0.068, 2, 0.267, 0.082, 2, 0.3, 0.095, 2, 0.333, 0.106, 2, 0.367, 0.114, 2, 0.4, 0.12, 2, 0.433, 0.122, 2, 0.467, 0.122, 2, 0.5, 0.121, 2, 0.533, 0.119, 2, 0.567, 0.117, 2, 0.6, 0.114, 2, 0.633, 0.11, 2, 0.667, 0.106, 2, 0.7, 0.101, 2, 0.733, 0.096, 2, 0.767, 0.09, 2, 0.8, 0.084, 2, 0.833, 0.077, 2, 0.867, 0.07, 2, 0.9, 0.063, 2, 0.933, 0.055, 2, 0.967, 0.047, 2, 1, 0.039, 2, 1.033, 0.031, 2, 1.067, 0.022, 2, 1.1, 0.013, 2, 1.133, 0.004, 2, 1.167, -0.005, 2, 1.2, -0.014, 2, 1.233, -0.024, 2, 1.267, -0.033, 2, 1.3, -0.042, 2, 1.333, -0.052, 2, 1.367, -0.061, 2, 1.4, -0.07, 2, 1.433, -0.079, 2, 1.467, -0.088, 2, 1.5, -0.097, 2, 1.533, -0.105, 2, 1.567, -0.113, 2, 1.6, -0.121, 2, 1.633, -0.129, 2, 1.667, -0.136, 2, 1.7, -0.143, 2, 1.733, -0.15, 2, 1.767, -0.156, 2, 1.8, -0.162, 2, 1.833, -0.167, 2, 1.867, -0.172, 2, 1.9, -0.176, 2, 1.933, -0.18, 2, 1.967, -0.183, 2, 2, -0.185, 2, 2.033, -0.187, 2, 2.067, -0.188, 2, 2.1, -0.188, 2, 2.133, -0.188, 2, 2.167, -0.186, 2, 2.2, -0.184, 2, 2.233, -0.181, 2, 2.267, -0.179, 2, 2.3, -0.177, 2, 2.333, -0.176, 2, 2.367, -0.176, 2, 2.4, -0.177, 2, 2.433, -0.178, 2, 2.467, -0.179, 2, 2.5, -0.18, 2, 2.533, -0.182, 2, 2.567, -0.183, 2, 2.6, -0.185, 2, 2.633, -0.187, 2, 2.667, -0.189, 2, 2.7, -0.192, 2, 2.733, -0.194, 2, 2.767, -0.196, 2, 2.8, -0.199, 2, 2.833, -0.201, 2, 2.867, -0.203, 2, 2.9, -0.206, 2, 2.933, -0.208, 2, 2.967, -0.21, 2, 3, -0.212, 2, 3.033, -0.214, 2, 3.067, -0.215, 2, 3.1, -0.216, 2, 3.133, -0.218, 2, 3.167, -0.218, 2, 3.2, -0.219, 2, 3.233, -0.219, 2, 3.367, -0.22, 2, 3.4, -0.22, 2, 3.433, -0.221, 2, 3.467, -0.221, 2, 3.5, -0.22, 2, 3.533, -0.219, 2, 3.567, -0.218, 2, 3.6, -0.219, 2, 3.633, -0.218, 2, 3.667, -0.218, 2, 3.7, -0.218, 2, 3.733, -0.216, 2, 3.767, -0.214, 2, 3.8, -0.212, 2, 3.833, -0.212, 2, 3.867, -0.212, 2, 3.9, -0.211, 2, 3.933, -0.211, 2, 3.967, -0.211, 2, 4, -0.21, 2, 4.033, -0.209, 2, 4.067, -0.207, 2, 4.1, -0.205, 2, 4.133, -0.202, 2, 4.167, -0.2, 2, 4.2, -0.196, 2, 4.233, -0.192, 2, 4.267, -0.188, 2, 4.3, -0.184, 2, 4.333, -0.179, 2, 4.367, -0.174, 2, 4.4, -0.168, 2, 4.433, -0.162, 2, 4.467, -0.156, 2, 4.5, -0.15, 2, 4.533, -0.143, 2, 4.567, -0.136, 2, 4.6, -0.129, 2, 4.633, -0.121, 2, 4.667, -0.113, 2, 4.7, -0.106, 2, 4.733, -0.098, 2, 4.767, -0.089, 2, 4.8, -0.081, 2, 4.833, -0.072, 2, 4.867, -0.064, 2, 4.9, -0.055, 2, 4.933, -0.046, 2, 4.967, -0.037, 2, 5, -0.028, 2, 5.033, -0.018, 2, 5.067, -0.009, 2, 5.1, 0, 2, 5.133, 0.01, 2, 5.167, 0.019, 2, 5.2, 0.028, 2, 5.233, 0.038, 2, 5.267, 0.047, 2, 5.3, 0.056, 2, 5.333, 0.066, 2, 5.367, 0.075, 2, 5.4, 0.084, 2, 5.433, 0.093, 2, 5.467, 0.102, 2, 5.5, 0.111, 2, 5.533, 0.119, 2, 5.567, 0.128, 2, 5.6, 0.137, 2, 5.633, 0.145, 2, 5.667, 0.153, 2, 5.7, 0.161, 2, 5.733, 0.168, 2, 5.767, 0.176, 2, 5.8, 0.183, 2, 5.833, 0.19, 2, 5.867, 0.197, 2, 5.9, 0.203, 2, 5.933, 0.209, 2, 5.967, 0.215, 2, 6, 0.221, 2, 6.033, 0.226, 2, 6.067, 0.231, 2, 6.1, 0.235, 2, 6.133, 0.24, 2, 6.167, 0.243, 2, 6.2, 0.247, 2, 6.233, 0.25, 2, 6.267, 0.252, 2, 6.3, 0.254, 2, 6.333, 0.256, 2, 6.367, 0.257, 2, 6.4, 0.258, 2, 6.433, 0.258, 2, 6.467, 0.258, 2, 6.5, 0.257, 2, 6.533, 0.255, 2, 6.567, 0.253, 2, 6.6, 0.249, 2, 6.633, 0.246, 2, 6.667, 0.242, 2, 6.7, 0.237, 2, 6.733, 0.231, 2, 6.767, 0.226, 2, 6.8, 0.219, 2, 6.833, 0.213, 2, 6.867, 0.206, 2, 6.9, 0.199, 2, 6.933, 0.191, 2, 6.967, 0.183, 2, 7, 0.175, 2, 7.033, 0.166, 2, 7.067, 0.157, 2, 7.1, 0.149, 2, 7.133, 0.14, 2, 7.167, 0.131, 2, 7.2, 0.121, 2, 7.233, 0.112, 2, 7.267, 0.103, 2, 7.3, 0.094, 2, 7.333, 0.085, 2, 7.367, 0.076, 2, 7.4, 0.067, 2, 7.433, 0.058, 2, 7.467, 0.049, 2, 7.5, 0.041, 2, 7.533, 0.032, 2, 7.567, 0.024, 2, 7.6, 0.017, 2, 7.633, 0.009, 2, 7.667, 0.002, 2, 7.7, -0.004, 2, 7.733, -0.01, 2, 7.767, -0.016, 2, 7.8, -0.022, 2, 7.833, -0.026, 2, 7.867, -0.031, 2, 7.9, -0.034, 2, 7.933, -0.037, 2, 7.967, -0.04, 2, 8, -0.042]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh2", "Segments": [0, 0, 2, 0.033, -0.003, 2, 0.067, -0.01, 2, 0.1, -0.02, 2, 0.133, -0.029, 2, 0.167, -0.037, 2, 0.2, -0.04, 2, 0.233, -0.038, 2, 0.267, -0.033, 2, 0.3, -0.026, 2, 0.333, -0.017, 2, 0.367, -0.008, 2, 0.4, 0.001, 2, 0.433, 0.01, 2, 0.467, 0.017, 2, 0.5, 0.022, 2, 0.533, 0.023, 2, 0.6, 0.023, 2, 0.633, 0.02, 2, 0.667, 0.017, 2, 0.7, 0.013, 2, 0.733, 0.008, 2, 0.767, 0.004, 2, 0.8, 0.001, 2, 0.833, -0.001, 2, 0.867, -0.002, 2, 0.9, -0.002, 2, 0.933, -0.001, 2, 0.967, 0, 2, 1, 0.002, 2, 1.033, 0.004, 2, 1.067, 0.006, 2, 1.1, 0.008, 2, 1.133, 0.01, 2, 1.167, 0.012, 2, 1.2, 0.014, 2, 1.233, 0.014, 2, 1.267, 0.015, 2, 1.333, 0.014, 2, 1.367, 0.013, 2, 1.4, 0.012, 2, 1.433, 0.011, 2, 1.467, 0.012, 2, 1.5, 0.012, 2, 1.533, 0.013, 2, 1.567, 0.013, 2, 1.6, 0.013, 2, 1.633, 0.014, 2, 1.667, 0.013, 2, 1.7, 0.014, 2, 1.733, 0.014, 2, 1.767, 0.015, 2, 1.8, 0.016, 2, 1.833, 0.016, 2, 1.867, 0.016, 2, 1.9, 0.016, 2, 1.933, 0.016, 2, 1.967, 0.016, 2, 2, 0.016, 2, 2.033, 0.014, 2, 2.067, 0.009, 2, 2.1, 0.002, 2, 2.133, -0.005, 2, 2.167, -0.012, 2, 2.2, -0.017, 2, 2.233, -0.019, 2, 2.267, -0.018, 2, 2.3, -0.016, 2, 2.333, -0.012, 2, 2.367, -0.007, 2, 2.4, -0.003, 2, 2.433, 0.002, 2, 2.467, 0.007, 2, 2.5, 0.011, 2, 2.533, 0.013, 2, 2.567, 0.014, 2, 2.6, 0.014, 2, 2.633, 0.013, 2, 2.667, 0.01, 2, 2.7, 0.008, 2, 2.733, 0.005, 2, 2.767, 0.003, 2, 2.8, 0, 2, 2.833, -0.002, 2, 2.867, -0.003, 2, 2.9, -0.004, 2, 2.933, -0.003, 2, 2.967, -0.003, 2, 3, -0.002, 2, 3.033, -0.001, 2, 3.067, 0, 2, 3.1, 0.001, 2, 3.133, 0.002, 2, 3.167, 0.002, 2, 3.2, 0.002, 2, 3.233, 0.001, 2, 3.267, 0.001, 2, 3.3, 0, 2, 3.333, 0, 2, 3.367, 0.001, 2, 3.4, 0, 2, 3.433, 0.001, 2, 3.467, 0, 2, 3.5, -0.001, 2, 3.533, -0.002, 2, 3.567, -0.002, 2, 3.6, -0.001, 2, 3.633, -0.002, 2, 3.667, -0.001, 2, 3.7, -0.001, 2, 3.733, -0.001, 2, 3.767, -0.002, 2, 3.8, -0.003, 2, 3.833, -0.003, 2, 3.867, -0.001, 2, 3.9, -0.002, 2, 3.933, 0, 2, 3.967, -0.002, 2, 4, -0.005, 2, 4.033, -0.009, 2, 4.067, -0.014, 2, 4.1, -0.02, 2, 4.133, -0.025, 2, 4.167, -0.029, 2, 4.2, -0.032, 2, 4.233, -0.034, 2, 4.267, -0.032, 2, 4.3, -0.029, 2, 4.333, -0.024, 2, 4.367, -0.018, 2, 4.4, -0.012, 2, 4.433, -0.006, 2, 4.467, -0.001, 2, 4.5, 0.003, 2, 4.533, 0.004, 2, 4.567, 0.003, 2, 4.6, 0.002, 2, 4.633, 0, 2, 4.667, -0.003, 2, 4.7, -0.006, 2, 4.733, -0.009, 2, 4.767, -0.011, 2, 4.8, -0.013, 2, 4.833, -0.013, 2, 4.867, -0.013, 2, 4.933, -0.012, 2, 4.967, -0.012, 2, 5, -0.01, 2, 5.033, -0.009, 2, 5.067, -0.007, 2, 5.1, -0.006, 2, 5.133, -0.005, 2, 5.167, -0.005, 2, 5.2, -0.005, 2, 5.233, -0.005, 2, 5.267, -0.005, 2, 5.3, -0.006, 2, 5.333, -0.006, 2, 5.367, -0.005, 2, 5.4, -0.006, 2, 5.433, -0.005, 2, 5.467, -0.006, 2, 5.5, -0.006, 2, 5.533, -0.006, 2, 5.6, -0.006, 2, 5.633, -0.006, 2, 5.667, -0.006, 2, 5.7, -0.006, 2, 5.733, -0.005, 2, 5.767, -0.005, 2, 5.8, -0.005, 2, 5.833, -0.004, 2, 5.867, -0.004, 2, 5.9, -0.004, 2, 5.933, -0.004, 2, 5.967, -0.004, 2, 6.033, -0.006, 2, 6.067, -0.01, 2, 6.1, -0.016, 2, 6.133, -0.022, 2, 6.167, -0.027, 2, 6.2, -0.029, 2, 6.233, -0.027, 2, 6.267, -0.025, 2, 6.3, -0.02, 2, 6.333, -0.015, 2, 6.367, -0.009, 2, 6.4, -0.003, 2, 6.433, 0.003, 2, 6.467, 0.009, 2, 6.5, 0.013, 2, 6.533, 0.016, 2, 6.567, 0.017, 2, 6.6, 0.016, 2, 6.633, 0.015, 2, 6.667, 0.013, 2, 6.7, 0.01, 2, 6.733, 0.007, 2, 6.767, 0.005, 2, 6.8, 0.002, 2, 6.833, 0.001, 2, 6.867, 0, 2, 6.9, 0.001, 2, 6.933, 0.002, 2, 6.967, 0.004, 2, 7, 0.006, 2, 7.033, 0.008, 2, 7.067, 0.01, 2, 7.1, 0.011, 2, 7.133, 0.012, 2, 7.167, 0.011, 2, 7.2, 0.012, 2, 7.233, 0.013, 2, 7.267, 0.014, 2, 7.3, 0.014, 2, 7.333, 0.013, 2, 7.367, 0.013, 2, 7.433, 0.012, 2, 7.467, 0.012, 2, 7.5, 0.013, 2, 7.533, 0.013, 2, 7.567, 0.014, 2, 7.6, 0.013, 2, 7.633, 0.014, 2, 7.667, 0.014, 2, 7.7, 0.014, 2, 7.733, 0.015, 2, 7.767, 0.015, 2, 7.8, 0.016, 2, 7.833, 0.016, 2, 7.867, 0.016, 2, 7.9, 0.016, 2, 7.933, 0.016, 2, 7.967, 0.016, 2, 8, 0.016]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh2", "Segments": [0, 0, 2, 0.033, -0.001, 2, 0.067, -0.004, 2, 0.1, -0.009, 2, 0.133, -0.014, 2, 0.167, -0.02, 2, 0.2, -0.026, 2, 0.233, -0.031, 2, 0.267, -0.036, 2, 0.3, -0.039, 2, 0.333, -0.04, 2, 0.367, -0.038, 2, 0.4, -0.032, 2, 0.433, -0.024, 2, 0.467, -0.014, 2, 0.5, -0.003, 2, 0.533, 0.008, 2, 0.567, 0.018, 2, 0.6, 0.026, 2, 0.633, 0.032, 2, 0.667, 0.034, 2, 0.7, 0.033, 2, 0.733, 0.029, 2, 0.767, 0.025, 2, 0.8, 0.019, 2, 0.833, 0.013, 2, 0.867, 0.006, 2, 0.9, 0.001, 2, 0.933, -0.004, 2, 0.967, -0.007, 2, 1, -0.008, 2, 1.033, -0.008, 2, 1.067, -0.006, 2, 1.1, -0.003, 2, 1.133, 0, 2, 1.167, 0.004, 2, 1.2, 0.007, 2, 1.233, 0.011, 2, 1.267, 0.014, 2, 1.3, 0.017, 2, 1.333, 0.019, 2, 1.367, 0.019, 2, 1.4, 0.019, 2, 1.433, 0.018, 2, 1.467, 0.017, 2, 1.5, 0.016, 2, 1.533, 0.015, 2, 1.567, 0.013, 2, 1.6, 0.012, 2, 1.633, 0.012, 2, 1.667, 0.011, 2, 1.7, 0.011, 2, 1.733, 0.012, 2, 1.767, 0.013, 2, 1.8, 0.013, 2, 1.833, 0.014, 2, 1.867, 0.015, 2, 1.9, 0.016, 2, 1.933, 0.016, 2, 1.967, 0.017, 2, 2, 0.017, 2, 2.033, 0.016, 2, 2.067, 0.013, 2, 2.1, 0.01, 2, 2.133, 0.005, 2, 2.167, 0, 2, 2.2, -0.005, 2, 2.233, -0.011, 2, 2.267, -0.015, 2, 2.3, -0.019, 2, 2.333, -0.022, 2, 2.367, -0.022, 2, 2.4, -0.021, 2, 2.433, -0.018, 2, 2.467, -0.013, 2, 2.5, -0.007, 2, 2.533, -0.001, 2, 2.567, 0.005, 2, 2.6, 0.011, 2, 2.633, 0.016, 2, 2.667, 0.019, 2, 2.7, 0.021, 2, 2.733, 0.02, 2, 2.767, 0.018, 2, 2.8, 0.014, 2, 2.833, 0.01, 2, 2.867, 0.006, 2, 2.9, 0.002, 2, 2.933, -0.002, 2, 2.967, -0.006, 2, 3, -0.008, 2, 3.033, -0.009, 2, 3.067, -0.008, 2, 3.1, -0.007, 2, 3.133, -0.005, 2, 3.167, -0.003, 2, 3.2, -0.002, 2, 3.233, 0, 2, 3.267, 0.002, 2, 3.3, 0.003, 2, 3.333, 0.004, 2, 3.4, 0.003, 2, 3.433, 0.003, 2, 3.467, 0.002, 2, 3.5, 0.002, 2, 3.533, 0.001, 2, 3.567, 0, 2, 3.6, -0.001, 2, 3.633, -0.002, 2, 3.667, -0.002, 2, 3.7, -0.003, 2, 3.733, -0.003, 2, 3.8, -0.003, 2, 3.867, -0.003, 2, 3.9, -0.002, 2, 3.933, -0.002, 2, 3.967, -0.002, 2, 4, -0.002, 2, 4.033, -0.003, 2, 4.067, -0.005, 2, 4.1, -0.009, 2, 4.133, -0.013, 2, 4.167, -0.018, 2, 4.2, -0.023, 2, 4.233, -0.028, 2, 4.267, -0.031, 2, 4.3, -0.034, 2, 4.333, -0.035, 2, 4.367, -0.034, 2, 4.4, -0.03, 2, 4.433, -0.025, 2, 4.467, -0.019, 2, 4.5, -0.012, 2, 4.533, -0.005, 2, 4.567, 0.001, 2, 4.6, 0.006, 2, 4.633, 0.01, 2, 4.667, 0.011, 2, 4.7, 0.01, 2, 4.733, 0.008, 2, 4.767, 0.005, 2, 4.8, 0.001, 2, 4.833, -0.003, 2, 4.867, -0.007, 2, 4.9, -0.011, 2, 4.933, -0.015, 2, 4.967, -0.017, 2, 5, -0.018, 2, 5.033, -0.017, 2, 5.067, -0.016, 2, 5.1, -0.015, 2, 5.133, -0.013, 2, 5.167, -0.011, 2, 5.2, -0.009, 2, 5.233, -0.007, 2, 5.267, -0.005, 2, 5.3, -0.003, 2, 5.333, -0.002, 2, 5.367, -0.002, 2, 5.4, -0.002, 2, 5.433, -0.003, 2, 5.467, -0.003, 2, 5.5, -0.004, 2, 5.533, -0.005, 2, 5.567, -0.006, 2, 5.6, -0.006, 2, 5.633, -0.007, 2, 5.667, -0.007, 2, 5.733, -0.007, 2, 5.767, -0.006, 2, 5.8, -0.006, 2, 5.833, -0.006, 2, 5.867, -0.005, 2, 5.9, -0.005, 2, 5.933, -0.004, 2, 5.967, -0.004, 2, 6, -0.004, 2, 6.067, -0.005, 2, 6.1, -0.007, 2, 6.133, -0.01, 2, 6.167, -0.014, 2, 6.2, -0.018, 2, 6.233, -0.022, 2, 6.267, -0.025, 2, 6.3, -0.027, 2, 6.333, -0.028, 2, 6.367, -0.026, 2, 6.4, -0.023, 2, 6.433, -0.017, 2, 6.467, -0.01, 2, 6.5, -0.002, 2, 6.533, 0.006, 2, 6.567, 0.013, 2, 6.6, 0.019, 2, 6.633, 0.023, 2, 6.667, 0.024, 2, 6.7, 0.023, 2, 6.733, 0.021, 2, 6.767, 0.018, 2, 6.8, 0.014, 2, 6.833, 0.01, 2, 6.867, 0.006, 2, 6.9, 0.003, 2, 6.933, -0.001, 2, 6.967, -0.003, 2, 7, -0.003, 2, 7.033, -0.003, 2, 7.067, -0.002, 2, 7.1, 0, 2, 7.133, 0.003, 2, 7.167, 0.006, 2, 7.2, 0.008, 2, 7.233, 0.011, 2, 7.267, 0.013, 2, 7.3, 0.015, 2, 7.333, 0.017, 2, 7.367, 0.017, 2, 7.4, 0.017, 2, 7.433, 0.016, 2, 7.467, 0.016, 2, 7.5, 0.015, 2, 7.533, 0.014, 2, 7.567, 0.013, 2, 7.6, 0.012, 2, 7.633, 0.012, 2, 7.7, 0.012, 2, 7.733, 0.013, 2, 7.767, 0.013, 2, 7.8, 0.014, 2, 7.833, 0.014, 2, 7.867, 0.015, 2, 7.9, 0.016, 2, 7.933, 0.016, 2, 7.967, 0.016, 2, 8, 0.017]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh2", "Segments": [0, 0, 2, 0.067, -0.001, 2, 0.1, -0.003, 2, 0.133, -0.007, 2, 0.167, -0.011, 2, 0.2, -0.016, 2, 0.233, -0.021, 2, 0.267, -0.026, 2, 0.3, -0.031, 2, 0.333, -0.035, 2, 0.367, -0.039, 2, 0.4, -0.041, 2, 0.433, -0.042, 2, 0.467, -0.04, 2, 0.5, -0.034, 2, 0.533, -0.026, 2, 0.567, -0.016, 2, 0.6, -0.004, 2, 0.633, 0.008, 2, 0.667, 0.019, 2, 0.7, 0.029, 2, 0.733, 0.038, 2, 0.767, 0.043, 2, 0.8, 0.045, 2, 0.833, 0.044, 2, 0.867, 0.039, 2, 0.9, 0.032, 2, 0.933, 0.023, 2, 0.967, 0.014, 2, 1, 0.005, 2, 1.033, -0.004, 2, 1.067, -0.011, 2, 1.1, -0.016, 2, 1.133, -0.018, 2, 1.167, -0.016, 2, 1.2, -0.013, 2, 1.233, -0.008, 2, 1.267, -0.002, 2, 1.3, 0.005, 2, 1.333, 0.011, 2, 1.367, 0.017, 2, 1.4, 0.022, 2, 1.433, 0.026, 2, 1.467, 0.027, 2, 1.5, 0.026, 2, 1.533, 0.025, 2, 1.567, 0.023, 2, 1.6, 0.02, 2, 1.633, 0.018, 2, 1.667, 0.015, 2, 1.7, 0.012, 2, 1.733, 0.01, 2, 1.767, 0.009, 2, 1.8, 0.008, 2, 1.833, 0.009, 2, 1.867, 0.009, 2, 1.9, 0.011, 2, 1.933, 0.012, 2, 1.967, 0.014, 2, 2, 0.016, 2, 2.033, 0.017, 2, 2.067, 0.018, 2, 2.1, 0.018, 2, 2.133, 0.017, 2, 2.167, 0.014, 2, 2.2, 0.01, 2, 2.233, 0.005, 2, 2.267, -0.001, 2, 2.3, -0.007, 2, 2.333, -0.013, 2, 2.367, -0.018, 2, 2.4, -0.023, 2, 2.433, -0.026, 2, 2.467, -0.027, 2, 2.5, -0.025, 2, 2.533, -0.021, 2, 2.567, -0.015, 2, 2.6, -0.007, 2, 2.633, 0.001, 2, 2.667, 0.009, 2, 2.7, 0.016, 2, 2.733, 0.022, 2, 2.767, 0.026, 2, 2.8, 0.028, 2, 2.833, 0.027, 2, 2.867, 0.024, 2, 2.9, 0.02, 2, 2.933, 0.015, 2, 2.967, 0.009, 2, 3, 0.003, 2, 3.033, -0.003, 2, 3.067, -0.008, 2, 3.1, -0.012, 2, 3.133, -0.015, 2, 3.167, -0.016, 2, 3.2, -0.015, 2, 3.233, -0.013, 2, 3.267, -0.011, 2, 3.3, -0.007, 2, 3.333, -0.004, 2, 3.367, -0.001, 2, 3.4, 0.003, 2, 3.433, 0.005, 2, 3.467, 0.007, 2, 3.5, 0.008, 2, 3.533, 0.007, 2, 3.567, 0.006, 2, 3.6, 0.005, 2, 3.633, 0.004, 2, 3.667, 0.002, 2, 3.7, 0, 2, 3.733, -0.001, 2, 3.767, -0.003, 2, 3.8, -0.004, 2, 3.833, -0.005, 2, 3.867, -0.005, 2, 3.9, -0.005, 2, 3.933, -0.005, 2, 3.967, -0.004, 2, 4, -0.003, 2, 4.033, -0.003, 2, 4.067, -0.002, 2, 4.1, -0.002, 2, 4.133, -0.003, 2, 4.167, -0.005, 2, 4.2, -0.008, 2, 4.233, -0.013, 2, 4.267, -0.017, 2, 4.3, -0.022, 2, 4.333, -0.027, 2, 4.367, -0.031, 2, 4.4, -0.034, 2, 4.433, -0.036, 2, 4.467, -0.037, 2, 4.5, -0.036, 2, 4.533, -0.031, 2, 4.567, -0.025, 2, 4.6, -0.017, 2, 4.633, -0.009, 2, 4.667, -0.001, 2, 4.7, 0.007, 2, 4.733, 0.013, 2, 4.767, 0.017, 2, 4.8, 0.019, 2, 4.833, 0.018, 2, 4.867, 0.014, 2, 4.9, 0.01, 2, 4.933, 0.004, 2, 4.967, -0.003, 2, 5, -0.009, 2, 5.033, -0.015, 2, 5.067, -0.02, 2, 5.1, -0.023, 2, 5.133, -0.024, 2, 5.167, -0.024, 2, 5.2, -0.022, 2, 5.233, -0.019, 2, 5.267, -0.016, 2, 5.3, -0.013, 2, 5.333, -0.009, 2, 5.367, -0.005, 2, 5.4, -0.002, 2, 5.433, 0, 2, 5.467, 0.002, 2, 5.5, 0.003, 2, 5.533, 0.002, 2, 5.567, 0.001, 2, 5.6, 0, 2, 5.633, -0.002, 2, 5.667, -0.004, 2, 5.7, -0.006, 2, 5.733, -0.007, 2, 5.767, -0.008, 2, 5.8, -0.009, 2, 5.833, -0.009, 2, 5.867, -0.008, 2, 5.9, -0.007, 2, 5.933, -0.006, 2, 5.967, -0.006, 2, 6, -0.005, 2, 6.033, -0.004, 2, 6.067, -0.003, 2, 6.1, -0.003, 2, 6.133, -0.004, 2, 6.167, -0.006, 2, 6.2, -0.009, 2, 6.233, -0.012, 2, 6.267, -0.016, 2, 6.3, -0.02, 2, 6.333, -0.023, 2, 6.367, -0.026, 2, 6.4, -0.028, 2, 6.433, -0.029, 2, 6.467, -0.027, 2, 6.5, -0.024, 2, 6.533, -0.018, 2, 6.567, -0.011, 2, 6.6, -0.002, 2, 6.633, 0.006, 2, 6.667, 0.014, 2, 6.7, 0.021, 2, 6.733, 0.027, 2, 6.767, 0.031, 2, 6.8, 0.032, 2, 6.833, 0.031, 2, 6.867, 0.028, 2, 6.9, 0.023, 2, 6.933, 0.018, 2, 6.967, 0.011, 2, 7, 0.005, 2, 7.033, 0, 2, 7.067, -0.005, 2, 7.1, -0.008, 2, 7.133, -0.009, 2, 7.167, -0.008, 2, 7.2, -0.006, 2, 7.233, -0.002, 2, 7.267, 0.002, 2, 7.3, 0.007, 2, 7.333, 0.011, 2, 7.367, 0.016, 2, 7.4, 0.019, 2, 7.433, 0.022, 2, 7.467, 0.022, 2, 7.5, 0.022, 2, 7.533, 0.021, 2, 7.567, 0.02, 2, 7.6, 0.018, 2, 7.633, 0.016, 2, 7.667, 0.015, 2, 7.7, 0.013, 2, 7.733, 0.011, 2, 7.767, 0.011, 2, 7.8, 0.01, 2, 7.833, 0.011, 2, 7.867, 0.011, 2, 7.9, 0.012, 2, 7.933, 0.014, 2, 7.967, 0.015, 2, 8, 0.016]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh3", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh3", "Segments": [0, 0, 2, 0.033, 0.002, 2, 0.067, 0.008, 2, 0.1, 0.017, 2, 0.133, 0.028, 2, 0.167, 0.04, 2, 0.2, 0.053, 2, 0.233, 0.065, 2, 0.267, 0.076, 2, 0.3, 0.085, 2, 0.333, 0.091, 2, 0.367, 0.093, 2, 0.4, 0.093, 2, 0.433, 0.092, 2, 0.467, 0.091, 2, 0.5, 0.089, 2, 0.533, 0.086, 2, 0.567, 0.083, 2, 0.6, 0.08, 2, 0.633, 0.076, 2, 0.667, 0.072, 2, 0.7, 0.067, 2, 0.733, 0.062, 2, 0.767, 0.056, 2, 0.8, 0.051, 2, 0.833, 0.045, 2, 0.867, 0.038, 2, 0.9, 0.032, 2, 0.933, 0.025, 2, 0.967, 0.018, 2, 1, 0.011, 2, 1.033, 0.003, 2, 1.067, -0.004, 2, 1.1, -0.012, 2, 1.133, -0.019, 2, 1.167, -0.027, 2, 1.2, -0.035, 2, 1.233, -0.043, 2, 1.267, -0.05, 2, 1.3, -0.058, 2, 1.333, -0.066, 2, 1.367, -0.074, 2, 1.4, -0.081, 2, 1.433, -0.089, 2, 1.467, -0.096, 2, 1.5, -0.103, 2, 1.533, -0.111, 2, 1.567, -0.117, 2, 1.6, -0.124, 2, 1.633, -0.13, 2, 1.667, -0.136, 2, 1.7, -0.142, 2, 1.733, -0.147, 2, 1.767, -0.152, 2, 1.8, -0.157, 2, 1.833, -0.161, 2, 1.867, -0.165, 2, 1.9, -0.169, 2, 1.933, -0.172, 2, 1.967, -0.174, 2, 2, -0.176, 2, 2.033, -0.178, 2, 2.067, -0.178, 2, 2.1, -0.179, 2, 2.133, -0.178, 2, 2.167, -0.175, 2, 2.2, -0.172, 2, 2.233, -0.169, 2, 2.267, -0.167, 2, 2.3, -0.166, 2, 2.333, -0.166, 2, 2.367, -0.167, 2, 2.4, -0.168, 2, 2.433, -0.169, 2, 2.467, -0.171, 2, 2.5, -0.173, 2, 2.533, -0.174, 2, 2.567, -0.176, 2, 2.6, -0.179, 2, 2.633, -0.181, 2, 2.667, -0.183, 2, 2.7, -0.185, 2, 2.733, -0.187, 2, 2.767, -0.188, 2, 2.8, -0.19, 2, 2.833, -0.191, 2, 2.867, -0.192, 2, 2.9, -0.193, 2, 2.933, -0.193, 2, 3.1, -0.194, 2, 3.133, -0.194, 2, 3.167, -0.195, 2, 3.2, -0.194, 2, 3.233, -0.193, 2, 3.267, -0.192, 2, 3.3, -0.192, 2, 3.333, -0.191, 2, 3.367, -0.192, 2, 3.4, -0.191, 2, 3.433, -0.191, 2, 3.467, -0.19, 2, 3.5, -0.189, 2, 3.533, -0.187, 2, 3.567, -0.186, 2, 3.6, -0.186, 2, 3.633, -0.186, 2, 3.667, -0.185, 2, 3.7, -0.184, 2, 3.733, -0.183, 2, 3.767, -0.182, 2, 3.8, -0.18, 2, 3.833, -0.178, 2, 3.867, -0.176, 2, 3.9, -0.173, 2, 3.933, -0.17, 2, 3.967, -0.167, 2, 4, -0.163, 2, 4.033, -0.16, 2, 4.067, -0.156, 2, 4.1, -0.152, 2, 4.133, -0.147, 2, 4.167, -0.143, 2, 4.2, -0.138, 2, 4.233, -0.133, 2, 4.267, -0.128, 2, 4.3, -0.122, 2, 4.333, -0.117, 2, 4.367, -0.111, 2, 4.4, -0.105, 2, 4.433, -0.099, 2, 4.467, -0.093, 2, 4.5, -0.087, 2, 4.533, -0.08, 2, 4.567, -0.074, 2, 4.6, -0.067, 2, 4.633, -0.06, 2, 4.667, -0.054, 2, 4.7, -0.047, 2, 4.733, -0.04, 2, 4.767, -0.032, 2, 4.8, -0.025, 2, 4.833, -0.018, 2, 4.867, -0.011, 2, 4.9, -0.004, 2, 4.933, 0.004, 2, 4.967, 0.011, 2, 5, 0.018, 2, 5.033, 0.026, 2, 5.067, 0.033, 2, 5.1, 0.04, 2, 5.133, 0.048, 2, 5.167, 0.055, 2, 5.2, 0.062, 2, 5.233, 0.069, 2, 5.267, 0.076, 2, 5.3, 0.084, 2, 5.333, 0.091, 2, 5.367, 0.098, 2, 5.4, 0.104, 2, 5.433, 0.111, 2, 5.467, 0.118, 2, 5.5, 0.124, 2, 5.533, 0.131, 2, 5.567, 0.137, 2, 5.6, 0.143, 2, 5.633, 0.149, 2, 5.667, 0.155, 2, 5.7, 0.161, 2, 5.733, 0.166, 2, 5.767, 0.172, 2, 5.8, 0.177, 2, 5.833, 0.182, 2, 5.867, 0.187, 2, 5.9, 0.191, 2, 5.933, 0.196, 2, 5.967, 0.2, 2, 6, 0.204, 2, 6.033, 0.207, 2, 6.067, 0.211, 2, 6.1, 0.214, 2, 6.133, 0.217, 2, 6.167, 0.22, 2, 6.2, 0.222, 2, 6.233, 0.224, 2, 6.267, 0.226, 2, 6.3, 0.227, 2, 6.333, 0.228, 2, 6.367, 0.229, 2, 6.4, 0.23, 2, 6.433, 0.23, 2, 6.467, 0.23, 2, 6.5, 0.229, 2, 6.533, 0.227, 2, 6.567, 0.225, 2, 6.6, 0.222, 2, 6.633, 0.219, 2, 6.667, 0.215, 2, 6.7, 0.211, 2, 6.733, 0.206, 2, 6.767, 0.201, 2, 6.8, 0.196, 2, 6.833, 0.19, 2, 6.867, 0.184, 2, 6.9, 0.177, 2, 6.933, 0.171, 2, 6.967, 0.164, 2, 7, 0.156, 2, 7.033, 0.149, 2, 7.067, 0.141, 2, 7.1, 0.134, 2, 7.133, 0.126, 2, 7.167, 0.118, 2, 7.2, 0.11, 2, 7.233, 0.102, 2, 7.267, 0.093, 2, 7.3, 0.085, 2, 7.333, 0.077, 2, 7.367, 0.069, 2, 7.4, 0.061, 2, 7.433, 0.054, 2, 7.467, 0.046, 2, 7.5, 0.038, 2, 7.533, 0.031, 2, 7.567, 0.024, 2, 7.6, 0.017, 2, 7.633, 0.011, 2, 7.667, 0.005, 2, 7.7, -0.001, 2, 7.733, -0.006, 2, 7.767, -0.012, 2, 7.8, -0.016, 2, 7.833, -0.02, 2, 7.867, -0.024, 2, 7.9, -0.027, 2, 7.933, -0.03, 2, 7.967, -0.032, 2, 8, -0.034]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh3", "Segments": [0, 0, 2, 0.033, -0.002, 2, 0.067, -0.008, 2, 0.1, -0.015, 2, 0.133, -0.022, 2, 0.167, -0.027, 2, 0.2, -0.03, 2, 0.233, -0.028, 2, 0.267, -0.024, 2, 0.3, -0.017, 2, 0.333, -0.009, 2, 0.367, -0.002, 2, 0.4, 0.006, 2, 0.433, 0.013, 2, 0.467, 0.017, 2, 0.5, 0.019, 2, 0.533, 0.018, 2, 0.567, 0.015, 2, 0.6, 0.012, 2, 0.633, 0.008, 2, 0.667, 0.004, 2, 0.7, 0.001, 2, 0.733, -0.001, 2, 0.767, -0.002, 2, 0.8, -0.002, 2, 0.833, -0.001, 2, 0.867, 0.001, 2, 0.9, 0.003, 2, 0.933, 0.005, 2, 0.967, 0.007, 2, 1, 0.009, 2, 1.033, 0.01, 2, 1.067, 0.01, 2, 1.1, 0.009, 2, 1.133, 0.01, 2, 1.167, 0.008, 2, 1.2, 0.009, 2, 1.233, 0.009, 2, 1.3, 0.008, 2, 1.333, 0.008, 2, 1.367, 0.007, 2, 1.4, 0.007, 2, 1.433, 0.007, 2, 1.467, 0.007, 2, 1.5, 0.008, 2, 1.533, 0.01, 2, 1.567, 0.01, 2, 1.6, 0.009, 2, 1.633, 0.01, 2, 1.667, 0.01, 2, 1.7, 0.01, 2, 1.733, 0.01, 2, 1.767, 0.011, 2, 1.8, 0.01, 2, 1.833, 0.01, 2, 1.867, 0.01, 2, 1.933, 0.01, 2, 1.967, 0.01, 2, 2, 0.01, 2, 2.033, 0.008, 2, 2.067, 0.003, 2, 2.1, -0.003, 2, 2.133, -0.009, 2, 2.167, -0.014, 2, 2.2, -0.015, 2, 2.233, -0.015, 2, 2.267, -0.012, 2, 2.3, -0.008, 2, 2.333, -0.004, 2, 2.367, 0, 2, 2.4, 0.004, 2, 2.433, 0.008, 2, 2.467, 0.011, 2, 2.5, 0.011, 2, 2.533, 0.011, 2, 2.567, 0.009, 2, 2.6, 0.007, 2, 2.633, 0.005, 2, 2.667, 0.002, 2, 2.7, 0, 2, 2.733, -0.002, 2, 2.767, -0.004, 2, 2.8, -0.004, 2, 2.833, -0.004, 2, 2.867, -0.004, 2, 2.9, -0.003, 2, 2.933, -0.002, 2, 2.967, -0.001, 2, 3, 0, 2, 3.033, 0.001, 2, 3.067, 0.001, 2, 3.1, 0.002, 2, 3.133, 0.001, 2, 3.167, 0.001, 2, 3.2, 0.001, 2, 3.233, 0, 2, 3.267, -0.001, 2, 3.3, -0.002, 2, 3.333, -0.002, 2, 3.367, -0.001, 2, 3.4, -0.001, 2, 3.433, 0, 2, 3.467, -0.001, 2, 3.5, -0.001, 2, 3.533, -0.002, 2, 3.567, -0.002, 2, 3.6, -0.001, 2, 3.633, -0.002, 2, 3.667, -0.001, 2, 3.7, -0.001, 2, 3.733, -0.002, 2, 3.767, -0.003, 2, 3.8, -0.003, 2, 3.867, -0.002, 2, 3.9, -0.002, 2, 3.933, -0.001, 2, 3.967, -0.002, 2, 4, -0.005, 2, 4.033, -0.008, 2, 4.067, -0.013, 2, 4.1, -0.017, 2, 4.133, -0.021, 2, 4.167, -0.024, 2, 4.2, -0.025, 2, 4.233, -0.024, 2, 4.267, -0.02, 2, 4.3, -0.016, 2, 4.333, -0.01, 2, 4.367, -0.004, 2, 4.4, 0, 2, 4.433, 0.004, 2, 4.467, 0.005, 2, 4.5, 0.005, 2, 4.533, 0.003, 2, 4.567, 0.001, 2, 4.6, -0.002, 2, 4.633, -0.004, 2, 4.667, -0.007, 2, 4.7, -0.009, 2, 4.733, -0.01, 2, 4.767, -0.011, 2, 4.833, -0.011, 2, 4.867, -0.01, 2, 4.9, -0.009, 2, 4.933, -0.008, 2, 4.967, -0.006, 2, 5, -0.005, 2, 5.033, -0.004, 2, 5.067, -0.003, 2, 5.1, -0.003, 2, 5.133, -0.003, 2, 5.167, -0.003, 2, 5.2, -0.003, 2, 5.233, -0.004, 2, 5.267, -0.005, 2, 5.3, -0.005, 2, 5.367, -0.004, 2, 5.433, -0.004, 2, 5.5, -0.004, 2, 5.567, -0.004, 2, 5.6, -0.003, 2, 5.667, -0.003, 2, 5.733, -0.003, 2, 5.8, -0.003, 2, 5.833, -0.003, 2, 5.867, -0.003, 2, 5.9, -0.003, 2, 5.933, -0.003, 2, 5.967, -0.003, 2, 6.033, -0.004, 2, 6.067, -0.009, 2, 6.1, -0.014, 2, 6.133, -0.019, 2, 6.167, -0.021, 2, 6.2, -0.02, 2, 6.233, -0.017, 2, 6.267, -0.014, 2, 6.3, -0.009, 2, 6.333, -0.004, 2, 6.367, 0.001, 2, 6.4, 0.006, 2, 6.433, 0.01, 2, 6.467, 0.012, 2, 6.5, 0.013, 2, 6.533, 0.012, 2, 6.567, 0.011, 2, 6.6, 0.008, 2, 6.633, 0.005, 2, 6.667, 0.002, 2, 6.7, 0.001, 2, 6.733, 0, 2, 6.767, 0, 2, 6.8, 0.001, 2, 6.833, 0.002, 2, 6.9, 0.002, 2, 6.933, 0.004, 2, 6.967, 0.006, 2, 7, 0.008, 2, 7.033, 0.009, 2, 7.067, 0.01, 2, 7.1, 0.009, 2, 7.133, 0.009, 2, 7.167, 0.008, 2, 7.2, 0.008, 2, 7.233, 0.009, 2, 7.267, 0.009, 2, 7.333, 0.008, 2, 7.367, 0.007, 2, 7.4, 0.008, 2, 7.433, 0.007, 2, 7.467, 0.008, 2, 7.5, 0.009, 2, 7.533, 0.01, 2, 7.567, 0.01, 2, 7.6, 0.009, 2, 7.633, 0.01, 2, 7.667, 0.009, 2, 7.7, 0.01, 2, 7.733, 0.01, 2, 7.767, 0.011, 2, 7.833, 0.01, 2, 7.867, 0.01, 2, 7.933, 0.01, 2, 7.967, 0.01, 2, 8, 0.01]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh3", "Segments": [0, 0, 2, 0.033, -0.001, 2, 0.067, -0.004, 2, 0.1, -0.008, 2, 0.133, -0.012, 2, 0.167, -0.017, 2, 0.2, -0.022, 2, 0.233, -0.026, 2, 0.267, -0.029, 2, 0.3, -0.03, 2, 0.333, -0.028, 2, 0.367, -0.023, 2, 0.4, -0.015, 2, 0.433, -0.006, 2, 0.467, 0.003, 2, 0.5, 0.012, 2, 0.533, 0.02, 2, 0.567, 0.025, 2, 0.6, 0.027, 2, 0.633, 0.026, 2, 0.667, 0.023, 2, 0.7, 0.018, 2, 0.733, 0.012, 2, 0.767, 0.007, 2, 0.8, 0.001, 2, 0.833, -0.004, 2, 0.867, -0.007, 2, 0.9, -0.008, 2, 0.933, -0.007, 2, 0.967, -0.005, 2, 1, -0.002, 2, 1.033, 0.001, 2, 1.067, 0.005, 2, 1.1, 0.008, 2, 1.133, 0.011, 2, 1.167, 0.013, 2, 1.2, 0.014, 2, 1.233, 0.014, 2, 1.267, 0.013, 2, 1.3, 0.012, 2, 1.333, 0.011, 2, 1.367, 0.009, 2, 1.4, 0.008, 2, 1.433, 0.007, 2, 1.467, 0.006, 2, 1.5, 0.006, 2, 1.533, 0.006, 2, 1.567, 0.007, 2, 1.6, 0.007, 2, 1.633, 0.008, 2, 1.667, 0.009, 2, 1.7, 0.01, 2, 1.733, 0.01, 2, 1.767, 0.011, 2, 1.8, 0.011, 2, 1.833, 0.011, 2, 1.867, 0.011, 2, 1.9, 0.01, 2, 1.933, 0.008, 2, 1.967, 0.006, 2, 2, 0.003, 2, 2.033, 0, 2, 2.067, -0.003, 2, 2.1, -0.006, 2, 2.133, -0.009, 2, 2.167, -0.012, 2, 2.2, -0.014, 2, 2.233, -0.016, 2, 2.267, -0.017, 2, 2.3, -0.017, 2, 2.333, -0.016, 2, 2.367, -0.013, 2, 2.4, -0.009, 2, 2.433, -0.003, 2, 2.467, 0.002, 2, 2.5, 0.008, 2, 2.533, 0.012, 2, 2.567, 0.015, 2, 2.6, 0.016, 2, 2.633, 0.015, 2, 2.667, 0.013, 2, 2.7, 0.01, 2, 2.733, 0.006, 2, 2.767, 0.002, 2, 2.8, -0.002, 2, 2.833, -0.005, 2, 2.867, -0.007, 2, 2.9, -0.008, 2, 2.933, -0.008, 2, 2.967, -0.007, 2, 3, -0.005, 2, 3.033, -0.003, 2, 3.067, -0.001, 2, 3.1, 0, 2, 3.133, 0.002, 2, 3.167, 0.003, 2, 3.2, 0.003, 2, 3.233, 0.003, 2, 3.267, 0.003, 2, 3.3, 0.002, 2, 3.333, 0.001, 2, 3.367, 0, 2, 3.4, 0, 2, 3.433, -0.001, 2, 3.467, -0.002, 2, 3.5, -0.002, 2, 3.533, -0.003, 2, 3.6, -0.002, 2, 3.633, -0.002, 2, 3.667, -0.002, 2, 3.7, -0.002, 2, 3.733, -0.002, 2, 3.8, -0.002, 2, 3.833, -0.002, 2, 3.867, -0.002, 2, 3.9, -0.002, 2, 3.933, -0.002, 2, 3.967, -0.002, 2, 4.033, -0.003, 2, 4.067, -0.005, 2, 4.1, -0.008, 2, 4.133, -0.012, 2, 4.167, -0.016, 2, 4.2, -0.02, 2, 4.233, -0.023, 2, 4.267, -0.026, 2, 4.3, -0.026, 2, 4.333, -0.025, 2, 4.367, -0.022, 2, 4.4, -0.017, 2, 4.433, -0.011, 2, 4.467, -0.005, 2, 4.5, 0.001, 2, 4.533, 0.005, 2, 4.567, 0.009, 2, 4.6, 0.01, 2, 4.633, 0.009, 2, 4.667, 0.007, 2, 4.7, 0.004, 2, 4.733, 0, 2, 4.767, -0.004, 2, 4.8, -0.008, 2, 4.833, -0.012, 2, 4.867, -0.014, 2, 4.9, -0.015, 2, 4.933, -0.014, 2, 4.967, -0.013, 2, 5, -0.011, 2, 5.033, -0.009, 2, 5.067, -0.006, 2, 5.1, -0.004, 2, 5.133, -0.002, 2, 5.167, -0.001, 2, 5.2, 0, 2, 5.233, -0.001, 2, 5.267, -0.001, 2, 5.3, -0.002, 2, 5.333, -0.003, 2, 5.367, -0.004, 2, 5.4, -0.005, 2, 5.433, -0.005, 2, 5.467, -0.005, 2, 5.5, -0.005, 2, 5.533, -0.005, 2, 5.567, -0.005, 2, 5.6, -0.004, 2, 5.633, -0.004, 2, 5.667, -0.004, 2, 5.7, -0.003, 2, 5.733, -0.003, 2, 5.767, -0.003, 2, 5.8, -0.003, 2, 5.9, -0.003, 2, 5.967, -0.003, 2, 6.033, -0.003, 2, 6.067, -0.005, 2, 6.1, -0.008, 2, 6.133, -0.012, 2, 6.167, -0.015, 2, 6.2, -0.018, 2, 6.233, -0.02, 2, 6.267, -0.021, 2, 6.3, -0.019, 2, 6.333, -0.016, 2, 6.367, -0.012, 2, 6.4, -0.007, 2, 6.433, -0.001, 2, 6.467, 0.005, 2, 6.5, 0.011, 2, 6.533, 0.015, 2, 6.567, 0.018, 2, 6.6, 0.019, 2, 6.633, 0.018, 2, 6.667, 0.016, 2, 6.7, 0.012, 2, 6.733, 0.008, 2, 6.767, 0.003, 2, 6.8, 0, 2, 6.833, -0.003, 2, 6.867, -0.004, 2, 6.9, -0.003, 2, 6.933, -0.002, 2, 6.967, 0, 2, 7, 0.002, 2, 7.033, 0.004, 2, 7.067, 0.006, 2, 7.1, 0.008, 2, 7.133, 0.01, 2, 7.167, 0.011, 2, 7.2, 0.012, 2, 7.233, 0.012, 2, 7.267, 0.011, 2, 7.3, 0.011, 2, 7.333, 0.01, 2, 7.367, 0.009, 2, 7.4, 0.008, 2, 7.433, 0.008, 2, 7.467, 0.007, 2, 7.5, 0.007, 2, 7.533, 0.007, 2, 7.567, 0.007, 2, 7.6, 0.008, 2, 7.633, 0.008, 2, 7.667, 0.009, 2, 7.7, 0.01, 2, 7.733, 0.01, 2, 7.767, 0.011, 2, 7.8, 0.011, 2, 7.833, 0.011, 2, 7.867, 0.01, 2, 7.9, 0.009, 2, 7.933, 0.006, 2, 7.967, 0.003, 2, 8, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh3", "Segments": [0, 0, 2, 0.067, -0.001, 2, 0.1, -0.003, 2, 0.133, -0.007, 2, 0.167, -0.011, 2, 0.2, -0.016, 2, 0.233, -0.02, 2, 0.267, -0.025, 2, 0.3, -0.028, 2, 0.333, -0.03, 2, 0.367, -0.031, 2, 0.4, -0.029, 2, 0.433, -0.024, 2, 0.467, -0.017, 2, 0.5, -0.007, 2, 0.533, 0.002, 2, 0.567, 0.012, 2, 0.6, 0.022, 2, 0.633, 0.029, 2, 0.667, 0.034, 2, 0.7, 0.036, 2, 0.733, 0.034, 2, 0.767, 0.03, 2, 0.8, 0.023, 2, 0.833, 0.014, 2, 0.867, 0.005, 2, 0.9, -0.003, 2, 0.933, -0.01, 2, 0.967, -0.015, 2, 1, -0.017, 2, 1.033, -0.015, 2, 1.067, -0.012, 2, 1.1, -0.007, 2, 1.133, -0.001, 2, 1.167, 0.005, 2, 1.2, 0.011, 2, 1.233, 0.016, 2, 1.267, 0.019, 2, 1.3, 0.02, 2, 1.333, 0.02, 2, 1.367, 0.018, 2, 1.4, 0.016, 2, 1.433, 0.013, 2, 1.467, 0.01, 2, 1.5, 0.008, 2, 1.533, 0.006, 2, 1.567, 0.004, 2, 1.6, 0.003, 2, 1.633, 0.004, 2, 1.667, 0.004, 2, 1.7, 0.005, 2, 1.733, 0.007, 2, 1.767, 0.008, 2, 1.8, 0.01, 2, 1.833, 0.011, 2, 1.867, 0.012, 2, 1.9, 0.013, 2, 1.933, 0.013, 2, 1.967, 0.013, 2, 2, 0.011, 2, 2.033, 0.009, 2, 2.067, 0.006, 2, 2.1, 0.003, 2, 2.133, 0, 2, 2.167, -0.004, 2, 2.2, -0.007, 2, 2.233, -0.01, 2, 2.267, -0.013, 2, 2.3, -0.016, 2, 2.333, -0.018, 2, 2.367, -0.02, 2, 2.4, -0.02, 2, 2.433, -0.019, 2, 2.467, -0.015, 2, 2.5, -0.009, 2, 2.533, -0.002, 2, 2.567, 0.004, 2, 2.6, 0.011, 2, 2.633, 0.017, 2, 2.667, 0.02, 2, 2.7, 0.022, 2, 2.733, 0.021, 2, 2.767, 0.018, 2, 2.8, 0.014, 2, 2.833, 0.009, 2, 2.867, 0.004, 2, 2.9, -0.001, 2, 2.933, -0.006, 2, 2.967, -0.01, 2, 3, -0.013, 2, 3.033, -0.014, 2, 3.067, -0.013, 2, 3.1, -0.011, 2, 3.133, -0.008, 2, 3.167, -0.005, 2, 3.2, -0.002, 2, 3.233, 0.001, 2, 3.267, 0.004, 2, 3.3, 0.006, 2, 3.333, 0.007, 2, 3.367, 0.006, 2, 3.4, 0.005, 2, 3.433, 0.004, 2, 3.467, 0.002, 2, 3.5, 0, 2, 3.533, -0.002, 2, 3.567, -0.003, 2, 3.6, -0.004, 2, 3.633, -0.005, 2, 3.667, -0.005, 2, 3.7, -0.004, 2, 3.733, -0.004, 2, 3.767, -0.003, 2, 3.8, -0.002, 2, 3.833, -0.002, 2, 3.867, -0.001, 2, 3.9, -0.001, 2, 3.933, -0.002, 2, 3.967, -0.003, 2, 4, -0.004, 2, 4.033, -0.006, 2, 4.067, -0.008, 2, 4.1, -0.011, 2, 4.133, -0.013, 2, 4.167, -0.016, 2, 4.2, -0.019, 2, 4.233, -0.021, 2, 4.267, -0.023, 2, 4.3, -0.025, 2, 4.333, -0.027, 2, 4.367, -0.028, 2, 4.4, -0.028, 2, 4.433, -0.027, 2, 4.467, -0.023, 2, 4.5, -0.017, 2, 4.533, -0.01, 2, 4.567, -0.002, 2, 4.6, 0.005, 2, 4.633, 0.011, 2, 4.667, 0.015, 2, 4.7, 0.016, 2, 4.733, 0.015, 2, 4.767, 0.012, 2, 4.8, 0.007, 2, 4.833, 0.001, 2, 4.867, -0.005, 2, 4.9, -0.011, 2, 4.933, -0.016, 2, 4.967, -0.019, 2, 5, -0.02, 2, 5.033, -0.019, 2, 5.067, -0.017, 2, 5.1, -0.014, 2, 5.133, -0.01, 2, 5.167, -0.006, 2, 5.2, -0.003, 2, 5.233, 0, 2, 5.267, 0.003, 2, 5.3, 0.004, 2, 5.333, 0.003, 2, 5.367, 0.002, 2, 5.4, 0.001, 2, 5.433, -0.001, 2, 5.467, -0.003, 2, 5.5, -0.005, 2, 5.533, -0.006, 2, 5.567, -0.007, 2, 5.6, -0.007, 2, 5.633, -0.007, 2, 5.667, -0.007, 2, 5.7, -0.006, 2, 5.733, -0.005, 2, 5.767, -0.004, 2, 5.8, -0.003, 2, 5.833, -0.002, 2, 5.867, -0.002, 2, 5.9, -0.002, 2, 5.967, -0.002, 2, 6, -0.003, 2, 6.033, -0.004, 2, 6.067, -0.006, 2, 6.1, -0.008, 2, 6.133, -0.01, 2, 6.167, -0.013, 2, 6.2, -0.015, 2, 6.233, -0.017, 2, 6.267, -0.019, 2, 6.3, -0.02, 2, 6.333, -0.021, 2, 6.367, -0.021, 2, 6.4, -0.02, 2, 6.433, -0.016, 2, 6.467, -0.011, 2, 6.5, -0.005, 2, 6.533, 0.002, 2, 6.567, 0.009, 2, 6.6, 0.015, 2, 6.633, 0.021, 2, 6.667, 0.024, 2, 6.7, 0.025, 2, 6.733, 0.024, 2, 6.767, 0.021, 2, 6.8, 0.016, 2, 6.833, 0.011, 2, 6.867, 0.005, 2, 6.9, 0, 2, 6.933, -0.005, 2, 6.967, -0.008, 2, 7, -0.009, 2, 7.033, -0.009, 2, 7.067, -0.006, 2, 7.1, -0.003, 2, 7.133, 0.001, 2, 7.167, 0.006, 2, 7.2, 0.01, 2, 7.233, 0.013, 2, 7.267, 0.016, 2, 7.3, 0.016, 2, 7.333, 0.016, 2, 7.367, 0.015, 2, 7.4, 0.013, 2, 7.433, 0.012, 2, 7.467, 0.01, 2, 7.5, 0.008, 2, 7.533, 0.007, 2, 7.567, 0.006, 2, 7.6, 0.005, 2, 7.633, 0.005, 2, 7.667, 0.006, 2, 7.7, 0.007, 2, 7.733, 0.008, 2, 7.767, 0.009, 2, 7.8, 0.01, 2, 7.833, 0.011, 2, 7.867, 0.011, 2, 7.9, 0.012, 2, 7.933, 0.012, 2, 7.967, 0.012, 2, 8, 0.01]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh46", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh46", "Segments": [0, 0, 2, 0.033, 0.001, 2, 0.067, 0.005, 2, 0.1, 0.011, 2, 0.133, 0.018, 2, 0.167, 0.027, 2, 0.2, 0.036, 2, 0.233, 0.046, 2, 0.267, 0.056, 2, 0.3, 0.065, 2, 0.333, 0.074, 2, 0.367, 0.081, 2, 0.4, 0.087, 2, 0.433, 0.091, 2, 0.467, 0.092, 2, 0.5, 0.092, 2, 0.533, 0.091, 2, 0.567, 0.09, 2, 0.6, 0.088, 2, 0.633, 0.085, 2, 0.667, 0.083, 2, 0.7, 0.08, 2, 0.733, 0.076, 2, 0.767, 0.072, 2, 0.8, 0.068, 2, 0.833, 0.063, 2, 0.867, 0.058, 2, 0.9, 0.053, 2, 0.933, 0.047, 2, 0.967, 0.042, 2, 1, 0.036, 2, 1.033, 0.03, 2, 1.067, 0.023, 2, 1.1, 0.017, 2, 1.133, 0.01, 2, 1.167, 0.003, 2, 1.2, -0.004, 2, 1.233, -0.011, 2, 1.267, -0.018, 2, 1.3, -0.025, 2, 1.333, -0.032, 2, 1.367, -0.039, 2, 1.4, -0.046, 2, 1.433, -0.052, 2, 1.467, -0.059, 2, 1.5, -0.066, 2, 1.533, -0.072, 2, 1.567, -0.079, 2, 1.6, -0.085, 2, 1.633, -0.091, 2, 1.667, -0.097, 2, 1.7, -0.102, 2, 1.733, -0.107, 2, 1.767, -0.112, 2, 1.8, -0.117, 2, 1.833, -0.121, 2, 1.867, -0.125, 2, 1.9, -0.129, 2, 1.933, -0.132, 2, 1.967, -0.135, 2, 2, -0.137, 2, 2.033, -0.139, 2, 2.067, -0.14, 2, 2.1, -0.141, 2, 2.133, -0.141, 2, 2.167, -0.141, 2, 2.2, -0.139, 2, 2.233, -0.138, 2, 2.267, -0.136, 2, 2.3, -0.135, 2, 2.333, -0.134, 2, 2.4, -0.135, 2, 2.433, -0.135, 2, 2.467, -0.136, 2, 2.5, -0.136, 2, 2.533, -0.137, 2, 2.567, -0.139, 2, 2.6, -0.14, 2, 2.633, -0.141, 2, 2.667, -0.143, 2, 2.7, -0.145, 2, 2.733, -0.146, 2, 2.767, -0.148, 2, 2.8, -0.15, 2, 2.833, -0.151, 2, 2.867, -0.153, 2, 2.9, -0.155, 2, 2.933, -0.156, 2, 2.967, -0.158, 2, 3, -0.159, 2, 3.033, -0.16, 2, 3.067, -0.161, 2, 3.1, -0.162, 2, 3.133, -0.163, 2, 3.167, -0.163, 2, 3.2, -0.163, 2, 3.267, -0.163, 2, 3.367, -0.164, 2, 3.4, -0.164, 2, 3.433, -0.165, 2, 3.467, -0.164, 2, 3.5, -0.164, 2, 3.533, -0.163, 2, 3.567, -0.163, 2, 3.6, -0.163, 2, 3.633, -0.163, 2, 3.667, -0.163, 2, 3.7, -0.162, 2, 3.733, -0.161, 2, 3.767, -0.16, 2, 3.8, -0.158, 2, 3.833, -0.158, 2, 3.867, -0.158, 2, 3.9, -0.157, 2, 3.967, -0.157, 2, 4, -0.156, 2, 4.033, -0.155, 2, 4.067, -0.154, 2, 4.1, -0.153, 2, 4.133, -0.151, 2, 4.167, -0.149, 2, 4.2, -0.146, 2, 4.233, -0.143, 2, 4.267, -0.14, 2, 4.3, -0.137, 2, 4.333, -0.133, 2, 4.367, -0.13, 2, 4.4, -0.126, 2, 4.433, -0.121, 2, 4.467, -0.117, 2, 4.5, -0.112, 2, 4.533, -0.107, 2, 4.567, -0.102, 2, 4.6, -0.097, 2, 4.633, -0.091, 2, 4.667, -0.086, 2, 4.7, -0.08, 2, 4.733, -0.074, 2, 4.767, -0.068, 2, 4.8, -0.062, 2, 4.833, -0.056, 2, 4.867, -0.049, 2, 4.9, -0.043, 2, 4.933, -0.036, 2, 4.967, -0.029, 2, 5, -0.023, 2, 5.033, -0.016, 2, 5.067, -0.009, 2, 5.1, -0.002, 2, 5.133, 0.005, 2, 5.167, 0.012, 2, 5.2, 0.019, 2, 5.233, 0.025, 2, 5.267, 0.032, 2, 5.3, 0.039, 2, 5.333, 0.046, 2, 5.367, 0.053, 2, 5.4, 0.06, 2, 5.433, 0.066, 2, 5.467, 0.073, 2, 5.5, 0.08, 2, 5.533, 0.086, 2, 5.567, 0.093, 2, 5.6, 0.099, 2, 5.633, 0.105, 2, 5.667, 0.111, 2, 5.7, 0.117, 2, 5.733, 0.123, 2, 5.767, 0.128, 2, 5.8, 0.134, 2, 5.833, 0.139, 2, 5.867, 0.144, 2, 5.9, 0.149, 2, 5.933, 0.154, 2, 5.967, 0.158, 2, 6, 0.163, 2, 6.033, 0.167, 2, 6.067, 0.171, 2, 6.1, 0.174, 2, 6.133, 0.177, 2, 6.167, 0.18, 2, 6.2, 0.183, 2, 6.233, 0.186, 2, 6.267, 0.188, 2, 6.3, 0.19, 2, 6.333, 0.191, 2, 6.367, 0.192, 2, 6.4, 0.193, 2, 6.433, 0.194, 2, 6.467, 0.194, 2, 6.533, 0.194, 2, 6.567, 0.193, 2, 6.6, 0.191, 2, 6.633, 0.189, 2, 6.667, 0.187, 2, 6.7, 0.184, 2, 6.733, 0.181, 2, 6.767, 0.177, 2, 6.8, 0.172, 2, 6.833, 0.168, 2, 6.867, 0.163, 2, 6.9, 0.157, 2, 6.933, 0.152, 2, 6.967, 0.146, 2, 7, 0.14, 2, 7.033, 0.134, 2, 7.067, 0.127, 2, 7.1, 0.12, 2, 7.133, 0.113, 2, 7.167, 0.106, 2, 7.2, 0.099, 2, 7.233, 0.092, 2, 7.267, 0.085, 2, 7.3, 0.078, 2, 7.333, 0.071, 2, 7.367, 0.064, 2, 7.4, 0.057, 2, 7.433, 0.05, 2, 7.467, 0.043, 2, 7.5, 0.036, 2, 7.533, 0.03, 2, 7.567, 0.023, 2, 7.6, 0.017, 2, 7.633, 0.011, 2, 7.667, 0.006, 2, 7.7, 0, 2, 7.733, -0.005, 2, 7.767, -0.009, 2, 7.8, -0.014, 2, 7.833, -0.017, 2, 7.867, -0.021, 2, 7.9, -0.024, 2, 7.933, -0.026, 2, 7.967, -0.028, 2, 8, -0.03]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh46", "Segments": [0, 0, 2, 0.033, -0.002, 2, 0.067, -0.006, 2, 0.1, -0.012, 2, 0.133, -0.019, 2, 0.167, -0.025, 2, 0.2, -0.029, 2, 0.233, -0.031, 2, 0.267, -0.03, 2, 0.3, -0.028, 2, 0.333, -0.024, 2, 0.367, -0.018, 2, 0.4, -0.013, 2, 0.433, -0.007, 2, 0.467, -0.001, 2, 0.5, 0.005, 2, 0.533, 0.01, 2, 0.567, 0.014, 2, 0.6, 0.017, 2, 0.633, 0.018, 2, 0.667, 0.017, 2, 0.7, 0.016, 2, 0.733, 0.013, 2, 0.767, 0.011, 2, 0.8, 0.008, 2, 0.833, 0.005, 2, 0.867, 0.003, 2, 0.9, 0.002, 2, 0.933, 0.001, 2, 0.967, 0.001, 2, 1, 0.002, 2, 1.033, 0.003, 2, 1.067, 0.004, 2, 1.1, 0.006, 2, 1.133, 0.007, 2, 1.167, 0.009, 2, 1.2, 0.01, 2, 1.233, 0.012, 2, 1.267, 0.013, 2, 1.3, 0.013, 2, 1.333, 0.014, 2, 1.367, 0.013, 2, 1.4, 0.013, 2, 1.433, 0.013, 2, 1.467, 0.013, 2, 1.5, 0.013, 2, 1.533, 0.013, 2, 1.6, 0.013, 2, 1.633, 0.013, 2, 1.667, 0.013, 2, 1.7, 0.013, 2, 1.733, 0.013, 2, 1.767, 0.014, 2, 1.8, 0.014, 2, 1.833, 0.014, 2, 1.9, 0.014, 2, 1.933, 0.014, 2, 1.967, 0.015, 2, 2, 0.015, 2, 2.033, 0.014, 2, 2.067, 0.011, 2, 2.1, 0.007, 2, 2.133, 0.003, 2, 2.167, -0.002, 2, 2.2, -0.007, 2, 2.233, -0.011, 2, 2.267, -0.013, 2, 2.3, -0.014, 2, 2.333, -0.014, 2, 2.367, -0.012, 2, 2.4, -0.01, 2, 2.433, -0.007, 2, 2.467, -0.003, 2, 2.5, 0, 2, 2.533, 0.003, 2, 2.567, 0.006, 2, 2.6, 0.008, 2, 2.633, 0.01, 2, 2.667, 0.011, 2, 2.7, 0.01, 2, 2.733, 0.009, 2, 2.767, 0.008, 2, 2.8, 0.007, 2, 2.833, 0.005, 2, 2.867, 0.003, 2, 2.9, 0.001, 2, 2.933, 0, 2, 2.967, -0.002, 2, 3, -0.002, 2, 3.033, -0.003, 2, 3.1, -0.003, 2, 3.133, -0.002, 2, 3.167, -0.002, 2, 3.2, -0.001, 2, 3.233, 0, 2, 3.267, 0, 2, 3.3, 0, 2, 3.367, 0.001, 2, 3.4, 0.001, 2, 3.433, 0.001, 2, 3.467, 0.001, 2, 3.5, 0, 2, 3.533, -0.001, 2, 3.567, -0.001, 2, 3.6, -0.001, 2, 3.633, -0.001, 2, 3.667, -0.001, 2, 3.7, -0.001, 2, 3.733, -0.001, 2, 3.767, -0.002, 2, 3.8, -0.003, 2, 3.833, -0.003, 2, 3.867, -0.002, 2, 3.9, -0.002, 2, 3.933, -0.001, 2, 3.967, -0.002, 2, 4, -0.004, 2, 4.033, -0.007, 2, 4.067, -0.01, 2, 4.1, -0.014, 2, 4.133, -0.018, 2, 4.167, -0.022, 2, 4.2, -0.025, 2, 4.233, -0.027, 2, 4.267, -0.027, 2, 4.3, -0.027, 2, 4.333, -0.024, 2, 4.367, -0.021, 2, 4.4, -0.017, 2, 4.433, -0.013, 2, 4.467, -0.009, 2, 4.5, -0.005, 2, 4.533, -0.001, 2, 4.567, 0.001, 2, 4.6, 0.002, 2, 4.7, 0.001, 2, 4.733, 0, 2, 4.767, -0.001, 2, 4.8, -0.003, 2, 4.833, -0.004, 2, 4.867, -0.006, 2, 4.9, -0.008, 2, 4.933, -0.009, 2, 4.967, -0.01, 2, 5, -0.01, 2, 5.033, -0.01, 2, 5.067, -0.01, 2, 5.1, -0.009, 2, 5.133, -0.009, 2, 5.167, -0.008, 2, 5.2, -0.007, 2, 5.233, -0.006, 2, 5.267, -0.006, 2, 5.3, -0.005, 2, 5.333, -0.005, 2, 5.367, -0.004, 2, 5.433, -0.004, 2, 5.467, -0.004, 2, 5.5, -0.005, 2, 5.533, -0.005, 2, 5.567, -0.005, 2, 5.6, -0.005, 2, 5.633, -0.005, 2, 5.667, -0.005, 2, 5.7, -0.005, 2, 5.8, -0.005, 2, 5.833, -0.005, 2, 5.867, -0.005, 2, 5.9, -0.004, 2, 5.933, -0.004, 2, 5.967, -0.004, 2, 6, -0.004, 2, 6.033, -0.005, 2, 6.067, -0.007, 2, 6.1, -0.011, 2, 6.133, -0.015, 2, 6.167, -0.019, 2, 6.2, -0.021, 2, 6.233, -0.022, 2, 6.267, -0.022, 2, 6.3, -0.02, 2, 6.333, -0.017, 2, 6.367, -0.013, 2, 6.4, -0.009, 2, 6.433, -0.005, 2, 6.467, 0, 2, 6.5, 0.004, 2, 6.533, 0.008, 2, 6.567, 0.01, 2, 6.6, 0.012, 2, 6.633, 0.013, 2, 6.667, 0.013, 2, 6.7, 0.012, 2, 6.733, 0.01, 2, 6.767, 0.009, 2, 6.8, 0.007, 2, 6.833, 0.005, 2, 6.867, 0.004, 2, 6.9, 0.003, 2, 6.933, 0.003, 2, 6.967, 0.003, 2, 7, 0.003, 2, 7.033, 0.004, 2, 7.067, 0.005, 2, 7.1, 0.007, 2, 7.133, 0.008, 2, 7.167, 0.009, 2, 7.2, 0.01, 2, 7.233, 0.011, 2, 7.267, 0.012, 2, 7.3, 0.013, 2, 7.333, 0.013, 2, 7.367, 0.013, 2, 7.4, 0.013, 2, 7.433, 0.012, 2, 7.467, 0.013, 2, 7.5, 0.013, 2, 7.533, 0.013, 2, 7.567, 0.013, 2, 7.6, 0.013, 2, 7.633, 0.013, 2, 7.667, 0.013, 2, 7.7, 0.013, 2, 7.733, 0.013, 2, 7.767, 0.014, 2, 7.8, 0.014, 2, 7.833, 0.014, 2, 7.867, 0.014, 2, 7.9, 0.014, 2, 7.933, 0.014, 2, 7.967, 0.015, 2, 8, 0.015]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh46", "Segments": [0, 0, 2, 0.033, -0.001, 2, 0.067, -0.003, 2, 0.1, -0.006, 2, 0.133, -0.009, 2, 0.167, -0.014, 2, 0.2, -0.018, 2, 0.233, -0.022, 2, 0.267, -0.026, 2, 0.3, -0.029, 2, 0.333, -0.031, 2, 0.367, -0.031, 2, 0.4, -0.03, 2, 0.433, -0.027, 2, 0.467, -0.022, 2, 0.5, -0.016, 2, 0.533, -0.01, 2, 0.567, -0.003, 2, 0.6, 0.004, 2, 0.633, 0.011, 2, 0.667, 0.017, 2, 0.7, 0.022, 2, 0.733, 0.025, 2, 0.767, 0.026, 2, 0.8, 0.025, 2, 0.833, 0.023, 2, 0.867, 0.021, 2, 0.9, 0.017, 2, 0.933, 0.014, 2, 0.967, 0.01, 2, 1, 0.006, 2, 1.033, 0.003, 2, 1.067, 0, 2, 1.1, -0.002, 2, 1.133, -0.002, 2, 1.167, -0.002, 2, 1.2, -0.001, 2, 1.233, 0.001, 2, 1.267, 0.003, 2, 1.3, 0.005, 2, 1.333, 0.007, 2, 1.367, 0.01, 2, 1.4, 0.012, 2, 1.433, 0.014, 2, 1.467, 0.015, 2, 1.5, 0.016, 2, 1.533, 0.017, 2, 1.567, 0.017, 2, 1.6, 0.016, 2, 1.633, 0.016, 2, 1.667, 0.015, 2, 1.7, 0.014, 2, 1.733, 0.014, 2, 1.767, 0.013, 2, 1.8, 0.013, 2, 1.833, 0.013, 2, 1.9, 0.013, 2, 1.933, 0.013, 2, 1.967, 0.013, 2, 2, 0.014, 2, 2.033, 0.014, 2, 2.067, 0.013, 2, 2.1, 0.012, 2, 2.133, 0.009, 2, 2.167, 0.006, 2, 2.2, 0.003, 2, 2.233, -0.001, 2, 2.267, -0.005, 2, 2.3, -0.008, 2, 2.333, -0.012, 2, 2.367, -0.014, 2, 2.4, -0.016, 2, 2.433, -0.016, 2, 2.467, -0.016, 2, 2.5, -0.014, 2, 2.533, -0.011, 2, 2.567, -0.007, 2, 2.6, -0.003, 2, 2.633, 0.001, 2, 2.667, 0.005, 2, 2.7, 0.009, 2, 2.733, 0.012, 2, 2.767, 0.014, 2, 2.8, 0.015, 2, 2.833, 0.014, 2, 2.867, 0.013, 2, 2.9, 0.011, 2, 2.933, 0.009, 2, 2.967, 0.006, 2, 3, 0.003, 2, 3.033, 0, 2, 3.067, -0.002, 2, 3.1, -0.004, 2, 3.133, -0.005, 2, 3.167, -0.006, 2, 3.2, -0.006, 2, 3.233, -0.005, 2, 3.267, -0.004, 2, 3.3, -0.003, 2, 3.333, -0.002, 2, 3.367, -0.001, 2, 3.4, 0, 2, 3.433, 0.001, 2, 3.467, 0.001, 2, 3.5, 0.002, 2, 3.533, 0.002, 2, 3.567, 0.002, 2, 3.6, 0.002, 2, 3.633, 0.001, 2, 3.667, 0.001, 2, 3.7, 0, 2, 3.733, -0.001, 2, 3.767, -0.001, 2, 3.8, -0.002, 2, 3.833, -0.002, 2, 3.867, -0.003, 2, 3.9, -0.003, 2, 3.933, -0.003, 2, 3.967, -0.003, 2, 4, -0.003, 2, 4.067, -0.003, 2, 4.1, -0.005, 2, 4.133, -0.007, 2, 4.167, -0.01, 2, 4.2, -0.014, 2, 4.233, -0.017, 2, 4.267, -0.021, 2, 4.3, -0.023, 2, 4.333, -0.026, 2, 4.367, -0.027, 2, 4.4, -0.028, 2, 4.433, -0.027, 2, 4.467, -0.025, 2, 4.5, -0.022, 2, 4.533, -0.018, 2, 4.567, -0.013, 2, 4.6, -0.009, 2, 4.633, -0.004, 2, 4.667, 0, 2, 4.7, 0.003, 2, 4.733, 0.005, 2, 4.767, 0.006, 2, 4.8, 0.006, 2, 4.833, 0.004, 2, 4.867, 0.003, 2, 4.9, 0, 2, 4.933, -0.002, 2, 4.967, -0.005, 2, 5, -0.008, 2, 5.033, -0.01, 2, 5.067, -0.012, 2, 5.1, -0.013, 2, 5.133, -0.013, 2, 5.167, -0.013, 2, 5.2, -0.013, 2, 5.233, -0.012, 2, 5.267, -0.011, 2, 5.3, -0.009, 2, 5.333, -0.008, 2, 5.367, -0.007, 2, 5.4, -0.005, 2, 5.433, -0.004, 2, 5.467, -0.003, 2, 5.5, -0.003, 2, 5.533, -0.003, 2, 5.567, -0.003, 2, 5.6, -0.003, 2, 5.633, -0.003, 2, 5.667, -0.004, 2, 5.7, -0.004, 2, 5.733, -0.004, 2, 5.767, -0.005, 2, 5.8, -0.005, 2, 5.833, -0.005, 2, 5.867, -0.005, 2, 5.933, -0.005, 2, 5.967, -0.005, 2, 6, -0.005, 2, 6.033, -0.005, 2, 6.067, -0.005, 2, 6.1, -0.006, 2, 6.133, -0.008, 2, 6.167, -0.011, 2, 6.2, -0.013, 2, 6.233, -0.016, 2, 6.267, -0.018, 2, 6.3, -0.02, 2, 6.333, -0.021, 2, 6.367, -0.022, 2, 6.4, -0.021, 2, 6.433, -0.019, 2, 6.467, -0.015, 2, 6.5, -0.011, 2, 6.533, -0.006, 2, 6.567, -0.001, 2, 6.6, 0.004, 2, 6.633, 0.008, 2, 6.667, 0.013, 2, 6.7, 0.016, 2, 6.733, 0.018, 2, 6.767, 0.019, 2, 6.8, 0.018, 2, 6.833, 0.017, 2, 6.867, 0.015, 2, 6.9, 0.013, 2, 6.933, 0.01, 2, 6.967, 0.007, 2, 7, 0.005, 2, 7.033, 0.003, 2, 7.067, 0.001, 2, 7.1, 0.001, 2, 7.167, 0.001, 2, 7.2, 0.002, 2, 7.233, 0.003, 2, 7.267, 0.005, 2, 7.3, 0.006, 2, 7.333, 0.008, 2, 7.367, 0.01, 2, 7.4, 0.012, 2, 7.433, 0.013, 2, 7.467, 0.014, 2, 7.5, 0.015, 2, 7.533, 0.015, 2, 7.567, 0.015, 2, 7.6, 0.015, 2, 7.633, 0.015, 2, 7.667, 0.014, 2, 7.7, 0.014, 2, 7.733, 0.013, 2, 7.767, 0.013, 2, 7.8, 0.013, 2, 7.867, 0.013, 2, 7.9, 0.013, 2, 7.933, 0.014, 2, 7.967, 0.014, 2, 8, 0.014]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh46", "Segments": [0, 0, 2, 0.067, -0.001, 2, 0.1, -0.002, 2, 0.133, -0.005, 2, 0.167, -0.009, 2, 0.2, -0.013, 2, 0.233, -0.017, 2, 0.267, -0.022, 2, 0.3, -0.026, 2, 0.333, -0.031, 2, 0.367, -0.035, 2, 0.4, -0.038, 2, 0.433, -0.041, 2, 0.467, -0.043, 2, 0.5, -0.044, 2, 0.533, -0.042, 2, 0.567, -0.037, 2, 0.6, -0.03, 2, 0.633, -0.02, 2, 0.667, -0.01, 2, 0.7, 0.001, 2, 0.733, 0.013, 2, 0.767, 0.023, 2, 0.8, 0.032, 2, 0.833, 0.04, 2, 0.867, 0.044, 2, 0.9, 0.046, 2, 0.933, 0.045, 2, 0.967, 0.041, 2, 1, 0.036, 2, 1.033, 0.029, 2, 1.067, 0.021, 2, 1.1, 0.014, 2, 1.133, 0.006, 2, 1.167, -0.001, 2, 1.2, -0.006, 2, 1.233, -0.01, 2, 1.267, -0.011, 2, 1.3, -0.01, 2, 1.333, -0.008, 2, 1.367, -0.005, 2, 1.4, -0.001, 2, 1.433, 0.004, 2, 1.467, 0.009, 2, 1.5, 0.014, 2, 1.533, 0.019, 2, 1.567, 0.023, 2, 1.6, 0.026, 2, 1.633, 0.028, 2, 1.667, 0.029, 2, 1.7, 0.029, 2, 1.733, 0.028, 2, 1.767, 0.026, 2, 1.8, 0.025, 2, 1.833, 0.023, 2, 1.867, 0.021, 2, 1.9, 0.019, 2, 1.933, 0.017, 2, 1.967, 0.016, 2, 2, 0.015, 2, 2.033, 0.015, 2, 2.067, 0.015, 2, 2.1, 0.015, 2, 2.133, 0.015, 2, 2.2, 0.015, 2, 2.233, 0.012, 2, 2.267, 0.009, 2, 2.3, 0.005, 2, 2.333, 0, 2, 2.367, -0.005, 2, 2.4, -0.01, 2, 2.433, -0.014, 2, 2.467, -0.018, 2, 2.5, -0.022, 2, 2.533, -0.024, 2, 2.567, -0.025, 2, 2.6, -0.023, 2, 2.633, -0.02, 2, 2.667, -0.015, 2, 2.7, -0.01, 2, 2.733, -0.003, 2, 2.767, 0.004, 2, 2.8, 0.011, 2, 2.833, 0.016, 2, 2.867, 0.021, 2, 2.9, 0.025, 2, 2.933, 0.026, 2, 2.967, 0.025, 2, 3, 0.023, 2, 3.033, 0.02, 2, 3.067, 0.015, 2, 3.1, 0.011, 2, 3.133, 0.006, 2, 3.167, 0.001, 2, 3.2, -0.003, 2, 3.233, -0.007, 2, 3.267, -0.011, 2, 3.3, -0.013, 2, 3.333, -0.014, 2, 3.367, -0.013, 2, 3.4, -0.012, 2, 3.433, -0.01, 2, 3.467, -0.008, 2, 3.5, -0.005, 2, 3.533, -0.003, 2, 3.567, 0, 2, 3.6, 0.002, 2, 3.633, 0.004, 2, 3.667, 0.005, 2, 3.7, 0.005, 2, 3.733, 0.005, 2, 3.767, 0.005, 2, 3.8, 0.004, 2, 3.833, 0.002, 2, 3.867, 0.001, 2, 3.9, -0.001, 2, 3.933, -0.003, 2, 3.967, -0.005, 2, 4, -0.008, 2, 4.033, -0.01, 2, 4.067, -0.013, 2, 4.1, -0.015, 2, 4.133, -0.018, 2, 4.167, -0.021, 2, 4.2, -0.023, 2, 4.233, -0.026, 2, 4.267, -0.028, 2, 4.3, -0.03, 2, 4.333, -0.032, 2, 4.367, -0.034, 2, 4.4, -0.036, 2, 4.433, -0.037, 2, 4.467, -0.038, 2, 4.5, -0.039, 2, 4.533, -0.039, 2, 4.567, -0.038, 2, 4.6, -0.034, 2, 4.633, -0.029, 2, 4.667, -0.023, 2, 4.7, -0.016, 2, 4.733, -0.008, 2, 4.767, -0.001, 2, 4.8, 0.005, 2, 4.833, 0.01, 2, 4.867, 0.014, 2, 4.9, 0.015, 2, 4.933, 0.014, 2, 4.967, 0.012, 2, 5, 0.009, 2, 5.033, 0.005, 2, 5.067, 0.001, 2, 5.1, -0.004, 2, 5.133, -0.009, 2, 5.167, -0.013, 2, 5.2, -0.017, 2, 5.233, -0.02, 2, 5.267, -0.023, 2, 5.3, -0.023, 2, 5.333, -0.023, 2, 5.367, -0.021, 2, 5.4, -0.019, 2, 5.433, -0.016, 2, 5.467, -0.013, 2, 5.5, -0.01, 2, 5.533, -0.007, 2, 5.567, -0.004, 2, 5.6, -0.002, 2, 5.633, 0, 2, 5.667, 0, 2, 5.733, 0, 2, 5.767, -0.001, 2, 5.8, -0.002, 2, 5.833, -0.003, 2, 5.867, -0.005, 2, 5.9, -0.006, 2, 5.933, -0.007, 2, 5.967, -0.008, 2, 6, -0.008, 2, 6.033, -0.002, 2, 6.067, 0.01, 2, 6.1, 0.023, 2, 6.133, 0.029, 2, 6.167, 0.026, 2, 6.2, 0.017, 2, 6.233, 0.004, 2, 6.267, -0.012, 2, 6.3, -0.028, 2, 6.333, -0.044, 2, 6.367, -0.057, 2, 6.4, -0.066, 2, 6.433, -0.069, 2, 6.467, -0.067, 2, 6.5, -0.062, 2, 6.533, -0.054, 2, 6.567, -0.044, 2, 6.6, -0.033, 2, 6.633, -0.02, 2, 6.667, -0.007, 2, 6.7, 0.005, 2, 6.733, 0.017, 2, 6.767, 0.027, 2, 6.8, 0.035, 2, 6.833, 0.04, 2, 6.867, 0.042, 2, 6.9, 0.041, 2, 6.933, 0.038, 2, 6.967, 0.034, 2, 7, 0.029, 2, 7.033, 0.024, 2, 7.067, 0.018, 2, 7.1, 0.012, 2, 7.133, 0.006, 2, 7.167, 0.002, 2, 7.2, -0.002, 2, 7.233, -0.005, 2, 7.267, -0.006, 2, 7.3, -0.005, 2, 7.333, -0.004, 2, 7.367, -0.001, 2, 7.4, 0.002, 2, 7.433, 0.006, 2, 7.467, 0.01, 2, 7.5, 0.014, 2, 7.533, 0.018, 2, 7.567, 0.021, 2, 7.6, 0.023, 2, 7.633, 0.025, 2, 7.667, 0.026, 2, 7.7, 0.026, 2, 7.733, 0.025, 2, 7.767, 0.024, 2, 7.8, 0.022, 2, 7.833, 0.021, 2, 7.867, 0.019, 2, 7.9, 0.018, 2, 7.933, 0.017, 2, 7.967, 0.016, 2, 8, 0.016]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh46", "Segments": [0, 0, 2, 0.1, -0.001, 2, 0.133, -0.002, 2, 0.167, -0.004, 2, 0.2, -0.007, 2, 0.233, -0.011, 2, 0.267, -0.015, 2, 0.3, -0.019, 2, 0.333, -0.023, 2, 0.367, -0.027, 2, 0.4, -0.031, 2, 0.433, -0.035, 2, 0.467, -0.039, 2, 0.5, -0.042, 2, 0.533, -0.044, 2, 0.567, -0.045, 2, 0.6, -0.046, 2, 0.633, -0.044, 2, 0.667, -0.039, 2, 0.7, -0.032, 2, 0.733, -0.022, 2, 0.767, -0.011, 2, 0.8, 0, 2, 0.833, 0.013, 2, 0.867, 0.024, 2, 0.9, 0.035, 2, 0.933, 0.045, 2, 0.967, 0.052, 2, 1, 0.057, 2, 1.033, 0.059, 2, 1.067, 0.057, 2, 1.1, 0.052, 2, 1.133, 0.044, 2, 1.167, 0.035, 2, 1.2, 0.024, 2, 1.233, 0.013, 2, 1.267, 0.002, 2, 1.3, -0.007, 2, 1.333, -0.015, 2, 1.367, -0.02, 2, 1.4, -0.022, 2, 1.433, -0.021, 2, 1.467, -0.018, 2, 1.5, -0.013, 2, 1.533, -0.006, 2, 1.567, 0.001, 2, 1.6, 0.008, 2, 1.633, 0.016, 2, 1.667, 0.023, 2, 1.7, 0.029, 2, 1.733, 0.034, 2, 1.767, 0.037, 2, 1.8, 0.038, 2, 1.833, 0.038, 2, 1.867, 0.036, 2, 1.9, 0.034, 2, 1.933, 0.031, 2, 1.967, 0.028, 2, 2, 0.024, 2, 2.033, 0.02, 2, 2.067, 0.017, 2, 2.1, 0.014, 2, 2.133, 0.012, 2, 2.167, 0.01, 2, 2.2, 0.009, 2, 2.233, 0.009, 2, 2.267, 0.01, 2, 2.3, 0.009, 2, 2.333, 0.007, 2, 2.367, 0.004, 2, 2.4, 0, 2, 2.433, -0.004, 2, 2.467, -0.009, 2, 2.5, -0.013, 2, 2.533, -0.017, 2, 2.567, -0.021, 2, 2.6, -0.024, 2, 2.633, -0.026, 2, 2.667, -0.027, 2, 2.7, -0.026, 2, 2.733, -0.022, 2, 2.767, -0.017, 2, 2.8, -0.011, 2, 2.833, -0.004, 2, 2.867, 0.003, 2, 2.9, 0.01, 2, 2.933, 0.017, 2, 2.967, 0.023, 2, 3, 0.028, 2, 3.033, 0.032, 2, 3.067, 0.033, 2, 3.1, 0.032, 2, 3.133, 0.029, 2, 3.167, 0.024, 2, 3.2, 0.019, 2, 3.233, 0.012, 2, 3.267, 0.006, 2, 3.3, -0.001, 2, 3.333, -0.007, 2, 3.367, -0.013, 2, 3.4, -0.018, 2, 3.433, -0.02, 2, 3.467, -0.021, 2, 3.5, -0.021, 2, 3.533, -0.019, 2, 3.567, -0.016, 2, 3.6, -0.012, 2, 3.633, -0.008, 2, 3.667, -0.003, 2, 3.7, 0.001, 2, 3.733, 0.004, 2, 3.767, 0.008, 2, 3.8, 0.01, 2, 3.833, 0.01, 2, 3.867, 0.01, 2, 3.9, 0.009, 2, 3.933, 0.008, 2, 3.967, 0.007, 2, 4, 0.005, 2, 4.033, 0.002, 2, 4.067, 0, 2, 4.1, -0.003, 2, 4.133, -0.006, 2, 4.167, -0.009, 2, 4.2, -0.012, 2, 4.233, -0.015, 2, 4.267, -0.018, 2, 4.3, -0.021, 2, 4.333, -0.024, 2, 4.367, -0.027, 2, 4.4, -0.03, 2, 4.433, -0.032, 2, 4.467, -0.035, 2, 4.5, -0.036, 2, 4.533, -0.038, 2, 4.567, -0.039, 2, 4.6, -0.04, 2, 4.633, -0.04, 2, 4.667, -0.039, 2, 4.7, -0.036, 2, 4.733, -0.03, 2, 4.767, -0.024, 2, 4.8, -0.017, 2, 4.833, -0.009, 2, 4.867, -0.001, 2, 4.9, 0.006, 2, 4.933, 0.013, 2, 4.967, 0.018, 2, 5, 0.021, 2, 5.033, 0.022, 2, 5.067, 0.021, 2, 5.1, 0.018, 2, 5.133, 0.014, 2, 5.167, 0.009, 2, 5.2, 0.002, 2, 5.233, -0.004, 2, 5.267, -0.011, 2, 5.3, -0.017, 2, 5.333, -0.022, 2, 5.367, -0.027, 2, 5.4, -0.03, 2, 5.433, -0.031, 2, 5.467, -0.03, 2, 5.5, -0.028, 2, 5.533, -0.024, 2, 5.567, -0.02, 2, 5.6, -0.015, 2, 5.633, -0.01, 2, 5.667, -0.005, 2, 5.7, -0.001, 2, 5.733, 0.003, 2, 5.767, 0.005, 2, 5.8, 0.006, 2, 5.833, 0.003, 2, 5.867, -0.004, 2, 5.9, -0.014, 2, 5.933, -0.027, 2, 5.967, -0.039, 2, 6, -0.052, 2, 6.033, -0.062, 2, 6.067, -0.069, 2, 6.1, -0.072, 2, 6.133, -0.067, 2, 6.167, -0.054, 2, 6.2, -0.035, 2, 6.233, -0.012, 2, 6.267, 0.012, 2, 6.3, 0.035, 2, 6.333, 0.054, 2, 6.367, 0.067, 2, 6.4, 0.072, 2, 6.433, 0.066, 2, 6.467, 0.049, 2, 6.5, 0.026, 2, 6.533, 0, 2, 6.567, -0.027, 2, 6.6, -0.05, 2, 6.633, -0.066, 2, 6.667, -0.073, 2, 6.7, -0.07, 2, 6.733, -0.061, 2, 6.767, -0.048, 2, 6.8, -0.032, 2, 6.833, -0.015, 2, 6.867, 0.003, 2, 6.9, 0.021, 2, 6.933, 0.037, 2, 6.967, 0.05, 2, 7, 0.058, 2, 7.033, 0.061, 2, 7.067, 0.059, 2, 7.1, 0.054, 2, 7.133, 0.047, 2, 7.167, 0.038, 2, 7.2, 0.027, 2, 7.233, 0.017, 2, 7.267, 0.006, 2, 7.3, -0.003, 2, 7.333, -0.011, 2, 7.367, -0.016, 2, 7.4, -0.018, 2, 7.433, -0.017, 2, 7.467, -0.014, 2, 7.5, -0.01, 2, 7.533, -0.004, 2, 7.567, 0.002, 2, 7.6, 0.008, 2, 7.633, 0.015, 2, 7.667, 0.021, 2, 7.7, 0.026, 2, 7.733, 0.03, 2, 7.767, 0.033, 2, 7.8, 0.034, 2, 7.833, 0.034, 2, 7.867, 0.033, 2, 7.9, 0.031, 2, 7.933, 0.029, 2, 7.967, 0.026, 2, 8, 0.023]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh46", "Segments": [0, 0, 2, 0.167, -0.001, 2, 0.2, -0.002, 2, 0.233, -0.005, 2, 0.267, -0.008, 2, 0.3, -0.011, 2, 0.333, -0.016, 2, 0.367, -0.02, 2, 0.4, -0.025, 2, 0.433, -0.03, 2, 0.467, -0.035, 2, 0.5, -0.04, 2, 0.533, -0.045, 2, 0.567, -0.049, 2, 0.6, -0.053, 2, 0.633, -0.056, 2, 0.667, -0.059, 2, 0.7, -0.06, 2, 0.733, -0.061, 2, 0.767, -0.058, 2, 0.8, -0.051, 2, 0.833, -0.04, 2, 0.867, -0.026, 2, 0.9, -0.011, 2, 0.933, 0.006, 2, 0.967, 0.024, 2, 1, 0.041, 2, 1.033, 0.057, 2, 1.067, 0.071, 2, 1.1, 0.081, 2, 1.133, 0.089, 2, 1.167, 0.091, 2, 1.2, 0.088, 2, 1.233, 0.079, 2, 1.267, 0.066, 2, 1.3, 0.05, 2, 1.333, 0.032, 2, 1.367, 0.014, 2, 1.4, -0.005, 2, 1.433, -0.021, 2, 1.467, -0.034, 2, 1.5, -0.042, 2, 1.533, -0.046, 2, 1.567, -0.044, 2, 1.6, -0.038, 2, 1.633, -0.029, 2, 1.667, -0.017, 2, 1.7, -0.004, 2, 1.733, 0.009, 2, 1.767, 0.023, 2, 1.8, 0.036, 2, 1.833, 0.047, 2, 1.867, 0.056, 2, 1.9, 0.062, 2, 1.933, 0.064, 2, 1.967, 0.063, 2, 2, 0.06, 2, 2.033, 0.056, 2, 2.067, 0.05, 2, 2.1, 0.043, 2, 2.133, 0.036, 2, 2.167, 0.028, 2, 2.2, 0.021, 2, 2.233, 0.015, 2, 2.267, 0.009, 2, 2.3, 0.004, 2, 2.333, 0.001, 2, 2.367, 0, 2, 2.4, 0, 2, 2.433, 0, 2, 2.467, -0.001, 2, 2.5, -0.003, 2, 2.533, -0.006, 2, 2.567, -0.01, 2, 2.6, -0.015, 2, 2.633, -0.019, 2, 2.667, -0.024, 2, 2.7, -0.028, 2, 2.733, -0.031, 2, 2.767, -0.033, 2, 2.8, -0.034, 2, 2.833, -0.033, 2, 2.867, -0.028, 2, 2.9, -0.021, 2, 2.933, -0.012, 2, 2.967, -0.003, 2, 3, 0.008, 2, 3.033, 0.019, 2, 3.067, 0.028, 2, 3.1, 0.037, 2, 3.133, 0.044, 2, 3.167, 0.049, 2, 3.2, 0.05, 2, 3.233, 0.049, 2, 3.267, 0.044, 2, 3.3, 0.036, 2, 3.333, 0.027, 2, 3.367, 0.017, 2, 3.4, 0.006, 2, 3.433, -0.006, 2, 3.467, -0.016, 2, 3.5, -0.025, 2, 3.533, -0.033, 2, 3.567, -0.037, 2, 3.6, -0.039, 2, 3.633, -0.038, 2, 3.667, -0.034, 2, 3.7, -0.028, 2, 3.733, -0.021, 2, 3.767, -0.013, 2, 3.8, -0.005, 2, 3.833, 0.003, 2, 3.867, 0.011, 2, 3.9, 0.016, 2, 3.933, 0.02, 2, 3.967, 0.022, 2, 4, 0.021, 2, 4.033, 0.02, 2, 4.067, 0.018, 2, 4.1, 0.016, 2, 4.133, 0.013, 2, 4.167, 0.01, 2, 4.2, 0.006, 2, 4.233, 0.002, 2, 4.267, -0.003, 2, 4.3, -0.007, 2, 4.333, -0.012, 2, 4.367, -0.017, 2, 4.4, -0.021, 2, 4.433, -0.026, 2, 4.467, -0.03, 2, 4.5, -0.034, 2, 4.533, -0.038, 2, 4.567, -0.042, 2, 4.6, -0.045, 2, 4.633, -0.047, 2, 4.667, -0.049, 2, 4.7, -0.05, 2, 4.733, -0.051, 2, 4.8, -0.049, 2, 4.833, -0.044, 2, 4.867, -0.037, 2, 4.9, -0.028, 2, 4.933, -0.017, 2, 4.967, -0.006, 2, 5, 0.005, 2, 5.033, 0.015, 2, 5.067, 0.024, 2, 5.1, 0.031, 2, 5.133, 0.036, 2, 5.167, 0.038, 2, 5.2, 0.036, 2, 5.233, 0.031, 2, 5.267, 0.024, 2, 5.3, 0.015, 2, 5.333, 0.005, 2, 5.367, -0.006, 2, 5.4, -0.017, 2, 5.433, -0.027, 2, 5.467, -0.036, 2, 5.5, -0.044, 2, 5.533, -0.048, 2, 5.567, -0.05, 2, 5.6, -0.047, 2, 5.633, -0.04, 2, 5.667, -0.033, 2, 5.7, -0.03, 2, 5.733, -0.032, 2, 5.767, -0.035, 2, 5.8, -0.032, 2, 5.833, -0.026, 2, 5.867, -0.017, 2, 5.9, -0.006, 2, 5.933, 0.005, 2, 5.967, 0.016, 2, 6, 0.025, 2, 6.033, 0.031, 2, 6.067, 0.034, 2, 6.1, 0.029, 2, 6.133, 0.016, 2, 6.167, -0.002, 2, 6.2, -0.023, 2, 6.233, -0.044, 2, 6.267, -0.062, 2, 6.3, -0.075, 2, 6.333, -0.08, 2, 6.367, -0.072, 2, 6.4, -0.052, 2, 6.433, -0.024, 2, 6.467, 0.006, 2, 6.5, 0.035, 2, 6.533, 0.055, 2, 6.567, 0.063, 2, 6.6, 0.059, 2, 6.633, 0.05, 2, 6.667, 0.037, 2, 6.7, 0.021, 2, 6.733, 0.004, 2, 6.767, -0.011, 2, 6.8, -0.025, 2, 6.833, -0.034, 2, 6.867, -0.037, 2, 6.9, -0.034, 2, 6.933, -0.025, 2, 6.967, -0.011, 2, 7, 0.005, 2, 7.033, 0.023, 2, 7.067, 0.041, 2, 7.1, 0.057, 2, 7.133, 0.071, 2, 7.167, 0.08, 2, 7.2, 0.084, 2, 7.233, 0.081, 2, 7.267, 0.073, 2, 7.3, 0.062, 2, 7.333, 0.048, 2, 7.367, 0.032, 2, 7.4, 0.015, 2, 7.433, -0.001, 2, 7.467, -0.015, 2, 7.5, -0.026, 2, 7.533, -0.034, 2, 7.567, -0.037, 2, 7.6, -0.035, 2, 7.633, -0.029, 2, 7.667, -0.02, 2, 7.7, -0.009, 2, 7.733, 0.004, 2, 7.767, 0.016, 2, 7.8, 0.029, 2, 7.833, 0.04, 2, 7.867, 0.049, 2, 7.9, 0.055, 2, 7.933, 0.057, 2, 7.967, 0.056, 2, 8, 0.055]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh0_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh1_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh2_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh3_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh46_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 8, 1]}]}