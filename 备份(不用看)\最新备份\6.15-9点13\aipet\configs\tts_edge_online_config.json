{"service_type": "edge_tts_python_lib", "default_speaker": "zh-CN-XiaoxiaoNeural", "speakers": ["zh-CN-XiaoxiaoNeural", "zh-CN-XiaoyiNeural", "zh-CN-YunxiNeural", "zh-CN-YunyangNeural", "zh-CN-liaoning-XiaobeiNeural"], "default_rate": "+0%", "default_pitch": "+0Hz", "default_volume": "+0%", "audio_format": "mp3", "available_voices_command_hint": "edge-tts --list-voices", "notes": "参数格式 (rate, pitch, volume) 遵循 edge-tts 命令行的百分比/Hz表示法。", "request_timeout": 25, "retry_settings": {"max_retries": 2, "backoff_factor": 1.5, "retry_status_codes": [500, 503, 429]}, "current_speaker": "zh-CN-XiaoyiNeural"}