{"Version": 3, "Meta": {"PhysicsSettingCount": 70, "TotalInputCount": 151, "TotalOutputCount": 351, "VertexCount": 536, "Fps": 60, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "眼珠X"}, {"Id": "PhysicsSetting2", "Name": "眼珠Y"}, {"Id": "PhysicsSetting3", "Name": "左眼"}, {"Id": "PhysicsSetting4", "Name": "右眼"}, {"Id": "PhysicsSetting5", "Name": "睫毛L"}, {"Id": "PhysicsSetting6", "Name": "睫毛R"}, {"Id": "PhysicsSetting7", "Name": "眼眶L"}, {"Id": "PhysicsSetting8", "Name": "眼眶R"}, {"Id": "PhysicsSetting9", "Name": "笑"}, {"Id": "PhysicsSetting10", "Name": "嘴x"}, {"Id": "PhysicsSetting11", "Name": "嘴y"}, {"Id": "PhysicsSetting12", "Name": "耳朵L"}, {"Id": "PhysicsSetting13", "Name": "耳朵R"}, {"Id": "PhysicsSetting14", "Name": "耳朵Z"}, {"Id": "PhysicsSetting15", "Name": "左配饰"}, {"Id": "PhysicsSetting16", "Name": "右配饰"}, {"Id": "PhysicsSetting17", "Name": "右侧发"}, {"Id": "PhysicsSetting18", "Name": "左侧发"}, {"Id": "PhysicsSetting19", "Name": "后发1"}, {"Id": "PhysicsSetting20", "Name": "后发2"}, {"Id": "PhysicsSetting21", "Name": "后发3"}, {"Id": "PhysicsSetting22", "Name": "后发4"}, {"Id": "PhysicsSetting23", "Name": "后发5"}, {"Id": "PhysicsSetting24", "Name": "后发6"}, {"Id": "PhysicsSetting25", "Name": "后发7"}, {"Id": "PhysicsSetting26", "Name": "后发8"}, {"Id": "PhysicsSetting27", "Name": "后发9"}, {"Id": "PhysicsSetting28", "Name": "后发10"}, {"Id": "PhysicsSetting29", "Name": "后发11"}, {"Id": "PhysicsSetting30", "Name": "右侧发2"}, {"Id": "PhysicsSetting31", "Name": "右侧发3"}, {"Id": "PhysicsSetting32", "Name": "右侧发4"}, {"Id": "PhysicsSetting33", "Name": "右侧发5"}, {"Id": "PhysicsSetting34", "Name": "左侧发2"}, {"Id": "PhysicsSetting35", "Name": "左侧发3"}, {"Id": "PhysicsSetting36", "Name": "左侧发4"}, {"Id": "PhysicsSetting37", "Name": "左侧发5"}, {"Id": "PhysicsSetting38", "Name": "右马尾1"}, {"Id": "PhysicsSetting39", "Name": "右马尾2"}, {"Id": "PhysicsSetting40", "Name": "右马尾3"}, {"Id": "PhysicsSetting41", "Name": "左马尾"}, {"Id": "PhysicsSetting42", "Name": "左马尾2"}, {"Id": "PhysicsSetting43", "Name": "左马尾3"}, {"Id": "PhysicsSetting44", "Name": "Z"}, {"Id": "PhysicsSetting45", "Name": "X"}, {"Id": "PhysicsSetting46", "Name": "Y"}, {"Id": "PhysicsSetting47", "Name": "中配饰"}, {"Id": "PhysicsSetting48", "Name": "中配饰Y"}, {"Id": "PhysicsSetting49", "Name": "opx"}, {"Id": "PhysicsSetting50", "Name": "opy"}, {"Id": "PhysicsSetting51", "Name": "裙子"}, {"Id": "PhysicsSetting52", "Name": "裙子Y"}, {"Id": "PhysicsSetting53", "Name": "链子"}, {"Id": "PhysicsSetting54", "Name": "左手"}, {"Id": "PhysicsSetting55", "Name": "右手"}, {"Id": "PhysicsSetting56", "Name": "尾巴"}, {"Id": "PhysicsSetting57", "Name": "链子2"}, {"Id": "PhysicsSetting58", "Name": "镜子"}, {"Id": "PhysicsSetting59", "Name": "尾巴2"}, {"Id": "PhysicsSetting60", "Name": "链子3"}, {"Id": "PhysicsSetting61", "Name": "翅膀"}, {"Id": "PhysicsSetting62", "Name": "笔记本R"}, {"Id": "PhysicsSetting63", "Name": "笔记本L"}, {"Id": "PhysicsSetting64", "Name": "抱狐狸"}, {"Id": "PhysicsSetting65", "Name": "尾巴3"}, {"Id": "PhysicsSetting66", "Name": "链子4"}, {"Id": "PhysicsSetting67", "Name": "扇子"}, {"Id": "PhysicsSetting68", "Name": "话筒"}, {"Id": "PhysicsSetting69", "Name": "比心R"}, {"Id": "PhysicsSetting70", "Name": "比心L"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "PhyEyeBallLX"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.77, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "PhyEyeBallLY"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.77, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param8"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param9"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param47"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param48"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param49"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.84, "Delay": 0.95, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.75, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param60"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param50"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param51"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.84, "Delay": 0.95, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.75, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param43"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param44"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param45"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param46"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param41"}, "VertexIndex": 1, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.82, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.82, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.82, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param39"}, "VertexIndex": 1, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.75, "Acceleration": 0.75, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.8, "Delay": 0.75, "Acceleration": 0.75, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.8, "Delay": 0.75, "Acceleration": 0.75, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param40"}, "VertexIndex": 1, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 1.5, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.5, "Delay": 1.5, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.5, "Delay": 1.5, "Acceleration": 1.5, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param10"}, "VertexIndex": 1, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param11"}, "VertexIndex": 2, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param61"}, "VertexIndex": 3, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.6, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.6, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.6, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param12"}, "VertexIndex": 1, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param13"}, "VertexIndex": 2, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param62"}, "VertexIndex": 3, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.6, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.6, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.6, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 70, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param63"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.95, "Delay": 0.86, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param20"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param21"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param22"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param23"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param3"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param4"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param72"}, "VertexIndex": 3, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 58}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 68}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 78}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 88}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairSide"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param2"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param71"}, "VertexIndex": 3, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 58}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 68}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 78}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 88}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1015"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1015"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1015"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1015"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1015"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1015"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1015"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1015"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1016"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1016"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1016"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1016"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1016"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1016"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1016"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1016"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1016"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1017"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1017"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1017"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1017"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1017"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1017"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1017"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1017"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1017"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1018"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1018"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1018"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1018"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1018"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1018"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1018"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1018"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1019"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1019"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1019"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1019"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1019"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1019"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1019"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1019"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1019"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1020"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1020"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1020"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1020"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1020"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1020"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1020"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1020"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1020"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1021"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1021"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1021"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1021"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1021"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1021"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1021"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1021"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1021"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1022"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1022"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1022"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1022"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1022"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1022"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1022"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1022"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1022"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1023"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1023"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1023"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1023"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1023"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1023"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1023"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1023"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1023"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1024"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1024"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1024"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1024"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1024"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1024"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1024"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1024"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1024"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1025"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1025"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1025"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1025"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1025"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1025"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1025"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1025"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1025"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh440"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh440"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh440"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh440"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh440"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh440"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh440"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh440"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh440"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh441"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh441"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh441"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh441"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh441"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh441"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh441"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh441"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh441"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh445"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh445"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh445"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh445"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh445"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh445"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh445"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh445"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh445"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh447"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh447"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh447"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh447"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh447"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh447"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh447"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh447"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh447"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting34", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh448"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh448"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh448"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh448"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh448"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh448"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh448"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh448"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh448"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting35", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh449"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh449"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh449"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh449"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh449"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh449"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh449"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh449"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh449"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting36", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh453"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh453"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh453"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh453"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh453"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh453"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh453"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh453"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh453"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting37", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh455"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh455"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh455"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh455"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh455"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh455"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh455"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh455"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh455"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting38", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh991"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh991"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh991"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh991"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh991"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh991"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh991"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh991"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting39", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh990"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh990"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh990"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh990"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh990"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh990"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh990"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh990"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh990"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting40", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh989"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh989"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh989"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh989"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh989"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh989"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh989"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh989"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh989"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting41", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh999"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh999"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh999"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh999"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh999"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh999"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh999"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh999"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting42", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh998"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh998"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh998"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh998"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh998"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh998"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh998"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh998"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh998"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting43", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh997"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh997"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh997"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh997"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh997"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh997"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh997"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh997"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh997"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting44", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12.5}, "Mobility": 0.9, "Delay": 0.4, "Acceleration": 0.1, "Radius": 12.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting45", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12.5}, "Mobility": 0.9, "Delay": 0.4, "Acceleration": 0.1, "Radius": 12.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting46", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12.5}, "Mobility": 0.9, "Delay": 0.4, "Acceleration": 0.1, "Radius": 12.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting47", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param35"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param36"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting48", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param37"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param38"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting49", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param24"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param25"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting50", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param26"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param27"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 0.9, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting51", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param14"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param15"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param16"}, "VertexIndex": 3, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting52", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param17"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param18"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param19"}, "VertexIndex": 3, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 32}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 45}, "Mobility": 1, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting53", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh973"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh973"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh973"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh973"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh973"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh973"}, "VertexIndex": 6, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh973"}, "VertexIndex": 7, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh973"}, "VertexIndex": 8, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh973"}, "VertexIndex": 9, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 52}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting54", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation18"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation19"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation20"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting55", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation22"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation23"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation24"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting56", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1033"}, "VertexIndex": 1, "Scale": 200, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1033"}, "VertexIndex": 2, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1033"}, "VertexIndex": 3, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1033"}, "VertexIndex": 4, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1033"}, "VertexIndex": 5, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh1033"}, "VertexIndex": 6, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh1033"}, "VertexIndex": 7, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1033"}, "VertexIndex": 8, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh1033"}, "VertexIndex": 9, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param91"}, "VertexIndex": 1, "Scale": 200, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 1, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.8, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.75, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.7, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.65, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.6, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting57", "Input": [{"Source": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh1033"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param92"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param93"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param94"}, "VertexIndex": 3, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 58}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 68}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 78}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 88}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting58", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation25"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation26"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting59", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh113"}, "VertexIndex": 1, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh113"}, "VertexIndex": 2, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh113"}, "VertexIndex": 3, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh113"}, "VertexIndex": 4, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh113"}, "VertexIndex": 5, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh113"}, "VertexIndex": 6, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh113"}, "VertexIndex": 7, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 1, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.8, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.75, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.7, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.65, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.6, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting60", "Input": [{"Source": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh113"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh107"}, "VertexIndex": 1, "Scale": 250, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh107"}, "VertexIndex": 2, "Scale": 250, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh107"}, "VertexIndex": 3, "Scale": 250, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh107"}, "VertexIndex": 4, "Scale": 250, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh107"}, "VertexIndex": 5, "Scale": 250, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh107"}, "VertexIndex": 6, "Scale": 250, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh107"}, "VertexIndex": 7, "Scale": 250, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 58}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 68}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 78}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 88}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting61", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh85"}, "VertexIndex": 1, "Scale": 200, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh85"}, "VertexIndex": 2, "Scale": 200, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh85"}, "VertexIndex": 3, "Scale": 200, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh85"}, "VertexIndex": 4, "Scale": 200, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh85"}, "VertexIndex": 5, "Scale": 200, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 1, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.8, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.75, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.7, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.65, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.6, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting62", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation28"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation29"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting63", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation30"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation31"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting64", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation32"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation33"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting65", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh230"}, "VertexIndex": 1, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh230"}, "VertexIndex": 2, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh230"}, "VertexIndex": 3, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh230"}, "VertexIndex": 4, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh230"}, "VertexIndex": 5, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh230"}, "VertexIndex": 6, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh230"}, "VertexIndex": 7, "Scale": 100, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 1, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.8, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.75, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.7, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.65, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.6, "Delay": 1.2, "Acceleration": 1.4, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting66", "Input": [{"Source": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh230"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh224"}, "VertexIndex": 1, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh224"}, "VertexIndex": 2, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh224"}, "VertexIndex": 3, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh224"}, "VertexIndex": 4, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh224"}, "VertexIndex": 5, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh224"}, "VertexIndex": 6, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh224"}, "VertexIndex": 7, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 58}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 68}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 78}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 88}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting67", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation34"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation35"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting68", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation36"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation37"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting69", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation38"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation39"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting70", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation40"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation41"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 2}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}