import os
import re
import logging

logger = logging.getLogger(__name__)

def load_companion_config():
    """加载游戏伴侣配置"""
    config = {
        'interval_seconds': 70,  # 默认值
        'prompt': '[智能画面伴侣] 请观察屏幕内容并给出简洁的分析和建议。'  # 默认简短提示词
    }
    
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'companion_config.env')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析间隔时间
            interval_match = re.search(r'COMPANION_INTERVAL_SECONDS=(\d+)', content)
            if interval_match:
                config['interval_seconds'] = int(interval_match.group(1))
            
            # 解析提示词（提取三重引号之间的内容）
            prompt_match = re.search(r'COMPANION_PROMPT="""(.*?)"""', content, re.DOTALL)
            if prompt_match:
                config['prompt'] = prompt_match.group(1).strip()
                
        logger.info(f"游戏伴侣配置加载: 间隔{config['interval_seconds']}秒")
    except Exception as e:
        logger.warning(f"加载游戏伴侣配置失败，使用默认设置: {e}")
    
    return config 