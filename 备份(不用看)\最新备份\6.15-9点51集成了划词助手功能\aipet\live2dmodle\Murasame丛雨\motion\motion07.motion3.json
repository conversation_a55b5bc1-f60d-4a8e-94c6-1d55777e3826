{"Version": 3, "Meta": {"Duration": 8, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 92, "TotalSegmentCount": 8169, "TotalPointCount": 8261, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 8, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.033, 0.129, 2, 0.067, 0.469, 2, 0.1, 0.949, 2, 0.133, 1.5, 2, 0.167, 2.051, 2, 0.2, 2.531, 2, 0.233, 2.871, 2, 0.267, 3, 2, 0.3, 2.818, 2, 0.333, 2.295, 2, 0.367, 1.453, 2, 0.4, 0.355, 2, 0.433, -1.013, 2, 0.467, -2.561, 2, 0.5, -4.324, 2, 0.533, -6.192, 2, 0.567, -8.218, 2, 0.6, -10.28, 2, 0.633, -12.437, 2, 0.667, -14.563, 2, 0.7, -16.72, 2, 0.733, -18.782, 2, 0.767, -20.808, 2, 0.8, -22.676, 2, 0.833, -24.439, 2, 0.867, -25.987, 2, 0.9, -27.355, 2, 0.933, -28.453, 2, 0.967, -29.295, 2, 1, -29.818, 2, 1.033, -30, 2, 8, -30]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.033, 0.301, 2, 0.067, 1.094, 2, 0.1, 2.215, 2, 0.133, 3.5, 2, 0.167, 4.785, 2, 0.2, 5.906, 2, 0.233, 6.699, 2, 0.267, 7, 2, 0.3, 6.796, 2, 0.333, 6.21, 2, 0.367, 5.265, 2, 0.4, 4.035, 2, 0.433, 2.5, 2, 0.467, 0.765, 2, 0.5, -1.211, 2, 0.533, -3.307, 2, 0.567, -5.578, 2, 0.6, -7.889, 2, 0.633, -10.308, 2, 0.667, -12.692, 2, 0.7, -15.111, 2, 0.733, -17.422, 2, 0.767, -19.693, 2, 0.8, -21.789, 2, 0.833, -23.765, 2, 0.867, -25.5, 2, 0.9, -27.035, 2, 0.933, -28.265, 2, 0.967, -29.21, 2, 1, -29.796, 2, 1.033, -30, 2, 8, -30]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.033, -0.215, 2, 0.067, -0.781, 2, 0.1, -1.582, 2, 0.133, -2.5, 2, 0.167, -3.418, 2, 0.2, -4.219, 2, 0.233, -4.785, 2, 0.267, -5, 2, 0.3, -4.911, 2, 0.333, -4.651, 2, 0.367, -4.223, 2, 0.4, -3.651, 2, 0.433, -2.92, 2, 0.467, -2.07, 2, 0.5, -1.072, 2, 0.533, 0.022, 2, 0.567, 1.253, 2, 0.6, 2.556, 2, 0.633, 3.984, 2, 0.667, 5.463, 2, 0.7, 7.052, 2, 0.733, 8.671, 2, 0.767, 10.386, 2, 0.8, 12.112, 2, 0.833, 13.918, 2, 0.867, 15.716, 2, 0.9, 17.578, 2, 0.933, 19.413, 2, 0.967, 21.295, 2, 1, 23.153, 2, 1.033, 25, 2, 1.067, 26.491, 2, 1.1, 27.651, 2, 1.133, 28.523, 2, 1.167, 29.147, 2, 1.2, 29.558, 2, 1.233, 29.814, 2, 1.267, 29.945, 2, 1.3, 29.993, 2, 1.333, 30, 2, 1.367, 29.944, 2, 1.4, 29.785, 2, 1.433, 29.539, 2, 1.467, 29.219, 2, 1.5, 28.84, 2, 1.533, 28.418, 2, 1.567, 27.966, 2, 1.6, 27.5, 2, 1.633, 27.034, 2, 1.667, 26.582, 2, 1.7, 26.16, 2, 1.733, 25.781, 2, 1.767, 25.461, 2, 1.8, 25.215, 2, 1.833, 25.056, 2, 1.867, 25, 2, 2.7, 25.172, 2, 2.733, 25.633, 2, 2.767, 26.301, 2, 2.8, 27.091, 2, 2.833, 27.909, 2, 2.867, 28.699, 2, 2.9, 29.367, 2, 2.933, 29.828, 2, 2.967, 30, 2, 3, 29.751, 2, 3.033, 29.033, 2, 3.067, 27.872, 2, 3.1, 26.368, 2, 3.133, 24.484, 2, 3.167, 22.333, 2, 3.2, 19.936, 2, 3.233, 17.339, 2, 3.267, 14.586, 2, 3.3, 11.682, 2, 3.333, 8.79, 2, 3.367, 5.795, 2, 3.4, 2.864, 2, 3.433, 0, 2, 3.467, -2.344, 2, 3.5, -3.795, 2, 3.533, -4.616, 2, 3.567, -4.959, 2, 3.6, -5.021, 2, 3.633, -5, 2, 3.667, -4.917, 2, 3.7, -4.68, 2, 3.733, -4.326, 2, 3.767, -3.872, 2, 3.8, -3.349, 2, 3.833, -2.792, 2, 3.867, -2.208, 2, 3.9, -1.651, 2, 3.933, -1.128, 2, 3.967, -0.674, 2, 4, -0.32, 2, 4.033, -0.083, 2, 4.067, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.3, 0.945, 2, 0.333, 0.802, 2, 0.367, 0.605, 2, 0.4, 0.395, 2, 0.433, 0.198, 2, 0.467, 0.055, 2, 0.5, 0, 2, 1.067, 0.055, 2, 1.1, 0.198, 2, 1.133, 0.395, 2, 1.167, 0.605, 2, 1.2, 0.802, 2, 1.233, 0.945, 2, 1.267, 1, 2, 2.8, 0.844, 2, 2.833, 0.5, 2, 2.867, 0.156, 2, 2.9, 0, 2, 3.3, 0.156, 2, 3.333, 0.5, 2, 3.367, 0.844, 2, 3.4, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.3, 0.945, 2, 0.333, 0.802, 2, 0.367, 0.605, 2, 0.4, 0.395, 2, 0.433, 0.198, 2, 0.467, 0.055, 2, 0.5, 0, 2, 1.067, 0.055, 2, 1.1, 0.198, 2, 1.133, 0.395, 2, 1.167, 0.605, 2, 1.2, 0.802, 2, 1.233, 0.945, 2, 1.267, 1, 2, 2.8, 0.844, 2, 2.833, 0.5, 2, 2.867, 0.156, 2, 2.9, 0, 2, 3.3, 0.156, 2, 3.333, 0.5, 2, 3.367, 0.844, 2, 3.4, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.01, 2, 0.1, 0.022, 2, 0.133, 0.035, 2, 0.167, 0.05, 2, 0.2, 0.065, 2, 0.233, 0.078, 2, 0.267, 0.09, 2, 0.3, 0.097, 2, 0.333, 0.1, 2, 0.367, 0.09, 2, 0.4, 0.062, 2, 0.433, 0.018, 2, 0.467, -0.039, 2, 0.5, -0.107, 2, 0.533, -0.185, 2, 0.567, -0.269, 2, 0.6, -0.359, 2, 0.633, -0.45, 2, 0.667, -0.541, 2, 0.7, -0.631, 2, 0.733, -0.715, 2, 0.767, -0.793, 2, 0.8, -0.861, 2, 0.833, -0.918, 2, 0.867, -0.962, 2, 0.9, -0.99, 2, 0.933, -1, 2, 2.167, -0.964, 2, 2.2, -0.87, 2, 2.233, -0.725, 2, 2.267, -0.55, 2, 2.3, -0.351, 2, 2.333, -0.149, 2, 2.367, 0.05, 2, 2.4, 0.225, 2, 2.433, 0.37, 2, 2.467, 0.464, 2, 2.5, 0.5, 2, 8, 0.5]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.033, 0.008, 2, 0.067, 0.031, 2, 0.1, 0.065, 2, 0.133, 0.106, 2, 0.167, 0.15, 2, 0.2, 0.194, 2, 0.233, 0.235, 2, 0.267, 0.269, 2, 0.3, 0.292, 2, 0.333, 0.3, 2, 0.367, 0.288, 2, 0.4, 0.255, 2, 0.433, 0.203, 2, 0.467, 0.135, 2, 0.5, 0.055, 2, 0.533, -0.036, 2, 0.567, -0.136, 2, 0.6, -0.242, 2, 0.633, -0.35, 2, 0.667, -0.458, 2, 0.7, -0.564, 2, 0.733, -0.664, 2, 0.767, -0.755, 2, 0.8, -0.835, 2, 0.833, -0.903, 2, 0.867, -0.955, 2, 0.9, -0.988, 2, 0.933, -1, 2, 2.167, -0.974, 2, 2.2, -0.904, 2, 2.233, -0.798, 2, 2.267, -0.67, 2, 2.3, -0.524, 2, 2.333, -0.376, 2, 2.367, -0.23, 2, 2.4, -0.102, 2, 2.433, 0.004, 2, 2.467, 0.074, 2, 2.5, 0.1, 2, 8, 0.1]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangL", "Segments": [0, 0, 2, 0.3, 0.102, 2, 0.333, 0.327, 2, 0.367, 0.552, 2, 0.4, 0.655, 2, 0.433, 0.519, 2, 0.467, 0.195, 2, 0.5, -0.2, 2, 0.533, -0.524, 2, 0.567, -0.66, 2, 0.6, -0.595, 2, 0.633, -0.436, 2, 0.667, -0.224, 2, 0.7, -0.013, 2, 0.733, 0.146, 2, 0.767, 0.212, 2, 0.8, 0.195, 2, 0.833, 0.153, 2, 0.867, 0.094, 2, 0.9, 0.031, 2, 0.933, -0.028, 2, 0.967, -0.071, 2, 1, -0.087, 2, 1.033, -0.067, 2, 1.067, -0.156, 2, 1.1, -0.351, 2, 1.133, -0.547, 2, 1.167, -0.636, 2, 1.2, -0.502, 2, 1.233, -0.181, 2, 1.267, 0.21, 2, 1.3, 0.531, 2, 1.333, 0.665, 2, 1.367, 0.599, 2, 1.4, 0.439, 2, 1.433, 0.226, 2, 1.467, 0.013, 2, 1.5, -0.147, 2, 1.533, -0.213, 2, 1.567, -0.197, 2, 1.6, -0.154, 2, 1.633, -0.094, 2, 1.667, -0.032, 2, 1.7, 0.028, 2, 1.733, 0.071, 2, 1.767, 0.087, 2, 1.8, 0.078, 2, 1.833, 0.055, 2, 1.867, 0.025, 2, 1.9, -0.006, 2, 1.933, -0.028, 2, 1.967, -0.038, 2, 2, -0.034, 2, 2.033, -0.024, 2, 2.067, -0.011, 2, 2.1, 0.002, 2, 2.133, 0.012, 2, 2.167, 0.016, 2, 2.2, 0.015, 2, 2.233, 0.01, 2, 2.267, 0.005, 2, 2.3, -0.001, 2, 2.333, -0.005, 2, 2.367, -0.007, 2, 2.4, -0.006, 2, 2.433, -0.004, 2, 2.467, -0.002, 2, 2.5, 0, 2, 2.533, 0.002, 2, 2.567, 0.003, 2, 2.6, 0.003, 2, 2.633, 0.002, 2, 2.667, 0.001, 2, 2.7, 0, 2, 2.733, -0.001, 2, 2.767, -0.001, 2, 2.8, 0.221, 2, 2.833, 0.622, 2, 2.867, 0.844, 2, 2.9, 0.437, 2, 2.933, -0.299, 2, 2.967, -0.706, 2, 3, -0.637, 2, 3.033, -0.468, 2, 3.067, -0.243, 2, 3.1, -0.019, 2, 3.133, 0.15, 2, 3.167, 0.22, 2, 3.2, 0.139, 2, 3.233, -0.057, 2, 3.267, -0.318, 2, 3.3, -0.578, 2, 3.333, -0.774, 2, 3.367, -0.855, 2, 3.4, -0.445, 2, 3.433, 0.295, 2, 3.467, 0.705, 2, 3.5, 0.635, 2, 3.533, 0.466, 2, 3.567, 0.242, 2, 3.6, 0.017, 2, 3.633, -0.152, 2, 3.667, -0.221, 2, 3.7, -0.198, 2, 3.733, -0.141, 2, 3.767, -0.066, 2, 3.8, 0.01, 2, 3.833, 0.067, 2, 3.867, 0.09, 2, 3.9, 0.083, 2, 3.933, 0.065, 2, 3.967, 0.039, 2, 4, 0.012, 2, 4.033, -0.013, 2, 4.067, -0.032, 2, 4.1, -0.039, 2, 4.133, -0.034, 2, 4.167, -0.024, 2, 4.2, -0.011, 2, 4.233, 0.002, 2, 4.267, 0.013, 2, 4.3, 0.017, 2, 4.333, 0.015, 2, 4.367, 0.011, 2, 4.4, 0.005, 2, 4.433, -0.001, 2, 4.467, -0.005, 2, 4.5, -0.007, 2, 4.533, -0.006, 2, 4.567, -0.005, 2, 4.6, -0.002, 2, 4.633, 0, 2, 4.667, 0.002, 2, 4.7, 0.003, 2, 4.733, 0.003, 2, 4.767, 0.002, 2, 4.8, 0.001, 2, 4.833, 0, 2, 4.867, -0.001, 2, 4.9, -0.001, 2, 4.933, -0.001, 2, 4.967, -0.001, 2, 5, 0, 2, 5.033, 0, 2, 5.067, 0, 2, 5.1, 0.001, 2, 5.133, 0.001, 2, 5.167, 0, 2, 5.2, 0, 2, 5.233, 0, 2, 5.267, 0, 2, 5.4, 0, 2, 5.433, 0, 2, 5.467, 0, 2, 5.6, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangR", "Segments": [0, 0, 2, 0.3, 0.102, 2, 0.333, 0.327, 2, 0.367, 0.552, 2, 0.4, 0.655, 2, 0.433, 0.519, 2, 0.467, 0.195, 2, 0.5, -0.2, 2, 0.533, -0.524, 2, 0.567, -0.66, 2, 0.6, -0.595, 2, 0.633, -0.436, 2, 0.667, -0.224, 2, 0.7, -0.013, 2, 0.733, 0.146, 2, 0.767, 0.212, 2, 0.8, 0.195, 2, 0.833, 0.153, 2, 0.867, 0.094, 2, 0.9, 0.031, 2, 0.933, -0.028, 2, 0.967, -0.071, 2, 1, -0.087, 2, 1.033, -0.067, 2, 1.067, -0.156, 2, 1.1, -0.351, 2, 1.133, -0.547, 2, 1.167, -0.636, 2, 1.2, -0.502, 2, 1.233, -0.181, 2, 1.267, 0.21, 2, 1.3, 0.531, 2, 1.333, 0.665, 2, 1.367, 0.599, 2, 1.4, 0.439, 2, 1.433, 0.226, 2, 1.467, 0.013, 2, 1.5, -0.147, 2, 1.533, -0.213, 2, 1.567, -0.197, 2, 1.6, -0.154, 2, 1.633, -0.094, 2, 1.667, -0.032, 2, 1.7, 0.028, 2, 1.733, 0.071, 2, 1.767, 0.087, 2, 1.8, 0.078, 2, 1.833, 0.055, 2, 1.867, 0.025, 2, 1.9, -0.006, 2, 1.933, -0.028, 2, 1.967, -0.038, 2, 2, -0.034, 2, 2.033, -0.024, 2, 2.067, -0.011, 2, 2.1, 0.002, 2, 2.133, 0.012, 2, 2.167, 0.016, 2, 2.2, 0.015, 2, 2.233, 0.01, 2, 2.267, 0.005, 2, 2.3, -0.001, 2, 2.333, -0.005, 2, 2.367, -0.007, 2, 2.4, -0.006, 2, 2.433, -0.004, 2, 2.467, -0.002, 2, 2.5, 0, 2, 2.533, 0.002, 2, 2.567, 0.003, 2, 2.6, 0.003, 2, 2.633, 0.002, 2, 2.667, 0.001, 2, 2.7, 0, 2, 2.733, -0.001, 2, 2.767, -0.001, 2, 2.8, 0.221, 2, 2.833, 0.622, 2, 2.867, 0.844, 2, 2.9, 0.437, 2, 2.933, -0.299, 2, 2.967, -0.706, 2, 3, -0.637, 2, 3.033, -0.468, 2, 3.067, -0.243, 2, 3.1, -0.019, 2, 3.133, 0.15, 2, 3.167, 0.22, 2, 3.2, 0.139, 2, 3.233, -0.057, 2, 3.267, -0.318, 2, 3.3, -0.578, 2, 3.333, -0.774, 2, 3.367, -0.855, 2, 3.4, -0.445, 2, 3.433, 0.295, 2, 3.467, 0.705, 2, 3.5, 0.635, 2, 3.533, 0.466, 2, 3.567, 0.242, 2, 3.6, 0.017, 2, 3.633, -0.152, 2, 3.667, -0.221, 2, 3.7, -0.198, 2, 3.733, -0.141, 2, 3.767, -0.066, 2, 3.8, 0.01, 2, 3.833, 0.067, 2, 3.867, 0.09, 2, 3.9, 0.083, 2, 3.933, 0.065, 2, 3.967, 0.039, 2, 4, 0.012, 2, 4.033, -0.013, 2, 4.067, -0.032, 2, 4.1, -0.039, 2, 4.133, -0.034, 2, 4.167, -0.024, 2, 4.2, -0.011, 2, 4.233, 0.002, 2, 4.267, 0.013, 2, 4.3, 0.017, 2, 4.333, 0.015, 2, 4.367, 0.011, 2, 4.4, 0.005, 2, 4.433, -0.001, 2, 4.467, -0.005, 2, 4.5, -0.007, 2, 4.533, -0.006, 2, 4.567, -0.005, 2, 4.6, -0.002, 2, 4.633, 0, 2, 4.667, 0.002, 2, 4.7, 0.003, 2, 4.733, 0.003, 2, 4.767, 0.002, 2, 4.8, 0.001, 2, 4.833, 0, 2, 4.867, -0.001, 2, 4.9, -0.001, 2, 4.933, -0.001, 2, 4.967, -0.001, 2, 5, 0, 2, 5.033, 0, 2, 5.067, 0, 2, 5.1, 0.001, 2, 5.133, 0.001, 2, 5.167, 0, 2, 5.2, 0, 2, 5.233, 0, 2, 5.267, 0, 2, 5.4, 0, 2, 5.433, 0, 2, 5.467, 0, 2, 5.6, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamGaoGguangL", "Segments": [0, 0, 2, 0.3, 0.102, 2, 0.333, 0.327, 2, 0.367, 0.552, 2, 0.4, 0.655, 2, 0.433, 0.519, 2, 0.467, 0.195, 2, 0.5, -0.2, 2, 0.533, -0.524, 2, 0.567, -0.66, 2, 0.6, -0.595, 2, 0.633, -0.436, 2, 0.667, -0.224, 2, 0.7, -0.013, 2, 0.733, 0.146, 2, 0.767, 0.212, 2, 0.8, 0.195, 2, 0.833, 0.153, 2, 0.867, 0.094, 2, 0.9, 0.031, 2, 0.933, -0.028, 2, 0.967, -0.071, 2, 1, -0.087, 2, 1.033, -0.067, 2, 1.067, -0.156, 2, 1.1, -0.351, 2, 1.133, -0.547, 2, 1.167, -0.636, 2, 1.2, -0.502, 2, 1.233, -0.181, 2, 1.267, 0.21, 2, 1.3, 0.531, 2, 1.333, 0.665, 2, 1.367, 0.599, 2, 1.4, 0.439, 2, 1.433, 0.226, 2, 1.467, 0.013, 2, 1.5, -0.147, 2, 1.533, -0.213, 2, 1.567, -0.197, 2, 1.6, -0.154, 2, 1.633, -0.094, 2, 1.667, -0.032, 2, 1.7, 0.028, 2, 1.733, 0.071, 2, 1.767, 0.087, 2, 1.8, 0.078, 2, 1.833, 0.055, 2, 1.867, 0.025, 2, 1.9, -0.006, 2, 1.933, -0.028, 2, 1.967, -0.038, 2, 2, -0.034, 2, 2.033, -0.024, 2, 2.067, -0.011, 2, 2.1, 0.002, 2, 2.133, 0.012, 2, 2.167, 0.016, 2, 2.2, 0.015, 2, 2.233, 0.01, 2, 2.267, 0.005, 2, 2.3, -0.001, 2, 2.333, -0.005, 2, 2.367, -0.007, 2, 2.4, -0.006, 2, 2.433, -0.004, 2, 2.467, -0.002, 2, 2.5, 0, 2, 2.533, 0.002, 2, 2.567, 0.003, 2, 2.6, 0.003, 2, 2.633, 0.002, 2, 2.667, 0.001, 2, 2.7, 0, 2, 2.733, -0.001, 2, 2.767, -0.001, 2, 2.8, 0.221, 2, 2.833, 0.622, 2, 2.867, 0.844, 2, 2.9, 0.437, 2, 2.933, -0.299, 2, 2.967, -0.706, 2, 3, -0.637, 2, 3.033, -0.468, 2, 3.067, -0.243, 2, 3.1, -0.019, 2, 3.133, 0.15, 2, 3.167, 0.22, 2, 3.2, 0.139, 2, 3.233, -0.057, 2, 3.267, -0.318, 2, 3.3, -0.578, 2, 3.333, -0.774, 2, 3.367, -0.855, 2, 3.4, -0.445, 2, 3.433, 0.295, 2, 3.467, 0.705, 2, 3.5, 0.635, 2, 3.533, 0.466, 2, 3.567, 0.242, 2, 3.6, 0.017, 2, 3.633, -0.152, 2, 3.667, -0.221, 2, 3.7, -0.198, 2, 3.733, -0.141, 2, 3.767, -0.066, 2, 3.8, 0.01, 2, 3.833, 0.067, 2, 3.867, 0.09, 2, 3.9, 0.083, 2, 3.933, 0.065, 2, 3.967, 0.039, 2, 4, 0.012, 2, 4.033, -0.013, 2, 4.067, -0.032, 2, 4.1, -0.039, 2, 4.133, -0.034, 2, 4.167, -0.024, 2, 4.2, -0.011, 2, 4.233, 0.002, 2, 4.267, 0.013, 2, 4.3, 0.017, 2, 4.333, 0.015, 2, 4.367, 0.011, 2, 4.4, 0.005, 2, 4.433, -0.001, 2, 4.467, -0.005, 2, 4.5, -0.007, 2, 4.533, -0.006, 2, 4.567, -0.005, 2, 4.6, -0.002, 2, 4.633, 0, 2, 4.667, 0.002, 2, 4.7, 0.003, 2, 4.733, 0.003, 2, 4.767, 0.002, 2, 4.8, 0.001, 2, 4.833, 0, 2, 4.867, -0.001, 2, 4.9, -0.001, 2, 4.933, -0.001, 2, 4.967, -0.001, 2, 5, 0, 2, 5.033, 0, 2, 5.067, 0, 2, 5.1, 0.001, 2, 5.133, 0.001, 2, 5.167, 0, 2, 5.2, 0, 2, 5.233, 0, 2, 5.267, 0, 2, 5.4, 0, 2, 5.433, 0, 2, 5.467, 0, 2, 5.6, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamGaoGuangR", "Segments": [0, 0, 2, 0.3, 0.102, 2, 0.333, 0.327, 2, 0.367, 0.552, 2, 0.4, 0.655, 2, 0.433, 0.519, 2, 0.467, 0.195, 2, 0.5, -0.2, 2, 0.533, -0.524, 2, 0.567, -0.66, 2, 0.6, -0.595, 2, 0.633, -0.436, 2, 0.667, -0.224, 2, 0.7, -0.013, 2, 0.733, 0.146, 2, 0.767, 0.212, 2, 0.8, 0.195, 2, 0.833, 0.153, 2, 0.867, 0.094, 2, 0.9, 0.031, 2, 0.933, -0.028, 2, 0.967, -0.071, 2, 1, -0.087, 2, 1.033, -0.067, 2, 1.067, -0.156, 2, 1.1, -0.351, 2, 1.133, -0.547, 2, 1.167, -0.636, 2, 1.2, -0.502, 2, 1.233, -0.181, 2, 1.267, 0.21, 2, 1.3, 0.531, 2, 1.333, 0.665, 2, 1.367, 0.599, 2, 1.4, 0.439, 2, 1.433, 0.226, 2, 1.467, 0.013, 2, 1.5, -0.147, 2, 1.533, -0.213, 2, 1.567, -0.197, 2, 1.6, -0.154, 2, 1.633, -0.094, 2, 1.667, -0.032, 2, 1.7, 0.028, 2, 1.733, 0.071, 2, 1.767, 0.087, 2, 1.8, 0.078, 2, 1.833, 0.055, 2, 1.867, 0.025, 2, 1.9, -0.006, 2, 1.933, -0.028, 2, 1.967, -0.038, 2, 2, -0.034, 2, 2.033, -0.024, 2, 2.067, -0.011, 2, 2.1, 0.002, 2, 2.133, 0.012, 2, 2.167, 0.016, 2, 2.2, 0.015, 2, 2.233, 0.01, 2, 2.267, 0.005, 2, 2.3, -0.001, 2, 2.333, -0.005, 2, 2.367, -0.007, 2, 2.4, -0.006, 2, 2.433, -0.004, 2, 2.467, -0.002, 2, 2.5, 0, 2, 2.533, 0.002, 2, 2.567, 0.003, 2, 2.6, 0.003, 2, 2.633, 0.002, 2, 2.667, 0.001, 2, 2.7, 0, 2, 2.733, -0.001, 2, 2.767, -0.001, 2, 2.8, 0.221, 2, 2.833, 0.622, 2, 2.867, 0.844, 2, 2.9, 0.437, 2, 2.933, -0.299, 2, 2.967, -0.706, 2, 3, -0.637, 2, 3.033, -0.468, 2, 3.067, -0.243, 2, 3.1, -0.019, 2, 3.133, 0.15, 2, 3.167, 0.22, 2, 3.2, 0.139, 2, 3.233, -0.057, 2, 3.267, -0.318, 2, 3.3, -0.578, 2, 3.333, -0.774, 2, 3.367, -0.855, 2, 3.4, -0.445, 2, 3.433, 0.295, 2, 3.467, 0.705, 2, 3.5, 0.635, 2, 3.533, 0.466, 2, 3.567, 0.242, 2, 3.6, 0.017, 2, 3.633, -0.152, 2, 3.667, -0.221, 2, 3.7, -0.198, 2, 3.733, -0.141, 2, 3.767, -0.066, 2, 3.8, 0.01, 2, 3.833, 0.067, 2, 3.867, 0.09, 2, 3.9, 0.083, 2, 3.933, 0.065, 2, 3.967, 0.039, 2, 4, 0.012, 2, 4.033, -0.013, 2, 4.067, -0.032, 2, 4.1, -0.039, 2, 4.133, -0.034, 2, 4.167, -0.024, 2, 4.2, -0.011, 2, 4.233, 0.002, 2, 4.267, 0.013, 2, 4.3, 0.017, 2, 4.333, 0.015, 2, 4.367, 0.011, 2, 4.4, 0.005, 2, 4.433, -0.001, 2, 4.467, -0.005, 2, 4.5, -0.007, 2, 4.533, -0.006, 2, 4.567, -0.005, 2, 4.6, -0.002, 2, 4.633, 0, 2, 4.667, 0.002, 2, 4.7, 0.003, 2, 4.733, 0.003, 2, 4.767, 0.002, 2, 4.8, 0.001, 2, 4.833, 0, 2, 4.867, -0.001, 2, 4.9, -0.001, 2, 4.933, -0.001, 2, 4.967, -0.001, 2, 5, 0, 2, 5.033, 0, 2, 5.067, 0, 2, 5.1, 0.001, 2, 5.133, 0.001, 2, 5.167, 0, 2, 5.2, 0, 2, 5.233, 0, 2, 5.267, 0, 2, 5.4, 0, 2, 5.433, 0, 2, 5.467, 0, 2, 5.6, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamTeShuEyeChuXian", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamHeiHuaShadow", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamXianTiaoChuXian", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamTeShuZuiCX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.633, 0.273, 2, 0.667, 0.875, 2, 0.7, 1, 2, 0.767, 0.993, 2, 0.8, 0.236, 2, 0.833, 1, 2, 0.967, 0.847, 2, 1, 0.636, 2, 1.033, 0.812, 2, 1.067, 0.988, 2, 1.1, 0.945, 2, 1.133, 0.833, 2, 1.167, 0.675, 2, 1.2, 0.494, 2, 1.233, 0.313, 2, 1.267, 0.155, 2, 1.3, 0.043, 2, 1.333, 0, 2, 1.367, 0.156, 2, 1.4, 0.5, 2, 1.433, 0.844, 2, 1.467, 1, 2, 1.533, 0.988, 2, 1.567, 1, 2, 1.633, 0.961, 2, 1.667, 0.172, 2, 1.7, 0.745, 2, 1.733, 1, 2, 1.833, 0.884, 2, 1.867, 0.8, 2, 1.9, 0.808, 2, 1.933, 0.816, 2, 1.967, 0.757, 2, 2, 0.609, 2, 2.033, 0.41, 2, 2.067, 0.212, 2, 2.1, 0.062, 2, 2.133, 0, 2, 2.3, 0.019, 2, 2.333, 0.059, 2, 2.367, 0.1, 2, 2.4, 0.118, 2, 2.433, 0.109, 2, 2.467, 0.088, 2, 2.5, 0.059, 2, 2.533, 0.031, 2, 2.567, 0.009, 2, 2.6, 0, 2, 2.9, 0.267, 2, 2.933, 0.855, 2, 2.967, 1, 2, 3.1, 0.89, 2, 3.133, 0.49, 2, 3.167, 0.195, 2, 3.2, 0.07, 2, 3.233, 0.338, 2, 3.267, 0.929, 2, 3.3, 1, 2, 3.4, 0.996, 2, 3.433, 0.451, 2, 3.467, 0.204, 2, 3.5, 0.234, 2, 3.533, 0.317, 2, 3.567, 0.447, 2, 3.6, 0.609, 2, 3.633, 0.788, 2, 3.667, 0.98, 2, 3.7, 1, 2, 3.967, 0.548, 2, 4, 0.322, 2, 4.033, 1, 2, 4.167, 0.528, 2, 4.2, 0.274, 2, 4.233, 0.529, 2, 4.267, 0.784, 2, 4.3, 0.678, 2, 4.333, 0.439, 2, 4.367, 0.2, 2, 4.4, 0.094, 2, 4.433, 0.3, 2, 4.467, 0.746, 2, 4.5, 1, 2, 4.6, 0.726, 2, 4.633, 0.257, 2, 4.667, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 0.033, 0.028, 2, 0.067, 0.103, 2, 0.1, 0.217, 2, 0.133, 0.353, 2, 0.167, 0.5, 2, 0.2, 0.647, 2, 0.233, 0.783, 2, 0.267, 0.897, 2, 0.3, 0.972, 2, 0.333, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.033, 0.003, 2, 0.067, 0.013, 2, 0.1, 0.028, 2, 0.133, 0.049, 2, 0.167, 0.074, 2, 0.2, 0.104, 2, 0.233, 0.138, 2, 0.267, 0.175, 2, 0.3, 0.216, 2, 0.333, 0.259, 2, 0.367, 0.305, 2, 0.4, 0.352, 2, 0.433, 0.401, 2, 0.467, 0.45, 2, 0.5, 0.5, 2, 0.533, 0.55, 2, 0.567, 0.599, 2, 0.6, 0.648, 2, 0.633, 0.695, 2, 0.667, 0.741, 2, 0.7, 0.784, 2, 0.733, 0.825, 2, 0.767, 0.862, 2, 0.8, 0.896, 2, 0.833, 0.926, 2, 0.867, 0.951, 2, 0.9, 0.972, 2, 0.933, 0.987, 2, 0.967, 0.997, 2, 1, 1, 2, 1.033, 0.999, 2, 1.067, 0.997, 2, 1.1, 0.993, 2, 1.133, 0.987, 2, 1.167, 0.98, 2, 1.2, 0.972, 2, 1.233, 0.962, 2, 1.267, 0.951, 2, 1.3, 0.939, 2, 1.333, 0.926, 2, 1.367, 0.911, 2, 1.4, 0.896, 2, 1.433, 0.879, 2, 1.467, 0.862, 2, 1.5, 0.844, 2, 1.533, 0.825, 2, 1.567, 0.805, 2, 1.6, 0.784, 2, 1.633, 0.763, 2, 1.667, 0.741, 2, 1.7, 0.718, 2, 1.733, 0.695, 2, 1.767, 0.672, 2, 1.8, 0.648, 2, 1.833, 0.624, 2, 1.867, 0.599, 2, 1.9, 0.575, 2, 1.933, 0.55, 2, 1.967, 0.525, 2, 2, 0.5, 2, 2.033, 0.475, 2, 2.067, 0.45, 2, 2.1, 0.425, 2, 2.133, 0.401, 2, 2.167, 0.376, 2, 2.2, 0.352, 2, 2.233, 0.328, 2, 2.267, 0.305, 2, 2.3, 0.282, 2, 2.333, 0.259, 2, 2.367, 0.237, 2, 2.4, 0.216, 2, 2.433, 0.195, 2, 2.467, 0.175, 2, 2.5, 0.156, 2, 2.533, 0.138, 2, 2.567, 0.121, 2, 2.6, 0.104, 2, 2.633, 0.089, 2, 2.667, 0.074, 2, 2.7, 0.061, 2, 2.733, 0.049, 2, 2.767, 0.038, 2, 2.8, 0.028, 2, 2.833, 0.02, 2, 2.867, 0.013, 2, 2.9, 0.007, 2, 2.933, 0.003, 2, 2.967, 0.001, 2, 3, 0, 2, 3.033, 0.003, 2, 3.067, 0.012, 2, 3.1, 0.026, 2, 3.133, 0.046, 2, 3.167, 0.07, 2, 3.2, 0.098, 2, 3.233, 0.13, 2, 3.267, 0.165, 2, 3.3, 0.204, 2, 3.333, 0.245, 2, 3.367, 0.289, 2, 3.4, 0.334, 2, 3.433, 0.38, 2, 3.467, 0.428, 2, 3.5, 0.476, 2, 3.533, 0.524, 2, 3.567, 0.572, 2, 3.6, 0.62, 2, 3.633, 0.666, 2, 3.667, 0.711, 2, 3.7, 0.755, 2, 3.733, 0.796, 2, 3.767, 0.835, 2, 3.8, 0.87, 2, 3.833, 0.902, 2, 3.867, 0.93, 2, 3.9, 0.954, 2, 3.933, 0.974, 2, 3.967, 0.988, 2, 4, 0.997, 2, 4.033, 1, 2, 4.067, 0.998, 2, 4.1, 0.994, 2, 4.133, 0.987, 2, 4.167, 0.977, 2, 4.2, 0.964, 2, 4.233, 0.949, 2, 4.267, 0.932, 2, 4.3, 0.913, 2, 4.333, 0.892, 2, 4.367, 0.869, 2, 4.4, 0.844, 2, 4.433, 0.817, 2, 4.467, 0.79, 2, 4.5, 0.76, 2, 4.533, 0.731, 2, 4.567, 0.699, 2, 4.6, 0.668, 2, 4.633, 0.635, 2, 4.667, 0.602, 2, 4.7, 0.568, 2, 4.733, 0.534, 2, 4.767, 0.5, 2, 4.8, 0.466, 2, 4.833, 0.432, 2, 4.867, 0.398, 2, 4.9, 0.365, 2, 4.933, 0.332, 2, 4.967, 0.301, 2, 5, 0.269, 2, 5.033, 0.24, 2, 5.067, 0.21, 2, 5.1, 0.183, 2, 5.133, 0.156, 2, 5.167, 0.131, 2, 5.2, 0.108, 2, 5.233, 0.087, 2, 5.267, 0.068, 2, 5.3, 0.051, 2, 5.333, 0.036, 2, 5.367, 0.023, 2, 5.4, 0.013, 2, 5.433, 0.006, 2, 5.467, 0.002, 2, 5.5, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.033, -0.055, 2, 0.067, -0.213, 2, 0.1, -0.469, 2, 0.133, -0.801, 2, 0.167, -1.216, 2, 0.2, -1.685, 2, 0.233, -2.219, 2, 0.267, -2.786, 2, 0.3, -3.399, 2, 0.333, -4.024, 2, 0.367, -4.678, 2, 0.4, -5.322, 2, 0.433, -5.976, 2, 0.467, -6.601, 2, 0.5, -7.214, 2, 0.533, -7.781, 2, 0.567, -8.315, 2, 0.6, -8.784, 2, 0.633, -9.199, 2, 0.667, -9.531, 2, 0.7, -9.787, 2, 0.733, -9.945, 2, 0.767, -10, 2, 0.8, -9.96, 2, 0.833, -9.841, 2, 0.867, -9.642, 2, 0.9, -9.372, 2, 0.933, -9.029, 2, 0.967, -8.608, 2, 1, -8.126, 2, 1.033, -7.578, 2, 1.067, -6.967, 2, 1.1, -6.293, 2, 1.133, -5.561, 2, 1.167, -4.757, 2, 1.2, -3.911, 2, 1.233, -3.012, 2, 1.267, -2.046, 2, 1.3, -1.046, 2, 1.333, 0, 2, 1.367, 1.037, 2, 1.4, 2.011, 2, 1.433, 2.94, 2, 1.467, 3.79, 2, 1.5, 4.58, 2, 1.533, 5.312, 2, 1.567, 5.986, 2, 1.6, 6.615, 2, 1.633, 7.176, 2, 1.667, 7.683, 2, 1.7, 8.136, 2, 1.733, 8.537, 2, 1.767, 8.894, 2, 1.8, 9.193, 2, 1.833, 9.443, 2, 1.867, 9.646, 2, 1.9, 9.802, 2, 1.933, 9.914, 2, 1.967, 9.979, 2, 2, 10, 2, 2.033, 9.978, 2, 2.067, 9.914, 2, 2.1, 9.806, 2, 2.133, 9.658, 2, 2.167, 9.464, 2, 2.2, 9.233, 2, 2.233, 8.955, 2, 2.267, 8.642, 2, 2.3, 8.281, 2, 2.333, 7.887, 2, 2.367, 7.443, 2, 2.4, 6.958, 2, 2.433, 6.444, 2, 2.467, 5.879, 2, 2.5, 5.286, 2, 2.533, 4.641, 2, 2.567, 3.971, 2, 2.6, 3.247, 2, 2.633, 2.501, 2, 2.667, 1.7, 2, 2.7, 0.878, 2, 2.733, 0, 2, 2.767, -0.867, 2, 2.8, -1.694, 2, 2.833, -2.489, 2, 2.867, -3.226, 2, 2.9, -3.939, 2, 2.933, -4.597, 2, 2.967, -5.229, 2, 3, -5.809, 2, 3.033, -6.361, 2, 3.067, -6.863, 2, 3.1, -7.337, 2, 3.133, -7.762, 2, 3.167, -8.158, 2, 3.2, -8.507, 2, 3.233, -8.825, 2, 3.267, -9.1, 2, 3.3, -9.342, 2, 3.333, -9.542, 2, 3.367, -9.709, 2, 3.4, -9.835, 2, 3.433, -9.927, 2, 3.467, -9.982, 2, 3.5, -10, 2, 3.533, -9.982, 2, 3.567, -9.927, 2, 3.6, -9.835, 2, 3.633, -9.709, 2, 3.667, -9.542, 2, 3.7, -9.342, 2, 3.733, -9.1, 2, 3.767, -8.825, 2, 3.8, -8.507, 2, 3.833, -8.158, 2, 3.867, -7.762, 2, 3.9, -7.337, 2, 3.933, -6.863, 2, 3.967, -6.361, 2, 4, -5.809, 2, 4.033, -5.229, 2, 4.067, -4.597, 2, 4.1, -3.939, 2, 4.133, -3.226, 2, 4.167, -2.489, 2, 4.2, -1.694, 2, 4.233, -0.867, 2, 4.267, 0, 2, 4.3, 0.878, 2, 4.333, 1.7, 2, 4.367, 2.501, 2, 4.4, 3.247, 2, 4.433, 3.971, 2, 4.467, 4.641, 2, 4.5, 5.286, 2, 4.533, 5.879, 2, 4.567, 6.444, 2, 4.6, 6.958, 2, 4.633, 7.443, 2, 4.667, 7.887, 2, 4.7, 8.281, 2, 4.733, 8.642, 2, 4.767, 8.955, 2, 4.8, 9.233, 2, 4.833, 9.464, 2, 4.867, 9.658, 2, 4.9, 9.806, 2, 4.933, 9.914, 2, 4.967, 9.978, 2, 5, 10, 2, 5.033, 9.986, 2, 5.067, 9.945, 2, 5.1, 9.877, 2, 5.133, 9.787, 2, 5.167, 9.67, 2, 5.2, 9.534, 2, 5.233, 9.374, 2, 5.267, 9.199, 2, 5.3, 9, 2, 5.333, 8.789, 2, 5.367, 8.557, 2, 5.4, 8.315, 2, 5.433, 8.054, 2, 5.467, 7.787, 2, 5.5, 7.502, 2, 5.533, 7.214, 2, 5.567, 6.911, 2, 5.6, 6.607, 2, 5.633, 6.291, 2, 5.667, 5.976, 2, 5.7, 5.65, 2, 5.733, 5.326, 2, 5.767, 5, 2, 5.8, 4.674, 2, 5.833, 4.35, 2, 5.867, 4.024, 2, 5.9, 3.709, 2, 5.933, 3.393, 2, 5.967, 3.089, 2, 6, 2.786, 2, 6.033, 2.498, 2, 6.067, 2.213, 2, 6.1, 1.946, 2, 6.133, 1.685, 2, 6.167, 1.443, 2, 6.2, 1.211, 2, 6.233, 1, 2, 6.267, 0.801, 2, 6.3, 0.626, 2, 6.333, 0.466, 2, 6.367, 0.33, 2, 6.4, 0.213, 2, 6.433, 0.123, 2, 6.467, 0.055, 2, 6.5, 0.014, 2, 6.533, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.033, 0.15, 2, 0.067, 0.515, 2, 0.1, 1, 2, 0.133, 1.485, 2, 0.167, 1.85, 2, 0.2, 2, 2, 0.233, 1.988, 2, 0.267, 1.954, 2, 0.3, 1.895, 2, 0.333, 1.813, 2, 0.367, 1.709, 2, 0.4, 1.579, 2, 0.433, 1.425, 2, 0.467, 1.25, 2, 0.5, 1.047, 2, 0.533, 0.819, 2, 0.567, 0.572, 2, 0.6, 0.294, 2, 0.633, -0.008, 2, 0.667, -0.329, 2, 0.7, -0.682, 2, 0.733, -1.061, 2, 0.767, -1.457, 2, 0.8, -1.887, 2, 0.833, -2.344, 2, 0.867, -2.816, 2, 0.9, -3.325, 2, 0.933, -3.861, 2, 0.967, -4.411, 2, 1, -5, 2, 1.033, -5.568, 2, 1.067, -6.047, 2, 1.1, -6.449, 2, 1.133, -6.749, 2, 1.167, -6.934, 2, 1.2, -7, 2, 1.233, -6.971, 2, 1.267, -6.89, 2, 1.3, -6.763, 2, 1.333, -6.605, 2, 1.367, -6.416, 2, 1.4, -6.212, 2, 1.433, -6, 2, 1.467, -5.788, 2, 1.5, -5.584, 2, 1.533, -5.395, 2, 1.567, -5.237, 2, 1.6, -5.11, 2, 1.633, -5.029, 2, 1.667, -5, 2, 2.567, -5.225, 2, 2.6, -5.773, 2, 2.633, -6.5, 2, 2.667, -7.227, 2, 2.7, -7.775, 2, 2.733, -8, 2, 2.767, -7.939, 2, 2.8, -7.76, 2, 2.833, -7.463, 2, 2.867, -7.064, 2, 2.9, -6.566, 2, 2.933, -5.964, 2, 2.967, -5.284, 2, 3, -4.523, 2, 3.033, -3.687, 2, 3.067, -2.782, 2, 3.1, -1.815, 2, 3.133, -0.774, 2, 3.167, 0.301, 2, 3.2, 1.42, 2, 3.233, 2.596, 2, 3.267, 3.785, 2, 3.3, 5, 2, 3.333, 6.068, 2, 3.367, 6.847, 2, 3.4, 7.389, 2, 3.433, 7.721, 2, 3.467, 7.908, 2, 3.5, 7.985, 2, 3.533, 8, 2, 3.567, 7.94, 2, 3.6, 7.779, 2, 3.633, 7.531, 2, 3.667, 7.22, 2, 3.7, 6.874, 2, 3.733, 6.5, 2, 3.767, 6.126, 2, 3.8, 5.78, 2, 3.833, 5.469, 2, 3.867, 5.221, 2, 3.9, 5.06, 2, 3.933, 5, 2, 8, 5]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.033, 0.15, 2, 0.067, 0.515, 2, 0.1, 1, 2, 0.133, 1.485, 2, 0.167, 1.85, 2, 0.2, 2, 2, 0.233, 1.988, 2, 0.267, 1.954, 2, 0.3, 1.895, 2, 0.333, 1.813, 2, 0.367, 1.709, 2, 0.4, 1.579, 2, 0.433, 1.425, 2, 0.467, 1.25, 2, 0.5, 1.047, 2, 0.533, 0.819, 2, 0.567, 0.572, 2, 0.6, 0.294, 2, 0.633, -0.008, 2, 0.667, -0.329, 2, 0.7, -0.682, 2, 0.733, -1.061, 2, 0.767, -1.457, 2, 0.8, -1.887, 2, 0.833, -2.344, 2, 0.867, -2.816, 2, 0.9, -3.325, 2, 0.933, -3.861, 2, 0.967, -4.411, 2, 1, -5, 2, 1.033, -5.568, 2, 1.067, -6.047, 2, 1.1, -6.449, 2, 1.133, -6.749, 2, 1.167, -6.934, 2, 1.2, -7, 2, 1.233, -6.971, 2, 1.267, -6.89, 2, 1.3, -6.763, 2, 1.333, -6.605, 2, 1.367, -6.416, 2, 1.4, -6.212, 2, 1.433, -6, 2, 1.467, -5.788, 2, 1.5, -5.584, 2, 1.533, -5.395, 2, 1.567, -5.237, 2, 1.6, -5.11, 2, 1.633, -5.029, 2, 1.667, -5, 2, 2.567, -5.225, 2, 2.6, -5.773, 2, 2.633, -6.5, 2, 2.667, -7.227, 2, 2.7, -7.775, 2, 2.733, -8, 2, 2.767, -7.939, 2, 2.8, -7.76, 2, 2.833, -7.463, 2, 2.867, -7.064, 2, 2.9, -6.566, 2, 2.933, -5.964, 2, 2.967, -5.284, 2, 3, -4.523, 2, 3.033, -3.687, 2, 3.067, -2.782, 2, 3.1, -1.815, 2, 3.133, -0.774, 2, 3.167, 0.301, 2, 3.2, 1.42, 2, 3.233, 2.596, 2, 3.267, 3.785, 2, 3.3, 5, 2, 3.333, 6.068, 2, 3.367, 6.847, 2, 3.4, 7.389, 2, 3.433, 7.721, 2, 3.467, 7.908, 2, 3.5, 7.985, 2, 3.533, 8, 2, 3.567, 7.94, 2, 3.6, 7.779, 2, 3.633, 7.531, 2, 3.667, 7.22, 2, 3.7, 6.874, 2, 3.733, 6.5, 2, 3.767, 6.126, 2, 3.8, 5.78, 2, 3.833, 5.469, 2, 3.867, 5.221, 2, 3.9, 5.06, 2, 3.933, 5, 2, 8, 5]}, {"Target": "Parameter", "Id": "ParamShenTiQianHou", "Segments": [0, 0, 2, 0.033, -0.301, 2, 0.067, -1.03, 2, 0.1, -2, 2, 0.133, -2.97, 2, 0.167, -3.699, 2, 0.2, -4, 2, 0.233, -3.954, 2, 0.267, -3.823, 2, 0.3, -3.599, 2, 0.333, -3.287, 2, 0.367, -2.897, 2, 0.4, -2.411, 2, 0.433, -1.84, 2, 0.467, -1.199, 2, 0.5, -0.459, 2, 0.533, 0.365, 2, 0.567, 1.249, 2, 0.6, 2.236, 2, 0.633, 3.304, 2, 0.667, 4.425, 2, 0.7, 5.652, 2, 0.733, 6.957, 2, 0.767, 8.307, 2, 0.8, 9.766, 2, 0.833, 11.301, 2, 0.867, 12.873, 2, 0.9, 14.556, 2, 0.933, 16.313, 2, 0.967, 18.1, 2, 1, 20, 2, 1.033, 21.716, 2, 1.067, 22.995, 2, 1.1, 23.941, 2, 1.133, 24.561, 2, 1.167, 24.895, 2, 1.2, 25, 2, 1.233, 24.927, 2, 1.267, 24.724, 2, 1.3, 24.407, 2, 1.333, 24.012, 2, 1.367, 23.54, 2, 1.4, 23.031, 2, 1.433, 22.5, 2, 1.467, 21.969, 2, 1.5, 21.46, 2, 1.533, 20.988, 2, 1.567, 20.593, 2, 1.6, 20.276, 2, 1.633, 20.073, 2, 1.667, 20, 2, 8, 20]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.467, 0.138, 2, 0.5, 0.507, 2, 0.533, 1.041, 2, 0.567, 1.673, 2, 0.6, 2.327, 2, 0.633, 2.959, 2, 0.667, 3.493, 2, 0.7, 3.862, 2, 0.733, 4, 2, 0.767, 3.862, 2, 0.8, 3.493, 2, 0.833, 2.959, 2, 0.867, 2.327, 2, 0.9, 1.673, 2, 0.933, 1.041, 2, 0.967, 0.507, 2, 1, 0.138, 2, 1.033, 0, 2, 1.867, 0.138, 2, 1.9, 0.507, 2, 1.933, 1.041, 2, 1.967, 1.673, 2, 2, 2.327, 2, 2.033, 2.959, 2, 2.067, 3.493, 2, 2.1, 3.862, 2, 2.133, 4, 2, 2.167, 3.862, 2, 2.2, 3.493, 2, 2.233, 2.959, 2, 2.267, 2.327, 2, 2.3, 1.673, 2, 2.333, 1.041, 2, 2.367, 0.507, 2, 2.4, 0.138, 2, 2.433, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.467, 0.138, 2, 0.5, 0.507, 2, 0.533, 1.041, 2, 0.567, 1.673, 2, 0.6, 2.327, 2, 0.633, 2.959, 2, 0.667, 3.493, 2, 0.7, 3.862, 2, 0.733, 4, 2, 0.767, 3.862, 2, 0.8, 3.493, 2, 0.833, 2.959, 2, 0.867, 2.327, 2, 0.9, 1.673, 2, 0.933, 1.041, 2, 0.967, 0.507, 2, 1, 0.138, 2, 1.033, 0, 2, 1.867, 0.138, 2, 1.9, 0.507, 2, 1.933, 1.041, 2, 1.967, 1.673, 2, 2, 2.327, 2, 2.033, 2.959, 2, 2.067, 3.493, 2, 2.1, 3.862, 2, 2.133, 4, 2, 2.167, 3.862, 2, 2.2, 3.493, 2, 2.233, 2.959, 2, 2.267, 2.327, 2, 2.3, 1.673, 2, 2.333, 1.041, 2, 2.367, 0.507, 2, 2.4, 0.138, 2, 2.433, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Paramzuodatui", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 2, 0.033, 0.011, 2, 0.067, 0.037, 2, 0.1, 0.068, 2, 0.133, 0.094, 2, 0.167, 0.105, 2, 0.2, 0.099, 2, 0.233, 0.083, 2, 0.267, 0.061, 2, 0.3, 0.038, 2, 0.333, 0.017, 2, 0.367, 0.001, 2, 0.4, -0.005, 2, 0.433, -0.003, 2, 0.467, 0.001, 2, 0.5, 0.006, 2, 0.533, 0.011, 2, 0.567, 0.012, 2, 0.6, 0.01, 2, 0.633, 0.004, 2, 0.667, -0.004, 2, 0.7, -0.016, 2, 0.733, -0.029, 2, 0.767, -0.043, 2, 0.8, -0.057, 2, 0.833, -0.071, 2, 0.867, -0.084, 2, 0.9, -0.095, 2, 0.933, -0.104, 2, 0.967, -0.11, 2, 1, -0.112, 2, 1.033, -0.103, 2, 1.067, -0.077, 2, 1.1, -0.04, 2, 1.133, 0.004, 2, 1.167, 0.05, 2, 1.2, 0.094, 2, 1.233, 0.131, 2, 1.267, 0.157, 2, 1.3, 0.166, 2, 1.333, 0.163, 2, 1.367, 0.155, 2, 1.4, 0.142, 2, 1.433, 0.126, 2, 1.467, 0.108, 2, 1.5, 0.089, 2, 1.533, 0.07, 2, 1.567, 0.052, 2, 1.6, 0.036, 2, 1.633, 0.024, 2, 1.667, 0.015, 2, 1.7, 0.012, 2, 1.733, 0.015, 2, 1.767, 0.021, 2, 1.8, 0.031, 2, 1.833, 0.043, 2, 1.867, 0.056, 2, 1.9, 0.069, 2, 1.933, 0.081, 2, 1.967, 0.09, 2, 2, 0.097, 2, 2.033, 0.1, 2, 2.067, 0.099, 2, 2.1, 0.096, 2, 2.133, 0.092, 2, 2.167, 0.088, 2, 2.2, 0.083, 2, 2.233, 0.079, 2, 2.267, 0.075, 2, 2.3, 0.072, 2, 2.333, 0.071, 2, 2.367, 0.072, 2, 2.4, 0.075, 2, 2.433, 0.078, 2, 2.467, 0.082, 2, 2.5, 0.086, 2, 2.533, 0.089, 2, 2.567, 0.092, 2, 2.6, 0.093, 2, 2.633, 0.091, 2, 2.667, 0.088, 2, 2.7, 0.083, 2, 2.733, 0.077, 2, 2.767, 0.07, 2, 2.8, 0.063, 2, 2.833, 0.057, 2, 2.867, 0.052, 2, 2.9, 0.048, 2, 2.933, 0.047, 2, 2.967, 0.048, 2, 3, 0.052, 2, 3.033, 0.057, 2, 3.067, 0.063, 2, 3.1, 0.069, 2, 3.133, 0.075, 2, 3.167, 0.08, 2, 3.2, 0.083, 2, 3.233, 0.085, 2, 3.267, 0.083, 2, 3.3, 0.079, 2, 3.333, 0.072, 2, 3.367, 0.063, 2, 3.4, 0.053, 2, 3.433, 0.043, 2, 3.467, 0.032, 2, 3.5, 0.021, 2, 3.533, 0.011, 2, 3.567, 0.002, 2, 3.6, -0.004, 2, 3.633, -0.009, 2, 3.667, -0.01, 2, 3.7, -0.01, 2, 3.733, -0.008, 2, 3.767, -0.006, 2, 3.8, -0.004, 2, 3.833, -0.001, 2, 3.867, 0.001, 2, 3.9, 0.004, 2, 3.933, 0.005, 2, 3.967, 0.006, 2, 4.033, 0.006, 2, 4.067, 0.005, 2, 4.1, 0.003, 2, 4.133, 0.001, 2, 4.167, -0.002, 2, 4.2, -0.004, 2, 4.233, -0.007, 2, 4.267, -0.008, 2, 4.3, -0.009, 2, 4.333, -0.007, 2, 4.367, -0.002, 2, 4.4, 0.006, 2, 4.433, 0.015, 2, 4.467, 0.025, 2, 4.5, 0.035, 2, 4.533, 0.045, 2, 4.567, 0.052, 2, 4.6, 0.058, 2, 4.633, 0.059, 2, 4.667, 0.059, 2, 4.7, 0.057, 2, 4.733, 0.055, 2, 4.767, 0.052, 2, 4.8, 0.049, 2, 4.833, 0.046, 2, 4.867, 0.045, 2, 4.9, 0.044, 2, 4.933, 0.044, 2, 4.967, 0.046, 2, 5, 0.047, 2, 5.033, 0.049, 2, 5.067, 0.052, 2, 5.1, 0.053, 2, 5.133, 0.055, 2, 5.167, 0.055, 2, 5.2, 0.055, 2, 5.233, 0.054, 2, 5.267, 0.053, 2, 5.3, 0.052, 2, 5.333, 0.051, 2, 5.367, 0.05, 2, 5.4, 0.049, 2, 5.433, 0.048, 2, 5.467, 0.047, 2, 5.5, 0.046, 2, 5.533, 0.046, 2, 5.567, 0.046, 2, 5.633, 0.046, 2, 5.667, 0.046, 2, 5.7, 0.046, 2, 5.733, 0.046, 2, 5.767, 0.046, 2, 5.8, 0.046, 2, 5.833, 0.045, 2, 5.867, 0.044, 2, 5.9, 0.043, 2, 5.933, 0.042, 2, 5.967, 0.041, 2, 6, 0.04, 2, 6.033, 0.038, 2, 6.067, 0.037, 2, 6.1, 0.036, 2, 6.133, 0.034, 2, 6.167, 0.033, 2, 6.2, 0.031, 2, 6.233, 0.03, 2, 6.267, 0.029, 2, 6.3, 0.028, 2, 6.333, 0.026, 2, 6.367, 0.025, 2, 6.4, 0.024, 2, 6.433, 0.024, 2, 6.467, 0.023, 2, 6.5, 0.022, 2, 6.533, 0.022, 2, 6.567, 0.022, 2, 6.6, 0.023, 2, 6.633, 0.024, 2, 6.667, 0.026, 2, 6.7, 0.029, 2, 6.733, 0.031, 2, 6.767, 0.034, 2, 6.8, 0.037, 2, 6.833, 0.039, 2, 6.867, 0.04, 2, 6.9, 0.041, 2, 6.933, 0.041, 2, 6.967, 0.04, 2, 7, 0.039, 2, 7.033, 0.037, 2, 7.067, 0.036, 2, 7.1, 0.034, 2, 7.133, 0.033, 2, 7.167, 0.032, 2, 7.2, 0.032, 2, 7.267, 0.032, 2, 7.3, 0.033, 2, 7.333, 0.033, 2, 7.367, 0.034, 2, 7.4, 0.035, 2, 7.433, 0.035, 2, 7.467, 0.036, 2, 7.5, 0.036, 2, 7.533, 0.036, 2, 7.567, 0.036, 2, 7.6, 0.036, 2, 7.633, 0.036, 2, 7.667, 0.035, 2, 7.7, 0.035, 2, 7.733, 0.035, 2, 7.767, 0.035, 2, 7.8, 0.034, 2, 7.833, 0.034, 2, 7.9, 0.034, 2, 7.933, 0.034, 2, 7.967, 0.035, 2, 8, 0.035]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 2, 0.033, 0.012, 2, 0.067, 0.042, 2, 0.1, 0.077, 2, 0.133, 0.107, 2, 0.167, 0.119, 2, 0.2, 0.109, 2, 0.233, 0.084, 2, 0.267, 0.051, 2, 0.3, 0.018, 2, 0.333, -0.007, 2, 0.367, -0.017, 2, 0.4, -0.014, 2, 0.433, -0.006, 2, 0.467, 0.005, 2, 0.5, 0.016, 2, 0.533, 0.024, 2, 0.567, 0.027, 2, 0.6, 0.025, 2, 0.633, 0.019, 2, 0.667, 0.011, 2, 0.7, 0, 2, 0.733, -0.013, 2, 0.767, -0.027, 2, 0.8, -0.041, 2, 0.833, -0.054, 2, 0.867, -0.067, 2, 0.9, -0.078, 2, 0.933, -0.087, 2, 0.967, -0.092, 2, 1, -0.094, 2, 1.033, -0.082, 2, 1.067, -0.048, 2, 1.1, 0, 2, 1.133, 0.054, 2, 1.167, 0.109, 2, 1.2, 0.156, 2, 1.233, 0.19, 2, 1.267, 0.203, 2, 1.3, 0.2, 2, 1.333, 0.192, 2, 1.367, 0.18, 2, 1.4, 0.164, 2, 1.433, 0.147, 2, 1.467, 0.129, 2, 1.5, 0.111, 2, 1.533, 0.094, 2, 1.567, 0.078, 2, 1.6, 0.066, 2, 1.633, 0.058, 2, 1.667, 0.055, 2, 1.7, 0.057, 2, 1.733, 0.062, 2, 1.767, 0.069, 2, 1.8, 0.077, 2, 1.833, 0.087, 2, 1.867, 0.096, 2, 1.9, 0.104, 2, 1.933, 0.112, 2, 1.967, 0.116, 2, 2, 0.118, 2, 2.033, 0.117, 2, 2.067, 0.115, 2, 2.1, 0.113, 2, 2.133, 0.109, 2, 2.167, 0.106, 2, 2.2, 0.103, 2, 2.233, 0.101, 2, 2.267, 0.101, 2, 2.3, 0.101, 2, 2.333, 0.103, 2, 2.367, 0.106, 2, 2.4, 0.109, 2, 2.433, 0.112, 2, 2.467, 0.115, 2, 2.5, 0.118, 2, 2.533, 0.12, 2, 2.567, 0.12, 2, 2.6, 0.118, 2, 2.633, 0.114, 2, 2.667, 0.106, 2, 2.7, 0.098, 2, 2.733, 0.09, 2, 2.767, 0.081, 2, 2.8, 0.074, 2, 2.833, 0.069, 2, 2.867, 0.068, 2, 2.9, 0.07, 2, 2.933, 0.076, 2, 2.967, 0.085, 2, 3, 0.095, 2, 3.033, 0.106, 2, 3.067, 0.117, 2, 3.1, 0.126, 2, 3.133, 0.132, 2, 3.167, 0.134, 2, 3.2, 0.132, 2, 3.233, 0.127, 2, 3.267, 0.119, 2, 3.3, 0.109, 2, 3.333, 0.096, 2, 3.367, 0.083, 2, 3.4, 0.069, 2, 3.433, 0.056, 2, 3.467, 0.042, 2, 3.5, 0.03, 2, 3.533, 0.02, 2, 3.567, 0.012, 2, 3.6, 0.006, 2, 3.633, 0.005, 2, 3.667, 0.005, 2, 3.7, 0.007, 2, 3.733, 0.009, 2, 3.767, 0.011, 2, 3.8, 0.014, 2, 3.833, 0.016, 2, 3.867, 0.017, 2, 3.9, 0.018, 2, 3.933, 0.018, 2, 3.967, 0.018, 2, 4, 0.018, 2, 4.033, 0.018, 2, 4.067, 0.018, 2, 4.1, 0.016, 2, 4.133, 0.014, 2, 4.167, 0.012, 2, 4.2, 0.009, 2, 4.233, 0.008, 2, 4.267, 0.007, 2, 4.3, 0.009, 2, 4.333, 0.014, 2, 4.367, 0.022, 2, 4.4, 0.032, 2, 4.433, 0.042, 2, 4.467, 0.052, 2, 4.5, 0.061, 2, 4.533, 0.069, 2, 4.567, 0.075, 2, 4.6, 0.076, 2, 4.633, 0.076, 2, 4.667, 0.073, 2, 4.7, 0.07, 2, 4.733, 0.067, 2, 4.767, 0.064, 2, 4.8, 0.061, 2, 4.833, 0.06, 2, 4.867, 0.061, 2, 4.9, 0.063, 2, 4.933, 0.065, 2, 4.967, 0.068, 2, 5, 0.071, 2, 5.033, 0.073, 2, 5.067, 0.075, 2, 5.1, 0.076, 2, 5.133, 0.075, 2, 5.167, 0.074, 2, 5.2, 0.073, 2, 5.233, 0.071, 2, 5.267, 0.069, 2, 5.3, 0.067, 2, 5.333, 0.065, 2, 5.367, 0.064, 2, 5.4, 0.064, 2, 5.433, 0.064, 2, 5.5, 0.064, 2, 5.533, 0.065, 2, 5.567, 0.066, 2, 5.633, 0.066, 2, 5.667, 0.066, 2, 5.7, 0.066, 2, 5.733, 0.065, 2, 5.767, 0.064, 2, 5.8, 0.064, 2, 5.833, 0.063, 2, 5.867, 0.062, 2, 5.9, 0.061, 2, 5.933, 0.059, 2, 5.967, 0.058, 2, 6, 0.057, 2, 6.033, 0.055, 2, 6.067, 0.054, 2, 6.1, 0.052, 2, 6.133, 0.051, 2, 6.167, 0.049, 2, 6.2, 0.048, 2, 6.233, 0.047, 2, 6.267, 0.045, 2, 6.3, 0.044, 2, 6.333, 0.043, 2, 6.367, 0.042, 2, 6.4, 0.041, 2, 6.433, 0.04, 2, 6.467, 0.039, 2, 6.5, 0.039, 2, 6.533, 0.039, 2, 6.567, 0.039, 2, 6.6, 0.039, 2, 6.633, 0.041, 2, 6.667, 0.044, 2, 6.7, 0.047, 2, 6.733, 0.05, 2, 6.767, 0.053, 2, 6.8, 0.056, 2, 6.833, 0.058, 2, 6.867, 0.059, 2, 6.9, 0.058, 2, 6.933, 0.057, 2, 6.967, 0.056, 2, 7, 0.054, 2, 7.033, 0.052, 2, 7.067, 0.051, 2, 7.1, 0.05, 2, 7.133, 0.05, 2, 7.2, 0.05, 2, 7.233, 0.05, 2, 7.267, 0.051, 2, 7.3, 0.052, 2, 7.333, 0.052, 2, 7.367, 0.053, 2, 7.4, 0.053, 2, 7.433, 0.054, 2, 7.5, 0.054, 2, 7.533, 0.053, 2, 7.567, 0.053, 2, 7.6, 0.053, 2, 7.633, 0.052, 2, 7.667, 0.052, 2, 7.7, 0.052, 2, 7.733, 0.052, 2, 7.8, 0.052, 2, 7.833, 0.052, 2, 7.867, 0.052, 2, 7.9, 0.052, 2, 7.933, 0.052, 2, 7.967, 0.053, 2, 8, 0.053]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.009, 2, 0.067, 0.03, 2, 0.1, 0.056, 2, 0.133, 0.077, 2, 0.167, 0.086, 2, 0.2, 0.082, 2, 0.233, 0.072, 2, 0.267, 0.058, 2, 0.3, 0.043, 2, 0.333, 0.027, 2, 0.367, 0.013, 2, 0.4, 0.003, 2, 0.433, -0.001, 2, 0.467, 0, 2, 0.5, 0.002, 2, 0.533, 0.005, 2, 0.567, 0.007, 2, 0.6, 0.009, 2, 0.633, 0.01, 2, 0.667, 0.008, 2, 0.7, 0.004, 2, 0.733, -0.003, 2, 0.767, -0.012, 2, 0.8, -0.022, 2, 0.833, -0.032, 2, 0.867, -0.041, 2, 0.9, -0.05, 2, 0.933, -0.057, 2, 0.967, -0.061, 2, 1, -0.063, 2, 1.033, -0.056, 2, 1.067, -0.038, 2, 1.1, -0.011, 2, 1.133, 0.022, 2, 1.167, 0.058, 2, 1.2, 0.093, 2, 1.233, 0.126, 2, 1.267, 0.154, 2, 1.3, 0.172, 2, 1.333, 0.179, 2, 1.367, 0.176, 2, 1.4, 0.169, 2, 1.433, 0.157, 2, 1.467, 0.143, 2, 1.5, 0.126, 2, 1.533, 0.109, 2, 1.567, 0.092, 2, 1.6, 0.076, 2, 1.633, 0.061, 2, 1.667, 0.05, 2, 1.7, 0.042, 2, 1.733, 0.039, 2, 1.767, 0.041, 2, 1.8, 0.047, 2, 1.833, 0.055, 2, 1.867, 0.065, 2, 1.9, 0.076, 2, 1.933, 0.087, 2, 1.967, 0.098, 2, 2, 0.108, 2, 2.033, 0.116, 2, 2.067, 0.122, 2, 2.1, 0.124, 2, 2.133, 0.123, 2, 2.167, 0.121, 2, 2.2, 0.118, 2, 2.233, 0.114, 2, 2.267, 0.11, 2, 2.3, 0.106, 2, 2.333, 0.102, 2, 2.367, 0.099, 2, 2.4, 0.097, 2, 2.433, 0.096, 2, 2.467, 0.097, 2, 2.5, 0.1, 2, 2.533, 0.103, 2, 2.567, 0.105, 2, 2.6, 0.106, 2, 2.633, 0.105, 2, 2.667, 0.101, 2, 2.7, 0.096, 2, 2.733, 0.089, 2, 2.767, 0.083, 2, 2.8, 0.078, 2, 2.833, 0.074, 2, 2.867, 0.072, 2, 2.9, 0.074, 2, 2.933, 0.079, 2, 2.967, 0.086, 2, 3, 0.095, 2, 3.033, 0.104, 2, 3.067, 0.113, 2, 3.1, 0.122, 2, 3.133, 0.129, 2, 3.167, 0.134, 2, 3.2, 0.136, 2, 3.233, 0.134, 2, 3.267, 0.128, 2, 3.3, 0.119, 2, 3.333, 0.108, 2, 3.367, 0.095, 2, 3.4, 0.08, 2, 3.433, 0.065, 2, 3.467, 0.05, 2, 3.5, 0.036, 2, 3.533, 0.023, 2, 3.567, 0.011, 2, 3.6, 0.002, 2, 3.633, -0.003, 2, 3.667, -0.005, 2, 3.7, -0.005, 2, 3.733, -0.002, 2, 3.767, 0.001, 2, 3.8, 0.005, 2, 3.833, 0.009, 2, 3.867, 0.014, 2, 3.9, 0.019, 2, 3.933, 0.023, 2, 3.967, 0.028, 2, 4, 0.031, 2, 4.033, 0.033, 2, 4.067, 0.034, 2, 4.1, 0.033, 2, 4.133, 0.03, 2, 4.167, 0.026, 2, 4.2, 0.022, 2, 4.233, 0.018, 2, 4.267, 0.015, 2, 4.3, 0.014, 2, 4.333, 0.015, 2, 4.367, 0.019, 2, 4.4, 0.024, 2, 4.433, 0.03, 2, 4.467, 0.037, 2, 4.5, 0.045, 2, 4.533, 0.052, 2, 4.567, 0.059, 2, 4.6, 0.066, 2, 4.633, 0.071, 2, 4.667, 0.074, 2, 4.7, 0.075, 2, 4.733, 0.075, 2, 4.767, 0.073, 2, 4.8, 0.071, 2, 4.833, 0.069, 2, 4.867, 0.066, 2, 4.9, 0.064, 2, 4.933, 0.063, 2, 4.967, 0.062, 2, 5, 0.062, 2, 5.033, 0.063, 2, 5.067, 0.064, 2, 5.1, 0.065, 2, 5.133, 0.066, 2, 5.167, 0.067, 2, 5.2, 0.067, 2, 5.233, 0.067, 2, 5.267, 0.067, 2, 5.3, 0.067, 2, 5.333, 0.067, 2, 5.367, 0.067, 2, 5.4, 0.066, 2, 5.433, 0.066, 2, 5.467, 0.065, 2, 5.5, 0.065, 2, 5.533, 0.064, 2, 5.567, 0.063, 2, 5.6, 0.063, 2, 5.633, 0.062, 2, 5.667, 0.061, 2, 5.7, 0.06, 2, 5.733, 0.059, 2, 5.767, 0.058, 2, 5.8, 0.057, 2, 5.833, 0.056, 2, 5.867, 0.055, 2, 5.9, 0.054, 2, 5.933, 0.053, 2, 5.967, 0.052, 2, 6, 0.051, 2, 6.033, 0.05, 2, 6.067, 0.049, 2, 6.1, 0.049, 2, 6.133, 0.048, 2, 6.167, 0.047, 2, 6.2, 0.046, 2, 6.233, 0.045, 2, 6.267, 0.044, 2, 6.3, 0.044, 2, 6.333, 0.043, 2, 6.367, 0.043, 2, 6.4, 0.042, 2, 6.433, 0.042, 2, 6.467, 0.042, 2, 6.5, 0.041, 2, 6.533, 0.041, 2, 6.567, 0.041, 2, 6.6, 0.042, 2, 6.633, 0.043, 2, 6.667, 0.045, 2, 6.7, 0.047, 2, 6.733, 0.049, 2, 6.767, 0.052, 2, 6.8, 0.054, 2, 6.833, 0.056, 2, 6.867, 0.057, 2, 6.9, 0.058, 2, 6.967, 0.057, 2, 7, 0.057, 2, 7.033, 0.056, 2, 7.067, 0.055, 2, 7.1, 0.054, 2, 7.133, 0.053, 2, 7.167, 0.052, 2, 7.2, 0.051, 2, 7.233, 0.05, 2, 7.267, 0.05, 2, 7.3, 0.05, 2, 7.333, 0.05, 2, 7.367, 0.051, 2, 7.4, 0.051, 2, 7.433, 0.052, 2, 7.467, 0.052, 2, 7.5, 0.053, 2, 7.533, 0.053, 2, 7.567, 0.053, 2, 7.6, 0.053, 2, 7.667, 0.053, 2, 7.7, 0.053, 2, 7.733, 0.053, 2, 7.767, 0.053, 2, 7.8, 0.053, 2, 7.833, 0.052, 2, 7.867, 0.052, 2, 7.9, 0.052, 2, 7.933, 0.052, 2, 8, 0.052]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.032, 2, 0.067, 0.107, 2, 0.1, 0.199, 2, 0.133, 0.274, 2, 0.167, 0.306, 2, 0.2, 0.292, 2, 0.233, 0.257, 2, 0.267, 0.206, 2, 0.3, 0.145, 2, 0.333, 0.083, 2, 0.367, 0.022, 2, 0.4, -0.029, 2, 0.433, -0.065, 2, 0.467, -0.078, 2, 0.5, -0.076, 2, 0.533, -0.074, 2, 0.567, -0.073, 2, 0.6, -0.077, 2, 0.633, -0.088, 2, 0.667, -0.104, 2, 0.7, -0.126, 2, 0.733, -0.15, 2, 0.767, -0.176, 2, 0.8, -0.204, 2, 0.833, -0.23, 2, 0.867, -0.255, 2, 0.9, -0.276, 2, 0.933, -0.293, 2, 0.967, -0.304, 2, 1, -0.308, 2, 1.033, -0.293, 2, 1.067, -0.253, 2, 1.1, -0.199, 2, 1.133, -0.141, 2, 1.167, -0.087, 2, 1.2, -0.047, 2, 1.233, -0.032, 2, 1.267, -0.044, 2, 1.3, -0.072, 2, 1.333, -0.107, 2, 1.367, -0.135, 2, 1.4, -0.147, 2, 1.433, -0.14, 2, 1.467, -0.122, 2, 1.5, -0.097, 2, 1.533, -0.071, 2, 1.567, -0.047, 2, 1.6, -0.029, 2, 1.633, -0.022, 2, 1.667, -0.025, 2, 1.7, -0.033, 2, 1.733, -0.04, 2, 1.767, -0.044, 2, 1.8, -0.043, 2, 1.833, -0.04, 2, 1.867, -0.036, 2, 1.9, -0.031, 2, 1.933, -0.025, 2, 1.967, -0.019, 2, 2, -0.013, 2, 2.033, -0.007, 2, 2.067, -0.002, 2, 2.1, 0.002, 2, 2.133, 0.005, 2, 2.167, 0.006, 2, 2.2, 0.006, 2, 2.233, 0.006, 2, 2.267, 0.008, 2, 2.3, 0.01, 2, 2.333, 0.013, 2, 2.367, 0.017, 2, 2.4, 0.02, 2, 2.433, 0.024, 2, 2.467, 0.027, 2, 2.5, 0.03, 2, 2.533, 0.033, 2, 2.567, 0.034, 2, 2.6, 0.035, 2, 2.633, 0.031, 2, 2.667, 0.021, 2, 2.7, 0.005, 2, 2.733, -0.014, 2, 2.767, -0.036, 2, 2.8, -0.059, 2, 2.833, -0.08, 2, 2.867, -0.1, 2, 2.9, -0.116, 2, 2.933, -0.126, 2, 2.967, -0.13, 2, 3, -0.126, 2, 3.033, -0.116, 2, 3.067, -0.1, 2, 3.1, -0.08, 2, 3.133, -0.057, 2, 3.167, -0.033, 2, 3.2, -0.007, 2, 3.233, 0.018, 2, 3.267, 0.041, 2, 3.3, 0.061, 2, 3.333, 0.076, 2, 3.367, 0.087, 2, 3.4, 0.091, 2, 3.433, 0.089, 2, 3.467, 0.086, 2, 3.5, 0.081, 2, 3.533, 0.075, 2, 3.567, 0.067, 2, 3.6, 0.059, 2, 3.633, 0.049, 2, 3.667, 0.039, 2, 3.7, 0.029, 2, 3.733, 0.018, 2, 3.767, 0.008, 2, 3.8, -0.001, 2, 3.833, -0.01, 2, 3.867, -0.017, 2, 3.9, -0.024, 2, 3.933, -0.029, 2, 3.967, -0.032, 2, 4, -0.033, 2, 4.033, -0.033, 2, 4.067, -0.035, 2, 4.1, -0.034, 2, 4.133, -0.037, 2, 4.167, -0.036, 2, 4.2, -0.039, 2, 4.233, -0.045, 2, 4.267, -0.048, 2, 4.3, -0.042, 2, 4.333, -0.026, 2, 4.367, -0.002, 2, 4.4, 0.025, 2, 4.433, 0.054, 2, 4.467, 0.082, 2, 4.5, 0.105, 2, 4.533, 0.121, 2, 4.567, 0.127, 2, 4.6, 0.125, 2, 4.633, 0.119, 2, 4.667, 0.112, 2, 4.7, 0.103, 2, 4.733, 0.096, 2, 4.767, 0.09, 2, 4.8, 0.088, 2, 4.833, 0.09, 2, 4.867, 0.095, 2, 4.9, 0.103, 2, 4.933, 0.112, 2, 4.967, 0.121, 2, 5, 0.128, 2, 5.033, 0.134, 2, 5.067, 0.136, 2, 5.1, 0.134, 2, 5.133, 0.13, 2, 5.167, 0.124, 2, 5.2, 0.118, 2, 5.233, 0.111, 2, 5.267, 0.105, 2, 5.3, 0.101, 2, 5.333, 0.1, 2, 5.367, 0.1, 2, 5.4, 0.101, 2, 5.433, 0.103, 2, 5.467, 0.106, 2, 5.5, 0.108, 2, 5.533, 0.109, 2, 5.567, 0.11, 2, 5.6, 0.109, 2, 5.633, 0.109, 2, 5.667, 0.107, 2, 5.7, 0.106, 2, 5.733, 0.104, 2, 5.767, 0.102, 2, 5.8, 0.099, 2, 5.833, 0.096, 2, 5.867, 0.093, 2, 5.9, 0.09, 2, 5.933, 0.086, 2, 5.967, 0.083, 2, 6, 0.079, 2, 6.033, 0.075, 2, 6.067, 0.071, 2, 6.1, 0.068, 2, 6.133, 0.064, 2, 6.167, 0.06, 2, 6.2, 0.057, 2, 6.233, 0.053, 2, 6.267, 0.05, 2, 6.3, 0.047, 2, 6.333, 0.044, 2, 6.367, 0.041, 2, 6.4, 0.039, 2, 6.433, 0.037, 2, 6.467, 0.036, 2, 6.5, 0.034, 2, 6.533, 0.034, 2, 6.567, 0.033, 2, 6.6, 0.036, 2, 6.633, 0.042, 2, 6.667, 0.05, 2, 6.7, 0.06, 2, 6.733, 0.069, 2, 6.767, 0.078, 2, 6.8, 0.084, 2, 6.833, 0.086, 2, 6.867, 0.085, 2, 6.9, 0.082, 2, 6.933, 0.079, 2, 6.967, 0.074, 2, 7, 0.07, 2, 7.033, 0.066, 2, 7.067, 0.064, 2, 7.1, 0.063, 2, 7.133, 0.063, 2, 7.167, 0.064, 2, 7.2, 0.066, 2, 7.233, 0.068, 2, 7.267, 0.07, 2, 7.3, 0.071, 2, 7.333, 0.072, 2, 7.367, 0.073, 2, 7.4, 0.073, 2, 7.433, 0.072, 2, 7.467, 0.072, 2, 7.5, 0.071, 2, 7.533, 0.07, 2, 7.567, 0.069, 2, 7.6, 0.069, 2, 7.633, 0.069, 2, 7.7, 0.069, 2, 7.733, 0.069, 2, 7.767, 0.069, 2, 7.8, 0.07, 2, 7.833, 0.07, 2, 7.867, 0.07, 2, 7.9, 0.07, 2, 7.967, 0.07, 2, 8, 0.07]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, -0.006, 2, 0.067, -0.021, 2, 0.1, -0.041, 2, 0.133, -0.062, 2, 0.167, -0.077, 2, 0.2, -0.083, 2, 0.233, -0.082, 2, 0.267, -0.079, 2, 0.3, -0.075, 2, 0.333, -0.069, 2, 0.367, -0.062, 2, 0.4, -0.054, 2, 0.433, -0.044, 2, 0.467, -0.034, 2, 0.5, -0.023, 2, 0.533, -0.012, 2, 0.567, 0, 2, 0.6, 0.012, 2, 0.633, 0.024, 2, 0.667, 0.035, 2, 0.7, 0.046, 2, 0.733, 0.057, 2, 0.767, 0.067, 2, 0.8, 0.077, 2, 0.833, 0.085, 2, 0.867, 0.092, 2, 0.9, 0.098, 2, 0.933, 0.103, 2, 0.967, 0.106, 2, 1, 0.106, 2, 1.033, 0.104, 2, 1.067, 0.098, 2, 1.1, 0.088, 2, 1.133, 0.076, 2, 1.167, 0.064, 2, 1.2, 0.051, 2, 1.233, 0.04, 2, 1.267, 0.03, 2, 1.3, 0.023, 2, 1.333, 0.021, 2, 1.367, 0.021, 2, 1.4, 0.021, 2, 1.433, 0.019, 2, 1.467, 0.018, 2, 1.5, 0.016, 2, 1.533, 0.015, 2, 1.567, 0.014, 2, 1.6, 0.015, 2, 1.633, 0.016, 2, 1.667, 0.017, 2, 1.7, 0.019, 2, 1.733, 0.02, 2, 1.767, 0.02, 2, 1.8, 0.02, 2, 1.833, 0.019, 2, 1.867, 0.017, 2, 1.9, 0.015, 2, 1.933, 0.013, 2, 1.967, 0.01, 2, 2, 0.007, 2, 2.033, 0.004, 2, 2.067, 0.001, 2, 2.1, -0.001, 2, 2.133, -0.003, 2, 2.167, -0.005, 2, 2.2, -0.006, 2, 2.233, -0.006, 2, 2.267, -0.006, 2, 2.3, -0.006, 2, 2.333, -0.006, 2, 2.367, -0.006, 2, 2.4, -0.007, 2, 2.467, -0.007, 2, 2.5, -0.008, 2, 2.533, -0.009, 2, 2.567, -0.009, 2, 2.6, -0.01, 2, 2.633, -0.009, 2, 2.667, -0.006, 2, 2.7, -0.002, 2, 2.733, 0.003, 2, 2.767, 0.009, 2, 2.8, 0.015, 2, 2.833, 0.021, 2, 2.867, 0.027, 2, 2.9, 0.032, 2, 2.933, 0.037, 2, 2.967, 0.039, 2, 3, 0.04, 2, 3.033, 0.039, 2, 3.067, 0.035, 2, 3.1, 0.029, 2, 3.133, 0.022, 2, 3.167, 0.013, 2, 3.2, 0.004, 2, 3.233, -0.006, 2, 3.267, -0.015, 2, 3.3, -0.025, 2, 3.333, -0.033, 2, 3.367, -0.041, 2, 3.4, -0.046, 2, 3.433, -0.05, 2, 3.467, -0.051, 2, 3.5, -0.05, 2, 3.533, -0.047, 2, 3.567, -0.043, 2, 3.6, -0.037, 2, 3.633, -0.03, 2, 3.667, -0.022, 2, 3.7, -0.014, 2, 3.733, -0.006, 2, 3.767, 0.001, 2, 3.8, 0.008, 2, 3.833, 0.014, 2, 3.867, 0.019, 2, 3.9, 0.022, 2, 3.933, 0.023, 2, 4, 0.023, 2, 4.033, 0.021, 2, 4.067, 0.019, 2, 4.1, 0.017, 2, 4.133, 0.014, 2, 4.167, 0.012, 2, 4.2, 0.01, 2, 4.233, 0.01, 2, 4.3, 0.009, 2, 4.333, 0.006, 2, 4.367, 0.002, 2, 4.4, -0.003, 2, 4.433, -0.009, 2, 4.467, -0.015, 2, 4.5, -0.022, 2, 4.533, -0.028, 2, 4.567, -0.034, 2, 4.6, -0.039, 2, 4.633, -0.043, 2, 4.667, -0.046, 2, 4.7, -0.046, 2, 4.733, -0.046, 2, 4.767, -0.045, 2, 4.8, -0.044, 2, 4.833, -0.042, 2, 4.867, -0.041, 2, 4.9, -0.039, 2, 4.933, -0.038, 2, 4.967, -0.038, 2, 5, -0.038, 2, 5.033, -0.038, 2, 5.067, -0.039, 2, 5.1, -0.039, 2, 5.133, -0.039, 2, 5.167, -0.04, 2, 5.2, -0.04, 2, 5.233, -0.04, 2, 5.267, -0.04, 2, 5.3, -0.04, 2, 5.333, -0.04, 2, 5.367, -0.039, 2, 5.4, -0.039, 2, 5.433, -0.038, 2, 5.467, -0.038, 2, 5.5, -0.037, 2, 5.533, -0.036, 2, 5.567, -0.036, 2, 5.6, -0.035, 2, 5.633, -0.034, 2, 5.667, -0.033, 2, 5.7, -0.032, 2, 5.733, -0.031, 2, 5.767, -0.03, 2, 5.8, -0.029, 2, 5.833, -0.028, 2, 5.867, -0.027, 2, 5.9, -0.026, 2, 5.933, -0.025, 2, 5.967, -0.024, 2, 6, -0.023, 2, 6.033, -0.022, 2, 6.067, -0.021, 2, 6.1, -0.02, 2, 6.133, -0.019, 2, 6.167, -0.018, 2, 6.2, -0.017, 2, 6.233, -0.016, 2, 6.267, -0.016, 2, 6.3, -0.015, 2, 6.333, -0.014, 2, 6.367, -0.014, 2, 6.4, -0.013, 2, 6.433, -0.013, 2, 6.467, -0.012, 2, 6.5, -0.012, 2, 6.533, -0.012, 2, 6.567, -0.012, 2, 6.6, -0.012, 2, 6.633, -0.014, 2, 6.667, -0.015, 2, 6.7, -0.017, 2, 6.733, -0.02, 2, 6.767, -0.022, 2, 6.8, -0.025, 2, 6.833, -0.027, 2, 6.867, -0.029, 2, 6.9, -0.03, 2, 6.933, -0.03, 2, 7, -0.03, 2, 7.033, -0.03, 2, 7.067, -0.029, 2, 7.1, -0.028, 2, 7.133, -0.027, 2, 7.167, -0.025, 2, 7.2, -0.024, 2, 7.233, -0.023, 2, 7.267, -0.022, 2, 7.3, -0.022, 2, 7.333, -0.021, 2, 7.367, -0.022, 2, 7.4, -0.022, 2, 7.433, -0.022, 2, 7.467, -0.023, 2, 7.5, -0.023, 2, 7.533, -0.024, 2, 7.567, -0.025, 2, 7.6, -0.025, 2, 7.633, -0.025, 2, 7.667, -0.026, 2, 7.7, -0.026, 2, 7.767, -0.026, 2, 7.8, -0.026, 2, 7.833, -0.025, 2, 7.867, -0.025, 2, 7.9, -0.025, 2, 7.933, -0.025, 2, 7.967, -0.024, 2, 8, -0.024]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh0", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh0", "Segments": [0, 0, 2, 0.033, 0.15, 2, 0.067, 0.507, 2, 0.1, 0.943, 2, 0.133, 1.301, 2, 0.167, 1.451, 2, 0.2, 1.338, 2, 0.233, 1.068, 2, 0.267, 0.739, 2, 0.3, 0.469, 2, 0.333, 0.356, 2, 0.367, 0.377, 2, 0.4, 0.431, 2, 0.433, 0.505, 2, 0.467, 0.584, 2, 0.5, 0.659, 2, 0.533, 0.713, 2, 0.567, 0.734, 2, 0.6, 0.698, 2, 0.633, 0.597, 2, 0.667, 0.441, 2, 0.7, 0.246, 2, 0.733, 0.013, 2, 0.767, -0.239, 2, 0.8, -0.501, 2, 0.833, -0.764, 2, 0.867, -1.015, 2, 0.9, -1.248, 2, 0.933, -1.443, 2, 0.967, -1.6, 2, 1, -1.701, 2, 1.033, -1.736, 2, 1.067, -1.619, 2, 1.1, -1.309, 2, 1.133, -0.87, 2, 1.167, -0.367, 2, 1.2, 0.135, 2, 1.233, 0.574, 2, 1.267, 0.884, 2, 1.3, 1.002, 2, 1.333, 0.992, 2, 1.367, 0.963, 2, 1.4, 0.92, 2, 1.433, 0.866, 2, 1.467, 0.803, 2, 1.5, 0.736, 2, 1.533, 0.665, 2, 1.567, 0.598, 2, 1.6, 0.535, 2, 1.633, 0.48, 2, 1.667, 0.438, 2, 1.7, 0.409, 2, 1.733, 0.399, 2, 1.767, 0.404, 2, 1.8, 0.419, 2, 1.833, 0.443, 2, 1.867, 0.475, 2, 1.9, 0.515, 2, 1.933, 0.561, 2, 1.967, 0.614, 2, 2, 0.67, 2, 2.033, 0.731, 2, 2.067, 0.795, 2, 2.1, 0.861, 2, 2.133, 0.931, 2, 2.167, 0.999, 2, 2.2, 1.068, 2, 2.233, 1.137, 2, 2.267, 1.203, 2, 2.3, 1.267, 2, 2.333, 1.328, 2, 2.367, 1.385, 2, 2.4, 1.437, 2, 2.433, 1.483, 2, 2.467, 1.523, 2, 2.5, 1.555, 2, 2.533, 1.579, 2, 2.567, 1.594, 2, 2.6, 1.599, 2, 2.633, 1.589, 2, 2.667, 1.563, 2, 2.7, 1.524, 2, 2.733, 1.478, 2, 2.767, 1.43, 2, 2.8, 1.385, 2, 2.833, 1.346, 2, 2.867, 1.319, 2, 2.9, 1.309, 2, 2.933, 1.32, 2, 2.967, 1.349, 2, 3, 1.389, 2, 3.033, 1.432, 2, 3.067, 1.472, 2, 3.1, 1.5, 2, 3.133, 1.512, 2, 3.167, 1.507, 2, 3.2, 1.493, 2, 3.233, 1.47, 2, 3.267, 1.44, 2, 3.3, 1.402, 2, 3.333, 1.357, 2, 3.367, 1.305, 2, 3.4, 1.249, 2, 3.433, 1.186, 2, 3.467, 1.119, 2, 3.5, 1.047, 2, 3.533, 0.973, 2, 3.567, 0.894, 2, 3.6, 0.815, 2, 3.633, 0.732, 2, 3.667, 0.649, 2, 3.7, 0.563, 2, 3.733, 0.479, 2, 3.767, 0.394, 2, 3.8, 0.311, 2, 3.833, 0.228, 2, 3.867, 0.148, 2, 3.9, 0.069, 2, 3.933, -0.005, 2, 3.967, -0.077, 2, 4, -0.143, 2, 4.033, -0.206, 2, 4.067, -0.263, 2, 4.1, -0.315, 2, 4.133, -0.36, 2, 4.167, -0.398, 2, 4.2, -0.428, 2, 4.233, -0.451, 2, 4.267, -0.465, 2, 4.3, -0.469, 2, 4.333, -0.467, 2, 4.367, -0.46, 2, 4.4, -0.448, 2, 4.433, -0.432, 2, 4.467, -0.412, 2, 4.5, -0.389, 2, 4.533, -0.362, 2, 4.567, -0.331, 2, 4.6, -0.298, 2, 4.633, -0.262, 2, 4.667, -0.223, 2, 4.7, -0.182, 2, 4.733, -0.139, 2, 4.767, -0.095, 2, 4.8, -0.049, 2, 4.833, -0.002, 2, 4.867, 0.047, 2, 4.9, 0.096, 2, 4.933, 0.146, 2, 4.967, 0.195, 2, 5, 0.245, 2, 5.033, 0.294, 2, 5.067, 0.344, 2, 5.1, 0.392, 2, 5.133, 0.439, 2, 5.167, 0.485, 2, 5.2, 0.53, 2, 5.233, 0.573, 2, 5.267, 0.614, 2, 5.3, 0.652, 2, 5.333, 0.688, 2, 5.367, 0.722, 2, 5.4, 0.752, 2, 5.433, 0.779, 2, 5.467, 0.803, 2, 5.5, 0.823, 2, 5.533, 0.839, 2, 5.567, 0.851, 2, 5.6, 0.858, 2, 5.633, 0.86, 2, 5.667, 0.857, 2, 5.7, 0.858, 2, 5.733, 0.856, 2, 5.767, 0.851, 2, 5.8, 0.842, 2, 5.833, 0.831, 2, 5.867, 0.817, 2, 5.9, 0.801, 2, 5.933, 0.783, 2, 5.967, 0.763, 2, 6, 0.742, 2, 6.033, 0.719, 2, 6.067, 0.695, 2, 6.1, 0.671, 2, 6.133, 0.646, 2, 6.167, 0.621, 2, 6.2, 0.597, 2, 6.233, 0.572, 2, 6.267, 0.548, 2, 6.3, 0.526, 2, 6.333, 0.504, 2, 6.367, 0.484, 2, 6.4, 0.466, 2, 6.433, 0.45, 2, 6.467, 0.436, 2, 6.5, 0.425, 2, 6.533, 0.417, 2, 6.567, 0.412, 2, 6.6, 0.41, 2, 6.633, 0.416, 2, 6.667, 0.432, 2, 6.7, 0.455, 2, 6.733, 0.482, 2, 6.767, 0.508, 2, 6.8, 0.531, 2, 6.833, 0.548, 2, 6.867, 0.554, 2, 6.933, 0.552, 2, 6.967, 0.548, 2, 7, 0.542, 2, 7.033, 0.535, 2, 7.067, 0.528, 2, 7.1, 0.521, 2, 7.133, 0.517, 2, 7.167, 0.515, 2, 7.2, 0.516, 2, 7.233, 0.517, 2, 7.267, 0.518, 2, 7.3, 0.52, 2, 7.333, 0.521, 2, 7.367, 0.523, 2, 7.4, 0.524, 2, 7.433, 0.525, 2, 7.467, 0.526, 2, 7.5, 0.526, 2, 7.533, 0.525, 2, 7.567, 0.525, 2, 7.6, 0.525, 2, 7.633, 0.524, 2, 7.667, 0.524, 2, 7.7, 0.523, 2, 7.733, 0.523, 2, 7.767, 0.523, 2, 7.833, 0.523, 2, 7.867, 0.523, 2, 7.9, 0.523, 2, 7.933, 0.524, 2, 7.967, 0.524, 2, 8, 0.524]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh0", "Segments": [0, 0, 2, 0.033, -0.124, 2, 0.067, -0.396, 2, 0.1, -0.669, 2, 0.133, -0.793, 2, 0.167, -0.623, 2, 0.2, -0.219, 2, 0.233, 0.273, 2, 0.267, 0.677, 2, 0.3, 0.846, 2, 0.333, 0.753, 2, 0.367, 0.528, 2, 0.4, 0.228, 2, 0.433, -0.071, 2, 0.467, -0.297, 2, 0.5, -0.39, 2, 0.533, -0.355, 2, 0.567, -0.264, 2, 0.6, -0.131, 2, 0.633, 0.026, 2, 0.667, 0.189, 2, 0.7, 0.346, 2, 0.733, 0.479, 2, 0.767, 0.57, 2, 0.8, 0.605, 2, 0.833, 0.559, 2, 0.867, 0.439, 2, 0.9, 0.256, 2, 0.933, 0.034, 2, 0.967, -0.218, 2, 1, -0.474, 2, 1.033, -0.726, 2, 1.067, -0.948, 2, 1.1, -1.131, 2, 1.133, -1.251, 2, 1.167, -1.296, 2, 1.2, -1.206, 2, 1.233, -0.967, 2, 1.267, -0.63, 2, 1.3, -0.243, 2, 1.333, 0.144, 2, 1.367, 0.482, 2, 1.4, 0.72, 2, 1.433, 0.811, 2, 1.467, 0.793, 2, 1.5, 0.741, 2, 1.533, 0.665, 2, 1.567, 0.566, 2, 1.6, 0.453, 2, 1.633, 0.333, 2, 1.667, 0.206, 2, 1.7, 0.085, 2, 1.733, -0.028, 2, 1.767, -0.126, 2, 1.8, -0.203, 2, 1.833, -0.255, 2, 1.867, -0.272, 2, 1.9, -0.26, 2, 1.933, -0.226, 2, 1.967, -0.177, 2, 2, -0.12, 2, 2.033, -0.06, 2, 2.067, -0.002, 2, 2.1, 0.046, 2, 2.133, 0.08, 2, 2.167, 0.093, 2, 2.2, 0.086, 2, 2.233, 0.069, 2, 2.267, 0.044, 2, 2.3, 0.015, 2, 2.333, -0.015, 2, 2.367, -0.045, 2, 2.4, -0.069, 2, 2.433, -0.087, 2, 2.467, -0.093, 2, 2.5, -0.084, 2, 2.533, -0.059, 2, 2.567, -0.024, 2, 2.6, 0.016, 2, 2.633, 0.057, 2, 2.667, 0.092, 2, 2.7, 0.116, 2, 2.733, 0.126, 2, 2.767, 0.122, 2, 2.8, 0.123, 2, 2.833, 0.108, 2, 2.867, 0.068, 2, 2.9, 0.012, 2, 2.933, -0.047, 2, 2.967, -0.102, 2, 3, -0.143, 2, 3.033, -0.158, 2, 3.067, -0.151, 2, 3.1, -0.131, 2, 3.133, -0.102, 2, 3.167, -0.063, 2, 3.2, -0.02, 2, 3.233, 0.027, 2, 3.267, 0.076, 2, 3.3, 0.123, 2, 3.333, 0.167, 2, 3.367, 0.205, 2, 3.4, 0.234, 2, 3.433, 0.254, 2, 3.467, 0.261, 2, 3.5, 0.248, 2, 3.533, 0.214, 2, 3.567, 0.164, 2, 3.6, 0.105, 2, 3.633, 0.044, 2, 3.667, -0.015, 2, 3.7, -0.064, 2, 3.733, -0.099, 2, 3.767, -0.112, 2, 3.8, -0.103, 2, 3.833, -0.081, 2, 3.867, -0.051, 2, 3.9, -0.019, 2, 3.933, 0.012, 2, 3.967, 0.034, 2, 4, 0.042, 2, 4.033, 0.038, 2, 4.067, 0.045, 2, 4.1, 0.043, 2, 4.133, 0.056, 2, 4.2, 0.064, 2, 4.233, 0.072, 2, 4.3, 0.045, 2, 4.333, -0.022, 2, 4.367, -0.112, 2, 4.4, -0.201, 2, 4.433, -0.268, 2, 4.467, -0.295, 2, 4.5, -0.278, 2, 4.533, -0.232, 2, 4.567, -0.167, 2, 4.6, -0.093, 2, 4.633, -0.019, 2, 4.667, 0.046, 2, 4.7, 0.092, 2, 4.733, 0.109, 2, 4.767, 0.1, 2, 4.8, 0.076, 2, 4.833, 0.042, 2, 4.867, 0.003, 2, 4.9, -0.035, 2, 4.933, -0.069, 2, 4.967, -0.093, 2, 5, -0.102, 2, 5.033, -0.096, 2, 5.067, -0.079, 2, 5.1, -0.054, 2, 5.133, -0.026, 2, 5.167, 0.002, 2, 5.2, 0.026, 2, 5.233, 0.043, 2, 5.267, 0.05, 2, 5.3, 0.046, 2, 5.333, 0.035, 2, 5.367, 0.02, 2, 5.4, 0.004, 2, 5.433, -0.012, 2, 5.467, -0.023, 2, 5.5, -0.027, 2, 5.533, -0.025, 2, 5.567, -0.022, 2, 5.6, -0.016, 2, 5.633, -0.009, 2, 5.667, -0.002, 2, 5.7, 0.006, 2, 5.733, 0.013, 2, 5.767, 0.018, 2, 5.8, 0.022, 2, 5.833, 0.024, 2, 5.867, 0.021, 2, 5.9, 0.024, 2, 5.933, 0.02, 2, 5.967, 0.021, 2, 6, 0.017, 2, 6.033, 0.03, 2, 6.067, 0.053, 2, 6.1, 0.065, 2, 6.133, 0.062, 2, 6.167, 0.054, 2, 6.2, 0.042, 2, 6.233, 0.03, 2, 6.267, 0.019, 2, 6.3, 0.011, 2, 6.333, 0.008, 2, 6.367, 0.011, 2, 6.4, 0.018, 2, 6.433, 0.028, 2, 6.467, 0.037, 2, 6.5, 0.045, 2, 6.533, 0.048, 2, 6.567, 0.038, 2, 6.6, 0.013, 2, 6.633, -0.019, 2, 6.667, -0.052, 2, 6.7, -0.077, 2, 6.733, -0.087, 2, 6.767, -0.082, 2, 6.8, -0.069, 2, 6.833, -0.051, 2, 6.867, -0.029, 2, 6.9, -0.007, 2, 6.933, 0.015, 2, 6.967, 0.033, 2, 7, 0.046, 2, 7.033, 0.051, 2, 7.067, 0.048, 2, 7.1, 0.04, 2, 7.133, 0.028, 2, 7.167, 0.015, 2, 7.2, 0.001, 2, 7.233, -0.01, 2, 7.267, -0.018, 2, 7.3, -0.022, 2, 7.333, -0.021, 2, 7.367, -0.018, 2, 7.4, -0.014, 2, 7.433, -0.009, 2, 7.467, -0.004, 2, 7.5, 0, 2, 7.533, 0.004, 2, 7.567, 0.007, 2, 7.6, 0.008, 2, 7.633, 0.008, 2, 7.667, 0.007, 2, 7.7, 0.005, 2, 7.733, 0.003, 2, 7.767, 0.002, 2, 7.8, 0, 2, 7.833, -0.001, 2, 7.867, -0.002, 2, 7.9, -0.003, 2, 7.933, -0.003, 2, 7.967, -0.002, 2, 8, -0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh0", "Segments": [0, 0, 2, 0.033, -0.045, 2, 0.067, -0.155, 2, 0.1, -0.301, 2, 0.133, -0.447, 2, 0.167, -0.557, 2, 0.2, -0.602, 2, 0.233, -0.527, 2, 0.267, -0.335, 2, 0.3, -0.068, 2, 0.333, 0.215, 2, 0.367, 0.483, 2, 0.4, 0.675, 2, 0.433, 0.75, 2, 0.467, 0.686, 2, 0.5, 0.521, 2, 0.533, 0.291, 2, 0.567, 0.048, 2, 0.6, -0.181, 2, 0.633, -0.346, 2, 0.667, -0.41, 2, 0.7, -0.347, 2, 0.733, -0.186, 2, 0.767, 0.037, 2, 0.8, 0.274, 2, 0.833, 0.498, 2, 0.867, 0.659, 2, 0.9, 0.721, 2, 0.933, 0.67, 2, 0.967, 0.533, 2, 1, 0.325, 2, 1.033, 0.072, 2, 1.067, -0.215, 2, 1.1, -0.505, 2, 1.133, -0.792, 2, 1.167, -1.045, 2, 1.2, -1.253, 2, 1.233, -1.39, 2, 1.267, -1.441, 2, 1.3, -1.331, 2, 1.333, -1.039, 2, 1.367, -0.627, 2, 1.4, -0.154, 2, 1.433, 0.318, 2, 1.467, 0.731, 2, 1.5, 1.022, 2, 1.533, 1.133, 2, 1.567, 1.087, 2, 1.6, 0.963, 2, 1.633, 0.775, 2, 1.667, 0.552, 2, 1.7, 0.309, 2, 1.733, 0.066, 2, 1.767, -0.158, 2, 1.8, -0.345, 2, 1.833, -0.47, 2, 1.867, -0.515, 2, 1.9, -0.499, 2, 1.933, -0.454, 2, 1.967, -0.385, 2, 2, -0.302, 2, 2.033, -0.208, 2, 2.067, -0.113, 2, 2.1, -0.019, 2, 2.133, 0.064, 2, 2.167, 0.132, 2, 2.2, 0.177, 2, 2.233, 0.194, 2, 2.267, 0.182, 2, 2.3, 0.151, 2, 2.333, 0.105, 2, 2.367, 0.051, 2, 2.4, -0.005, 2, 2.433, -0.059, 2, 2.467, -0.105, 2, 2.5, -0.137, 2, 2.533, -0.149, 2, 2.567, -0.139, 2, 2.6, -0.115, 2, 2.633, -0.077, 2, 2.667, -0.032, 2, 2.7, 0.016, 2, 2.733, 0.065, 2, 2.767, 0.11, 2, 2.8, 0.147, 2, 2.833, 0.172, 2, 2.867, 0.181, 2, 2.9, 0.165, 2, 2.933, 0.123, 2, 2.967, 0.063, 2, 3, -0.006, 2, 3.033, -0.075, 2, 3.067, -0.135, 2, 3.1, -0.178, 2, 3.133, -0.194, 2, 3.167, -0.181, 2, 3.2, -0.147, 2, 3.233, -0.094, 2, 3.267, -0.031, 2, 3.3, 0.041, 2, 3.333, 0.114, 2, 3.367, 0.186, 2, 3.4, 0.249, 2, 3.433, 0.302, 2, 3.467, 0.336, 2, 3.5, 0.349, 2, 3.533, 0.334, 2, 3.567, 0.293, 2, 3.6, 0.232, 2, 3.633, 0.159, 2, 3.667, 0.08, 2, 3.7, 0, 2, 3.733, -0.072, 2, 3.767, -0.134, 2, 3.8, -0.174, 2, 3.833, -0.189, 2, 3.867, -0.181, 2, 3.9, -0.159, 2, 3.933, -0.126, 2, 3.967, -0.086, 2, 4, -0.042, 2, 4.033, 0.001, 2, 4.067, 0.041, 2, 4.1, 0.074, 2, 4.133, 0.096, 2, 4.167, 0.105, 2, 4.2, 0.096, 2, 4.233, 0.073, 2, 4.267, 0.038, 2, 4.3, -0.006, 2, 4.333, -0.055, 2, 4.367, -0.108, 2, 4.4, -0.161, 2, 4.433, -0.21, 2, 4.467, -0.254, 2, 4.5, -0.289, 2, 4.533, -0.312, 2, 4.567, -0.32, 2, 4.6, -0.303, 2, 4.633, -0.257, 2, 4.667, -0.191, 2, 4.7, -0.112, 2, 4.733, -0.031, 2, 4.767, 0.048, 2, 4.8, 0.114, 2, 4.833, 0.16, 2, 4.867, 0.177, 2, 4.9, 0.163, 2, 4.933, 0.127, 2, 4.967, 0.075, 2, 5, 0.016, 2, 5.033, -0.043, 2, 5.067, -0.094, 2, 5.1, -0.13, 2, 5.133, -0.144, 2, 5.167, -0.135, 2, 5.2, -0.109, 2, 5.233, -0.073, 2, 5.267, -0.032, 2, 5.3, 0.009, 2, 5.333, 0.045, 2, 5.367, 0.07, 2, 5.4, 0.08, 2, 5.433, 0.075, 2, 5.467, 0.061, 2, 5.5, 0.041, 2, 5.533, 0.019, 2, 5.567, -0.004, 2, 5.6, -0.024, 2, 5.633, -0.037, 2, 5.667, -0.043, 2, 5.7, -0.04, 2, 5.733, -0.032, 2, 5.767, -0.022, 2, 5.8, -0.009, 2, 5.833, 0.005, 2, 5.867, 0.017, 2, 5.9, 0.028, 2, 5.933, 0.036, 2, 5.967, 0.038, 2, 6, 0.032, 2, 6.033, 0.021, 2, 6.067, 0.014, 2, 6.1, 0.017, 2, 6.133, 0.022, 2, 6.167, 0.029, 2, 6.2, 0.035, 2, 6.233, 0.037, 2, 6.267, 0.036, 2, 6.3, 0.031, 2, 6.333, 0.025, 2, 6.367, 0.019, 2, 6.4, 0.014, 2, 6.433, 0.012, 2, 6.467, 0.015, 2, 6.5, 0.021, 2, 6.533, 0.028, 2, 6.567, 0.034, 2, 6.6, 0.037, 2, 6.633, 0.031, 2, 6.667, 0.017, 2, 6.7, -0.004, 2, 6.733, -0.028, 2, 6.767, -0.051, 2, 6.8, -0.072, 2, 6.833, -0.086, 2, 6.867, -0.092, 2, 6.9, -0.085, 2, 6.933, -0.066, 2, 6.967, -0.039, 2, 7, -0.008, 2, 7.033, 0.022, 2, 7.067, 0.049, 2, 7.1, 0.068, 2, 7.133, 0.075, 2, 7.167, 0.071, 2, 7.2, 0.061, 2, 7.233, 0.045, 2, 7.267, 0.027, 2, 7.3, 0.008, 2, 7.333, -0.011, 2, 7.367, -0.026, 2, 7.4, -0.037, 2, 7.433, -0.041, 2, 7.467, -0.038, 2, 7.5, -0.032, 2, 7.533, -0.022, 2, 7.567, -0.011, 2, 7.6, 0, 2, 7.633, 0.009, 2, 7.667, 0.016, 2, 7.7, 0.019, 2, 7.733, 0.018, 2, 7.767, 0.015, 2, 7.8, 0.012, 2, 7.833, 0.008, 2, 7.867, 0.003, 2, 7.9, -0.001, 2, 7.933, -0.004, 2, 7.967, -0.007, 2, 8, -0.008]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh0", "Segments": [0, 0.001, 2, 0.033, -0.022, 2, 0.067, -0.082, 2, 0.1, -0.167, 2, 0.133, -0.264, 2, 0.167, -0.361, 2, 0.2, -0.446, 2, 0.233, -0.506, 2, 0.267, -0.529, 2, 0.3, -0.471, 2, 0.333, -0.319, 2, 0.367, -0.104, 2, 0.4, 0.143, 2, 0.433, 0.389, 2, 0.467, 0.604, 2, 0.5, 0.756, 2, 0.533, 0.814, 2, 0.567, 0.742, 2, 0.6, 0.557, 2, 0.633, 0.299, 2, 0.667, 0.027, 2, 0.7, -0.231, 2, 0.733, -0.416, 2, 0.767, -0.488, 2, 0.8, -0.43, 2, 0.833, -0.277, 2, 0.867, -0.06, 2, 0.9, 0.188, 2, 0.933, 0.435, 2, 0.967, 0.652, 2, 1, 0.805, 2, 1.033, 0.863, 2, 1.067, 0.775, 2, 1.1, 0.539, 2, 1.133, 0.197, 2, 1.167, -0.208, 2, 1.2, -0.626, 2, 1.233, -1.031, 2, 1.267, -1.372, 2, 1.3, -1.608, 2, 1.333, -1.696, 2, 1.367, -1.583, 2, 1.4, -1.281, 2, 1.433, -0.843, 2, 1.467, -0.324, 2, 1.5, 0.212, 2, 1.533, 0.731, 2, 1.567, 1.169, 2, 1.6, 1.472, 2, 1.633, 1.585, 2, 1.667, 1.513, 2, 1.7, 1.32, 2, 1.733, 1.028, 2, 1.767, 0.681, 2, 1.8, 0.303, 2, 1.833, -0.075, 2, 1.867, -0.423, 2, 1.9, -0.714, 2, 1.933, -0.908, 2, 1.967, -0.979, 2, 2, -0.94, 2, 2.033, -0.835, 2, 2.067, -0.676, 2, 2.1, -0.487, 2, 2.133, -0.281, 2, 2.167, -0.075, 2, 2.2, 0.115, 2, 2.233, 0.273, 2, 2.267, 0.379, 2, 2.3, 0.418, 2, 2.333, 0.399, 2, 2.367, 0.348, 2, 2.4, 0.27, 2, 2.433, 0.178, 2, 2.467, 0.079, 2, 2.5, -0.021, 2, 2.533, -0.113, 2, 2.567, -0.191, 2, 2.6, -0.242, 2, 2.633, -0.261, 2, 2.667, -0.243, 2, 2.7, -0.195, 2, 2.733, -0.126, 2, 2.767, -0.044, 2, 2.8, 0.04, 2, 2.833, 0.122, 2, 2.867, 0.191, 2, 2.9, 0.238, 2, 2.933, 0.256, 2, 2.967, 0.238, 2, 3, 0.191, 2, 3.033, 0.122, 2, 3.067, 0.041, 2, 3.1, -0.043, 2, 3.133, -0.124, 2, 3.167, -0.193, 2, 3.2, -0.241, 2, 3.233, -0.258, 2, 3.267, -0.238, 2, 3.3, -0.184, 2, 3.333, -0.101, 2, 3.367, -0.003, 2, 3.4, 0.103, 2, 3.433, 0.21, 2, 3.467, 0.308, 2, 3.5, 0.39, 2, 3.533, 0.445, 2, 3.567, 0.465, 2, 3.6, 0.447, 2, 3.633, 0.399, 2, 3.667, 0.325, 2, 3.7, 0.236, 2, 3.733, 0.135, 2, 3.767, 0.032, 2, 3.8, -0.069, 2, 3.833, -0.158, 2, 3.867, -0.232, 2, 3.9, -0.28, 2, 3.933, -0.298, 2, 3.967, -0.284, 2, 4, -0.247, 2, 4.033, -0.192, 2, 4.067, -0.125, 2, 4.1, -0.053, 2, 4.133, 0.019, 2, 4.167, 0.085, 2, 4.2, 0.141, 2, 4.233, 0.178, 2, 4.267, 0.192, 2, 4.3, 0.178, 2, 4.333, 0.143, 2, 4.367, 0.089, 2, 4.4, 0.023, 2, 4.433, -0.051, 2, 4.467, -0.126, 2, 4.5, -0.201, 2, 4.533, -0.266, 2, 4.567, -0.32, 2, 4.6, -0.356, 2, 4.633, -0.369, 2, 4.667, -0.351, 2, 4.7, -0.303, 2, 4.733, -0.231, 2, 4.767, -0.145, 2, 4.8, -0.052, 2, 4.833, 0.042, 2, 4.867, 0.128, 2, 4.9, 0.2, 2, 4.933, 0.248, 2, 4.967, 0.265, 2, 5, 0.245, 2, 5.033, 0.191, 2, 5.067, 0.114, 2, 5.1, 0.026, 2, 5.133, -0.062, 2, 5.167, -0.138, 2, 5.2, -0.192, 2, 5.233, -0.213, 2, 5.267, -0.198, 2, 5.3, -0.159, 2, 5.333, -0.104, 2, 5.367, -0.041, 2, 5.4, 0.022, 2, 5.433, 0.077, 2, 5.467, 0.116, 2, 5.5, 0.131, 2, 5.533, 0.122, 2, 5.567, 0.099, 2, 5.6, 0.066, 2, 5.633, 0.029, 2, 5.667, -0.008, 2, 5.7, -0.041, 2, 5.733, -0.064, 2, 5.767, -0.072, 2, 5.8, -0.068, 2, 5.833, -0.056, 2, 5.867, -0.038, 2, 5.9, -0.018, 2, 5.933, 0.004, 2, 5.967, 0.024, 2, 6, 0.042, 2, 6.033, 0.054, 2, 6.067, 0.058, 2, 6.1, 0.057, 2, 6.133, 0.055, 2, 6.167, 0.052, 2, 6.2, 0.048, 2, 6.233, 0.044, 2, 6.267, 0.039, 2, 6.3, 0.033, 2, 6.333, 0.029, 2, 6.367, 0.024, 2, 6.4, 0.02, 2, 6.433, 0.017, 2, 6.467, 0.015, 2, 6.5, 0.014, 2, 6.533, 0.016, 2, 6.567, 0.019, 2, 6.6, 0.024, 2, 6.633, 0.028, 2, 6.667, 0.029, 2, 6.7, 0.024, 2, 6.733, 0.009, 2, 6.767, -0.012, 2, 6.8, -0.036, 2, 6.833, -0.06, 2, 6.867, -0.08, 2, 6.9, -0.095, 2, 6.933, -0.101, 2, 6.967, -0.094, 2, 7, -0.075, 2, 7.033, -0.048, 2, 7.067, -0.016, 2, 7.1, 0.018, 2, 7.133, 0.05, 2, 7.167, 0.077, 2, 7.2, 0.096, 2, 7.233, 0.103, 2, 7.267, 0.097, 2, 7.3, 0.081, 2, 7.333, 0.058, 2, 7.367, 0.031, 2, 7.4, 0.003, 2, 7.433, -0.024, 2, 7.467, -0.047, 2, 7.5, -0.063, 2, 7.533, -0.069, 2, 7.567, -0.065, 2, 7.6, -0.055, 2, 7.633, -0.041, 2, 7.667, -0.025, 2, 7.7, -0.007, 2, 7.733, 0.009, 2, 7.767, 0.023, 2, 7.8, 0.033, 2, 7.833, 0.037, 2, 7.867, 0.035, 2, 7.9, 0.03, 2, 7.933, 0.023, 2, 7.967, 0.014, 2, 8, 0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1", "Segments": [0, 0, 2, 0.033, 0.15, 2, 0.067, 0.507, 2, 0.1, 0.943, 2, 0.133, 1.301, 2, 0.167, 1.451, 2, 0.2, 1.338, 2, 0.233, 1.068, 2, 0.267, 0.739, 2, 0.3, 0.469, 2, 0.333, 0.356, 2, 0.367, 0.377, 2, 0.4, 0.431, 2, 0.433, 0.505, 2, 0.467, 0.584, 2, 0.5, 0.659, 2, 0.533, 0.713, 2, 0.567, 0.734, 2, 0.6, 0.698, 2, 0.633, 0.597, 2, 0.667, 0.441, 2, 0.7, 0.246, 2, 0.733, 0.013, 2, 0.767, -0.239, 2, 0.8, -0.501, 2, 0.833, -0.764, 2, 0.867, -1.015, 2, 0.9, -1.248, 2, 0.933, -1.443, 2, 0.967, -1.6, 2, 1, -1.701, 2, 1.033, -1.736, 2, 1.067, -1.619, 2, 1.1, -1.309, 2, 1.133, -0.87, 2, 1.167, -0.367, 2, 1.2, 0.135, 2, 1.233, 0.574, 2, 1.267, 0.884, 2, 1.3, 1.002, 2, 1.333, 0.992, 2, 1.367, 0.963, 2, 1.4, 0.92, 2, 1.433, 0.866, 2, 1.467, 0.803, 2, 1.5, 0.736, 2, 1.533, 0.665, 2, 1.567, 0.598, 2, 1.6, 0.535, 2, 1.633, 0.48, 2, 1.667, 0.438, 2, 1.7, 0.409, 2, 1.733, 0.399, 2, 1.767, 0.404, 2, 1.8, 0.419, 2, 1.833, 0.443, 2, 1.867, 0.475, 2, 1.9, 0.515, 2, 1.933, 0.561, 2, 1.967, 0.614, 2, 2, 0.67, 2, 2.033, 0.731, 2, 2.067, 0.795, 2, 2.1, 0.861, 2, 2.133, 0.931, 2, 2.167, 0.999, 2, 2.2, 1.068, 2, 2.233, 1.137, 2, 2.267, 1.203, 2, 2.3, 1.267, 2, 2.333, 1.328, 2, 2.367, 1.385, 2, 2.4, 1.437, 2, 2.433, 1.483, 2, 2.467, 1.523, 2, 2.5, 1.555, 2, 2.533, 1.579, 2, 2.567, 1.594, 2, 2.6, 1.599, 2, 2.633, 1.589, 2, 2.667, 1.563, 2, 2.7, 1.524, 2, 2.733, 1.478, 2, 2.767, 1.43, 2, 2.8, 1.385, 2, 2.833, 1.346, 2, 2.867, 1.319, 2, 2.9, 1.309, 2, 2.933, 1.32, 2, 2.967, 1.349, 2, 3, 1.389, 2, 3.033, 1.432, 2, 3.067, 1.472, 2, 3.1, 1.5, 2, 3.133, 1.512, 2, 3.167, 1.507, 2, 3.2, 1.493, 2, 3.233, 1.47, 2, 3.267, 1.44, 2, 3.3, 1.402, 2, 3.333, 1.357, 2, 3.367, 1.305, 2, 3.4, 1.249, 2, 3.433, 1.186, 2, 3.467, 1.119, 2, 3.5, 1.047, 2, 3.533, 0.973, 2, 3.567, 0.894, 2, 3.6, 0.815, 2, 3.633, 0.732, 2, 3.667, 0.649, 2, 3.7, 0.563, 2, 3.733, 0.479, 2, 3.767, 0.394, 2, 3.8, 0.311, 2, 3.833, 0.228, 2, 3.867, 0.148, 2, 3.9, 0.069, 2, 3.933, -0.005, 2, 3.967, -0.077, 2, 4, -0.143, 2, 4.033, -0.206, 2, 4.067, -0.263, 2, 4.1, -0.315, 2, 4.133, -0.36, 2, 4.167, -0.398, 2, 4.2, -0.428, 2, 4.233, -0.451, 2, 4.267, -0.465, 2, 4.3, -0.469, 2, 4.333, -0.467, 2, 4.367, -0.46, 2, 4.4, -0.448, 2, 4.433, -0.432, 2, 4.467, -0.412, 2, 4.5, -0.389, 2, 4.533, -0.362, 2, 4.567, -0.331, 2, 4.6, -0.298, 2, 4.633, -0.262, 2, 4.667, -0.223, 2, 4.7, -0.182, 2, 4.733, -0.139, 2, 4.767, -0.095, 2, 4.8, -0.049, 2, 4.833, -0.002, 2, 4.867, 0.047, 2, 4.9, 0.096, 2, 4.933, 0.146, 2, 4.967, 0.195, 2, 5, 0.245, 2, 5.033, 0.294, 2, 5.067, 0.344, 2, 5.1, 0.392, 2, 5.133, 0.439, 2, 5.167, 0.485, 2, 5.2, 0.53, 2, 5.233, 0.573, 2, 5.267, 0.614, 2, 5.3, 0.652, 2, 5.333, 0.688, 2, 5.367, 0.722, 2, 5.4, 0.752, 2, 5.433, 0.779, 2, 5.467, 0.803, 2, 5.5, 0.823, 2, 5.533, 0.839, 2, 5.567, 0.851, 2, 5.6, 0.858, 2, 5.633, 0.86, 2, 5.667, 0.857, 2, 5.7, 0.858, 2, 5.733, 0.856, 2, 5.767, 0.851, 2, 5.8, 0.842, 2, 5.833, 0.831, 2, 5.867, 0.817, 2, 5.9, 0.801, 2, 5.933, 0.783, 2, 5.967, 0.763, 2, 6, 0.742, 2, 6.033, 0.719, 2, 6.067, 0.695, 2, 6.1, 0.671, 2, 6.133, 0.646, 2, 6.167, 0.621, 2, 6.2, 0.597, 2, 6.233, 0.572, 2, 6.267, 0.548, 2, 6.3, 0.526, 2, 6.333, 0.504, 2, 6.367, 0.484, 2, 6.4, 0.466, 2, 6.433, 0.45, 2, 6.467, 0.436, 2, 6.5, 0.425, 2, 6.533, 0.417, 2, 6.567, 0.412, 2, 6.6, 0.41, 2, 6.633, 0.416, 2, 6.667, 0.432, 2, 6.7, 0.455, 2, 6.733, 0.482, 2, 6.767, 0.508, 2, 6.8, 0.531, 2, 6.833, 0.548, 2, 6.867, 0.554, 2, 6.933, 0.552, 2, 6.967, 0.548, 2, 7, 0.542, 2, 7.033, 0.535, 2, 7.067, 0.528, 2, 7.1, 0.521, 2, 7.133, 0.517, 2, 7.167, 0.515, 2, 7.2, 0.516, 2, 7.233, 0.517, 2, 7.267, 0.518, 2, 7.3, 0.52, 2, 7.333, 0.521, 2, 7.367, 0.523, 2, 7.4, 0.524, 2, 7.433, 0.525, 2, 7.467, 0.526, 2, 7.5, 0.526, 2, 7.533, 0.525, 2, 7.567, 0.525, 2, 7.6, 0.525, 2, 7.633, 0.524, 2, 7.667, 0.524, 2, 7.7, 0.523, 2, 7.733, 0.523, 2, 7.767, 0.523, 2, 7.833, 0.523, 2, 7.867, 0.523, 2, 7.9, 0.523, 2, 7.933, 0.524, 2, 7.967, 0.524, 2, 8, 0.524]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1", "Segments": [0, 0, 2, 0.033, -0.124, 2, 0.067, -0.396, 2, 0.1, -0.669, 2, 0.133, -0.793, 2, 0.167, -0.623, 2, 0.2, -0.219, 2, 0.233, 0.273, 2, 0.267, 0.677, 2, 0.3, 0.846, 2, 0.333, 0.753, 2, 0.367, 0.528, 2, 0.4, 0.228, 2, 0.433, -0.071, 2, 0.467, -0.297, 2, 0.5, -0.39, 2, 0.533, -0.355, 2, 0.567, -0.264, 2, 0.6, -0.131, 2, 0.633, 0.026, 2, 0.667, 0.189, 2, 0.7, 0.346, 2, 0.733, 0.479, 2, 0.767, 0.57, 2, 0.8, 0.605, 2, 0.833, 0.559, 2, 0.867, 0.439, 2, 0.9, 0.256, 2, 0.933, 0.034, 2, 0.967, -0.218, 2, 1, -0.474, 2, 1.033, -0.726, 2, 1.067, -0.948, 2, 1.1, -1.131, 2, 1.133, -1.251, 2, 1.167, -1.296, 2, 1.2, -1.206, 2, 1.233, -0.967, 2, 1.267, -0.63, 2, 1.3, -0.243, 2, 1.333, 0.144, 2, 1.367, 0.482, 2, 1.4, 0.72, 2, 1.433, 0.811, 2, 1.467, 0.793, 2, 1.5, 0.741, 2, 1.533, 0.665, 2, 1.567, 0.566, 2, 1.6, 0.453, 2, 1.633, 0.333, 2, 1.667, 0.206, 2, 1.7, 0.085, 2, 1.733, -0.028, 2, 1.767, -0.126, 2, 1.8, -0.203, 2, 1.833, -0.255, 2, 1.867, -0.272, 2, 1.9, -0.26, 2, 1.933, -0.226, 2, 1.967, -0.177, 2, 2, -0.12, 2, 2.033, -0.06, 2, 2.067, -0.002, 2, 2.1, 0.046, 2, 2.133, 0.08, 2, 2.167, 0.093, 2, 2.2, 0.086, 2, 2.233, 0.069, 2, 2.267, 0.044, 2, 2.3, 0.015, 2, 2.333, -0.015, 2, 2.367, -0.045, 2, 2.4, -0.069, 2, 2.433, -0.087, 2, 2.467, -0.093, 2, 2.5, -0.084, 2, 2.533, -0.059, 2, 2.567, -0.024, 2, 2.6, 0.016, 2, 2.633, 0.057, 2, 2.667, 0.092, 2, 2.7, 0.116, 2, 2.733, 0.126, 2, 2.767, 0.122, 2, 2.8, 0.123, 2, 2.833, 0.108, 2, 2.867, 0.068, 2, 2.9, 0.012, 2, 2.933, -0.047, 2, 2.967, -0.102, 2, 3, -0.143, 2, 3.033, -0.158, 2, 3.067, -0.151, 2, 3.1, -0.131, 2, 3.133, -0.102, 2, 3.167, -0.063, 2, 3.2, -0.02, 2, 3.233, 0.027, 2, 3.267, 0.076, 2, 3.3, 0.123, 2, 3.333, 0.167, 2, 3.367, 0.205, 2, 3.4, 0.234, 2, 3.433, 0.254, 2, 3.467, 0.261, 2, 3.5, 0.248, 2, 3.533, 0.214, 2, 3.567, 0.164, 2, 3.6, 0.105, 2, 3.633, 0.044, 2, 3.667, -0.015, 2, 3.7, -0.064, 2, 3.733, -0.099, 2, 3.767, -0.112, 2, 3.8, -0.103, 2, 3.833, -0.081, 2, 3.867, -0.051, 2, 3.9, -0.019, 2, 3.933, 0.012, 2, 3.967, 0.034, 2, 4, 0.042, 2, 4.033, 0.038, 2, 4.067, 0.045, 2, 4.1, 0.043, 2, 4.133, 0.056, 2, 4.2, 0.064, 2, 4.233, 0.072, 2, 4.3, 0.045, 2, 4.333, -0.022, 2, 4.367, -0.112, 2, 4.4, -0.201, 2, 4.433, -0.268, 2, 4.467, -0.295, 2, 4.5, -0.278, 2, 4.533, -0.232, 2, 4.567, -0.167, 2, 4.6, -0.093, 2, 4.633, -0.019, 2, 4.667, 0.046, 2, 4.7, 0.092, 2, 4.733, 0.109, 2, 4.767, 0.1, 2, 4.8, 0.076, 2, 4.833, 0.042, 2, 4.867, 0.003, 2, 4.9, -0.035, 2, 4.933, -0.069, 2, 4.967, -0.093, 2, 5, -0.102, 2, 5.033, -0.096, 2, 5.067, -0.079, 2, 5.1, -0.054, 2, 5.133, -0.026, 2, 5.167, 0.002, 2, 5.2, 0.026, 2, 5.233, 0.043, 2, 5.267, 0.05, 2, 5.3, 0.046, 2, 5.333, 0.035, 2, 5.367, 0.02, 2, 5.4, 0.004, 2, 5.433, -0.012, 2, 5.467, -0.023, 2, 5.5, -0.027, 2, 5.533, -0.025, 2, 5.567, -0.022, 2, 5.6, -0.016, 2, 5.633, -0.009, 2, 5.667, -0.002, 2, 5.7, 0.006, 2, 5.733, 0.013, 2, 5.767, 0.018, 2, 5.8, 0.022, 2, 5.833, 0.024, 2, 5.867, 0.021, 2, 5.9, 0.024, 2, 5.933, 0.02, 2, 5.967, 0.021, 2, 6, 0.017, 2, 6.033, 0.03, 2, 6.067, 0.053, 2, 6.1, 0.065, 2, 6.133, 0.062, 2, 6.167, 0.054, 2, 6.2, 0.042, 2, 6.233, 0.03, 2, 6.267, 0.019, 2, 6.3, 0.011, 2, 6.333, 0.008, 2, 6.367, 0.011, 2, 6.4, 0.018, 2, 6.433, 0.028, 2, 6.467, 0.037, 2, 6.5, 0.045, 2, 6.533, 0.048, 2, 6.567, 0.038, 2, 6.6, 0.013, 2, 6.633, -0.019, 2, 6.667, -0.052, 2, 6.7, -0.077, 2, 6.733, -0.087, 2, 6.767, -0.082, 2, 6.8, -0.069, 2, 6.833, -0.051, 2, 6.867, -0.029, 2, 6.9, -0.007, 2, 6.933, 0.015, 2, 6.967, 0.033, 2, 7, 0.046, 2, 7.033, 0.051, 2, 7.067, 0.048, 2, 7.1, 0.04, 2, 7.133, 0.028, 2, 7.167, 0.015, 2, 7.2, 0.001, 2, 7.233, -0.01, 2, 7.267, -0.018, 2, 7.3, -0.022, 2, 7.333, -0.021, 2, 7.367, -0.018, 2, 7.4, -0.014, 2, 7.433, -0.009, 2, 7.467, -0.004, 2, 7.5, 0, 2, 7.533, 0.004, 2, 7.567, 0.007, 2, 7.6, 0.008, 2, 7.633, 0.008, 2, 7.667, 0.007, 2, 7.7, 0.005, 2, 7.733, 0.003, 2, 7.767, 0.002, 2, 7.8, 0, 2, 7.833, -0.001, 2, 7.867, -0.002, 2, 7.9, -0.003, 2, 7.933, -0.003, 2, 7.967, -0.002, 2, 8, -0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1", "Segments": [0, 0, 2, 0.033, -0.045, 2, 0.067, -0.155, 2, 0.1, -0.301, 2, 0.133, -0.447, 2, 0.167, -0.557, 2, 0.2, -0.602, 2, 0.233, -0.527, 2, 0.267, -0.335, 2, 0.3, -0.068, 2, 0.333, 0.215, 2, 0.367, 0.483, 2, 0.4, 0.675, 2, 0.433, 0.75, 2, 0.467, 0.686, 2, 0.5, 0.521, 2, 0.533, 0.291, 2, 0.567, 0.048, 2, 0.6, -0.181, 2, 0.633, -0.346, 2, 0.667, -0.41, 2, 0.7, -0.347, 2, 0.733, -0.186, 2, 0.767, 0.037, 2, 0.8, 0.274, 2, 0.833, 0.498, 2, 0.867, 0.659, 2, 0.9, 0.721, 2, 0.933, 0.67, 2, 0.967, 0.533, 2, 1, 0.325, 2, 1.033, 0.072, 2, 1.067, -0.215, 2, 1.1, -0.505, 2, 1.133, -0.792, 2, 1.167, -1.045, 2, 1.2, -1.253, 2, 1.233, -1.39, 2, 1.267, -1.441, 2, 1.3, -1.331, 2, 1.333, -1.039, 2, 1.367, -0.627, 2, 1.4, -0.154, 2, 1.433, 0.318, 2, 1.467, 0.731, 2, 1.5, 1.022, 2, 1.533, 1.133, 2, 1.567, 1.087, 2, 1.6, 0.963, 2, 1.633, 0.775, 2, 1.667, 0.552, 2, 1.7, 0.309, 2, 1.733, 0.066, 2, 1.767, -0.158, 2, 1.8, -0.345, 2, 1.833, -0.47, 2, 1.867, -0.515, 2, 1.9, -0.499, 2, 1.933, -0.454, 2, 1.967, -0.385, 2, 2, -0.302, 2, 2.033, -0.208, 2, 2.067, -0.113, 2, 2.1, -0.019, 2, 2.133, 0.064, 2, 2.167, 0.132, 2, 2.2, 0.177, 2, 2.233, 0.194, 2, 2.267, 0.182, 2, 2.3, 0.151, 2, 2.333, 0.105, 2, 2.367, 0.051, 2, 2.4, -0.005, 2, 2.433, -0.059, 2, 2.467, -0.105, 2, 2.5, -0.137, 2, 2.533, -0.149, 2, 2.567, -0.139, 2, 2.6, -0.115, 2, 2.633, -0.077, 2, 2.667, -0.032, 2, 2.7, 0.016, 2, 2.733, 0.065, 2, 2.767, 0.11, 2, 2.8, 0.147, 2, 2.833, 0.172, 2, 2.867, 0.181, 2, 2.9, 0.165, 2, 2.933, 0.123, 2, 2.967, 0.063, 2, 3, -0.006, 2, 3.033, -0.075, 2, 3.067, -0.135, 2, 3.1, -0.178, 2, 3.133, -0.194, 2, 3.167, -0.181, 2, 3.2, -0.147, 2, 3.233, -0.094, 2, 3.267, -0.031, 2, 3.3, 0.041, 2, 3.333, 0.114, 2, 3.367, 0.186, 2, 3.4, 0.249, 2, 3.433, 0.302, 2, 3.467, 0.336, 2, 3.5, 0.349, 2, 3.533, 0.334, 2, 3.567, 0.293, 2, 3.6, 0.232, 2, 3.633, 0.159, 2, 3.667, 0.08, 2, 3.7, 0, 2, 3.733, -0.072, 2, 3.767, -0.134, 2, 3.8, -0.174, 2, 3.833, -0.189, 2, 3.867, -0.181, 2, 3.9, -0.159, 2, 3.933, -0.126, 2, 3.967, -0.086, 2, 4, -0.042, 2, 4.033, 0.001, 2, 4.067, 0.041, 2, 4.1, 0.074, 2, 4.133, 0.096, 2, 4.167, 0.105, 2, 4.2, 0.096, 2, 4.233, 0.073, 2, 4.267, 0.038, 2, 4.3, -0.006, 2, 4.333, -0.055, 2, 4.367, -0.108, 2, 4.4, -0.161, 2, 4.433, -0.21, 2, 4.467, -0.254, 2, 4.5, -0.289, 2, 4.533, -0.312, 2, 4.567, -0.32, 2, 4.6, -0.303, 2, 4.633, -0.257, 2, 4.667, -0.191, 2, 4.7, -0.112, 2, 4.733, -0.031, 2, 4.767, 0.048, 2, 4.8, 0.114, 2, 4.833, 0.16, 2, 4.867, 0.177, 2, 4.9, 0.163, 2, 4.933, 0.127, 2, 4.967, 0.075, 2, 5, 0.016, 2, 5.033, -0.043, 2, 5.067, -0.094, 2, 5.1, -0.13, 2, 5.133, -0.144, 2, 5.167, -0.135, 2, 5.2, -0.109, 2, 5.233, -0.073, 2, 5.267, -0.032, 2, 5.3, 0.009, 2, 5.333, 0.045, 2, 5.367, 0.07, 2, 5.4, 0.08, 2, 5.433, 0.075, 2, 5.467, 0.061, 2, 5.5, 0.041, 2, 5.533, 0.019, 2, 5.567, -0.004, 2, 5.6, -0.024, 2, 5.633, -0.037, 2, 5.667, -0.043, 2, 5.7, -0.04, 2, 5.733, -0.032, 2, 5.767, -0.022, 2, 5.8, -0.009, 2, 5.833, 0.005, 2, 5.867, 0.017, 2, 5.9, 0.028, 2, 5.933, 0.036, 2, 5.967, 0.038, 2, 6, 0.032, 2, 6.033, 0.021, 2, 6.067, 0.014, 2, 6.1, 0.017, 2, 6.133, 0.022, 2, 6.167, 0.029, 2, 6.2, 0.035, 2, 6.233, 0.037, 2, 6.267, 0.036, 2, 6.3, 0.031, 2, 6.333, 0.025, 2, 6.367, 0.019, 2, 6.4, 0.014, 2, 6.433, 0.012, 2, 6.467, 0.015, 2, 6.5, 0.021, 2, 6.533, 0.028, 2, 6.567, 0.034, 2, 6.6, 0.037, 2, 6.633, 0.031, 2, 6.667, 0.017, 2, 6.7, -0.004, 2, 6.733, -0.028, 2, 6.767, -0.051, 2, 6.8, -0.072, 2, 6.833, -0.086, 2, 6.867, -0.092, 2, 6.9, -0.085, 2, 6.933, -0.066, 2, 6.967, -0.039, 2, 7, -0.008, 2, 7.033, 0.022, 2, 7.067, 0.049, 2, 7.1, 0.068, 2, 7.133, 0.075, 2, 7.167, 0.071, 2, 7.2, 0.061, 2, 7.233, 0.045, 2, 7.267, 0.027, 2, 7.3, 0.008, 2, 7.333, -0.011, 2, 7.367, -0.026, 2, 7.4, -0.037, 2, 7.433, -0.041, 2, 7.467, -0.038, 2, 7.5, -0.032, 2, 7.533, -0.022, 2, 7.567, -0.011, 2, 7.6, 0, 2, 7.633, 0.009, 2, 7.667, 0.016, 2, 7.7, 0.019, 2, 7.733, 0.018, 2, 7.767, 0.015, 2, 7.8, 0.012, 2, 7.833, 0.008, 2, 7.867, 0.003, 2, 7.9, -0.001, 2, 7.933, -0.004, 2, 7.967, -0.007, 2, 8, -0.008]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1", "Segments": [0, 0.001, 2, 0.033, -0.022, 2, 0.067, -0.082, 2, 0.1, -0.167, 2, 0.133, -0.264, 2, 0.167, -0.361, 2, 0.2, -0.446, 2, 0.233, -0.506, 2, 0.267, -0.529, 2, 0.3, -0.471, 2, 0.333, -0.319, 2, 0.367, -0.104, 2, 0.4, 0.143, 2, 0.433, 0.389, 2, 0.467, 0.604, 2, 0.5, 0.756, 2, 0.533, 0.814, 2, 0.567, 0.742, 2, 0.6, 0.557, 2, 0.633, 0.299, 2, 0.667, 0.027, 2, 0.7, -0.231, 2, 0.733, -0.416, 2, 0.767, -0.488, 2, 0.8, -0.43, 2, 0.833, -0.277, 2, 0.867, -0.06, 2, 0.9, 0.188, 2, 0.933, 0.435, 2, 0.967, 0.652, 2, 1, 0.805, 2, 1.033, 0.863, 2, 1.067, 0.775, 2, 1.1, 0.539, 2, 1.133, 0.197, 2, 1.167, -0.208, 2, 1.2, -0.626, 2, 1.233, -1.031, 2, 1.267, -1.372, 2, 1.3, -1.608, 2, 1.333, -1.696, 2, 1.367, -1.583, 2, 1.4, -1.281, 2, 1.433, -0.843, 2, 1.467, -0.324, 2, 1.5, 0.212, 2, 1.533, 0.731, 2, 1.567, 1.169, 2, 1.6, 1.472, 2, 1.633, 1.585, 2, 1.667, 1.513, 2, 1.7, 1.32, 2, 1.733, 1.028, 2, 1.767, 0.681, 2, 1.8, 0.303, 2, 1.833, -0.075, 2, 1.867, -0.423, 2, 1.9, -0.714, 2, 1.933, -0.908, 2, 1.967, -0.979, 2, 2, -0.94, 2, 2.033, -0.835, 2, 2.067, -0.676, 2, 2.1, -0.487, 2, 2.133, -0.281, 2, 2.167, -0.075, 2, 2.2, 0.115, 2, 2.233, 0.273, 2, 2.267, 0.379, 2, 2.3, 0.418, 2, 2.333, 0.399, 2, 2.367, 0.348, 2, 2.4, 0.27, 2, 2.433, 0.178, 2, 2.467, 0.079, 2, 2.5, -0.021, 2, 2.533, -0.113, 2, 2.567, -0.191, 2, 2.6, -0.242, 2, 2.633, -0.261, 2, 2.667, -0.243, 2, 2.7, -0.195, 2, 2.733, -0.126, 2, 2.767, -0.044, 2, 2.8, 0.04, 2, 2.833, 0.122, 2, 2.867, 0.191, 2, 2.9, 0.238, 2, 2.933, 0.256, 2, 2.967, 0.238, 2, 3, 0.191, 2, 3.033, 0.122, 2, 3.067, 0.041, 2, 3.1, -0.043, 2, 3.133, -0.124, 2, 3.167, -0.193, 2, 3.2, -0.241, 2, 3.233, -0.258, 2, 3.267, -0.238, 2, 3.3, -0.184, 2, 3.333, -0.101, 2, 3.367, -0.003, 2, 3.4, 0.103, 2, 3.433, 0.21, 2, 3.467, 0.308, 2, 3.5, 0.39, 2, 3.533, 0.445, 2, 3.567, 0.465, 2, 3.6, 0.447, 2, 3.633, 0.399, 2, 3.667, 0.325, 2, 3.7, 0.236, 2, 3.733, 0.135, 2, 3.767, 0.032, 2, 3.8, -0.069, 2, 3.833, -0.158, 2, 3.867, -0.232, 2, 3.9, -0.28, 2, 3.933, -0.298, 2, 3.967, -0.284, 2, 4, -0.247, 2, 4.033, -0.192, 2, 4.067, -0.125, 2, 4.1, -0.053, 2, 4.133, 0.019, 2, 4.167, 0.085, 2, 4.2, 0.141, 2, 4.233, 0.178, 2, 4.267, 0.192, 2, 4.3, 0.178, 2, 4.333, 0.143, 2, 4.367, 0.089, 2, 4.4, 0.023, 2, 4.433, -0.051, 2, 4.467, -0.126, 2, 4.5, -0.201, 2, 4.533, -0.266, 2, 4.567, -0.32, 2, 4.6, -0.356, 2, 4.633, -0.369, 2, 4.667, -0.351, 2, 4.7, -0.303, 2, 4.733, -0.231, 2, 4.767, -0.145, 2, 4.8, -0.052, 2, 4.833, 0.042, 2, 4.867, 0.128, 2, 4.9, 0.2, 2, 4.933, 0.248, 2, 4.967, 0.265, 2, 5, 0.245, 2, 5.033, 0.191, 2, 5.067, 0.114, 2, 5.1, 0.026, 2, 5.133, -0.062, 2, 5.167, -0.138, 2, 5.2, -0.192, 2, 5.233, -0.213, 2, 5.267, -0.198, 2, 5.3, -0.159, 2, 5.333, -0.104, 2, 5.367, -0.041, 2, 5.4, 0.022, 2, 5.433, 0.077, 2, 5.467, 0.116, 2, 5.5, 0.131, 2, 5.533, 0.122, 2, 5.567, 0.099, 2, 5.6, 0.066, 2, 5.633, 0.029, 2, 5.667, -0.008, 2, 5.7, -0.041, 2, 5.733, -0.064, 2, 5.767, -0.072, 2, 5.8, -0.068, 2, 5.833, -0.056, 2, 5.867, -0.038, 2, 5.9, -0.018, 2, 5.933, 0.004, 2, 5.967, 0.024, 2, 6, 0.042, 2, 6.033, 0.054, 2, 6.067, 0.058, 2, 6.1, 0.057, 2, 6.133, 0.055, 2, 6.167, 0.052, 2, 6.2, 0.048, 2, 6.233, 0.044, 2, 6.267, 0.039, 2, 6.3, 0.033, 2, 6.333, 0.029, 2, 6.367, 0.024, 2, 6.4, 0.02, 2, 6.433, 0.017, 2, 6.467, 0.015, 2, 6.5, 0.014, 2, 6.533, 0.016, 2, 6.567, 0.019, 2, 6.6, 0.024, 2, 6.633, 0.028, 2, 6.667, 0.029, 2, 6.7, 0.024, 2, 6.733, 0.009, 2, 6.767, -0.012, 2, 6.8, -0.036, 2, 6.833, -0.06, 2, 6.867, -0.08, 2, 6.9, -0.095, 2, 6.933, -0.101, 2, 6.967, -0.094, 2, 7, -0.075, 2, 7.033, -0.048, 2, 7.067, -0.016, 2, 7.1, 0.018, 2, 7.133, 0.05, 2, 7.167, 0.077, 2, 7.2, 0.096, 2, 7.233, 0.103, 2, 7.267, 0.097, 2, 7.3, 0.081, 2, 7.333, 0.058, 2, 7.367, 0.031, 2, 7.4, 0.003, 2, 7.433, -0.024, 2, 7.467, -0.047, 2, 7.5, -0.063, 2, 7.533, -0.069, 2, 7.567, -0.065, 2, 7.6, -0.055, 2, 7.633, -0.041, 2, 7.667, -0.025, 2, 7.7, -0.007, 2, 7.733, 0.009, 2, 7.767, 0.023, 2, 7.8, 0.033, 2, 7.833, 0.037, 2, 7.867, 0.035, 2, 7.9, 0.03, 2, 7.933, 0.023, 2, 7.967, 0.014, 2, 8, 0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh2", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh2", "Segments": [0, 0, 2, 0.033, 0.146, 2, 0.067, 0.494, 2, 0.1, 0.918, 2, 0.133, 1.266, 2, 0.167, 1.411, 2, 0.2, 1.349, 2, 0.233, 1.197, 2, 0.267, 0.994, 2, 0.3, 0.792, 2, 0.333, 0.64, 2, 0.367, 0.577, 2, 0.4, 0.591, 2, 0.433, 0.626, 2, 0.467, 0.671, 2, 0.5, 0.717, 2, 0.533, 0.751, 2, 0.567, 0.766, 2, 0.6, 0.729, 2, 0.633, 0.628, 2, 0.667, 0.47, 2, 0.7, 0.273, 2, 0.733, 0.037, 2, 0.767, -0.217, 2, 0.8, -0.482, 2, 0.833, -0.747, 2, 0.867, -1.001, 2, 0.9, -1.237, 2, 0.933, -1.434, 2, 0.967, -1.592, 2, 1, -1.694, 2, 1.033, -1.73, 2, 1.067, -1.659, 2, 1.1, -1.47, 2, 1.133, -1.182, 2, 1.167, -0.834, 2, 1.2, -0.438, 2, 1.233, -0.037, 2, 1.267, 0.359, 2, 1.3, 0.708, 2, 1.333, 0.995, 2, 1.367, 1.184, 2, 1.4, 1.255, 2, 1.433, 1.239, 2, 1.467, 1.198, 2, 1.5, 1.135, 2, 1.533, 1.059, 2, 1.567, 0.972, 2, 1.6, 0.884, 2, 1.633, 0.797, 2, 1.667, 0.721, 2, 1.7, 0.658, 2, 1.733, 0.617, 2, 1.767, 0.601, 2, 1.8, 0.607, 2, 1.833, 0.625, 2, 1.867, 0.654, 2, 1.9, 0.692, 2, 1.933, 0.738, 2, 1.967, 0.793, 2, 2, 0.854, 2, 2.033, 0.92, 2, 2.067, 0.991, 2, 2.1, 1.066, 2, 2.133, 1.142, 2, 2.167, 1.221, 2, 2.2, 1.3, 2, 2.233, 1.379, 2, 2.267, 1.455, 2, 2.3, 1.529, 2, 2.333, 1.601, 2, 2.367, 1.667, 2, 2.4, 1.728, 2, 2.433, 1.782, 2, 2.467, 1.829, 2, 2.5, 1.867, 2, 2.533, 1.896, 2, 2.567, 1.914, 2, 2.6, 1.92, 2, 2.633, 1.908, 2, 2.667, 1.875, 2, 2.7, 1.83, 2, 2.733, 1.778, 2, 2.767, 1.725, 2, 2.8, 1.68, 2, 2.833, 1.648, 2, 2.867, 1.635, 2, 2.9, 1.65, 2, 2.933, 1.69, 2, 2.967, 1.746, 2, 3, 1.81, 2, 3.033, 1.874, 2, 3.067, 1.93, 2, 3.1, 1.97, 2, 3.133, 1.985, 2, 3.167, 1.974, 2, 3.2, 1.945, 2, 3.233, 1.896, 2, 3.267, 1.832, 2, 3.3, 1.755, 2, 3.333, 1.663, 2, 3.367, 1.561, 2, 3.4, 1.452, 2, 3.433, 1.334, 2, 3.467, 1.21, 2, 3.5, 1.086, 2, 3.533, 0.956, 2, 3.567, 0.827, 2, 3.6, 0.702, 2, 3.633, 0.579, 2, 3.667, 0.46, 2, 3.7, 0.351, 2, 3.733, 0.249, 2, 3.767, 0.158, 2, 3.8, 0.081, 2, 3.833, 0.016, 2, 3.867, -0.032, 2, 3.9, -0.062, 2, 3.933, -0.072, 2, 3.967, -0.072, 2, 4, -0.08, 2, 4.033, -0.104, 2, 4.067, -0.139, 2, 4.1, -0.181, 2, 4.133, -0.226, 2, 4.167, -0.272, 2, 4.2, -0.314, 2, 4.233, -0.349, 2, 4.267, -0.372, 2, 4.3, -0.381, 2, 4.333, -0.378, 2, 4.367, -0.37, 2, 4.4, -0.357, 2, 4.433, -0.34, 2, 4.467, -0.318, 2, 4.5, -0.293, 2, 4.533, -0.263, 2, 4.567, -0.23, 2, 4.6, -0.194, 2, 4.633, -0.154, 2, 4.667, -0.112, 2, 4.7, -0.068, 2, 4.733, -0.021, 2, 4.767, 0.028, 2, 4.8, 0.078, 2, 4.833, 0.129, 2, 4.867, 0.181, 2, 4.9, 0.236, 2, 4.933, 0.29, 2, 4.967, 0.344, 2, 5, 0.398, 2, 5.033, 0.451, 2, 5.067, 0.506, 2, 5.1, 0.558, 2, 5.133, 0.61, 2, 5.167, 0.66, 2, 5.2, 0.708, 2, 5.233, 0.755, 2, 5.267, 0.8, 2, 5.3, 0.842, 2, 5.333, 0.881, 2, 5.367, 0.917, 2, 5.4, 0.951, 2, 5.433, 0.98, 2, 5.467, 1.006, 2, 5.5, 1.027, 2, 5.533, 1.045, 2, 5.567, 1.057, 2, 5.6, 1.065, 2, 5.633, 1.068, 2, 5.667, 1.067, 2, 5.7, 1.069, 2, 5.733, 1.067, 2, 5.767, 1.061, 2, 5.8, 1.052, 2, 5.833, 1.04, 2, 5.867, 1.024, 2, 5.9, 1.007, 2, 5.933, 0.987, 2, 5.967, 0.965, 2, 6, 0.941, 2, 6.033, 0.916, 2, 6.067, 0.89, 2, 6.1, 0.864, 2, 6.133, 0.836, 2, 6.167, 0.809, 2, 6.2, 0.782, 2, 6.233, 0.755, 2, 6.267, 0.729, 2, 6.3, 0.704, 2, 6.333, 0.681, 2, 6.367, 0.659, 2, 6.4, 0.639, 2, 6.433, 0.621, 2, 6.467, 0.606, 2, 6.5, 0.593, 2, 6.533, 0.584, 2, 6.567, 0.578, 2, 6.6, 0.577, 2, 6.633, 0.581, 2, 6.667, 0.592, 2, 6.7, 0.61, 2, 6.733, 0.63, 2, 6.767, 0.653, 2, 6.8, 0.675, 2, 6.833, 0.696, 2, 6.867, 0.714, 2, 6.9, 0.725, 2, 6.933, 0.729, 2, 6.967, 0.728, 2, 7, 0.725, 2, 7.033, 0.721, 2, 7.067, 0.716, 2, 7.1, 0.71, 2, 7.133, 0.704, 2, 7.167, 0.699, 2, 7.2, 0.694, 2, 7.233, 0.691, 2, 7.267, 0.69, 2, 7.333, 0.69, 2, 7.367, 0.691, 2, 7.4, 0.693, 2, 7.433, 0.694, 2, 7.467, 0.696, 2, 7.5, 0.698, 2, 7.533, 0.699, 2, 7.567, 0.7, 2, 7.6, 0.7, 2, 7.667, 0.7, 2, 7.7, 0.7, 2, 7.733, 0.7, 2, 7.767, 0.699, 2, 7.8, 0.699, 2, 7.833, 0.698, 2, 7.867, 0.698, 2, 7.9, 0.698, 2, 7.933, 0.698, 2, 8, 0.698]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh2", "Segments": [0, 0, 2, 0.033, -0.132, 2, 0.067, -0.424, 2, 0.1, -0.715, 2, 0.133, -0.847, 2, 0.167, -0.732, 2, 0.2, -0.452, 2, 0.233, -0.079, 2, 0.267, 0.293, 2, 0.3, 0.573, 2, 0.333, 0.689, 2, 0.367, 0.64, 2, 0.4, 0.513, 2, 0.433, 0.337, 2, 0.467, 0.15, 2, 0.5, -0.026, 2, 0.533, -0.153, 2, 0.567, -0.202, 2, 0.6, -0.174, 2, 0.633, -0.098, 2, 0.667, 0.011, 2, 0.7, 0.141, 2, 0.733, 0.275, 2, 0.767, 0.405, 2, 0.8, 0.515, 2, 0.833, 0.59, 2, 0.867, 0.619, 2, 0.9, 0.558, 2, 0.933, 0.394, 2, 0.967, 0.147, 2, 1, -0.148, 2, 1.033, -0.469, 2, 1.067, -0.79, 2, 1.1, -1.085, 2, 1.133, -1.332, 2, 1.167, -1.496, 2, 1.2, -1.557, 2, 1.233, -1.493, 2, 1.267, -1.32, 2, 1.3, -1.059, 2, 1.333, -0.748, 2, 1.367, -0.409, 2, 1.4, -0.071, 2, 1.433, 0.24, 2, 1.467, 0.501, 2, 1.5, 0.674, 2, 1.533, 0.738, 2, 1.567, 0.713, 2, 1.6, 0.646, 2, 1.633, 0.543, 2, 1.667, 0.413, 2, 1.7, 0.269, 2, 1.733, 0.114, 2, 1.767, -0.042, 2, 1.8, -0.186, 2, 1.833, -0.316, 2, 1.867, -0.419, 2, 1.9, -0.486, 2, 1.933, -0.511, 2, 1.967, -0.493, 2, 2, -0.444, 2, 2.033, -0.369, 2, 2.067, -0.28, 2, 2.1, -0.184, 2, 2.133, -0.087, 2, 2.167, 0.001, 2, 2.2, 0.076, 2, 2.233, 0.125, 2, 2.267, 0.143, 2, 2.3, 0.135, 2, 2.333, 0.112, 2, 2.367, 0.078, 2, 2.4, 0.039, 2, 2.433, -0.002, 2, 2.467, -0.041, 2, 2.5, -0.075, 2, 2.533, -0.098, 2, 2.567, -0.106, 2, 2.6, -0.092, 2, 2.633, -0.055, 2, 2.667, -0.004, 2, 2.7, 0.051, 2, 2.733, 0.102, 2, 2.767, 0.139, 2, 2.8, 0.153, 2, 2.833, 0.137, 2, 2.867, 0.095, 2, 2.9, 0.035, 2, 2.933, -0.034, 2, 2.967, -0.103, 2, 3, -0.163, 2, 3.033, -0.205, 2, 3.067, -0.221, 2, 3.1, -0.209, 2, 3.133, -0.176, 2, 3.167, -0.126, 2, 3.2, -0.062, 2, 3.233, 0.011, 2, 3.267, 0.089, 2, 3.3, 0.171, 2, 3.333, 0.25, 2, 3.367, 0.323, 2, 3.4, 0.387, 2, 3.433, 0.437, 2, 3.467, 0.47, 2, 3.5, 0.482, 2, 3.533, 0.463, 2, 3.567, 0.412, 2, 3.6, 0.336, 2, 3.633, 0.244, 2, 3.667, 0.145, 2, 3.7, 0.045, 2, 3.733, -0.046, 2, 3.767, -0.123, 2, 3.8, -0.174, 2, 3.833, -0.192, 2, 3.867, -0.187, 2, 3.9, -0.17, 2, 3.933, -0.145, 2, 3.967, -0.114, 2, 4, -0.077, 2, 4.033, -0.038, 2, 4.067, 0.002, 2, 4.1, 0.041, 2, 4.133, 0.078, 2, 4.167, 0.109, 2, 4.2, 0.134, 2, 4.233, 0.15, 2, 4.267, 0.156, 2, 4.3, 0.134, 2, 4.333, 0.076, 2, 4.367, -0.006, 2, 4.4, -0.1, 2, 4.433, -0.194, 2, 4.467, -0.276, 2, 4.5, -0.334, 2, 4.533, -0.356, 2, 4.567, -0.34, 2, 4.6, -0.299, 2, 4.633, -0.239, 2, 4.667, -0.168, 2, 4.7, -0.094, 2, 4.733, -0.023, 2, 4.767, 0.037, 2, 4.8, 0.079, 2, 4.833, 0.094, 2, 4.867, 0.087, 2, 4.9, 0.067, 2, 4.933, 0.039, 2, 4.967, 0.007, 2, 5, -0.024, 2, 5.033, -0.052, 2, 5.067, -0.072, 2, 5.1, -0.079, 2, 5.133, -0.076, 2, 5.167, -0.066, 2, 5.2, -0.053, 2, 5.233, -0.037, 2, 5.267, -0.02, 2, 5.3, -0.005, 2, 5.333, 0.009, 2, 5.367, 0.018, 2, 5.4, 0.022, 2, 5.433, 0.019, 2, 5.467, 0.012, 2, 5.5, 0.003, 2, 5.533, -0.004, 2, 5.567, -0.007, 2, 5.633, -0.01, 2, 5.667, -0.009, 2, 5.7, -0.005, 2, 5.733, -0.001, 2, 5.767, 0.005, 2, 5.8, 0.011, 2, 5.833, 0.018, 2, 5.867, 0.023, 2, 5.9, 0.028, 2, 5.933, 0.031, 2, 5.967, 0.033, 2, 6, 0.032, 2, 6.033, 0.035, 2, 6.067, 0.033, 2, 6.1, 0.035, 2, 6.133, 0.033, 2, 6.167, 0.035, 2, 6.2, 0.032, 2, 6.233, 0.025, 2, 6.267, 0.016, 2, 6.3, 0.008, 2, 6.333, 0.005, 2, 6.367, 0.009, 2, 6.4, 0.018, 2, 6.433, 0.029, 2, 6.467, 0.041, 2, 6.5, 0.05, 2, 6.533, 0.054, 2, 6.567, 0.047, 2, 6.6, 0.031, 2, 6.633, 0.007, 2, 6.667, -0.02, 2, 6.7, -0.047, 2, 6.733, -0.071, 2, 6.767, -0.088, 2, 6.8, -0.094, 2, 6.833, -0.089, 2, 6.867, -0.075, 2, 6.9, -0.056, 2, 6.933, -0.033, 2, 6.967, -0.009, 2, 7, 0.015, 2, 7.033, 0.034, 2, 7.067, 0.048, 2, 7.1, 0.053, 2, 7.133, 0.051, 2, 7.167, 0.045, 2, 7.2, 0.037, 2, 7.233, 0.027, 2, 7.267, 0.016, 2, 7.3, 0.005, 2, 7.333, -0.005, 2, 7.367, -0.014, 2, 7.4, -0.019, 2, 7.433, -0.021, 2, 7.467, -0.02, 2, 7.5, -0.018, 2, 7.533, -0.015, 2, 7.567, -0.011, 2, 7.6, -0.007, 2, 7.633, -0.003, 2, 7.667, 0.001, 2, 7.7, 0.005, 2, 7.733, 0.007, 2, 7.767, 0.007, 2, 7.8, 0.007, 2, 7.833, 0.006, 2, 7.867, 0.005, 2, 7.9, 0.004, 2, 7.933, 0.003, 2, 7.967, 0.001, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh2", "Segments": [0, -0.001, 2, 0.033, -0.034, 2, 0.067, -0.12, 2, 0.1, -0.24, 2, 0.133, -0.367, 2, 0.167, -0.486, 2, 0.2, -0.573, 2, 0.233, -0.606, 2, 0.267, -0.554, 2, 0.3, -0.415, 2, 0.333, -0.219, 2, 0.367, 0.005, 2, 0.4, 0.229, 2, 0.433, 0.425, 2, 0.467, 0.563, 2, 0.5, 0.616, 2, 0.533, 0.575, 2, 0.567, 0.47, 2, 0.6, 0.324, 2, 0.633, 0.169, 2, 0.667, 0.022, 2, 0.7, -0.083, 2, 0.733, -0.124, 2, 0.767, -0.09, 2, 0.8, 0, 2, 0.833, 0.127, 2, 0.867, 0.272, 2, 0.9, 0.417, 2, 0.933, 0.544, 2, 0.967, 0.633, 2, 1, 0.667, 2, 1.033, 0.603, 2, 1.067, 0.427, 2, 1.1, 0.162, 2, 1.133, -0.154, 2, 1.167, -0.497, 2, 1.2, -0.84, 2, 1.233, -1.156, 2, 1.267, -1.421, 2, 1.3, -1.596, 2, 1.333, -1.661, 2, 1.367, -1.581, 2, 1.4, -1.365, 2, 1.433, -1.039, 2, 1.467, -0.65, 2, 1.5, -0.228, 2, 1.533, 0.195, 2, 1.567, 0.584, 2, 1.6, 0.91, 2, 1.633, 1.126, 2, 1.667, 1.206, 2, 1.7, 1.158, 2, 1.733, 1.03, 2, 1.767, 0.835, 2, 1.8, 0.599, 2, 1.833, 0.33, 2, 1.867, 0.059, 2, 1.9, -0.21, 2, 1.933, -0.446, 2, 1.967, -0.641, 2, 2, -0.769, 2, 2.033, -0.817, 2, 2.067, -0.789, 2, 2.1, -0.717, 2, 2.133, -0.606, 2, 2.167, -0.472, 2, 2.2, -0.32, 2, 2.233, -0.166, 2, 2.267, -0.013, 2, 2.3, 0.121, 2, 2.333, 0.231, 2, 2.367, 0.304, 2, 2.4, 0.331, 2, 2.433, 0.311, 2, 2.467, 0.256, 2, 2.5, 0.18, 2, 2.533, 0.092, 2, 2.567, 0.004, 2, 2.6, -0.073, 2, 2.633, -0.127, 2, 2.667, -0.148, 2, 2.7, -0.135, 2, 2.733, -0.102, 2, 2.767, -0.054, 2, 2.8, 0, 2, 2.833, 0.054, 2, 2.867, 0.101, 2, 2.9, 0.135, 2, 2.933, 0.147, 2, 2.967, 0.132, 2, 3, 0.091, 2, 3.033, 0.033, 2, 3.067, -0.034, 2, 3.1, -0.1, 2, 3.133, -0.158, 2, 3.167, -0.199, 2, 3.2, -0.215, 2, 3.233, -0.196, 2, 3.267, -0.145, 2, 3.3, -0.068, 2, 3.333, 0.025, 2, 3.367, 0.131, 2, 3.4, 0.239, 2, 3.433, 0.345, 2, 3.467, 0.439, 2, 3.5, 0.516, 2, 3.533, 0.567, 2, 3.567, 0.586, 2, 3.6, 0.564, 2, 3.633, 0.505, 2, 3.667, 0.417, 2, 3.7, 0.309, 2, 3.733, 0.187, 2, 3.767, 0.063, 2, 3.8, -0.059, 2, 3.833, -0.166, 2, 3.867, -0.255, 2, 3.9, -0.313, 2, 3.933, -0.335, 2, 3.967, -0.322, 2, 4, -0.286, 2, 4.033, -0.232, 2, 4.067, -0.166, 2, 4.1, -0.091, 2, 4.133, -0.016, 2, 4.167, 0.059, 2, 4.2, 0.125, 2, 4.233, 0.179, 2, 4.267, 0.214, 2, 4.3, 0.228, 2, 4.333, 0.21, 2, 4.367, 0.161, 2, 4.4, 0.087, 2, 4.433, 0, 2, 4.467, -0.096, 2, 4.5, -0.191, 2, 4.533, -0.279, 2, 4.567, -0.352, 2, 4.6, -0.401, 2, 4.633, -0.419, 2, 4.667, -0.402, 2, 4.7, -0.357, 2, 4.733, -0.289, 2, 4.767, -0.207, 2, 4.8, -0.119, 2, 4.833, -0.03, 2, 4.867, 0.051, 2, 4.9, 0.12, 2, 4.933, 0.165, 2, 4.967, 0.182, 2, 5, 0.172, 2, 5.033, 0.145, 2, 5.067, 0.107, 2, 5.1, 0.062, 2, 5.133, 0.015, 2, 5.167, -0.03, 2, 5.2, -0.068, 2, 5.233, -0.094, 2, 5.267, -0.104, 2, 5.3, -0.099, 2, 5.333, -0.086, 2, 5.367, -0.067, 2, 5.4, -0.044, 2, 5.433, -0.021, 2, 5.467, 0.002, 2, 5.5, 0.021, 2, 5.533, 0.034, 2, 5.567, 0.039, 2, 5.6, 0.037, 2, 5.633, 0.032, 2, 5.667, 0.024, 2, 5.7, 0.016, 2, 5.733, 0.008, 2, 5.767, 0.001, 2, 5.8, -0.004, 2, 5.833, -0.006, 2, 5.867, -0.005, 2, 5.9, -0.001, 2, 5.933, 0.006, 2, 5.967, 0.014, 2, 6, 0.024, 2, 6.033, 0.035, 2, 6.067, 0.046, 2, 6.1, 0.057, 2, 6.133, 0.067, 2, 6.167, 0.077, 2, 6.2, 0.085, 2, 6.233, 0.092, 2, 6.267, 0.096, 2, 6.3, 0.098, 2, 6.333, 0.094, 2, 6.367, 0.086, 2, 6.4, 0.074, 2, 6.433, 0.06, 2, 6.467, 0.046, 2, 6.5, 0.034, 2, 6.533, 0.025, 2, 6.567, 0.022, 2, 6.6, 0.023, 2, 6.633, 0.019, 2, 6.667, 0.008, 2, 6.7, -0.008, 2, 6.733, -0.027, 2, 6.767, -0.047, 2, 6.8, -0.065, 2, 6.833, -0.081, 2, 6.867, -0.092, 2, 6.9, -0.096, 2, 6.933, -0.092, 2, 6.967, -0.079, 2, 7, -0.059, 2, 7.033, -0.036, 2, 7.067, -0.01, 2, 7.1, 0.015, 2, 7.133, 0.039, 2, 7.167, 0.058, 2, 7.2, 0.071, 2, 7.233, 0.076, 2, 7.267, 0.073, 2, 7.3, 0.064, 2, 7.333, 0.051, 2, 7.367, 0.035, 2, 7.4, 0.018, 2, 7.433, 0.001, 2, 7.467, -0.015, 2, 7.5, -0.028, 2, 7.533, -0.037, 2, 7.567, -0.04, 2, 7.6, -0.039, 2, 7.633, -0.034, 2, 7.667, -0.028, 2, 7.7, -0.02, 2, 7.733, -0.011, 2, 7.767, -0.003, 2, 7.8, 0.005, 2, 7.833, 0.012, 2, 7.867, 0.016, 2, 7.9, 0.018, 2, 7.933, 0.017, 2, 7.967, 0.015, 2, 8, 0.012]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh2", "Segments": [0, -0.002, 2, 0.033, -0.016, 2, 0.067, -0.057, 2, 0.1, -0.117, 2, 0.133, -0.19, 2, 0.167, -0.268, 2, 0.2, -0.347, 2, 0.233, -0.42, 2, 0.267, -0.48, 2, 0.3, -0.521, 2, 0.333, -0.535, 2, 0.367, -0.493, 2, 0.4, -0.38, 2, 0.433, -0.217, 2, 0.467, -0.023, 2, 0.5, 0.178, 2, 0.533, 0.371, 2, 0.567, 0.535, 2, 0.6, 0.648, 2, 0.633, 0.69, 2, 0.667, 0.647, 2, 0.7, 0.535, 2, 0.733, 0.379, 2, 0.767, 0.214, 2, 0.8, 0.059, 2, 0.833, -0.053, 2, 0.867, -0.097, 2, 0.9, -0.065, 2, 0.933, 0.018, 2, 0.967, 0.136, 2, 1, 0.272, 2, 1.033, 0.407, 2, 1.067, 0.525, 2, 1.1, 0.608, 2, 1.133, 0.64, 2, 1.167, 0.555, 2, 1.2, 0.328, 2, 1.233, -0.001, 2, 1.267, -0.391, 2, 1.3, -0.793, 2, 1.333, -1.183, 2, 1.367, -1.512, 2, 1.4, -1.739, 2, 1.433, -1.824, 2, 1.467, -1.74, 2, 1.5, -1.517, 2, 1.533, -1.177, 2, 1.567, -0.765, 2, 1.6, -0.297, 2, 1.633, 0.177, 2, 1.667, 0.645, 2, 1.7, 1.057, 2, 1.733, 1.397, 2, 1.767, 1.62, 2, 1.8, 1.704, 2, 1.833, 1.621, 2, 1.867, 1.397, 2, 1.9, 1.059, 2, 1.933, 0.656, 2, 1.967, 0.218, 2, 2, -0.22, 2, 2.033, -0.623, 2, 2.067, -0.961, 2, 2.1, -1.186, 2, 2.133, -1.268, 2, 2.167, -1.223, 2, 2.2, -1.102, 2, 2.233, -0.918, 2, 2.267, -0.695, 2, 2.3, -0.441, 2, 2.333, -0.185, 2, 2.367, 0.069, 2, 2.4, 0.292, 2, 2.433, 0.476, 2, 2.467, 0.597, 2, 2.5, 0.642, 2, 2.533, 0.617, 2, 2.567, 0.548, 2, 2.6, 0.444, 2, 2.633, 0.32, 2, 2.667, 0.186, 2, 2.7, 0.051, 2, 2.733, -0.073, 2, 2.767, -0.176, 2, 2.8, -0.245, 2, 2.833, -0.271, 2, 2.867, -0.248, 2, 2.9, -0.189, 2, 2.933, -0.108, 2, 2.967, -0.022, 2, 3, 0.059, 2, 3.033, 0.118, 2, 3.067, 0.141, 2, 3.1, 0.126, 2, 3.133, 0.089, 2, 3.167, 0.036, 2, 3.2, -0.024, 2, 3.233, -0.085, 2, 3.267, -0.137, 2, 3.3, -0.175, 2, 3.333, -0.189, 2, 3.367, -0.164, 2, 3.4, -0.098, 2, 3.433, 0.002, 2, 3.467, 0.121, 2, 3.5, 0.251, 2, 3.533, 0.38, 2, 3.567, 0.5, 2, 3.6, 0.6, 2, 3.633, 0.666, 2, 3.667, 0.69, 2, 3.7, 0.662, 2, 3.733, 0.586, 2, 3.767, 0.471, 2, 3.8, 0.332, 2, 3.833, 0.173, 2, 3.867, 0.012, 2, 3.9, -0.147, 2, 3.933, -0.286, 2, 3.967, -0.402, 2, 4, -0.477, 2, 4.033, -0.506, 2, 4.067, -0.485, 2, 4.1, -0.431, 2, 4.133, -0.348, 2, 4.167, -0.248, 2, 4.2, -0.134, 2, 4.233, -0.019, 2, 4.267, 0.095, 2, 4.3, 0.196, 2, 4.333, 0.278, 2, 4.367, 0.333, 2, 4.4, 0.353, 2, 4.433, 0.332, 2, 4.467, 0.277, 2, 4.5, 0.192, 2, 4.533, 0.09, 2, 4.567, -0.026, 2, 4.6, -0.144, 2, 4.633, -0.26, 2, 4.667, -0.363, 2, 4.7, -0.447, 2, 4.733, -0.503, 2, 4.767, -0.523, 2, 4.8, -0.501, 2, 4.833, -0.439, 2, 4.867, -0.345, 2, 4.9, -0.234, 2, 4.933, -0.112, 2, 4.967, 0.009, 2, 5, 0.12, 2, 5.033, 0.214, 2, 5.067, 0.276, 2, 5.1, 0.299, 2, 5.133, 0.283, 2, 5.167, 0.239, 2, 5.2, 0.177, 2, 5.233, 0.103, 2, 5.267, 0.026, 2, 5.3, -0.048, 2, 5.333, -0.11, 2, 5.367, -0.154, 2, 5.4, -0.17, 2, 5.433, -0.163, 2, 5.467, -0.144, 2, 5.5, -0.117, 2, 5.533, -0.083, 2, 5.567, -0.047, 2, 5.6, -0.011, 2, 5.633, 0.023, 2, 5.667, 0.05, 2, 5.7, 0.069, 2, 5.733, 0.076, 2, 5.767, 0.072, 2, 5.8, 0.062, 2, 5.833, 0.048, 2, 5.867, 0.032, 2, 5.9, 0.016, 2, 5.933, 0.002, 2, 5.967, -0.008, 2, 6, -0.012, 2, 6.033, -0.008, 2, 6.067, 0, 2, 6.1, 0.012, 2, 6.133, 0.023, 2, 6.167, 0.031, 2, 6.2, 0.035, 2, 6.233, 0.03, 2, 6.267, 0.033, 2, 6.3, 0.042, 2, 6.333, 0.053, 2, 6.367, 0.065, 2, 6.4, 0.073, 2, 6.433, 0.077, 2, 6.467, 0.075, 2, 6.5, 0.069, 2, 6.533, 0.06, 2, 6.567, 0.049, 2, 6.6, 0.035, 2, 6.633, 0.019, 2, 6.667, 0.002, 2, 6.7, -0.015, 2, 6.733, -0.033, 2, 6.767, -0.05, 2, 6.8, -0.067, 2, 6.833, -0.083, 2, 6.867, -0.096, 2, 6.9, -0.108, 2, 6.933, -0.117, 2, 6.967, -0.123, 2, 7, -0.125, 2, 7.033, -0.119, 2, 7.067, -0.104, 2, 7.1, -0.082, 2, 7.133, -0.055, 2, 7.167, -0.024, 2, 7.2, 0.007, 2, 7.233, 0.038, 2, 7.267, 0.065, 2, 7.3, 0.087, 2, 7.333, 0.102, 2, 7.367, 0.107, 2, 7.4, 0.103, 2, 7.433, 0.089, 2, 7.467, 0.069, 2, 7.5, 0.046, 2, 7.533, 0.02, 2, 7.567, -0.006, 2, 7.6, -0.03, 2, 7.633, -0.05, 2, 7.667, -0.063, 2, 7.7, -0.068, 2, 7.733, -0.065, 2, 7.767, -0.057, 2, 7.8, -0.045, 2, 7.833, -0.032, 2, 7.867, -0.016, 2, 7.9, -0.001, 2, 7.933, 0.013, 2, 7.967, 0.024, 2, 8, 0.032]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh3", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh3", "Segments": [0, 0, 2, 0.033, 0.131, 2, 0.067, 0.443, 2, 0.1, 0.823, 2, 0.133, 1.135, 2, 0.167, 1.266, 2, 0.2, 1.169, 2, 0.233, 0.939, 2, 0.267, 0.658, 2, 0.3, 0.428, 2, 0.333, 0.332, 2, 0.367, 0.351, 2, 0.4, 0.4, 2, 0.433, 0.468, 2, 0.467, 0.54, 2, 0.5, 0.608, 2, 0.533, 0.657, 2, 0.567, 0.676, 2, 0.6, 0.649, 2, 0.633, 0.572, 2, 0.667, 0.451, 2, 0.7, 0.302, 2, 0.733, 0.123, 2, 0.767, -0.069, 2, 0.8, -0.27, 2, 0.833, -0.472, 2, 0.867, -0.664, 2, 0.9, -0.843, 2, 0.933, -0.992, 2, 0.967, -1.113, 2, 1, -1.19, 2, 1.033, -1.217, 2, 1.067, -1.11, 2, 1.1, -0.829, 2, 1.133, -0.432, 2, 1.167, 0.023, 2, 1.2, 0.479, 2, 1.233, 0.876, 2, 1.267, 1.157, 2, 1.3, 1.264, 2, 1.333, 1.255, 2, 1.367, 1.229, 2, 1.4, 1.191, 2, 1.433, 1.142, 2, 1.467, 1.085, 2, 1.5, 1.024, 2, 1.533, 0.961, 2, 1.567, 0.901, 2, 1.6, 0.844, 2, 1.633, 0.795, 2, 1.667, 0.756, 2, 1.7, 0.73, 2, 1.733, 0.722, 2, 1.767, 0.726, 2, 1.8, 0.739, 2, 1.833, 0.759, 2, 1.867, 0.787, 2, 1.9, 0.821, 2, 1.933, 0.861, 2, 1.967, 0.906, 2, 2, 0.954, 2, 2.033, 1.007, 2, 2.067, 1.062, 2, 2.1, 1.119, 2, 2.133, 1.179, 2, 2.167, 1.238, 2, 2.2, 1.296, 2, 2.233, 1.356, 2, 2.267, 1.413, 2, 2.3, 1.468, 2, 2.333, 1.521, 2, 2.367, 1.569, 2, 2.4, 1.614, 2, 2.433, 1.654, 2, 2.467, 1.688, 2, 2.5, 1.716, 2, 2.533, 1.736, 2, 2.567, 1.749, 2, 2.6, 1.754, 2, 2.633, 1.74, 2, 2.667, 1.704, 2, 2.7, 1.653, 2, 2.733, 1.595, 2, 2.767, 1.536, 2, 2.8, 1.486, 2, 2.833, 1.45, 2, 2.867, 1.436, 2, 2.9, 1.458, 2, 2.933, 1.516, 2, 2.967, 1.598, 2, 3, 1.692, 2, 3.033, 1.786, 2, 3.067, 1.868, 2, 3.1, 1.926, 2, 3.133, 1.948, 2, 3.167, 1.938, 2, 3.2, 1.911, 2, 3.233, 1.867, 2, 3.267, 1.808, 2, 3.3, 1.737, 2, 3.333, 1.654, 2, 3.367, 1.56, 2, 3.4, 1.46, 2, 3.433, 1.352, 2, 3.467, 1.239, 2, 3.5, 1.125, 2, 3.533, 1.006, 2, 3.567, 0.888, 2, 3.6, 0.774, 2, 3.633, 0.66, 2, 3.667, 0.552, 2, 3.7, 0.452, 2, 3.733, 0.359, 2, 3.767, 0.275, 2, 3.8, 0.205, 2, 3.833, 0.145, 2, 3.867, 0.101, 2, 3.9, 0.074, 2, 3.933, 0.065, 2, 3.967, 0.065, 2, 4, 0.059, 2, 4.033, 0.061, 2, 4.067, 0.051, 2, 4.1, 0.024, 2, 4.133, -0.015, 2, 4.167, -0.059, 2, 4.2, -0.103, 2, 4.233, -0.141, 2, 4.267, -0.168, 2, 4.3, -0.179, 2, 4.333, -0.177, 2, 4.367, -0.17, 2, 4.4, -0.16, 2, 4.433, -0.146, 2, 4.467, -0.128, 2, 4.5, -0.107, 2, 4.533, -0.084, 2, 4.567, -0.056, 2, 4.6, -0.027, 2, 4.633, 0.005, 2, 4.667, 0.039, 2, 4.7, 0.075, 2, 4.733, 0.113, 2, 4.767, 0.153, 2, 4.8, 0.193, 2, 4.833, 0.235, 2, 4.867, 0.277, 2, 4.9, 0.321, 2, 4.933, 0.365, 2, 4.967, 0.409, 2, 5, 0.453, 2, 5.033, 0.496, 2, 5.067, 0.54, 2, 5.1, 0.583, 2, 5.133, 0.625, 2, 5.167, 0.665, 2, 5.2, 0.705, 2, 5.233, 0.743, 2, 5.267, 0.779, 2, 5.3, 0.813, 2, 5.333, 0.845, 2, 5.367, 0.874, 2, 5.4, 0.901, 2, 5.433, 0.925, 2, 5.467, 0.946, 2, 5.5, 0.964, 2, 5.533, 0.978, 2, 5.567, 0.988, 2, 5.6, 0.994, 2, 5.633, 0.997, 2, 5.667, 0.994, 2, 5.7, 0.996, 2, 5.733, 0.994, 2, 5.767, 0.989, 2, 5.8, 0.982, 2, 5.833, 0.972, 2, 5.867, 0.959, 2, 5.9, 0.945, 2, 5.933, 0.929, 2, 5.967, 0.911, 2, 6, 0.893, 2, 6.033, 0.872, 2, 6.067, 0.851, 2, 6.1, 0.83, 2, 6.133, 0.808, 2, 6.167, 0.785, 2, 6.2, 0.763, 2, 6.233, 0.742, 2, 6.267, 0.721, 2, 6.3, 0.701, 2, 6.333, 0.682, 2, 6.367, 0.664, 2, 6.4, 0.648, 2, 6.433, 0.634, 2, 6.467, 0.621, 2, 6.5, 0.611, 2, 6.533, 0.604, 2, 6.567, 0.599, 2, 6.6, 0.598, 2, 6.633, 0.602, 2, 6.667, 0.614, 2, 6.7, 0.631, 2, 6.733, 0.651, 2, 6.767, 0.672, 2, 6.8, 0.692, 2, 6.833, 0.709, 2, 6.867, 0.721, 2, 6.9, 0.725, 2, 6.933, 0.724, 2, 6.967, 0.721, 2, 7, 0.716, 2, 7.033, 0.711, 2, 7.067, 0.705, 2, 7.1, 0.7, 2, 7.133, 0.695, 2, 7.167, 0.692, 2, 7.2, 0.691, 2, 7.233, 0.691, 2, 7.267, 0.692, 2, 7.3, 0.693, 2, 7.333, 0.695, 2, 7.367, 0.696, 2, 7.4, 0.698, 2, 7.433, 0.699, 2, 7.467, 0.7, 2, 7.5, 0.7, 2, 7.533, 0.7, 2, 7.567, 0.7, 2, 7.6, 0.699, 2, 7.633, 0.699, 2, 7.667, 0.699, 2, 7.7, 0.698, 2, 7.733, 0.698, 2, 7.767, 0.698, 2, 7.8, 0.698, 2, 7.867, 0.698, 2, 7.9, 0.698, 2, 7.933, 0.698, 2, 7.967, 0.698, 2, 8, 0.698]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh3", "Segments": [0, 0, 2, 0.033, -0.111, 2, 0.067, -0.355, 2, 0.1, -0.599, 2, 0.133, -0.71, 2, 0.167, -0.562, 2, 0.2, -0.209, 2, 0.233, 0.221, 2, 0.267, 0.574, 2, 0.3, 0.722, 2, 0.333, 0.663, 2, 0.367, 0.512, 2, 0.4, 0.302, 2, 0.433, 0.079, 2, 0.467, -0.131, 2, 0.5, -0.282, 2, 0.533, -0.34, 2, 0.567, -0.304, 2, 0.6, -0.208, 2, 0.633, -0.073, 2, 0.667, 0.082, 2, 0.7, 0.237, 2, 0.733, 0.372, 2, 0.767, 0.468, 2, 0.8, 0.504, 2, 0.833, 0.471, 2, 0.867, 0.38, 2, 0.9, 0.24, 2, 0.933, 0.063, 2, 0.967, -0.132, 2, 1, -0.343, 2, 1.033, -0.555, 2, 1.067, -0.75, 2, 1.1, -0.926, 2, 1.133, -1.066, 2, 1.167, -1.157, 2, 1.2, -1.191, 2, 1.233, -1.087, 2, 1.267, -0.819, 2, 1.3, -0.447, 2, 1.333, -0.053, 2, 1.367, 0.319, 2, 1.4, 0.587, 2, 1.433, 0.691, 2, 1.467, 0.677, 2, 1.5, 0.638, 2, 1.533, 0.576, 2, 1.567, 0.5, 2, 1.6, 0.409, 2, 1.633, 0.31, 2, 1.667, 0.207, 2, 1.7, 0.105, 2, 1.733, 0.006, 2, 1.767, -0.085, 2, 1.8, -0.162, 2, 1.833, -0.223, 2, 1.867, -0.262, 2, 1.9, -0.276, 2, 1.933, -0.264, 2, 1.967, -0.23, 2, 2, -0.182, 2, 2.033, -0.124, 2, 2.067, -0.065, 2, 2.1, -0.007, 2, 2.133, 0.042, 2, 2.167, 0.075, 2, 2.2, 0.088, 2, 2.233, 0.08, 2, 2.267, 0.061, 2, 2.3, 0.034, 2, 2.333, 0.003, 2, 2.367, -0.028, 2, 2.4, -0.056, 2, 2.433, -0.075, 2, 2.467, -0.082, 2, 2.5, -0.08, 2, 2.533, -0.081, 2, 2.567, -0.061, 2, 2.6, -0.015, 2, 2.633, 0.047, 2, 2.667, 0.109, 2, 2.7, 0.155, 2, 2.733, 0.174, 2, 2.767, 0.159, 2, 2.8, 0.117, 2, 2.833, 0.057, 2, 2.867, -0.014, 2, 2.9, -0.087, 2, 2.933, -0.158, 2, 2.967, -0.218, 2, 3, -0.26, 2, 3.033, -0.275, 2, 3.067, -0.265, 2, 3.1, -0.235, 2, 3.133, -0.192, 2, 3.167, -0.136, 2, 3.2, -0.071, 2, 3.233, -0.002, 2, 3.267, 0.07, 2, 3.3, 0.139, 2, 3.333, 0.204, 2, 3.367, 0.26, 2, 3.4, 0.304, 2, 3.433, 0.333, 2, 3.467, 0.344, 2, 3.5, 0.326, 2, 3.533, 0.278, 2, 3.567, 0.21, 2, 3.6, 0.129, 2, 3.633, 0.045, 2, 3.667, -0.036, 2, 3.7, -0.105, 2, 3.733, -0.152, 2, 3.767, -0.17, 2, 3.8, -0.166, 2, 3.833, -0.156, 2, 3.867, -0.139, 2, 3.9, -0.119, 2, 3.933, -0.094, 2, 3.967, -0.068, 2, 4, -0.041, 2, 4.033, -0.013, 2, 4.067, 0.013, 2, 4.1, 0.037, 2, 4.133, 0.058, 2, 4.167, 0.074, 2, 4.2, 0.085, 2, 4.233, 0.088, 2, 4.267, 0.069, 2, 4.3, 0.018, 2, 4.333, -0.053, 2, 4.367, -0.128, 2, 4.4, -0.199, 2, 4.433, -0.25, 2, 4.467, -0.27, 2, 4.5, -0.258, 2, 4.533, -0.224, 2, 4.567, -0.176, 2, 4.6, -0.119, 2, 4.633, -0.06, 2, 4.667, -0.002, 2, 4.7, 0.046, 2, 4.733, 0.079, 2, 4.767, 0.092, 2, 4.8, 0.084, 2, 4.833, 0.063, 2, 4.867, 0.034, 2, 4.9, 0.001, 2, 4.933, -0.032, 2, 4.967, -0.061, 2, 5, -0.082, 2, 5.033, -0.089, 2, 5.067, -0.082, 2, 5.1, -0.064, 2, 5.133, -0.039, 2, 5.167, -0.012, 2, 5.2, 0.013, 2, 5.233, 0.031, 2, 5.267, 0.038, 2, 5.3, 0.036, 2, 5.333, 0.031, 2, 5.367, 0.023, 2, 5.4, 0.014, 2, 5.433, 0.004, 2, 5.467, -0.005, 2, 5.5, -0.013, 2, 5.533, -0.018, 2, 5.567, -0.02, 2, 5.6, -0.019, 2, 5.633, -0.014, 2, 5.667, -0.007, 2, 5.7, 0, 2, 5.733, 0.007, 2, 5.767, 0.014, 2, 5.8, 0.019, 2, 5.833, 0.02, 2, 5.867, 0.02, 2, 5.9, 0.023, 2, 5.933, 0.02, 2, 5.967, 0.022, 2, 6, 0.018, 2, 6.033, 0.02, 2, 6.067, 0.017, 2, 6.1, 0.019, 2, 6.133, 0.017, 2, 6.167, 0.02, 2, 6.2, 0.02, 2, 6.233, 0.02, 2, 6.267, 0.022, 2, 6.3, 0.023, 2, 6.333, 0.026, 2, 6.367, 0.028, 2, 6.4, 0.03, 2, 6.433, 0.033, 2, 6.467, 0.035, 2, 6.5, 0.036, 2, 6.533, 0.036, 2, 6.567, 0.028, 2, 6.6, 0.007, 2, 6.633, -0.02, 2, 6.667, -0.048, 2, 6.7, -0.069, 2, 6.733, -0.077, 2, 6.767, -0.073, 2, 6.8, -0.062, 2, 6.833, -0.045, 2, 6.867, -0.026, 2, 6.9, -0.006, 2, 6.933, 0.014, 2, 6.967, 0.03, 2, 7, 0.041, 2, 7.033, 0.046, 2, 7.067, 0.043, 2, 7.1, 0.038, 2, 7.133, 0.029, 2, 7.167, 0.019, 2, 7.2, 0.008, 2, 7.233, -0.002, 2, 7.267, -0.011, 2, 7.3, -0.017, 2, 7.333, -0.019, 2, 7.367, -0.018, 2, 7.4, -0.016, 2, 7.433, -0.012, 2, 7.467, -0.008, 2, 7.5, -0.004, 2, 7.533, 0, 2, 7.567, 0.004, 2, 7.6, 0.006, 2, 7.633, 0.007, 2, 7.667, 0.007, 2, 7.7, 0.006, 2, 7.733, 0.005, 2, 7.767, 0.003, 2, 7.8, 0.002, 2, 7.833, 0, 2, 7.867, -0.001, 2, 7.9, -0.002, 2, 7.933, -0.002, 2, 7.967, -0.002, 2, 8, -0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh3", "Segments": [0, 0, 2, 0.033, -0.039, 2, 0.067, -0.136, 2, 0.1, -0.264, 2, 0.133, -0.392, 2, 0.167, -0.488, 2, 0.2, -0.528, 2, 0.233, -0.463, 2, 0.267, -0.295, 2, 0.3, -0.061, 2, 0.333, 0.186, 2, 0.367, 0.42, 2, 0.4, 0.588, 2, 0.433, 0.653, 2, 0.467, 0.597, 2, 0.5, 0.452, 2, 0.533, 0.251, 2, 0.567, 0.037, 2, 0.6, -0.164, 2, 0.633, -0.309, 2, 0.667, -0.366, 2, 0.7, -0.324, 2, 0.733, -0.214, 2, 0.767, -0.059, 2, 0.8, 0.119, 2, 0.833, 0.297, 2, 0.867, 0.453, 2, 0.9, 0.563, 2, 0.933, 0.604, 2, 0.967, 0.551, 2, 1, 0.407, 2, 1.033, 0.189, 2, 1.067, -0.07, 2, 1.1, -0.352, 2, 1.133, -0.635, 2, 1.167, -0.894, 2, 1.2, -1.112, 2, 1.233, -1.256, 2, 1.267, -1.309, 2, 1.3, -1.23, 2, 1.333, -1.017, 2, 1.367, -0.708, 2, 1.4, -0.343, 2, 1.433, 0.035, 2, 1.467, 0.4, 2, 1.5, 0.709, 2, 1.533, 0.922, 2, 1.567, 1.001, 2, 1.6, 0.966, 2, 1.633, 0.872, 2, 1.667, 0.728, 2, 1.7, 0.555, 2, 1.733, 0.357, 2, 1.767, 0.157, 2, 1.8, -0.04, 2, 1.833, -0.214, 2, 1.867, -0.357, 2, 1.9, -0.451, 2, 1.933, -0.487, 2, 1.967, -0.468, 2, 2, -0.417, 2, 2.033, -0.34, 2, 2.067, -0.248, 2, 2.1, -0.148, 2, 2.133, -0.049, 2, 2.167, 0.043, 2, 2.2, 0.12, 2, 2.233, 0.171, 2, 2.267, 0.19, 2, 2.3, 0.181, 2, 2.333, 0.156, 2, 2.367, 0.119, 2, 2.4, 0.075, 2, 2.433, 0.027, 2, 2.467, -0.02, 2, 2.5, -0.065, 2, 2.533, -0.101, 2, 2.567, -0.126, 2, 2.6, -0.135, 2, 2.633, -0.115, 2, 2.667, -0.063, 2, 2.7, 0.01, 2, 2.733, 0.086, 2, 2.767, 0.159, 2, 2.8, 0.211, 2, 2.833, 0.231, 2, 2.867, 0.212, 2, 2.9, 0.16, 2, 2.933, 0.086, 2, 2.967, -0.003, 2, 3, -0.094, 2, 3.033, -0.183, 2, 3.067, -0.258, 2, 3.1, -0.309, 2, 3.133, -0.329, 2, 3.167, -0.306, 2, 3.2, -0.245, 2, 3.233, -0.153, 2, 3.267, -0.043, 2, 3.3, 0.077, 2, 3.333, 0.197, 2, 3.367, 0.307, 2, 3.4, 0.399, 2, 3.433, 0.46, 2, 3.467, 0.483, 2, 3.5, 0.468, 2, 3.533, 0.427, 2, 3.567, 0.365, 2, 3.6, 0.286, 2, 3.633, 0.199, 2, 3.667, 0.105, 2, 3.7, 0.011, 2, 3.733, -0.076, 2, 3.767, -0.155, 2, 3.8, -0.217, 2, 3.833, -0.258, 2, 3.867, -0.273, 2, 3.9, -0.263, 2, 3.933, -0.238, 2, 3.967, -0.198, 2, 4, -0.151, 2, 4.033, -0.097, 2, 4.067, -0.043, 2, 4.1, 0.011, 2, 4.133, 0.058, 2, 4.167, 0.097, 2, 4.2, 0.123, 2, 4.233, 0.133, 2, 4.267, 0.12, 2, 4.3, 0.088, 2, 4.333, 0.038, 2, 4.367, -0.021, 2, 4.4, -0.085, 2, 4.433, -0.15, 2, 4.467, -0.209, 2, 4.5, -0.258, 2, 4.533, -0.291, 2, 4.567, -0.303, 2, 4.6, -0.287, 2, 4.633, -0.244, 2, 4.667, -0.183, 2, 4.7, -0.109, 2, 4.733, -0.034, 2, 4.767, 0.04, 2, 4.8, 0.102, 2, 4.833, 0.144, 2, 4.867, 0.16, 2, 4.9, 0.148, 2, 4.933, 0.117, 2, 4.967, 0.072, 2, 5, 0.02, 2, 5.033, -0.031, 2, 5.067, -0.076, 2, 5.1, -0.108, 2, 5.133, -0.12, 2, 5.2, -0.112, 2, 5.233, -0.092, 2, 5.267, -0.062, 2, 5.3, -0.029, 2, 5.333, 0.005, 2, 5.367, 0.034, 2, 5.4, 0.055, 2, 5.433, 0.062, 2, 5.467, 0.058, 2, 5.5, 0.048, 2, 5.533, 0.033, 2, 5.567, 0.016, 2, 5.6, -0.002, 2, 5.633, -0.017, 2, 5.667, -0.027, 2, 5.7, -0.031, 2, 5.733, -0.029, 2, 5.767, -0.023, 2, 5.8, -0.014, 2, 5.833, -0.004, 2, 5.867, 0.007, 2, 5.9, 0.017, 2, 5.933, 0.026, 2, 5.967, 0.032, 2, 6, 0.034, 2, 6.033, 0.033, 2, 6.067, 0.031, 2, 6.1, 0.029, 2, 6.133, 0.025, 2, 6.167, 0.022, 2, 6.2, 0.02, 2, 6.233, 0.018, 2, 6.267, 0.017, 2, 6.3, 0.018, 2, 6.333, 0.019, 2, 6.367, 0.022, 2, 6.4, 0.025, 2, 6.433, 0.029, 2, 6.467, 0.032, 2, 6.5, 0.035, 2, 6.533, 0.037, 2, 6.567, 0.037, 2, 6.6, 0.033, 2, 6.633, 0.022, 2, 6.667, 0.005, 2, 6.7, -0.014, 2, 6.733, -0.034, 2, 6.767, -0.054, 2, 6.8, -0.07, 2, 6.833, -0.081, 2, 6.867, -0.086, 2, 6.9, -0.079, 2, 6.933, -0.062, 2, 6.967, -0.037, 2, 7, -0.009, 2, 7.033, 0.019, 2, 7.067, 0.043, 2, 7.1, 0.061, 2, 7.133, 0.067, 2, 7.167, 0.064, 2, 7.2, 0.054, 2, 7.233, 0.04, 2, 7.267, 0.024, 2, 7.3, 0.007, 2, 7.333, -0.009, 2, 7.367, -0.023, 2, 7.4, -0.033, 2, 7.433, -0.036, 2, 7.467, -0.034, 2, 7.5, -0.03, 2, 7.533, -0.023, 2, 7.567, -0.014, 2, 7.6, -0.006, 2, 7.633, 0.003, 2, 7.667, 0.01, 2, 7.7, 0.014, 2, 7.733, 0.016, 2, 7.8, 0.015, 2, 7.833, 0.013, 2, 7.867, 0.01, 2, 7.9, 0.007, 2, 7.933, 0.003, 2, 7.967, -0.001, 2, 8, -0.004]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh3", "Segments": [0, 0.001, 2, 0.033, -0.015, 2, 0.067, -0.057, 2, 0.1, -0.119, 2, 0.133, -0.192, 2, 0.167, -0.268, 2, 0.2, -0.341, 2, 0.233, -0.402, 2, 0.267, -0.445, 2, 0.3, -0.461, 2, 0.333, -0.397, 2, 0.367, -0.233, 2, 0.4, -0.004, 2, 0.433, 0.238, 2, 0.467, 0.467, 2, 0.5, 0.631, 2, 0.533, 0.695, 2, 0.567, 0.647, 2, 0.6, 0.521, 2, 0.633, 0.342, 2, 0.667, 0.137, 2, 0.7, -0.068, 2, 0.733, -0.247, 2, 0.767, -0.373, 2, 0.8, -0.421, 2, 0.833, -0.372, 2, 0.867, -0.243, 2, 0.9, -0.061, 2, 0.933, 0.148, 2, 0.967, 0.357, 2, 1, 0.539, 2, 1.033, 0.668, 2, 1.067, 0.717, 2, 1.1, 0.639, 2, 1.133, 0.431, 2, 1.167, 0.129, 2, 1.2, -0.228, 2, 1.233, -0.597, 2, 1.267, -0.955, 2, 1.3, -1.256, 2, 1.333, -1.464, 2, 1.367, -1.542, 2, 1.4, -1.44, 2, 1.433, -1.167, 2, 1.467, -0.772, 2, 1.5, -0.304, 2, 1.533, 0.179, 2, 1.567, 0.647, 2, 1.6, 1.042, 2, 1.633, 1.315, 2, 1.667, 1.417, 2, 1.7, 1.353, 2, 1.733, 1.179, 2, 1.767, 0.917, 2, 1.8, 0.604, 2, 1.833, 0.264, 2, 1.867, -0.076, 2, 1.9, -0.388, 2, 1.933, -0.651, 2, 1.967, -0.825, 2, 2, -0.889, 2, 2.033, -0.858, 2, 2.067, -0.777, 2, 2.1, -0.653, 2, 2.133, -0.503, 2, 2.167, -0.332, 2, 2.2, -0.159, 2, 2.233, 0.011, 2, 2.267, 0.161, 2, 2.3, 0.285, 2, 2.333, 0.367, 2, 2.367, 0.397, 2, 2.4, 0.375, 2, 2.433, 0.317, 2, 2.467, 0.232, 2, 2.5, 0.132, 2, 2.533, 0.028, 2, 2.567, -0.072, 2, 2.6, -0.157, 2, 2.633, -0.215, 2, 2.667, -0.237, 2, 2.7, -0.22, 2, 2.733, -0.173, 2, 2.767, -0.106, 2, 2.8, -0.026, 2, 2.833, 0.057, 2, 2.867, 0.137, 2, 2.9, 0.204, 2, 2.933, 0.251, 2, 2.967, 0.268, 2, 3, 0.24, 2, 3.033, 0.165, 2, 3.067, 0.059, 2, 3.1, -0.063, 2, 3.133, -0.184, 2, 3.167, -0.29, 2, 3.2, -0.365, 2, 3.233, -0.393, 2, 3.267, -0.364, 2, 3.3, -0.285, 2, 3.333, -0.166, 2, 3.367, -0.024, 2, 3.4, 0.131, 2, 3.433, 0.285, 2, 3.467, 0.427, 2, 3.5, 0.546, 2, 3.533, 0.625, 2, 3.567, 0.654, 2, 3.6, 0.629, 2, 3.633, 0.56, 2, 3.667, 0.456, 2, 3.7, 0.329, 2, 3.733, 0.185, 2, 3.767, 0.04, 2, 3.8, -0.104, 2, 3.833, -0.23, 2, 3.867, -0.335, 2, 3.9, -0.403, 2, 3.933, -0.429, 2, 3.967, -0.413, 2, 4, -0.37, 2, 4.033, -0.305, 2, 4.067, -0.226, 2, 4.1, -0.136, 2, 4.133, -0.045, 2, 4.167, 0.045, 2, 4.2, 0.124, 2, 4.233, 0.189, 2, 4.267, 0.232, 2, 4.3, 0.248, 2, 4.333, 0.234, 2, 4.367, 0.194, 2, 4.4, 0.134, 2, 4.433, 0.062, 2, 4.467, -0.021, 2, 4.5, -0.105, 2, 4.533, -0.187, 2, 4.567, -0.26, 2, 4.6, -0.32, 2, 4.633, -0.359, 2, 4.667, -0.374, 2, 4.7, -0.353, 2, 4.733, -0.295, 2, 4.767, -0.212, 2, 4.8, -0.114, 2, 4.833, -0.012, 2, 4.867, 0.086, 2, 4.9, 0.169, 2, 4.933, 0.227, 2, 4.967, 0.248, 2, 5, 0.233, 2, 5.033, 0.194, 2, 5.067, 0.136, 2, 5.1, 0.068, 2, 5.133, -0.002, 2, 5.167, -0.071, 2, 5.2, -0.128, 2, 5.233, -0.168, 2, 5.267, -0.183, 2, 5.3, -0.173, 2, 5.333, -0.146, 2, 5.367, -0.108, 2, 5.4, -0.063, 2, 5.433, -0.016, 2, 5.467, 0.029, 2, 5.5, 0.067, 2, 5.533, 0.093, 2, 5.567, 0.103, 2, 5.6, 0.097, 2, 5.633, 0.079, 2, 5.667, 0.054, 2, 5.7, 0.025, 2, 5.733, -0.004, 2, 5.767, -0.029, 2, 5.8, -0.046, 2, 5.833, -0.053, 2, 5.867, -0.05, 2, 5.9, -0.04, 2, 5.933, -0.026, 2, 5.967, -0.009, 2, 6, 0.008, 2, 6.033, 0.025, 2, 6.067, 0.039, 2, 6.1, 0.049, 2, 6.133, 0.053, 2, 6.167, 0.051, 2, 6.2, 0.046, 2, 6.233, 0.04, 2, 6.267, 0.032, 2, 6.3, 0.024, 2, 6.333, 0.018, 2, 6.367, 0.013, 2, 6.4, 0.011, 2, 6.433, 0.013, 2, 6.467, 0.018, 2, 6.5, 0.024, 2, 6.533, 0.029, 2, 6.567, 0.031, 2, 6.6, 0.028, 2, 6.633, 0.02, 2, 6.667, 0.008, 2, 6.7, -0.007, 2, 6.733, -0.025, 2, 6.767, -0.042, 2, 6.8, -0.059, 2, 6.833, -0.074, 2, 6.867, -0.087, 2, 6.9, -0.095, 2, 6.933, -0.098, 2, 6.967, -0.093, 2, 7, -0.078, 2, 7.033, -0.057, 2, 7.067, -0.031, 2, 7.1, -0.002, 2, 7.133, 0.026, 2, 7.167, 0.052, 2, 7.2, 0.073, 2, 7.233, 0.088, 2, 7.267, 0.093, 2, 7.3, 0.088, 2, 7.333, 0.074, 2, 7.367, 0.053, 2, 7.4, 0.029, 2, 7.433, 0.003, 2, 7.467, -0.021, 2, 7.5, -0.042, 2, 7.533, -0.056, 2, 7.567, -0.061, 2, 7.6, -0.058, 2, 7.633, -0.049, 2, 7.667, -0.037, 2, 7.7, -0.022, 2, 7.733, -0.007, 2, 7.767, 0.008, 2, 7.8, 0.021, 2, 7.833, 0.029, 2, 7.867, 0.033, 2, 7.9, 0.031, 2, 7.933, 0.027, 2, 7.967, 0.02, 2, 8, 0.013]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh46", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh46", "Segments": [0, 0, 2, 0.033, 0.096, 2, 0.067, 0.325, 2, 0.1, 0.605, 2, 0.133, 0.834, 2, 0.167, 0.93, 2, 0.2, 0.907, 2, 0.233, 0.848, 2, 0.267, 0.765, 2, 0.3, 0.678, 2, 0.333, 0.595, 2, 0.367, 0.535, 2, 0.4, 0.512, 2, 0.433, 0.518, 2, 0.467, 0.533, 2, 0.5, 0.551, 2, 0.533, 0.565, 2, 0.567, 0.571, 2, 0.6, 0.545, 2, 0.633, 0.472, 2, 0.667, 0.358, 2, 0.7, 0.217, 2, 0.733, 0.047, 2, 0.767, -0.136, 2, 0.8, -0.326, 2, 0.833, -0.517, 2, 0.867, -0.7, 2, 0.9, -0.869, 2, 0.933, -1.011, 2, 0.967, -1.125, 2, 1, -1.198, 2, 1.033, -1.224, 2, 1.067, -1.192, 2, 1.1, -1.102, 2, 1.133, -0.962, 2, 1.167, -0.787, 2, 1.2, -0.579, 2, 1.233, -0.354, 2, 1.267, -0.119, 2, 1.3, 0.116, 2, 1.333, 0.341, 2, 1.367, 0.549, 2, 1.4, 0.723, 2, 1.433, 0.864, 2, 1.467, 0.953, 2, 1.5, 0.986, 2, 1.533, 0.968, 2, 1.567, 0.921, 2, 1.6, 0.853, 2, 1.633, 0.772, 2, 1.667, 0.689, 2, 1.7, 0.609, 2, 1.733, 0.541, 2, 1.767, 0.494, 2, 1.8, 0.476, 2, 1.833, 0.481, 2, 1.867, 0.495, 2, 1.9, 0.517, 2, 1.933, 0.547, 2, 1.967, 0.582, 2, 2, 0.624, 2, 2.033, 0.671, 2, 2.067, 0.721, 2, 2.1, 0.776, 2, 2.133, 0.833, 2, 2.167, 0.89, 2, 2.2, 0.95, 2, 2.233, 1.01, 2, 2.267, 1.067, 2, 2.3, 1.124, 2, 2.333, 1.179, 2, 2.367, 1.229, 2, 2.4, 1.276, 2, 2.433, 1.318, 2, 2.467, 1.353, 2, 2.5, 1.383, 2, 2.533, 1.405, 2, 2.567, 1.419, 2, 2.6, 1.424, 2, 2.633, 1.416, 2, 2.667, 1.396, 2, 2.7, 1.368, 2, 2.733, 1.335, 2, 2.767, 1.303, 2, 2.8, 1.275, 2, 2.833, 1.255, 2, 2.867, 1.247, 2, 2.9, 1.256, 2, 2.933, 1.279, 2, 2.967, 1.313, 2, 3, 1.353, 2, 3.033, 1.394, 2, 3.067, 1.434, 2, 3.1, 1.468, 2, 3.133, 1.491, 2, 3.167, 1.5, 2, 3.2, 1.491, 2, 3.233, 1.467, 2, 3.267, 1.426, 2, 3.3, 1.374, 2, 3.333, 1.309, 2, 3.367, 1.235, 2, 3.4, 1.151, 2, 3.433, 1.062, 2, 3.467, 0.965, 2, 3.5, 0.867, 2, 3.533, 0.764, 2, 3.567, 0.663, 2, 3.6, 0.56, 2, 3.633, 0.462, 2, 3.667, 0.365, 2, 3.7, 0.276, 2, 3.733, 0.192, 2, 3.767, 0.119, 2, 3.8, 0.053, 2, 3.833, 0.001, 2, 3.867, -0.039, 2, 3.9, -0.064, 2, 3.933, -0.073, 2, 3.967, -0.069, 2, 4, -0.062, 2, 4.033, -0.058, 2, 4.067, -0.067, 2, 4.1, -0.089, 2, 4.133, -0.122, 2, 4.167, -0.159, 2, 4.2, -0.195, 2, 4.233, -0.228, 2, 4.267, -0.25, 2, 4.3, -0.259, 2, 4.333, -0.257, 2, 4.367, -0.251, 2, 4.4, -0.242, 2, 4.433, -0.229, 2, 4.467, -0.213, 2, 4.5, -0.195, 2, 4.533, -0.173, 2, 4.567, -0.149, 2, 4.6, -0.122, 2, 4.633, -0.093, 2, 4.667, -0.063, 2, 4.7, -0.03, 2, 4.733, 0.004, 2, 4.767, 0.04, 2, 4.8, 0.076, 2, 4.833, 0.114, 2, 4.867, 0.152, 2, 4.9, 0.192, 2, 4.933, 0.231, 2, 4.967, 0.271, 2, 5, 0.31, 2, 5.033, 0.35, 2, 5.067, 0.389, 2, 5.1, 0.428, 2, 5.133, 0.465, 2, 5.167, 0.502, 2, 5.2, 0.537, 2, 5.233, 0.572, 2, 5.267, 0.604, 2, 5.3, 0.635, 2, 5.333, 0.663, 2, 5.367, 0.69, 2, 5.4, 0.715, 2, 5.433, 0.736, 2, 5.467, 0.755, 2, 5.5, 0.771, 2, 5.533, 0.783, 2, 5.567, 0.793, 2, 5.6, 0.798, 2, 5.633, 0.8, 2, 5.667, 0.8, 2, 5.7, 0.802, 2, 5.733, 0.8, 2, 5.767, 0.796, 2, 5.8, 0.79, 2, 5.833, 0.782, 2, 5.867, 0.771, 2, 5.9, 0.759, 2, 5.933, 0.745, 2, 5.967, 0.729, 2, 6, 0.713, 2, 6.033, 0.695, 2, 6.067, 0.677, 2, 6.1, 0.658, 2, 6.133, 0.639, 2, 6.167, 0.62, 2, 6.2, 0.6, 2, 6.233, 0.581, 2, 6.267, 0.562, 2, 6.3, 0.544, 2, 6.333, 0.526, 2, 6.367, 0.51, 2, 6.4, 0.494, 2, 6.433, 0.481, 2, 6.467, 0.468, 2, 6.5, 0.458, 2, 6.533, 0.449, 2, 6.567, 0.443, 2, 6.6, 0.439, 2, 6.633, 0.438, 2, 6.667, 0.44, 2, 6.7, 0.447, 2, 6.733, 0.457, 2, 6.767, 0.47, 2, 6.8, 0.484, 2, 6.833, 0.498, 2, 6.867, 0.513, 2, 6.9, 0.525, 2, 6.933, 0.536, 2, 6.967, 0.542, 2, 7, 0.545, 2, 7.033, 0.544, 2, 7.067, 0.543, 2, 7.1, 0.54, 2, 7.133, 0.537, 2, 7.167, 0.533, 2, 7.2, 0.53, 2, 7.233, 0.526, 2, 7.267, 0.523, 2, 7.3, 0.521, 2, 7.333, 0.519, 2, 7.367, 0.518, 2, 7.433, 0.519, 2, 7.467, 0.519, 2, 7.5, 0.52, 2, 7.533, 0.52, 2, 7.567, 0.521, 2, 7.6, 0.522, 2, 7.633, 0.523, 2, 7.667, 0.524, 2, 7.7, 0.524, 2, 7.733, 0.525, 2, 7.767, 0.525, 2, 7.8, 0.525, 2, 7.833, 0.525, 2, 7.867, 0.525, 2, 7.9, 0.524, 2, 7.933, 0.524, 2, 7.967, 0.524, 2, 8, 0.524]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh46", "Segments": [0, 0, 2, 0.033, -0.092, 2, 0.067, -0.294, 2, 0.1, -0.497, 2, 0.133, -0.589, 2, 0.167, -0.536, 2, 0.2, -0.402, 2, 0.233, -0.214, 2, 0.267, -0.015, 2, 0.3, 0.172, 2, 0.333, 0.307, 2, 0.367, 0.359, 2, 0.4, 0.339, 2, 0.433, 0.288, 2, 0.467, 0.218, 2, 0.5, 0.143, 2, 0.533, 0.072, 2, 0.567, 0.021, 2, 0.6, 0.001, 2, 0.633, 0.014, 2, 0.667, 0.049, 2, 0.7, 0.103, 2, 0.733, 0.166, 2, 0.767, 0.235, 2, 0.8, 0.303, 2, 0.833, 0.367, 2, 0.867, 0.42, 2, 0.9, 0.455, 2, 0.933, 0.468, 2, 0.967, 0.412, 2, 1, 0.262, 2, 1.033, 0.045, 2, 1.067, -0.212, 2, 1.1, -0.477, 2, 1.133, -0.734, 2, 1.167, -0.951, 2, 1.2, -1.101, 2, 1.233, -1.157, 2, 1.267, -1.129, 2, 1.3, -1.047, 2, 1.333, -0.924, 2, 1.367, -0.767, 2, 1.4, -0.587, 2, 1.433, -0.395, 2, 1.467, -0.193, 2, 1.5, 0, 2, 1.533, 0.18, 2, 1.567, 0.337, 2, 1.6, 0.46, 2, 1.633, 0.542, 2, 1.667, 0.57, 2, 1.7, 0.543, 2, 1.733, 0.467, 2, 1.767, 0.354, 2, 1.8, 0.219, 2, 1.833, 0.072, 2, 1.867, -0.075, 2, 1.9, -0.21, 2, 1.933, -0.323, 2, 1.967, -0.398, 2, 2, -0.426, 2, 2.033, -0.414, 2, 2.067, -0.381, 2, 2.1, -0.331, 2, 2.133, -0.27, 2, 2.167, -0.201, 2, 2.2, -0.132, 2, 2.233, -0.063, 2, 2.267, -0.002, 2, 2.3, 0.048, 2, 2.333, 0.081, 2, 2.367, 0.093, 2, 2.4, 0.087, 2, 2.433, 0.071, 2, 2.467, 0.05, 2, 2.5, 0.027, 2, 2.533, 0.005, 2, 2.567, -0.01, 2, 2.6, -0.016, 2, 2.633, -0.009, 2, 2.667, 0.009, 2, 2.7, 0.032, 2, 2.733, 0.056, 2, 2.767, 0.074, 2, 2.8, 0.081, 2, 2.833, 0.071, 2, 2.867, 0.044, 2, 2.9, 0.005, 2, 2.933, -0.039, 2, 2.967, -0.082, 2, 3, -0.121, 2, 3.033, -0.148, 2, 3.067, -0.158, 2, 3.1, -0.148, 2, 3.133, -0.119, 2, 3.167, -0.077, 2, 3.2, -0.022, 2, 3.233, 0.041, 2, 3.267, 0.108, 2, 3.3, 0.179, 2, 3.333, 0.246, 2, 3.367, 0.309, 2, 3.4, 0.363, 2, 3.433, 0.406, 2, 3.467, 0.435, 2, 3.5, 0.445, 2, 3.533, 0.432, 2, 3.567, 0.4, 2, 3.6, 0.349, 2, 3.633, 0.285, 2, 3.667, 0.215, 2, 3.7, 0.139, 2, 3.733, 0.062, 2, 3.767, -0.008, 2, 3.8, -0.072, 2, 3.833, -0.122, 2, 3.867, -0.155, 2, 3.9, -0.168, 2, 3.933, -0.16, 2, 3.967, -0.14, 2, 4, -0.11, 2, 4.033, -0.073, 2, 4.067, -0.031, 2, 4.1, 0.011, 2, 4.133, 0.053, 2, 4.167, 0.089, 2, 4.2, 0.12, 2, 4.233, 0.14, 2, 4.267, 0.147, 2, 4.3, 0.133, 2, 4.333, 0.095, 2, 4.367, 0.039, 2, 4.4, -0.026, 2, 4.433, -0.094, 2, 4.467, -0.159, 2, 4.5, -0.215, 2, 4.533, -0.253, 2, 4.567, -0.267, 2, 4.6, -0.26, 2, 4.633, -0.24, 2, 4.667, -0.21, 2, 4.7, -0.174, 2, 4.733, -0.133, 2, 4.767, -0.091, 2, 4.8, -0.049, 2, 4.833, -0.013, 2, 4.867, 0.017, 2, 4.9, 0.036, 2, 4.933, 0.044, 2, 4.967, 0.04, 2, 5, 0.031, 2, 5.033, 0.018, 2, 5.067, 0.004, 2, 5.1, -0.009, 2, 5.133, -0.018, 2, 5.167, -0.022, 2, 5.2, -0.021, 2, 5.233, -0.018, 2, 5.267, -0.015, 2, 5.3, -0.011, 2, 5.333, -0.007, 2, 5.367, -0.005, 2, 5.4, -0.004, 2, 5.433, -0.004, 2, 5.467, -0.002, 2, 5.5, -0.003, 2, 5.533, -0.001, 2, 5.567, -0.003, 2, 5.6, -0.001, 2, 5.633, -0.001, 2, 5.667, -0.001, 2, 5.7, 0.001, 2, 5.733, 0.003, 2, 5.767, 0.005, 2, 5.8, 0.008, 2, 5.833, 0.012, 2, 5.867, 0.015, 2, 5.9, 0.019, 2, 5.933, 0.022, 2, 5.967, 0.025, 2, 6, 0.028, 2, 6.033, 0.03, 2, 6.067, 0.031, 2, 6.1, 0.032, 2, 6.133, 0.032, 2, 6.167, 0.034, 2, 6.2, 0.033, 2, 6.233, 0.034, 2, 6.267, 0.035, 2, 6.3, 0.037, 2, 6.333, 0.039, 2, 6.367, 0.041, 2, 6.4, 0.043, 2, 6.433, 0.046, 2, 6.467, 0.047, 2, 6.5, 0.048, 2, 6.533, 0.049, 2, 6.567, 0.045, 2, 6.6, 0.033, 2, 6.633, 0.017, 2, 6.667, -0.003, 2, 6.7, -0.023, 2, 6.733, -0.042, 2, 6.767, -0.059, 2, 6.8, -0.07, 2, 6.833, -0.074, 2, 6.867, -0.071, 2, 6.9, -0.063, 2, 6.933, -0.05, 2, 6.967, -0.035, 2, 7, -0.018, 2, 7.033, -0.002, 2, 7.067, 0.013, 2, 7.1, 0.026, 2, 7.133, 0.034, 2, 7.167, 0.037, 2, 7.2, 0.036, 2, 7.233, 0.034, 2, 7.267, 0.029, 2, 7.3, 0.024, 2, 7.333, 0.018, 2, 7.367, 0.012, 2, 7.4, 0.005, 2, 7.433, -0.001, 2, 7.467, -0.006, 2, 7.5, -0.01, 2, 7.533, -0.013, 2, 7.567, -0.014, 2, 7.6, -0.014, 2, 7.633, -0.012, 2, 7.667, -0.011, 2, 7.7, -0.008, 2, 7.733, -0.006, 2, 7.767, -0.003, 2, 7.8, -0.001, 2, 7.833, 0.001, 2, 7.867, 0.003, 2, 7.9, 0.004, 2, 7.933, 0.005, 2, 7.967, 0.005, 2, 8, 0.004]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh46", "Segments": [0, 0.001, 2, 0.033, -0.017, 2, 0.067, -0.063, 2, 0.1, -0.129, 2, 0.133, -0.205, 2, 0.167, -0.28, 2, 0.2, -0.346, 2, 0.233, -0.392, 2, 0.267, -0.41, 2, 0.3, -0.384, 2, 0.333, -0.314, 2, 0.367, -0.213, 2, 0.4, -0.094, 2, 0.433, 0.03, 2, 0.467, 0.149, 2, 0.5, 0.25, 2, 0.533, 0.32, 2, 0.567, 0.346, 2, 0.6, 0.329, 2, 0.633, 0.287, 2, 0.667, 0.231, 2, 0.7, 0.176, 2, 0.733, 0.134, 2, 0.767, 0.117, 2, 0.8, 0.131, 2, 0.833, 0.169, 2, 0.867, 0.222, 2, 0.9, 0.284, 2, 0.933, 0.345, 2, 0.967, 0.398, 2, 1, 0.436, 2, 1.033, 0.45, 2, 1.067, 0.411, 2, 1.1, 0.308, 2, 1.133, 0.151, 2, 1.167, -0.04, 2, 1.2, -0.256, 2, 1.233, -0.476, 2, 1.267, -0.693, 2, 1.3, -0.883, 2, 1.333, -1.04, 2, 1.367, -1.144, 2, 1.4, -1.182, 2, 1.433, -1.133, 2, 1.467, -1.002, 2, 1.5, -0.803, 2, 1.533, -0.561, 2, 1.567, -0.286, 2, 1.6, -0.008, 2, 1.633, 0.267, 2, 1.667, 0.508, 2, 1.7, 0.708, 2, 1.733, 0.839, 2, 1.767, 0.888, 2, 1.8, 0.858, 2, 1.833, 0.775, 2, 1.867, 0.649, 2, 1.9, 0.489, 2, 1.933, 0.313, 2, 1.967, 0.122, 2, 2, -0.07, 2, 2.033, -0.246, 2, 2.067, -0.405, 2, 2.1, -0.532, 2, 2.133, -0.614, 2, 2.167, -0.645, 2, 2.2, -0.624, 2, 2.233, -0.568, 2, 2.267, -0.484, 2, 2.3, -0.381, 2, 2.333, -0.264, 2, 2.367, -0.146, 2, 2.4, -0.029, 2, 2.433, 0.074, 2, 2.467, 0.159, 2, 2.5, 0.215, 2, 2.533, 0.235, 2, 2.567, 0.233, 2, 2.6, 0.225, 2, 2.633, 0.213, 2, 2.667, 0.196, 2, 2.7, 0.177, 2, 2.733, 0.154, 2, 2.767, 0.13, 2, 2.8, 0.103, 2, 2.833, 0.075, 2, 2.867, 0.047, 2, 2.9, 0.019, 2, 2.933, -0.008, 2, 2.967, -0.035, 2, 3, -0.06, 2, 3.033, -0.082, 2, 3.067, -0.102, 2, 3.1, -0.118, 2, 3.133, -0.131, 2, 3.167, -0.138, 2, 3.2, -0.141, 2, 3.233, -0.131, 2, 3.267, -0.1, 2, 3.3, -0.055, 2, 3.333, 0.003, 2, 3.367, 0.07, 2, 3.4, 0.141, 2, 3.433, 0.215, 2, 3.467, 0.287, 2, 3.5, 0.353, 2, 3.533, 0.411, 2, 3.567, 0.456, 2, 3.6, 0.487, 2, 3.633, 0.497, 2, 3.667, 0.482, 2, 3.7, 0.44, 2, 3.733, 0.375, 2, 3.767, 0.293, 2, 3.8, 0.203, 2, 3.833, 0.105, 2, 3.867, 0.007, 2, 3.9, -0.083, 2, 3.933, -0.165, 2, 3.967, -0.229, 2, 4, -0.271, 2, 4.033, -0.287, 2, 4.067, -0.276, 2, 4.1, -0.246, 2, 4.133, -0.201, 2, 4.167, -0.146, 2, 4.2, -0.083, 2, 4.233, -0.02, 2, 4.267, 0.043, 2, 4.3, 0.098, 2, 4.333, 0.143, 2, 4.367, 0.173, 2, 4.4, 0.184, 2, 4.433, 0.17, 2, 4.467, 0.133, 2, 4.5, 0.078, 2, 4.533, 0.011, 2, 4.567, -0.061, 2, 4.6, -0.134, 2, 4.633, -0.2, 2, 4.667, -0.256, 2, 4.7, -0.293, 2, 4.733, -0.307, 2, 4.767, -0.297, 2, 4.8, -0.272, 2, 4.833, -0.232, 2, 4.867, -0.185, 2, 4.9, -0.131, 2, 4.933, -0.076, 2, 4.967, -0.022, 2, 5, 0.026, 2, 5.033, 0.065, 2, 5.067, 0.091, 2, 5.1, 0.101, 2, 5.133, 0.097, 2, 5.167, 0.086, 2, 5.2, 0.071, 2, 5.233, 0.052, 2, 5.267, 0.031, 2, 5.3, 0.011, 2, 5.333, -0.008, 2, 5.367, -0.024, 2, 5.4, -0.034, 2, 5.433, -0.038, 2, 5.467, -0.038, 2, 5.5, -0.037, 2, 5.533, -0.036, 2, 5.567, -0.035, 2, 5.6, -0.033, 2, 5.633, -0.031, 2, 5.667, -0.029, 2, 5.7, -0.026, 2, 5.733, -0.023, 2, 5.767, -0.02, 2, 5.8, -0.017, 2, 5.833, -0.014, 2, 5.867, -0.01, 2, 5.9, -0.007, 2, 5.933, -0.003, 2, 5.967, 0, 2, 6, 0.004, 2, 6.033, 0.008, 2, 6.067, 0.011, 2, 6.1, 0.015, 2, 6.133, 0.018, 2, 6.167, 0.022, 2, 6.2, 0.025, 2, 6.233, 0.028, 2, 6.267, 0.031, 2, 6.3, 0.034, 2, 6.333, 0.037, 2, 6.367, 0.039, 2, 6.4, 0.041, 2, 6.433, 0.043, 2, 6.467, 0.044, 2, 6.5, 0.045, 2, 6.533, 0.046, 2, 6.567, 0.046, 2, 6.6, 0.043, 2, 6.633, 0.035, 2, 6.667, 0.022, 2, 6.7, 0.008, 2, 6.733, -0.009, 2, 6.767, -0.026, 2, 6.8, -0.043, 2, 6.833, -0.058, 2, 6.867, -0.07, 2, 6.9, -0.078, 2, 6.933, -0.081, 2, 6.967, -0.079, 2, 7, -0.071, 2, 7.033, -0.06, 2, 7.067, -0.046, 2, 7.1, -0.03, 2, 7.133, -0.013, 2, 7.167, 0.004, 2, 7.2, 0.02, 2, 7.233, 0.034, 2, 7.267, 0.046, 2, 7.3, 0.053, 2, 7.333, 0.056, 2, 7.367, 0.054, 2, 7.4, 0.049, 2, 7.433, 0.041, 2, 7.467, 0.031, 2, 7.5, 0.02, 2, 7.533, 0.009, 2, 7.567, -0.002, 2, 7.6, -0.012, 2, 7.633, -0.02, 2, 7.667, -0.025, 2, 7.7, -0.027, 2, 7.733, -0.026, 2, 7.767, -0.024, 2, 7.8, -0.02, 2, 7.833, -0.016, 2, 7.867, -0.011, 2, 7.9, -0.006, 2, 7.933, 0, 2, 7.967, 0.004, 2, 8, 0.008]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh46", "Segments": [0, 0.001, 2, 0.033, 0.002, 2, 0.067, -0.012, 2, 0.1, -0.049, 2, 0.133, -0.106, 2, 0.167, -0.173, 2, 0.2, -0.246, 2, 0.233, -0.319, 2, 0.267, -0.386, 2, 0.3, -0.442, 2, 0.333, -0.479, 2, 0.367, -0.493, 2, 0.4, -0.468, 2, 0.433, -0.402, 2, 0.467, -0.3, 2, 0.5, -0.177, 2, 0.533, -0.037, 2, 0.567, 0.104, 2, 0.6, 0.244, 2, 0.633, 0.367, 2, 0.667, 0.469, 2, 0.7, 0.535, 2, 0.733, 0.56, 2, 0.767, 0.544, 2, 0.8, 0.502, 2, 0.833, 0.443, 2, 0.867, 0.381, 2, 0.9, 0.323, 2, 0.933, 0.281, 2, 0.967, 0.265, 2, 1, 0.28, 2, 1.033, 0.317, 2, 1.067, 0.366, 2, 1.1, 0.415, 2, 1.133, 0.452, 2, 1.167, 0.467, 2, 1.2, 0.416, 2, 1.233, 0.28, 2, 1.267, 0.072, 2, 1.3, -0.179, 2, 1.333, -0.465, 2, 1.367, -0.754, 2, 1.4, -1.04, 2, 1.433, -1.291, 2, 1.467, -1.498, 2, 1.5, -1.635, 2, 1.533, -1.686, 2, 1.567, -1.608, 2, 1.6, -1.4, 2, 1.633, -1.083, 2, 1.667, -0.7, 2, 1.7, -0.264, 2, 1.733, 0.178, 2, 1.767, 0.614, 2, 1.8, 0.997, 2, 1.833, 1.314, 2, 1.867, 1.522, 2, 1.9, 1.6, 2, 1.933, 1.543, 2, 1.967, 1.39, 2, 2, 1.153, 2, 2.033, 0.856, 2, 2.067, 0.527, 2, 2.1, 0.169, 2, 2.133, -0.188, 2, 2.167, -0.517, 2, 2.2, -0.814, 2, 2.233, -1.051, 2, 2.267, -1.204, 2, 2.3, -1.261, 2, 2.333, -1.224, 2, 2.367, -1.124, 2, 2.4, -0.97, 2, 2.433, -0.777, 2, 2.467, -0.563, 2, 2.5, -0.331, 2, 2.533, -0.099, 2, 2.567, 0.115, 2, 2.6, 0.309, 2, 2.633, 0.462, 2, 2.667, 0.562, 2, 2.7, 0.599, 2, 2.733, 0.59, 2, 2.767, 0.565, 2, 2.8, 0.525, 2, 2.833, 0.474, 2, 2.867, 0.414, 2, 2.9, 0.346, 2, 2.933, 0.274, 2, 2.967, 0.199, 2, 3, 0.125, 2, 3.033, 0.053, 2, 3.067, -0.015, 2, 3.1, -0.075, 2, 3.133, -0.126, 2, 3.167, -0.166, 2, 3.2, -0.191, 2, 3.233, -0.2, 2, 3.267, -0.188, 2, 3.3, -0.154, 2, 3.333, -0.101, 2, 3.367, -0.033, 2, 3.4, 0.047, 2, 3.433, 0.135, 2, 3.467, 0.228, 2, 3.5, 0.323, 2, 3.533, 0.416, 2, 3.567, 0.504, 2, 3.6, 0.584, 2, 3.633, 0.652, 2, 3.667, 0.705, 2, 3.7, 0.739, 2, 3.733, 0.751, 2, 3.767, 0.729, 2, 3.8, 0.667, 2, 3.833, 0.575, 2, 3.867, 0.456, 2, 3.9, 0.319, 2, 3.933, 0.173, 2, 3.967, 0.021, 2, 4, -0.125, 2, 4.033, -0.262, 2, 4.067, -0.381, 2, 4.1, -0.473, 2, 4.133, -0.535, 2, 4.167, -0.557, 2, 4.2, -0.536, 2, 4.233, -0.479, 2, 4.267, -0.392, 2, 4.3, -0.288, 2, 4.333, -0.169, 2, 4.367, -0.048, 2, 4.4, 0.071, 2, 4.433, 0.176, 2, 4.467, 0.262, 2, 4.5, 0.319, 2, 4.533, 0.34, 2, 4.567, 0.317, 2, 4.6, 0.254, 2, 4.633, 0.159, 2, 4.667, 0.045, 2, 4.7, -0.078, 2, 4.733, -0.202, 2, 4.767, -0.315, 2, 4.8, -0.41, 2, 4.833, -0.473, 2, 4.867, -0.497, 2, 4.9, -0.479, 2, 4.933, -0.432, 2, 4.967, -0.361, 2, 5, -0.275, 2, 5.033, -0.178, 2, 5.067, -0.078, 2, 5.1, 0.019, 2, 5.133, 0.105, 2, 5.167, 0.176, 2, 5.2, 0.223, 2, 5.233, 0.241, 2, 5.267, 0.232, 2, 5.3, 0.21, 2, 5.333, 0.177, 2, 5.367, 0.137, 2, 5.4, 0.091, 2, 5.433, 0.044, 2, 5.467, -0.002, 2, 5.5, -0.043, 2, 5.533, -0.076, 2, 5.567, -0.098, 2, 5.6, -0.106, 2, 5.633, -0.104, 2, 5.667, -0.097, 2, 5.7, -0.087, 2, 5.733, -0.075, 2, 5.767, -0.06, 2, 5.8, -0.043, 2, 5.833, -0.026, 2, 5.867, -0.01, 2, 5.9, 0.007, 2, 5.933, 0.022, 2, 5.967, 0.034, 2, 6, 0.044, 2, 6.033, 0.051, 2, 6.067, 0.053, 2, 6.1, 0.053, 2, 6.133, 0.051, 2, 6.167, 0.049, 2, 6.2, 0.047, 2, 6.233, 0.045, 2, 6.267, 0.044, 2, 6.3, 0.043, 2, 6.333, 0.044, 2, 6.367, 0.045, 2, 6.4, 0.046, 2, 6.433, 0.048, 2, 6.467, 0.051, 2, 6.5, 0.053, 2, 6.533, 0.055, 2, 6.567, 0.057, 2, 6.6, 0.059, 2, 6.633, 0.06, 2, 6.667, 0.061, 2, 6.7, 0.056, 2, 6.733, 0.045, 2, 6.767, 0.027, 2, 6.8, 0.005, 2, 6.833, -0.02, 2, 6.867, -0.046, 2, 6.9, -0.073, 2, 6.933, -0.097, 2, 6.967, -0.12, 2, 7, -0.137, 2, 7.033, -0.149, 2, 7.067, -0.153, 2, 7.1, -0.148, 2, 7.133, -0.133, 2, 7.167, -0.112, 2, 7.2, -0.084, 2, 7.233, -0.054, 2, 7.267, -0.02, 2, 7.3, 0.013, 2, 7.333, 0.043, 2, 7.367, 0.071, 2, 7.4, 0.093, 2, 7.433, 0.107, 2, 7.467, 0.112, 2, 7.5, 0.108, 2, 7.533, 0.097, 2, 7.567, 0.08, 2, 7.6, 0.059, 2, 7.633, 0.036, 2, 7.667, 0.013, 2, 7.7, -0.01, 2, 7.733, -0.031, 2, 7.767, -0.048, 2, 7.8, -0.059, 2, 7.833, -0.063, 2, 7.867, -0.061, 2, 7.9, -0.056, 2, 7.933, -0.048, 2, 7.967, -0.039, 2, 8, -0.028]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh46", "Segments": [0, -0.021, 2, 0.033, -0.019, 2, 0.067, -0.013, 2, 0.1, -0.008, 2, 0.133, -0.006, 2, 0.167, -0.017, 2, 0.2, -0.047, 2, 0.233, -0.092, 2, 0.267, -0.147, 2, 0.3, -0.209, 2, 0.333, -0.273, 2, 0.367, -0.335, 2, 0.4, -0.39, 2, 0.433, -0.436, 2, 0.467, -0.466, 2, 0.5, -0.477, 2, 0.533, -0.449, 2, 0.567, -0.374, 2, 0.6, -0.26, 2, 0.633, -0.123, 2, 0.667, 0.034, 2, 0.7, 0.193, 2, 0.733, 0.349, 2, 0.767, 0.487, 2, 0.8, 0.601, 2, 0.833, 0.675, 2, 0.867, 0.703, 2, 0.9, 0.689, 2, 0.933, 0.652, 2, 0.967, 0.598, 2, 1, 0.534, 2, 1.033, 0.468, 2, 1.067, 0.404, 2, 1.1, 0.35, 2, 1.133, 0.313, 2, 1.167, 0.299, 2, 1.2, 0.304, 2, 1.233, 0.308, 2, 1.267, 0.266, 2, 1.3, 0.152, 2, 1.333, -0.023, 2, 1.367, -0.243, 2, 1.4, -0.487, 2, 1.433, -0.752, 2, 1.467, -1.016, 2, 1.5, -1.26, 2, 1.533, -1.48, 2, 1.567, -1.656, 2, 1.6, -1.769, 2, 1.633, -1.812, 2, 1.667, -1.734, 2, 1.7, -1.528, 2, 1.733, -1.208, 2, 1.767, -0.807, 2, 1.8, -0.363, 2, 1.833, 0.119, 2, 1.867, 0.6, 2, 1.9, 1.045, 2, 1.933, 1.446, 2, 1.967, 1.765, 2, 2, 1.972, 2, 2.033, 2.049, 2, 2.067, 1.972, 2, 2.1, 1.767, 2, 2.133, 1.451, 2, 2.167, 1.054, 2, 2.2, 0.613, 2, 2.233, 0.136, 2, 2.267, -0.341, 2, 2.3, -0.781, 2, 2.333, -1.179, 2, 2.367, -1.495, 2, 2.4, -1.7, 2, 2.433, -1.776, 2, 2.467, -1.72, 2, 2.5, -1.57, 2, 2.533, -1.338, 2, 2.567, -1.047, 2, 2.6, -0.724, 2, 2.633, -0.373, 2, 2.667, -0.023, 2, 2.7, 0.3, 2, 2.733, 0.591, 2, 2.767, 0.823, 2, 2.8, 0.974, 2, 2.833, 1.03, 2, 2.867, 1.001, 2, 2.9, 0.922, 2, 2.933, 0.801, 2, 2.967, 0.649, 2, 3, 0.48, 2, 3.033, 0.297, 2, 3.067, 0.114, 2, 3.1, -0.054, 2, 3.133, -0.207, 2, 3.167, -0.328, 2, 3.2, -0.406, 2, 3.233, -0.436, 2, 3.267, -0.424, 2, 3.3, -0.392, 2, 3.333, -0.34, 2, 3.367, -0.273, 2, 3.4, -0.195, 2, 3.433, -0.104, 2, 3.467, -0.005, 2, 3.5, 0.098, 2, 3.533, 0.205, 2, 3.567, 0.311, 2, 3.6, 0.415, 2, 3.633, 0.514, 2, 3.667, 0.604, 2, 3.7, 0.683, 2, 3.733, 0.75, 2, 3.767, 0.801, 2, 3.8, 0.834, 2, 3.833, 0.845, 2, 3.867, 0.819, 2, 3.9, 0.743, 2, 3.933, 0.629, 2, 3.967, 0.484, 2, 4, 0.316, 2, 4.033, 0.137, 2, 4.067, -0.05, 2, 4.1, -0.229, 2, 4.133, -0.396, 2, 4.167, -0.542, 2, 4.2, -0.655, 2, 4.233, -0.732, 2, 4.267, -0.758, 2, 4.3, -0.733, 2, 4.333, -0.667, 2, 4.367, -0.564, 2, 4.4, -0.435, 2, 4.433, -0.292, 2, 4.467, -0.136, 2, 4.5, 0.019, 2, 4.533, 0.162, 2, 4.567, 0.291, 2, 4.6, 0.394, 2, 4.633, 0.46, 2, 4.667, 0.485, 2, 4.7, 0.455, 2, 4.733, 0.371, 2, 4.767, 0.246, 2, 4.8, 0.096, 2, 4.833, -0.067, 2, 4.867, -0.23, 2, 4.9, -0.379, 2, 4.933, -0.505, 2, 4.967, -0.588, 2, 5, -0.619, 2, 5.033, -0.599, 2, 5.067, -0.545, 2, 5.1, -0.462, 2, 5.133, -0.358, 2, 5.167, -0.243, 2, 5.2, -0.118, 2, 5.233, 0.008, 2, 5.267, 0.123, 2, 5.3, 0.227, 2, 5.333, 0.31, 2, 5.367, 0.364, 2, 5.4, 0.384, 2, 5.433, 0.37, 2, 5.467, 0.333, 2, 5.5, 0.277, 2, 5.533, 0.209, 2, 5.567, 0.132, 2, 5.6, 0.054, 2, 5.633, -0.023, 2, 5.667, -0.091, 2, 5.7, -0.147, 2, 5.733, -0.184, 2, 5.767, -0.198, 2, 5.8, -0.191, 2, 5.833, -0.175, 2, 5.867, -0.15, 2, 5.9, -0.118, 2, 5.933, -0.083, 2, 5.967, -0.045, 2, 6, -0.007, 2, 6.033, 0.028, 2, 6.067, 0.059, 2, 6.1, 0.085, 2, 6.133, 0.101, 2, 6.167, 0.107, 2, 6.2, 0.105, 2, 6.233, 0.1, 2, 6.267, 0.093, 2, 6.3, 0.084, 2, 6.333, 0.075, 2, 6.367, 0.065, 2, 6.4, 0.055, 2, 6.433, 0.046, 2, 6.467, 0.039, 2, 6.5, 0.034, 2, 6.533, 0.032, 2, 6.567, 0.034, 2, 6.6, 0.037, 2, 6.633, 0.042, 2, 6.667, 0.048, 2, 6.7, 0.053, 2, 6.733, 0.056, 2, 6.767, 0.058, 2, 6.8, 0.054, 2, 6.833, 0.044, 2, 6.867, 0.029, 2, 6.9, 0.009, 2, 6.933, -0.013, 2, 6.967, -0.037, 2, 7, -0.062, 2, 7.033, -0.086, 2, 7.067, -0.108, 2, 7.1, -0.127, 2, 7.133, -0.143, 2, 7.167, -0.153, 2, 7.2, -0.156, 2, 7.233, -0.15, 2, 7.267, -0.134, 2, 7.3, -0.108, 2, 7.333, -0.076, 2, 7.367, -0.041, 2, 7.4, -0.003, 2, 7.433, 0.036, 2, 7.467, 0.071, 2, 7.5, 0.103, 2, 7.533, 0.128, 2, 7.567, 0.145, 2, 7.6, 0.151, 2, 7.633, 0.145, 2, 7.667, 0.129, 2, 7.7, 0.105, 2, 7.733, 0.076, 2, 7.767, 0.043, 2, 7.8, 0.009, 2, 7.833, -0.024, 2, 7.867, -0.053, 2, 7.9, -0.077, 2, 7.933, -0.093, 2, 7.967, -0.099, 2, 8, -0.096]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh46", "Segments": [0, -0.076, 2, 0.033, -0.083, 2, 0.1, -0.078, 2, 0.133, -0.067, 2, 0.167, -0.053, 2, 0.2, -0.041, 2, 0.233, -0.037, 2, 0.267, -0.05, 2, 0.3, -0.086, 2, 0.333, -0.14, 2, 0.367, -0.205, 2, 0.4, -0.28, 2, 0.433, -0.355, 2, 0.467, -0.43, 2, 0.5, -0.496, 2, 0.533, -0.55, 2, 0.567, -0.585, 2, 0.6, -0.599, 2, 0.633, -0.565, 2, 0.667, -0.475, 2, 0.7, -0.336, 2, 0.733, -0.161, 2, 0.767, 0.033, 2, 0.8, 0.243, 2, 0.833, 0.453, 2, 0.867, 0.646, 2, 0.9, 0.821, 2, 0.933, 0.961, 2, 0.967, 1.051, 2, 1, 1.084, 2, 1.033, 1.065, 2, 1.067, 1.01, 2, 1.1, 0.922, 2, 1.133, 0.806, 2, 1.167, 0.663, 2, 1.2, 0.5, 2, 1.233, 0.315, 2, 1.267, 0.118, 2, 1.3, -0.094, 2, 1.333, -0.311, 2, 1.367, -0.538, 2, 1.4, -0.761, 2, 1.433, -0.988, 2, 1.467, -1.204, 2, 1.5, -1.417, 2, 1.533, -1.613, 2, 1.567, -1.799, 2, 1.6, -1.961, 2, 1.633, -2.105, 2, 1.667, -2.22, 2, 1.7, -2.309, 2, 1.733, -2.364, 2, 1.767, -2.383, 2, 1.8, -2.272, 2, 1.833, -1.975, 2, 1.867, -1.517, 2, 1.9, -0.941, 2, 1.933, -0.302, 2, 1.967, 0.39, 2, 2, 1.082, 2, 2.033, 1.72, 2, 2.067, 2.296, 2, 2.1, 2.754, 2, 2.133, 3.051, 2, 2.167, 3.162, 2, 2.2, 3.039, 2, 2.233, 2.708, 2, 2.267, 2.196, 2, 2.3, 1.554, 2, 2.333, 0.843, 2, 2.367, 0.072, 2, 2.4, -0.699, 2, 2.433, -1.411, 2, 2.467, -2.053, 2, 2.5, -2.564, 2, 2.533, -2.895, 2, 2.567, -3.018, 2, 2.6, -2.917, 2, 2.633, -2.647, 2, 2.667, -2.23, 2, 2.7, -1.706, 2, 2.733, -1.125, 2, 2.767, -0.496, 2, 2.8, 0.134, 2, 2.833, 0.714, 2, 2.867, 1.239, 2, 2.9, 1.656, 2, 2.933, 1.926, 2, 2.967, 2.027, 2, 3, 1.965, 2, 3.033, 1.8, 2, 3.067, 1.545, 2, 3.1, 1.225, 2, 3.133, 0.87, 2, 3.167, 0.486, 2, 3.2, 0.101, 2, 3.233, -0.254, 2, 3.267, -0.574, 2, 3.3, -0.829, 2, 3.333, -0.994, 2, 3.367, -1.055, 2, 3.4, -1.033, 2, 3.433, -0.968, 2, 3.467, -0.866, 2, 3.5, -0.734, 2, 3.533, -0.578, 2, 3.567, -0.399, 2, 3.6, -0.209, 2, 3.633, -0.01, 2, 3.667, 0.191, 2, 3.7, 0.39, 2, 3.733, 0.58, 2, 3.767, 0.759, 2, 3.8, 0.915, 2, 3.833, 1.047, 2, 3.867, 1.149, 2, 3.9, 1.214, 2, 3.933, 1.236, 2, 3.967, 1.201, 2, 4, 1.1, 2, 4.033, 0.943, 2, 4.067, 0.748, 2, 4.1, 0.515, 2, 4.133, 0.263, 2, 4.167, 0.001, 2, 4.2, -0.261, 2, 4.233, -0.513, 2, 4.267, -0.746, 2, 4.3, -0.941, 2, 4.333, -1.098, 2, 4.367, -1.198, 2, 4.4, -1.234, 2, 4.433, -1.185, 2, 4.467, -1.052, 2, 4.5, -0.85, 2, 4.533, -0.606, 2, 4.567, -0.328, 2, 4.6, -0.047, 2, 4.633, 0.231, 2, 4.667, 0.475, 2, 4.7, 0.677, 2, 4.733, 0.809, 2, 4.767, 0.859, 2, 4.8, 0.822, 2, 4.833, 0.723, 2, 4.867, 0.57, 2, 4.9, 0.378, 2, 4.933, 0.165, 2, 4.967, -0.065, 2, 5, -0.296, 2, 5.033, -0.509, 2, 5.067, -0.701, 2, 5.1, -0.853, 2, 5.133, -0.952, 2, 5.167, -0.989, 2, 5.2, -0.949, 2, 5.233, -0.841, 2, 5.267, -0.677, 2, 5.3, -0.478, 2, 5.333, -0.251, 2, 5.367, -0.022, 2, 5.4, 0.204, 2, 5.433, 0.403, 2, 5.467, 0.568, 2, 5.5, 0.675, 2, 5.533, 0.716, 2, 5.567, 0.689, 2, 5.6, 0.618, 2, 5.633, 0.509, 2, 5.667, 0.377, 2, 5.7, 0.227, 2, 5.733, 0.075, 2, 5.767, -0.075, 2, 5.8, -0.207, 2, 5.833, -0.316, 2, 5.867, -0.387, 2, 5.9, -0.414, 2, 5.933, -0.401, 2, 5.967, -0.366, 2, 6, -0.311, 2, 6.033, -0.243, 2, 6.067, -0.168, 2, 6.1, -0.085, 2, 6.133, -0.003, 2, 6.167, 0.072, 2, 6.2, 0.141, 2, 6.233, 0.195, 2, 6.267, 0.23, 2, 6.3, 0.243, 2, 6.333, 0.238, 2, 6.367, 0.222, 2, 6.4, 0.199, 2, 6.433, 0.17, 2, 6.467, 0.138, 2, 6.5, 0.105, 2, 6.533, 0.073, 2, 6.567, 0.045, 2, 6.6, 0.021, 2, 6.633, 0.006, 2, 6.667, 0, 2, 6.7, 0.003, 2, 6.733, 0.01, 2, 6.767, 0.02, 2, 6.8, 0.032, 2, 6.833, 0.043, 2, 6.867, 0.053, 2, 6.9, 0.06, 2, 6.933, 0.063, 2, 6.967, 0.058, 2, 7, 0.043, 2, 7.033, 0.02, 2, 7.067, -0.009, 2, 7.1, -0.042, 2, 7.133, -0.076, 2, 7.167, -0.111, 2, 7.2, -0.143, 2, 7.233, -0.172, 2, 7.267, -0.195, 2, 7.3, -0.21, 2, 7.333, -0.216, 2, 7.367, -0.207, 2, 7.4, -0.182, 2, 7.433, -0.144, 2, 7.467, -0.096, 2, 7.5, -0.044, 2, 7.533, 0.014, 2, 7.567, 0.071, 2, 7.6, 0.124, 2, 7.633, 0.171, 2, 7.667, 0.209, 2, 7.7, 0.234, 2, 7.733, 0.243, 2, 7.767, 0.234, 2, 7.8, 0.212, 2, 7.833, 0.176, 2, 7.867, 0.132, 2, 7.9, 0.083, 2, 7.933, 0.03, 2, 7.967, -0.023, 2, 8, -0.072]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh0_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh1_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh2_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh3_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh46_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 8, 1]}]}