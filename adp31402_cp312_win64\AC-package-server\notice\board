<h1 style='text-align: center; font-size: 20px;'>公告 Notice</h1>

---

# 简体中文

#### 软件正在获取**软件著作权**……

---

## 感谢您的支持！🎉

### 安全承诺

- 🔒 我们重视并承诺保护您的个人信息安全，欢迎您注册账号体验更多功能！

### 社交媒体关注

- 关注我们的[Bilibili频道](https://space.bilibili.com/****************)，与创作者@[肥波不肥11](https://space.bilibili.com/****************)互动，获取最新动态！

### 开源信息

- 本项目完全免费且开源：https://github.com/HeavyNotFat/Agentic-AI-Desktop-Pet
- 🚫**重要提示**🚫：如果有人向您收取费用用于下载此程序，请警惕——这可能是欺诈行为！

---

## 创造背景💡

鉴于网络上现有的AI Agent桌宠缺乏智能性，我们决定动手创造一个更智能、更贴心的桌宠解决方案。

---

## 未来规划🚀

我们将根据用户反馈持续优化产品，期待推出插件商店和工具商店，打造个性化、智能化的桌面伴侣。

---

## 核心亮点🌟

- **高度自定义**：满足您的各种需求。🎨
- **完全开源**：确保透明度，允许代码审查。📖
- **集成AI技术**：提供更智能的交互体验。🧠
- **开放API接口**：便于深度定制，让您的想象成为可能。🌟

---

# English

#### The software is applying for **software copyright**...

---

## Thank You For Your Support! 🎉

### Security Commitment

- 🔒 We value and commit to protecting your personal information security. Welcome to register an account to experience more functionalities!

### Social Media Follow

- Follow our [Bilibili Channel](https://space.bilibili.com/****************) and interact with creator @[HeavyNotFat11](https://space.bilibili.com/****************) for the latest updates!

### Open Source Information

- This project is completely free and open source: https://github.com/HeavyNotFat/Agentic-AI-Desktop-Pet
- 🚫**Important Notice**🚫: If someone charges you a fee for downloading this program, beware—it may be fraudulent!

---

## Creation Background 💡

Given that existing AI Agent desktop pets on the internet lack intelligence, we decided to create a smarter and more considerate desktop pet solution.

---

## Future Plans 🚀

We will continuously optimize the product based on user feedback, looking forward to launching plugin stores and tool stores, creating personalized, intelligent desktop companions.

---

## Core Highlights 🌟

- **Highly Customizable**: Meets all your needs.🎨
- **Fully Open Source**: Ensures transparency and allows code review.📖
- **Integrated AI Technology**: Provides smarter interactive experiences.🧠
- **Open API Interface**: Facilitates deep customization, making your imagination possible.🌟

---

---

# 用户隐私收集 User Privacy Collection

- 本产品完全免费且开源，我们不会收集您的任何个人信息。
- This product is completely free and open source, we will not collect any of your personal information.

---

- 原版并不会主动上传您的任何数据到服务器！
- The original version will not actively upload any of your data to the server!

---

- 任何基于本程序的改版将不由本程序开发者负责！
- Any modified version based on this program will not be responsible by the developer of this program!

# 使用说明 Instructions

- 本程序可能会定期发布改进反馈用于更好的接受用户反馈。
- This program may release improved feedback periodically to better accept user feedback.

---

- 注册用于统计人数解锁更多功能（无注册也可以正常使用）
- Register to count the number of users and unlock more features (no registration can also be used)

---

# 技术引用说明 Technical Reference

- Live2D for Python - (Live2D)
- PyQt5
- QFluentWidgets - (PyQt5)
- Python - (Python)

# 版权申明 Copyright Statement

- 该软件版权为 肥波不肥(HeavyNotFat) 所有，禁止任何未经授权使用。
- The software copyright belongs to 肥波不肥(HeavyNotFat), and any unauthorized use is prohibited.
