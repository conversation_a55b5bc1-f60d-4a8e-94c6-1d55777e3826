"""
划词助手模块 - 基于 VCPChat 的全局文本选择功能
提供全局文本选择监听和悬浮工具条功能
"""

import sys
import time
import logging
from typing import Optional, Callable, Tuple
from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QPushButton, QApplication, 
                             QFrame, QGraphicsDropShadowEffect)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt, QPoint
from PyQt5.QtGui import QCursor, QClipboard

# Windows 特定导入
if sys.platform == "win32":
    try:
        import win32gui
        import win32con
        import win32api
        import win32clipboard
        WINDOWS_AVAILABLE = True
    except ImportError:
        WINDOWS_AVAILABLE = False
        logging.warning("Windows API 不可用，划词助手功能将受限")
else:
    WINDOWS_AVAILABLE = False

logger = logging.getLogger(__name__)

class SelectionMonitor(QThread):
    """全局文本选择监听线程"""
    text_selected = pyqtSignal(str, QPoint)  # 选中的文本和鼠标位置
    
    def __init__(self):
        super().__init__()
        self.running = False
        self.last_clipboard_text = ""
        self.check_interval = 0.5  # 检查间隔（秒）
        
    def run(self):
        """监听线程主循环"""
        self.running = True
        while self.running:
            try:
                if WINDOWS_AVAILABLE:
                    self.check_windows_selection()
                else:
                    self.check_clipboard_selection()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"选择监听错误: {e}")
                time.sleep(1)  # 错误时延长等待时间
    
    def check_windows_selection(self):
        """Windows 平台的选择检测"""
        try:
            # 获取当前鼠标位置
            cursor_pos = win32gui.GetCursorPos()
            
            # 模拟 Ctrl+C 来获取选中文本
            # 注意：这是一个简化实现，实际可能需要更复杂的逻辑
            clipboard = QApplication.clipboard()
            current_text = clipboard.text()
            
            if current_text and current_text != self.last_clipboard_text:
                if len(current_text.strip()) > 0 and len(current_text) < 1000:  # 合理的文本长度
                    self.last_clipboard_text = current_text
                    self.text_selected.emit(current_text.strip(), QPoint(cursor_pos[0], cursor_pos[1]))
                    
        except Exception as e:
            logger.debug(f"Windows 选择检测错误: {e}")
    
    def check_clipboard_selection(self):
        """通用剪贴板选择检测"""
        try:
            clipboard = QApplication.clipboard()
            current_text = clipboard.text()
            
            if current_text and current_text != self.last_clipboard_text:
                if len(current_text.strip()) > 0 and len(current_text) < 1000:
                    self.last_clipboard_text = current_text
                    cursor_pos = QCursor.pos()
                    self.text_selected.emit(current_text.strip(), cursor_pos)
                    
        except Exception as e:
            logger.debug(f"剪贴板检测错误: {e}")
    
    def stop(self):
        """停止监听"""
        self.running = False
        self.quit()
        self.wait()

class FloatingToolbar(QWidget):
    """悬浮工具条"""
    action_triggered = pyqtSignal(str, str)  # 动作类型，选中文本
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_text = ""
        self.setup_ui()
        self.setup_style()
        
        # 自动隐藏定时器
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide)
        self.hide_timer.setSingleShot(True)
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 主容器
        self.container = QFrame(self)
        layout = QHBoxLayout(self.container)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(4)
        
        # 操作按钮
        self.actions = [
            ("翻译", "translate", "🌐"),
            ("总结", "summarize", "📝"),
            ("解释", "explain", "💡"),
            ("搜索", "search", "🔍"),
        ]
        
        self.buttons = {}
        for name, action, icon in self.actions:
            btn = QPushButton(f"{icon} {name}")
            btn.clicked.connect(lambda checked, a=action: self.trigger_action(a))
            btn.setFixedHeight(32)
            btn.setMinimumWidth(60)
            layout.addWidget(btn)
            self.buttons[action] = btn
        
        # 设置主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.container)
        
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(40, 40, 44, 0.95);
                border: 1px solid rgba(100, 100, 100, 0.3);
                border-radius: 8px;
            }
            QPushButton {
                background-color: rgba(70, 70, 74, 0.8);
                color: #e0e0e0;
                border: 1px solid rgba(100, 100, 100, 0.2);
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: rgba(90, 90, 94, 0.9);
                border-color: rgba(120, 120, 120, 0.4);
            }
            QPushButton:pressed {
                background-color: rgba(60, 60, 64, 0.9);
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(0)
        shadow.setYOffset(4)
        shadow.setColor(Qt.black)
        self.container.setGraphicsEffect(shadow)
    
    def show_at_position(self, text: str, position: QPoint):
        """在指定位置显示工具条"""
        self.selected_text = text
        
        # 调整位置，确保不超出屏幕边界
        screen = QApplication.primaryScreen().geometry()
        toolbar_size = self.sizeHint()
        
        x = position.x() - toolbar_size.width() // 2
        y = position.y() + 20  # 在鼠标下方显示
        
        # 边界检查
        if x < 0:
            x = 0
        elif x + toolbar_size.width() > screen.width():
            x = screen.width() - toolbar_size.width()
            
        if y + toolbar_size.height() > screen.height():
            y = position.y() - toolbar_size.height() - 10  # 在鼠标上方显示
        
        self.move(x, y)
        self.show()
        self.raise_()
        
        # 设置自动隐藏
        self.hide_timer.start(5000)  # 5秒后自动隐藏
    
    def trigger_action(self, action: str):
        """触发动作"""
        if self.selected_text:
            self.action_triggered.emit(action, self.selected_text)
        self.hide()
    
    def enterEvent(self, event):
        """鼠标进入时停止自动隐藏"""
        self.hide_timer.stop()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开时重新开始自动隐藏"""
        self.hide_timer.start(2000)  # 2秒后隐藏
        super().leaveEvent(event)

class SelectionAssistant:
    """划词助手主类"""
    
    def __init__(self, app_controller):
        self.app_controller = app_controller
        self.monitor = None
        self.toolbar = None
        self.enabled = False
        
    def start(self):
        """启动划词助手"""
        if self.enabled:
            return
            
        try:
            # 创建监听器
            self.monitor = SelectionMonitor()
            self.monitor.text_selected.connect(self.on_text_selected)
            
            # 创建工具条
            self.toolbar = FloatingToolbar()
            self.toolbar.action_triggered.connect(self.handle_action)
            
            # 启动监听
            self.monitor.start()
            self.enabled = True
            
            logger.info("划词助手已启动")
            
        except Exception as e:
            logger.error(f"启动划词助手失败: {e}")
    
    def stop(self):
        """停止划词助手"""
        if not self.enabled:
            return
            
        try:
            if self.monitor:
                self.monitor.stop()
                self.monitor = None
                
            if self.toolbar:
                self.toolbar.hide()
                self.toolbar = None
                
            self.enabled = False
            logger.info("划词助手已停止")
            
        except Exception as e:
            logger.error(f"停止划词助手失败: {e}")
    
    def on_text_selected(self, text: str, position: QPoint):
        """处理文本选择事件"""
        if not self.enabled or not text.strip():
            return
            
        # 过滤太短或太长的文本
        if len(text.strip()) < 3 or len(text) > 500:
            return
            
        # 显示工具条
        if self.toolbar:
            self.toolbar.show_at_position(text, position)
    
    def handle_action(self, action: str, text: str):
        """处理用户操作"""
        try:
            # 根据动作类型生成提示词
            prompts = {
                "translate": f"请将以下文本翻译成中文：\n\n{text}",
                "summarize": f"请总结以下文本的主要内容：\n\n{text}",
                "explain": f"请解释以下文本的含义：\n\n{text}",
                "search": f"请帮我搜索关于以下内容的信息：\n\n{text}",
            }
            
            prompt = prompts.get(action, f"请处理以下文本：\n\n{text}")
            
            # 通过 app_controller 发送消息
            if self.app_controller and hasattr(self.app_controller, 'send_message'):
                self.app_controller.send_message(prompt)
            else:
                logger.warning("无法发送消息：app_controller 不可用")
                
        except Exception as e:
            logger.error(f"处理动作失败: {e}")
    
    def is_enabled(self) -> bool:
        """检查是否已启用"""
        return self.enabled
