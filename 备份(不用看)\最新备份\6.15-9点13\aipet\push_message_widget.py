# -*- coding: utf-8 -*-
"""
推送消息折叠组件
实现推送消息的折叠显示功能
"""

import logging
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFrame, QScrollArea, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QPalette

from push_message_manager import PushMessageManager, MessageFormatter
from push_message_item import PushMessageItem
from push_message_styles import PushMessageStyles, FontConfig, LayoutConfig, AnimationConfig

logger = logging.getLogger(__name__)

class CollapsiblePushMessageWidget(QFrame):
    """可折叠的推送消息组件"""
    
    # 信号定义
    message_cleared = pyqtSignal()  # 消息清空信号
    expand_state_changed = pyqtSignal(bool)  # 展开状态变化信号
    
    def __init__(self, parent=None, max_messages=50):
        super().__init__(parent)
        
        # 初始化消息管理器
        self.message_manager = PushMessageManager(max_messages)
        
        # 状态变量
        self.is_expanded = False
        self.is_dark_theme = False
        self.animation_in_progress = False
        
        # UI组件引用
        self.header_widget = None
        self.content_widget = None
        self.scroll_area = None
        self.message_items = []
        
        # 动画对象
        self.expand_animation = None
        
        # 设置基本属性
        self.setObjectName("push_message_container")
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # 改为Preferred
        self.setFrameStyle(QFrame.NoFrame)
        self.setMaximumWidth(450)  # 限制整个推送消息容器的最大宽度
        
        # 初始化UI
        self._init_ui()
        self._apply_styles()
        
        # 设置初始状态
        self.setFixedHeight(LayoutConfig.HEIGHTS['collapsed'])
        
        logger.info("推送消息折叠组件初始化完成")
    
    def _init_ui(self):
        """初始化UI布局"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(*LayoutConfig.MARGINS['container'])
        self.main_layout.setSpacing(0)
        
        # 创建头部区域
        self._create_header()
        
        # 创建内容区域
        self._create_content()
        
        # 初始隐藏内容区域
        self.content_widget.hide()
    
    def _create_header(self):
        """创建头部区域（折叠状态显示）"""
        self.header_widget = QWidget()
        self.header_widget.setObjectName("push_header")
        
        header_layout = QHBoxLayout(self.header_widget)
        header_layout.setContentsMargins(LayoutConfig.SPACING['normal'], 
                                       LayoutConfig.SPACING['small'],
                                       LayoutConfig.SPACING['normal'], 
                                       LayoutConfig.SPACING['small'])
        header_layout.setSpacing(LayoutConfig.SPACING['medium'])
        
        # 推送消息标签
        title_label = QLabel("📬 推送消息")
        title_label.setFont(QFont(FontConfig.PRIMARY_FONT, FontConfig.SIZES['medium'], QFont.Bold))
        
        # 消息计数标签
        self.count_label = QLabel("0")
        self.count_label.setObjectName("count_label")
        self.count_label.setFixedSize(20, 20)
        self.count_label.setAlignment(Qt.AlignCenter)
        
        # 最新消息预览
        self.preview_label = QLabel("暂无推送消息")
        self.preview_label.setObjectName("preview_label")
        self.preview_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        # 时间标签
        self.time_label = QLabel("")
        self.time_label.setObjectName("time_label")
        
        # 展开/折叠按钮
        self.toggle_button = QPushButton("▼")
        self.toggle_button.setObjectName("toggle_button")
        self.toggle_button.setFixedSize(24, 24)
        self.toggle_button.clicked.connect(self.toggle_expanded)
        
        # 添加到布局
        header_layout.addWidget(title_label)
        header_layout.addWidget(self.count_label)
        header_layout.addWidget(self.preview_label, 1)
        header_layout.addWidget(self.time_label)
        header_layout.addWidget(self.toggle_button)
        
        self.main_layout.addWidget(self.header_widget)
    
    def _create_content(self):
        """创建内容区域（展开状态显示）"""
        self.content_widget = QWidget()
        self.content_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        content_layout = QVBoxLayout(self.content_widget)
        content_layout.setContentsMargins(*LayoutConfig.MARGINS['content'])
        content_layout.setSpacing(LayoutConfig.SPACING['small'])
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setFixedHeight(LayoutConfig.HEIGHTS['separator'])
        content_layout.addWidget(separator)
        
        # 操作按钮区域
        self._create_action_buttons(content_layout)
        
        # 滚动区域 - 让它自适应高度，但设置最小和最大高度
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setFrameStyle(QFrame.NoFrame)
        self.scroll_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        # 设置合理的最小和最大高度
        self.scroll_area.setMinimumHeight(100)
        self.scroll_area.setMaximumHeight(500)  # 增加到500px
        
        # 消息容器
        self.message_container = QWidget()
        self.message_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.message_layout = QVBoxLayout(self.message_container)
        self.message_layout.setContentsMargins(0, 0, 0, 0)
        self.message_layout.setSpacing(LayoutConfig.SPACING['tiny'])
        self.message_layout.addStretch()  # 在底部添加弹性空间
        
        self.scroll_area.setWidget(self.message_container)
        content_layout.addWidget(self.scroll_area, 1)  # 让滚动区域占用剩余空间
        
        self.main_layout.addWidget(self.content_widget)
    
    def _create_action_buttons(self, parent_layout):
        """创建操作按钮区域"""
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, LayoutConfig.SPACING['small'])
        button_layout.setSpacing(LayoutConfig.SPACING['medium'])
        
        # 统计信息标签
        self.stats_label = QLabel("")
        self.stats_label.setFont(QFont(FontConfig.MONOSPACE_FONT, FontConfig.SIZES['small']))
        
        # 右侧按钮
        button_layout.addWidget(self.stats_label)
        button_layout.addStretch()
        
        # 刷新按钮
        refresh_button = QPushButton("🔄 刷新")
        refresh_button.setFixedSize(60, LayoutConfig.HEIGHTS['button'])
        refresh_button.clicked.connect(self.refresh_display)
        
        # 清空按钮
        self.clear_button = QPushButton("🗑️ 清空")
        self.clear_button.setObjectName("clear_button")
        self.clear_button.setFixedSize(60, LayoutConfig.HEIGHTS['button'])
        self.clear_button.clicked.connect(self._confirm_clear_messages)
        
        button_layout.addWidget(refresh_button)
        button_layout.addWidget(self.clear_button)
        
        parent_layout.addWidget(button_widget)
    
    def _apply_styles(self):
        """应用样式"""
        stylesheet = PushMessageStyles.get_complete_stylesheet(self.is_dark_theme)
        self.setStyleSheet(stylesheet)
    
    def add_push_message(self, msg_type, content, status="info", source="unknown", extra_data=None):
        """添加推送消息"""
        # 使用消息管理器添加消息
        message_data = self.message_manager.add_message(
            msg_type, content, status, source, extra_data
        )
        
        # 更新显示
        self._update_header_display()
        
        # 如果当前展开，添加到UI
        if self.is_expanded:
            self._add_message_item(message_data)
        
        # 新消息提示效果
        self._show_new_message_indicator()
        
        logger.debug(f"添加推送消息: {msg_type} - {content[:30]}...")
    
    def add_vcp_message(self, message_data):
        """添加VCP日志消息"""
        formatted = MessageFormatter.format_vcp_message(message_data)
        self.add_push_message(
            formatted['type'], 
            formatted['content'], 
            formatted['status'], 
            formatted['source'],
            formatted.get('extra_data')
        )
    
    def add_agent_message(self, message_data):
        """添加Agent消息"""
        formatted = MessageFormatter.format_agent_message(message_data)
        self.add_push_message(
            formatted['type'], 
            formatted['content'], 
            formatted['status'], 
            formatted['source'],
            formatted.get('extra_data')
        )
    
    def _add_message_item(self, message_data):
        """在UI中添加消息项"""
        # 创建消息项组件
        item = PushMessageItem(message_data, self)
        item.remove_requested.connect(self._remove_message_item)
        
        # 插入到消息布局的顶部（最新消息在上）
        self.message_layout.insertWidget(0, item)
        self.message_items.insert(0, item)
        
        # 滚动到顶部显示新消息
        QTimer.singleShot(100, lambda: self.scroll_area.verticalScrollBar().setValue(0))
    
    def _remove_message_item(self, message_id):
        """移除消息项"""
        # 从管理器中移除
        removed = self.message_manager.remove_message(message_id)
        
        if removed:
            # 从UI中移除
            for i, item in enumerate(self.message_items):
                if item.get_message_id() == message_id:
                    item.setParent(None)
                    item.deleteLater()
                    self.message_items.pop(i)
                    break
            
            # 更新显示
            self._update_header_display()
            self._update_stats_display()
    
    def _update_header_display(self):
        """更新头部显示"""
        count = self.message_manager.get_message_count()
        self.count_label.setText(str(count))
        
        # 更新预览文本
        preview_text = self.message_manager.get_preview_text()
        self.preview_label.setText(preview_text)
        
        # 更新时间标签
        latest = self.message_manager.get_latest_message()
        if latest:
            time_str = latest['timestamp'].strftime("%H:%M:%S")
            self.time_label.setText(time_str)
        else:
            self.time_label.setText("")
        
        # 更新清空按钮状态
        self.clear_button.setEnabled(count > 0)
    
    def _update_stats_display(self):
        """更新统计信息显示"""
        if not self.is_expanded:
            return
        
        stats = self.message_manager.get_statistics()
        stats_text = f"总计: {stats['total']} 条"
        
        if stats['by_type']:
            type_info = []
            for msg_type, count in stats['by_type'].items():
                type_info.append(f"{msg_type}: {count}")
            stats_text += f" | {' | '.join(type_info)}"
        
        self.stats_label.setText(stats_text)
    
    def _show_new_message_indicator(self):
        """显示新消息指示器"""
        # 简单的闪烁效果
        if not self.is_expanded:
            original_style = self.count_label.styleSheet()
            self.count_label.setStyleSheet(original_style + """
                background-color: #ff6b6b;
                color: white;
            """)
            
            # 0.5秒后恢复原样
            QTimer.singleShot(500, lambda: self.count_label.setStyleSheet(original_style))
    
    def toggle_expanded(self):
        """切换展开/折叠状态"""
        if self.animation_in_progress:
            return
        
        if self.is_expanded:
            self.collapse()
        else:
            self.expand()
    
    def expand(self):
        """展开组件 - 最简化版本，完全依赖布局自适应"""
        if self.is_expanded or self.animation_in_progress:
            return
        
        logger.debug("展开推送消息组件")
        self.animation_in_progress = True
        
        # 更新状态
        self.is_expanded = True
        self.toggle_button.setText("▲")
        
        # 刷新消息显示
        self._refresh_message_items()
        
        # 直接显示内容区域，完全移除高度限制
        self.setFixedHeight(16777215)  # 移除固定高度
        self.setMaximumHeight(16777215)  # 移除最大高度限制
        self.setMinimumHeight(0)  # 移除最小高度限制
        
        # 显示内容并让布局自动调整
        self.content_widget.show()
        
        # 强制更新布局
        self.updateGeometry()
        self.adjustSize()
        
        # 简单的延迟处理，确保布局完成
        QTimer.singleShot(50, self._on_expand_finished)
        
        # 发送信号
        self.expand_state_changed.emit(True)
    
    def collapse(self):
        """折叠组件"""
        if not self.is_expanded or self.animation_in_progress:
            return
        
        logger.debug("折叠推送消息组件")
        self.animation_in_progress = True
        
        # 更新状态
        self.is_expanded = False
        self.toggle_button.setText("▼")
        
        # 隐藏内容区域
        self.content_widget.hide()
        
        # 恢复折叠状态的固定高度
        self.setFixedHeight(LayoutConfig.HEIGHTS['collapsed'])
        
        # 简单的延迟处理
        QTimer.singleShot(50, self._on_collapse_finished)
        
        # 发送信号
        self.expand_state_changed.emit(False)
    
    def _on_expand_finished(self):
        """展开动画完成处理"""
        self.animation_in_progress = False
        self._update_stats_display()
        
        logger.debug("推送消息组件展开完成")
    
    def _on_collapse_finished(self):
        """折叠动画完成处理"""
        self.animation_in_progress = False
        
        logger.debug("推送消息组件折叠完成")
    
    def _refresh_message_items(self):
        """刷新消息项显示"""
        # 清空现有UI项
        for item in self.message_items:
            item.setParent(None)
            item.deleteLater()
        self.message_items.clear()
        
        # 重新创建所有消息项
        messages = self.message_manager.get_messages()
        for message_data in messages:
            self._add_message_item(message_data)
    
    def refresh_display(self):
        """刷新显示"""
        self._update_header_display()
        if self.is_expanded:
            self._refresh_message_items()
            self._update_stats_display()
        
        logger.debug("推送消息显示已刷新")
    
    def _confirm_clear_messages(self):
        """确认清空消息"""
        from PyQt5.QtWidgets import QMessageBox
        
        if self.message_manager.get_message_count() == 0:
            return
        
        reply = QMessageBox.question(
            self, "确认清空", 
            "确定要清空所有推送消息吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.clear_messages()
    
    def clear_messages(self):
        """清空所有推送消息"""
        count = self.message_manager.clear_all()
        
        # 清空UI
        for item in self.message_items:
            item.setParent(None)
            item.deleteLater()
        self.message_items.clear()
        
        # 更新显示
        self._update_header_display()
        self._update_stats_display()
        
        # 发送信号
        self.message_cleared.emit()
        
        logger.info(f"已清空 {count} 条推送消息")
    
    def update_theme(self, is_dark_theme=False):
        """更新主题"""
        self.is_dark_theme = is_dark_theme
        self._apply_styles()
        
        # 更新所有消息项的主题
        for item in self.message_items:
            item.update_theme(is_dark_theme)
        
        logger.debug(f"推送消息组件主题已更新: {'暗色' if is_dark_theme else '亮色'}")
    
    def get_message_count(self):
        """获取消息总数"""
        return self.message_manager.get_message_count()
    
    def get_expanded_state(self):
        """获取展开状态"""
        return self.is_expanded
    
    def set_max_messages(self, max_messages):
        """设置最大消息数量"""
        self.message_manager.set_max_messages(max_messages)
        if self.is_expanded:
            self._refresh_message_items()
        self._update_header_display()