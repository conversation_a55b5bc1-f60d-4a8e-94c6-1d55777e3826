{"Version": 3, "Meta": {"Duration": 10, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 92, "TotalSegmentCount": 8304, "TotalPointCount": 8396, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.5, 0.127, 2, 0.533, 0.508, 2, 0.567, 1.103, 2, 0.6, 1.911, 2, 0.633, 2.904, 2, 0.667, 4.046, 2, 0.7, 5.363, 2, 0.733, 6.77, 2, 0.767, 8.297, 2, 0.8, 9.906, 2, 0.833, 11.556, 2, 0.867, 13.289, 2, 0.9, 15, 2, 0.933, 16.711, 2, 0.967, 18.444, 2, 1, 20.094, 2, 1.033, 21.703, 2, 1.067, 23.23, 2, 1.1, 24.637, 2, 1.133, 25.954, 2, 1.167, 27.096, 2, 1.2, 28.089, 2, 1.233, 28.897, 2, 1.267, 29.492, 2, 1.3, 29.873, 2, 1.333, 30, 2, 1.367, 29.897, 2, 1.4, 29.62, 2, 1.433, 29.22, 2, 1.467, 28.745, 2, 1.5, 28.255, 2, 1.533, 27.78, 2, 1.567, 27.38, 2, 1.6, 27.103, 2, 1.633, 27, 2, 1.667, 27.012, 2, 1.7, 27.047, 2, 1.733, 27.103, 2, 1.767, 27.179, 2, 1.8, 27.272, 2, 1.833, 27.378, 2, 1.867, 27.5, 2, 1.9, 27.634, 2, 1.933, 27.776, 2, 1.967, 27.929, 2, 2, 28.087, 2, 2.033, 28.251, 2, 2.067, 28.417, 2, 2.1, 28.583, 2, 2.133, 28.749, 2, 2.167, 28.913, 2, 2.2, 29.071, 2, 2.233, 29.224, 2, 2.267, 29.366, 2, 2.3, 29.5, 2, 2.333, 29.622, 2, 2.367, 29.728, 2, 2.4, 29.821, 2, 2.433, 29.897, 2, 2.467, 29.953, 2, 2.5, 29.988, 2, 2.533, 30, 2, 2.567, 29.873, 2, 2.6, 29.517, 2, 2.633, 28.956, 2, 2.667, 28.253, 2, 2.7, 27.404, 2, 2.733, 26.476, 2, 2.767, 25.492, 2, 2.8, 24.489, 2, 2.833, 23.501, 2, 2.867, 22.55, 2, 2.9, 21.708, 2, 2.933, 20.965, 2, 2.967, 20.387, 2, 3, 20, 2, 3.033, 19.721, 2, 3.067, 19.458, 2, 3.1, 19.199, 2, 3.133, 18.954, 2, 3.167, 18.714, 2, 3.2, 18.489, 2, 3.233, 18.267, 2, 3.267, 18.059, 2, 3.3, 17.855, 2, 3.333, 17.664, 2, 3.367, 17.478, 2, 3.4, 17.3, 2, 3.433, 17.133, 2, 3.467, 16.971, 2, 3.5, 16.82, 2, 3.533, 16.674, 2, 3.567, 16.537, 2, 3.6, 16.405, 2, 3.633, 16.283, 2, 3.667, 16.166, 2, 3.7, 16.057, 2, 3.733, 15.953, 2, 3.767, 15.855, 2, 3.8, 15.765, 2, 3.833, 15.68, 2, 3.867, 15.602, 2, 3.9, 15.528, 2, 3.933, 15.461, 2, 3.967, 15.399, 2, 4, 15.342, 2, 4.033, 15.29, 2, 4.067, 15.244, 2, 4.1, 15.201, 2, 4.133, 15.163, 2, 4.167, 15.13, 2, 4.2, 15.101, 2, 4.233, 15.076, 2, 4.267, 15.055, 2, 4.3, 15.037, 2, 4.333, 15.023, 2, 4.367, 15.013, 2, 4.4, 15.006, 2, 4.433, 15.001, 2, 4.467, 15, 2, 4.5, 15.002, 2, 4.533, 15.007, 2, 4.567, 15.016, 2, 4.6, 15.03, 2, 4.633, 15.047, 2, 4.667, 15.069, 2, 4.7, 15.096, 2, 4.733, 15.128, 2, 4.767, 15.165, 2, 4.8, 15.207, 2, 4.833, 15.254, 2, 4.867, 15.308, 2, 4.9, 15.368, 2, 4.933, 15.433, 2, 4.967, 15.505, 2, 5, 15.583, 2, 5.033, 15.668, 2, 5.067, 15.762, 2, 5.1, 15.862, 2, 5.133, 15.968, 2, 5.167, 16.083, 2, 5.2, 16.205, 2, 5.233, 16.337, 2, 5.267, 16.476, 2, 5.3, 16.623, 2, 5.333, 16.779, 2, 5.367, 16.943, 2, 5.4, 17.12, 2, 5.433, 17.303, 2, 5.467, 17.496, 2, 5.5, 17.698, 2, 5.533, 17.91, 2, 5.567, 18.136, 2, 5.6, 18.369, 2, 5.633, 18.612, 2, 5.667, 18.866, 2, 5.7, 19.131, 2, 5.733, 19.413, 2, 5.767, 19.701, 2, 5.8, 20, 2, 5.833, 20.459, 2, 5.867, 21.16, 2, 5.9, 22.079, 2, 5.933, 23.146, 2, 5.967, 24.274, 2, 6, 25.46, 2, 6.033, 26.619, 2, 6.067, 27.672, 2, 6.1, 28.61, 2, 6.133, 29.349, 2, 6.167, 29.824, 2, 6.2, 30, 2, 10, 30]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.5, -0.127, 2, 0.533, -0.508, 2, 0.567, -1.103, 2, 0.6, -1.911, 2, 0.633, -2.904, 2, 0.667, -4.046, 2, 0.7, -5.363, 2, 0.733, -6.77, 2, 0.767, -8.297, 2, 0.8, -9.906, 2, 0.833, -11.556, 2, 0.867, -13.289, 2, 0.9, -15, 2, 0.933, -16.711, 2, 0.967, -18.444, 2, 1, -20.094, 2, 1.033, -21.703, 2, 1.067, -23.23, 2, 1.1, -24.637, 2, 1.133, -25.954, 2, 1.167, -27.096, 2, 1.2, -28.089, 2, 1.233, -28.897, 2, 1.267, -29.492, 2, 1.3, -29.873, 2, 1.333, -30, 2, 1.367, -29.725, 2, 1.4, -28.987, 2, 1.433, -27.919, 2, 1.467, -26.654, 2, 1.5, -25.346, 2, 1.533, -24.081, 2, 1.567, -23.013, 2, 1.6, -22.275, 2, 1.633, -22, 2, 1.667, -22.032, 2, 1.7, -22.126, 2, 1.733, -22.275, 2, 1.767, -22.476, 2, 1.8, -22.724, 2, 1.833, -23.009, 2, 1.867, -23.335, 2, 1.9, -23.692, 2, 1.933, -24.071, 2, 1.967, -24.476, 2, 2, -24.899, 2, 2.033, -25.335, 2, 2.067, -25.777, 2, 2.1, -26.223, 2, 2.133, -26.665, 2, 2.167, -27.101, 2, 2.2, -27.524, 2, 2.233, -27.929, 2, 2.267, -28.308, 2, 2.3, -28.665, 2, 2.333, -28.991, 2, 2.367, -29.276, 2, 2.4, -29.524, 2, 2.433, -29.725, 2, 2.467, -29.874, 2, 2.5, -29.968, 2, 2.533, -30, 2, 10, -30]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.5, 0.43, 2, 0.533, 1.562, 2, 0.567, 3.164, 2, 0.6, 5, 2, 0.633, 6.836, 2, 0.667, 8.438, 2, 0.7, 9.57, 2, 0.733, 10, 2, 0.767, 9.642, 2, 0.8, 8.623, 2, 0.833, 7.026, 2, 0.867, 4.934, 2, 0.9, 2.476, 2, 0.933, -0.353, 2, 0.967, -3.431, 2, 1, -6.674, 2, 1.033, -10, 2, 1.067, -13.326, 2, 1.1, -16.569, 2, 1.133, -19.647, 2, 1.167, -22.476, 2, 1.2, -24.934, 2, 1.233, -27.026, 2, 1.267, -28.623, 2, 1.3, -29.642, 2, 1.333, -30, 2, 1.367, -29.828, 2, 1.4, -29.367, 2, 1.433, -28.699, 2, 1.467, -27.909, 2, 1.5, -27.091, 2, 1.533, -26.301, 2, 1.567, -25.633, 2, 1.6, -25.172, 2, 1.633, -25, 2, 10, -25]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.5, 0.004, 2, 0.533, 0.017, 2, 0.567, 0.037, 2, 0.6, 0.064, 2, 0.633, 0.097, 2, 0.667, 0.135, 2, 0.7, 0.179, 2, 0.733, 0.226, 2, 0.767, 0.277, 2, 0.8, 0.33, 2, 0.833, 0.385, 2, 0.867, 0.443, 2, 0.9, 0.5, 2, 0.933, 0.557, 2, 0.967, 0.615, 2, 1, 0.67, 2, 1.033, 0.723, 2, 1.067, 0.774, 2, 1.1, 0.821, 2, 1.133, 0.865, 2, 1.167, 0.903, 2, 1.2, 0.936, 2, 1.233, 0.963, 2, 1.267, 0.983, 2, 1.3, 0.996, 2, 1.333, 1, 2, 2.567, 0.971, 2, 2.6, 0.89, 2, 2.633, 0.763, 2, 2.667, 0.605, 2, 2.7, 0.416, 2, 2.733, 0.212, 2, 2.767, 0, 2, 2.8, -0.212, 2, 2.833, -0.416, 2, 2.867, -0.605, 2, 2.9, -0.763, 2, 2.933, -0.89, 2, 2.967, -0.971, 2, 3, -1, 2, 5.833, -0.96, 2, 5.867, -0.853, 2, 5.9, -0.688, 2, 5.933, -0.48, 2, 5.967, -0.25, 2, 6, 0, 2, 6.033, 0.25, 2, 6.067, 0.48, 2, 6.1, 0.688, 2, 6.133, 0.853, 2, 6.167, 0.96, 2, 6.2, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.5, -0.004, 2, 0.533, -0.017, 2, 0.567, -0.037, 2, 0.6, -0.064, 2, 0.633, -0.097, 2, 0.667, -0.135, 2, 0.7, -0.179, 2, 0.733, -0.226, 2, 0.767, -0.277, 2, 0.8, -0.33, 2, 0.833, -0.385, 2, 0.867, -0.443, 2, 0.9, -0.5, 2, 0.933, -0.557, 2, 0.967, -0.615, 2, 1, -0.67, 2, 1.033, -0.723, 2, 1.067, -0.774, 2, 1.1, -0.821, 2, 1.133, -0.865, 2, 1.167, -0.903, 2, 1.2, -0.936, 2, 1.233, -0.963, 2, 1.267, -0.983, 2, 1.3, -0.996, 2, 1.333, -1, 2, 2.567, -0.971, 2, 2.6, -0.89, 2, 2.633, -0.763, 2, 2.667, -0.605, 2, 2.7, -0.416, 2, 2.733, -0.212, 2, 2.767, 0, 2, 2.8, 0.212, 2, 2.833, 0.416, 2, 2.867, 0.605, 2, 2.9, 0.763, 2, 2.933, 0.89, 2, 2.967, 0.971, 2, 3, 1, 2, 5.833, 0.96, 2, 5.867, 0.853, 2, 5.9, 0.688, 2, 5.933, 0.48, 2, 5.967, 0.25, 2, 6, 0, 2, 6.033, -0.25, 2, 6.067, -0.48, 2, 6.1, -0.688, 2, 6.133, -0.853, 2, 6.167, -0.96, 2, 6.2, -1, 2, 10, -1]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangL", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangR", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamGaoGguangL", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamGaoGuangR", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamTeShuEyeChuXian", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamHeiHuaShadow", "Segments": [0, 0, 2, 0.5, 0.013, 2, 0.533, 0.048, 2, 0.567, 0.104, 2, 0.6, 0.175, 2, 0.633, 0.26, 2, 0.667, 0.353, 2, 0.7, 0.45, 2, 0.733, 0.55, 2, 0.767, 0.647, 2, 0.8, 0.74, 2, 0.833, 0.825, 2, 0.867, 0.896, 2, 0.9, 0.952, 2, 0.933, 0.987, 2, 0.967, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamXianTiaoChuXian", "Segments": [0, 0, 2, 0.5, 0.013, 2, 0.533, 0.048, 2, 0.567, 0.104, 2, 0.6, 0.175, 2, 0.633, 0.26, 2, 0.667, 0.353, 2, 0.7, 0.45, 2, 0.733, 0.55, 2, 0.767, 0.647, 2, 0.8, 0.74, 2, 0.833, 0.825, 2, 0.867, 0.896, 2, 0.9, 0.952, 2, 0.933, 0.987, 2, 0.967, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamTeShuZuiCX", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0.001, 2, 0.1, 0.001, 2, 0.133, 0, 2, 0.167, -0.002, 2, 0.2, -0.007, 2, 0.233, -0.013, 2, 0.267, -0.022, 2, 0.3, -0.035, 2, 0.333, -0.052, 2, 0.367, -0.072, 2, 0.4, -0.099, 2, 0.433, -0.13, 2, 0.467, -0.167, 2, 0.5, -0.207, 2, 0.533, -0.25, 2, 0.567, -0.292, 2, 0.6, -0.335, 2, 0.633, -0.379, 2, 0.667, -0.422, 2, 0.7, -0.467, 2, 0.733, -0.509, 2, 0.767, -0.552, 2, 0.8, -0.595, 2, 0.833, -0.635, 2, 0.867, -0.676, 2, 0.9, -0.714, 2, 0.933, -0.751, 2, 0.967, -0.787, 2, 1, -0.82, 2, 1.033, -0.852, 2, 1.067, -0.881, 2, 1.1, -0.907, 2, 1.133, -0.93, 2, 1.167, -0.951, 2, 1.2, -0.968, 2, 1.233, -0.982, 2, 1.267, -0.992, 2, 1.3, -0.998, 2, 1.333, -1, 2, 10, -1]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0.001, 2, 0.1, 0.001, 2, 0.133, 0, 2, 0.167, -0.002, 2, 0.2, -0.007, 2, 0.233, -0.013, 2, 0.267, -0.022, 2, 0.3, -0.035, 2, 0.333, -0.052, 2, 0.367, -0.072, 2, 0.4, -0.099, 2, 0.433, -0.13, 2, 0.467, -0.167, 2, 0.5, -0.207, 2, 0.533, -0.25, 2, 0.567, -0.292, 2, 0.6, -0.335, 2, 0.633, -0.379, 2, 0.667, -0.422, 2, 0.7, -0.467, 2, 0.733, -0.509, 2, 0.767, -0.552, 2, 0.8, -0.595, 2, 0.833, -0.635, 2, 0.867, -0.676, 2, 0.9, -0.714, 2, 0.933, -0.751, 2, 0.967, -0.787, 2, 1, -0.82, 2, 1.033, -0.852, 2, 1.067, -0.881, 2, 1.1, -0.907, 2, 1.133, -0.93, 2, 1.167, -0.951, 2, 1.2, -0.968, 2, 1.233, -0.982, 2, 1.267, -0.992, 2, 1.3, -0.998, 2, 1.333, -1, 2, 10, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, -0.001, 2, 0.1, -0.001, 2, 0.133, 0, 2, 0.167, 0.002, 2, 0.2, 0.007, 2, 0.233, 0.013, 2, 0.267, 0.022, 2, 0.3, 0.035, 2, 0.333, 0.052, 2, 0.367, 0.072, 2, 0.4, 0.099, 2, 0.433, 0.13, 2, 0.467, 0.167, 2, 0.5, 0.207, 2, 0.533, 0.25, 2, 0.567, 0.292, 2, 0.6, 0.335, 2, 0.633, 0.379, 2, 0.667, 0.422, 2, 0.7, 0.467, 2, 0.733, 0.509, 2, 0.767, 0.552, 2, 0.8, 0.595, 2, 0.833, 0.635, 2, 0.867, 0.676, 2, 0.9, 0.714, 2, 0.933, 0.751, 2, 0.967, 0.787, 2, 1, 0.82, 2, 1.033, 0.852, 2, 1.067, 0.881, 2, 1.1, 0.907, 2, 1.133, 0.93, 2, 1.167, 0.951, 2, 1.2, 0.968, 2, 1.233, 0.982, 2, 1.267, 0.992, 2, 1.3, 0.998, 2, 1.333, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, -0.001, 2, 0.1, -0.001, 2, 0.133, 0, 2, 0.167, 0.002, 2, 0.2, 0.007, 2, 0.233, 0.013, 2, 0.267, 0.022, 2, 0.3, 0.035, 2, 0.333, 0.052, 2, 0.367, 0.072, 2, 0.4, 0.099, 2, 0.433, 0.13, 2, 0.467, 0.167, 2, 0.5, 0.207, 2, 0.533, 0.25, 2, 0.567, 0.292, 2, 0.6, 0.335, 2, 0.633, 0.379, 2, 0.667, 0.422, 2, 0.7, 0.467, 2, 0.733, 0.509, 2, 0.767, 0.552, 2, 0.8, 0.595, 2, 0.833, 0.635, 2, 0.867, 0.676, 2, 0.9, 0.714, 2, 0.933, 0.751, 2, 0.967, 0.787, 2, 1, 0.82, 2, 1.033, 0.852, 2, 1.067, 0.881, 2, 1.1, 0.907, 2, 1.133, 0.93, 2, 1.167, 0.951, 2, 1.2, 0.968, 2, 1.233, 0.982, 2, 1.267, 0.992, 2, 1.3, 0.998, 2, 1.333, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.5, 0.004, 2, 0.533, 0.017, 2, 0.567, 0.037, 2, 0.6, 0.064, 2, 0.633, 0.097, 2, 0.667, 0.135, 2, 0.7, 0.179, 2, 0.733, 0.226, 2, 0.767, 0.277, 2, 0.8, 0.33, 2, 0.833, 0.385, 2, 0.867, 0.443, 2, 0.9, 0.5, 2, 0.933, 0.557, 2, 0.967, 0.615, 2, 1, 0.67, 2, 1.033, 0.723, 2, 1.067, 0.774, 2, 1.1, 0.821, 2, 1.133, 0.865, 2, 1.167, 0.903, 2, 1.2, 0.936, 2, 1.233, 0.963, 2, 1.267, 0.983, 2, 1.3, 0.996, 2, 1.333, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.5, 0.029, 2, 0.533, 0.109, 2, 0.567, 0.234, 2, 0.6, 0.388, 2, 0.633, 0.573, 2, 0.667, 0.771, 2, 0.7, 0.976, 2, 0.733, 1, 2, 1.1, 0.839, 2, 1.133, 0.746, 2, 1.167, 1, 2, 1.267, 0.282, 2, 1.3, 0.808, 2, 1.333, 1, 2, 1.4, 0.995, 2, 1.433, 0.677, 2, 1.467, 0.359, 2, 1.5, 0.114, 2, 1.533, 0.016, 2, 1.567, 0.232, 2, 1.6, 0.668, 2, 1.633, 1, 2, 1.7, 0.686, 2, 1.733, 0.054, 2, 1.767, 0.337, 2, 1.8, 0.62, 2, 1.833, 0.575, 2, 1.867, 0.462, 2, 1.9, 0.312, 2, 1.933, 0.161, 2, 1.967, 0.047, 2, 2, 0, 2, 2.1, 0.185, 2, 2.133, 0.58, 2, 2.167, 0.985, 2, 2.2, 1, 2, 2.233, 0.631, 2, 2.267, 0.094, 2, 2.3, 0.153, 2, 2.333, 0.212, 2, 2.367, 0.106, 2, 2.4, 0, 2, 2.433, 0.004, 2, 2.467, 0.008, 2, 2.5, 0.004, 2, 2.533, 0, 2, 2.633, 0.143, 2, 2.667, 0.458, 2, 2.7, 0.771, 2, 2.733, 0.91, 2, 2.767, 0.771, 2, 2.8, 0.458, 2, 2.833, 0.143, 2, 2.867, 0, 2, 2.9, 0.085, 2, 2.933, 0.273, 2, 2.967, 0.459, 2, 3, 0.542, 2, 3.033, 0.318, 2, 3.067, 0.094, 2, 3.1, 0.105, 2, 3.133, 0.134, 2, 3.167, 0.172, 2, 3.2, 0.21, 2, 3.233, 0.239, 2, 3.267, 0.25, 2, 3.3, 0.156, 2, 3.333, 0.062, 2, 3.367, 0.239, 2, 3.4, 0.616, 2, 3.433, 1, 2, 3.5, 0.992, 2, 3.533, 0.584, 2, 3.567, 0.186, 2, 3.6, 0, 2, 3.633, 0.057, 2, 3.667, 0.202, 2, 3.7, 0.412, 2, 3.733, 0.653, 2, 3.767, 0.889, 2, 3.8, 1, 2, 4.067, 0.804, 2, 4.1, 0.251, 2, 4.133, 0, 2, 4.3, 0.031, 2, 4.333, 0.062, 2, 4.433, 0.862, 2, 4.467, 1, 2, 4.5, 0.886, 2, 4.533, 0.11, 2, 4.567, 1, 2, 4.667, 0.228, 2, 4.7, 0.977, 2, 4.733, 1, 2, 4.8, 0.898, 2, 4.833, 0.328, 2, 4.867, 0.07, 2, 4.9, 0.334, 2, 4.933, 0.917, 2, 4.967, 1, 2, 5.067, 0.282, 2, 5.1, 0.682, 2, 5.133, 1, 2, 5.167, 0.678, 2, 5.2, 0.274, 2, 5.233, 0.499, 2, 5.267, 0.996, 2, 5.3, 1, 2, 5.4, 0.886, 2, 5.433, 0.313, 2, 5.467, 0.054, 2, 5.5, 0.266, 2, 5.533, 0.478, 2, 5.567, 0.282, 2, 5.6, 0.086, 2, 5.633, 0.542, 2, 5.667, 1, 2, 5.7, 0.971, 2, 5.733, 0.777, 2, 5.767, 0.526, 2, 5.8, 0.27, 2, 5.833, 0.077, 2, 5.867, 0, 2, 5.9, 0.076, 2, 5.933, 0.266, 2, 5.967, 0.519, 2, 6, 0.767, 2, 6.033, 0.958, 2, 6.067, 1, 2, 6.1, 0.996, 2, 6.133, 0.956, 2, 6.167, 1, 2, 6.233, 0.816, 2, 6.267, 0.33, 2, 6.3, 0.574, 2, 6.333, 1, 2, 6.5, 0.705, 2, 6.533, 0.486, 2, 6.567, 0.548, 2, 6.6, 0.686, 2, 6.633, 0.824, 2, 6.667, 0.886, 2, 6.7, 0.869, 2, 6.733, 0.831, 2, 6.767, 0.793, 2, 6.8, 0.776, 2, 6.833, 0.926, 2, 6.867, 1, 2, 6.967, 0.953, 2, 7, 0.172, 2, 7.033, 0.382, 2, 7.067, 0.847, 2, 7.1, 1, 2, 7.233, 0.761, 2, 7.267, 0.392, 2, 7.3, 0.114, 2, 7.333, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.5, 0.001, 2, 0.533, 0.004, 2, 0.567, 0.009, 2, 0.6, 0.016, 2, 0.633, 0.025, 2, 0.667, 0.036, 2, 0.7, 0.048, 2, 0.733, 0.061, 2, 0.767, 0.077, 2, 0.8, 0.093, 2, 0.833, 0.111, 2, 0.867, 0.131, 2, 0.9, 0.151, 2, 0.933, 0.172, 2, 0.967, 0.195, 2, 1, 0.218, 2, 1.033, 0.243, 2, 1.067, 0.268, 2, 1.1, 0.293, 2, 1.133, 0.32, 2, 1.167, 0.347, 2, 1.2, 0.374, 2, 1.233, 0.402, 2, 1.267, 0.43, 2, 1.3, 0.458, 2, 1.333, 0.486, 2, 1.367, 0.514, 2, 1.4, 0.542, 2, 1.433, 0.57, 2, 1.467, 0.598, 2, 1.5, 0.626, 2, 1.533, 0.653, 2, 1.567, 0.68, 2, 1.6, 0.707, 2, 1.633, 0.732, 2, 1.667, 0.757, 2, 1.7, 0.782, 2, 1.733, 0.805, 2, 1.767, 0.828, 2, 1.8, 0.849, 2, 1.833, 0.869, 2, 1.867, 0.889, 2, 1.9, 0.907, 2, 1.933, 0.923, 2, 1.967, 0.939, 2, 2, 0.952, 2, 2.033, 0.964, 2, 2.067, 0.975, 2, 2.1, 0.984, 2, 2.133, 0.991, 2, 2.167, 0.996, 2, 2.2, 0.999, 2, 2.233, 1, 2, 2.267, 0.999, 2, 2.3, 0.996, 2, 2.333, 0.991, 2, 2.367, 0.984, 2, 2.4, 0.975, 2, 2.433, 0.964, 2, 2.467, 0.952, 2, 2.5, 0.939, 2, 2.533, 0.923, 2, 2.567, 0.907, 2, 2.6, 0.889, 2, 2.633, 0.869, 2, 2.667, 0.849, 2, 2.7, 0.828, 2, 2.733, 0.805, 2, 2.767, 0.782, 2, 2.8, 0.757, 2, 2.833, 0.732, 2, 2.867, 0.707, 2, 2.9, 0.68, 2, 2.933, 0.653, 2, 2.967, 0.626, 2, 3, 0.598, 2, 3.033, 0.57, 2, 3.067, 0.542, 2, 3.1, 0.514, 2, 3.133, 0.486, 2, 3.167, 0.458, 2, 3.2, 0.43, 2, 3.233, 0.402, 2, 3.267, 0.374, 2, 3.3, 0.347, 2, 3.333, 0.32, 2, 3.367, 0.293, 2, 3.4, 0.268, 2, 3.433, 0.243, 2, 3.467, 0.218, 2, 3.5, 0.195, 2, 3.533, 0.172, 2, 3.567, 0.151, 2, 3.6, 0.131, 2, 3.633, 0.111, 2, 3.667, 0.093, 2, 3.7, 0.077, 2, 3.733, 0.061, 2, 3.767, 0.048, 2, 3.8, 0.036, 2, 3.833, 0.025, 2, 3.867, 0.016, 2, 3.9, 0.009, 2, 3.933, 0.004, 2, 3.967, 0.001, 2, 4, 0, 2, 4.033, 0.001, 2, 4.067, 0.003, 2, 4.1, 0.007, 2, 4.133, 0.013, 2, 4.167, 0.02, 2, 4.2, 0.028, 2, 4.233, 0.038, 2, 4.267, 0.049, 2, 4.3, 0.061, 2, 4.333, 0.074, 2, 4.367, 0.089, 2, 4.4, 0.104, 2, 4.433, 0.121, 2, 4.467, 0.138, 2, 4.5, 0.156, 2, 4.533, 0.175, 2, 4.567, 0.195, 2, 4.6, 0.216, 2, 4.633, 0.237, 2, 4.667, 0.259, 2, 4.7, 0.282, 2, 4.733, 0.305, 2, 4.767, 0.328, 2, 4.8, 0.352, 2, 4.833, 0.376, 2, 4.867, 0.401, 2, 4.9, 0.425, 2, 4.933, 0.45, 2, 4.967, 0.475, 2, 5, 0.5, 2, 5.033, 0.525, 2, 5.067, 0.55, 2, 5.1, 0.575, 2, 5.133, 0.599, 2, 5.167, 0.624, 2, 5.2, 0.648, 2, 5.233, 0.672, 2, 5.267, 0.695, 2, 5.3, 0.718, 2, 5.333, 0.741, 2, 5.367, 0.763, 2, 5.4, 0.784, 2, 5.433, 0.805, 2, 5.467, 0.825, 2, 5.5, 0.844, 2, 5.533, 0.862, 2, 5.567, 0.879, 2, 5.6, 0.896, 2, 5.633, 0.911, 2, 5.667, 0.926, 2, 5.7, 0.939, 2, 5.733, 0.951, 2, 5.767, 0.962, 2, 5.8, 0.972, 2, 5.833, 0.98, 2, 5.867, 0.987, 2, 5.9, 0.993, 2, 5.933, 0.997, 2, 5.967, 0.999, 2, 6, 1, 2, 6.033, 0.999, 2, 6.067, 0.997, 2, 6.1, 0.993, 2, 6.133, 0.987, 2, 6.167, 0.98, 2, 6.2, 0.972, 2, 6.233, 0.962, 2, 6.267, 0.951, 2, 6.3, 0.939, 2, 6.333, 0.926, 2, 6.367, 0.911, 2, 6.4, 0.896, 2, 6.433, 0.879, 2, 6.467, 0.862, 2, 6.5, 0.844, 2, 6.533, 0.825, 2, 6.567, 0.805, 2, 6.6, 0.784, 2, 6.633, 0.763, 2, 6.667, 0.741, 2, 6.7, 0.718, 2, 6.733, 0.695, 2, 6.767, 0.672, 2, 6.8, 0.648, 2, 6.833, 0.624, 2, 6.867, 0.599, 2, 6.9, 0.575, 2, 6.933, 0.55, 2, 6.967, 0.525, 2, 7, 0.5, 2, 7.033, 0.475, 2, 7.067, 0.45, 2, 7.1, 0.425, 2, 7.133, 0.401, 2, 7.167, 0.376, 2, 7.2, 0.352, 2, 7.233, 0.328, 2, 7.267, 0.305, 2, 7.3, 0.282, 2, 7.333, 0.259, 2, 7.367, 0.237, 2, 7.4, 0.216, 2, 7.433, 0.195, 2, 7.467, 0.175, 2, 7.5, 0.156, 2, 7.533, 0.138, 2, 7.567, 0.121, 2, 7.6, 0.104, 2, 7.633, 0.089, 2, 7.667, 0.074, 2, 7.7, 0.061, 2, 7.733, 0.049, 2, 7.767, 0.038, 2, 7.8, 0.028, 2, 7.833, 0.02, 2, 7.867, 0.013, 2, 7.9, 0.007, 2, 7.933, 0.003, 2, 7.967, 0.001, 2, 8, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.5, -0.129, 2, 0.533, -0.469, 2, 0.567, -0.949, 2, 0.6, -1.5, 2, 0.633, -2.051, 2, 0.667, -2.531, 2, 0.7, -2.871, 2, 0.733, -3, 2, 0.767, -2.884, 2, 0.8, -2.553, 2, 0.833, -2.034, 2, 0.867, -1.354, 2, 0.9, -0.555, 2, 0.933, 0.365, 2, 0.967, 1.365, 2, 1, 2.419, 2, 1.033, 3.5, 2, 1.067, 4.581, 2, 1.1, 5.635, 2, 1.133, 6.635, 2, 1.167, 7.555, 2, 1.2, 8.354, 2, 1.233, 9.034, 2, 1.267, 9.553, 2, 1.3, 9.884, 2, 1.333, 10, 2, 1.367, 9.931, 2, 1.4, 9.747, 2, 1.433, 9.48, 2, 1.467, 9.163, 2, 1.5, 8.837, 2, 1.533, 8.52, 2, 1.567, 8.253, 2, 1.6, 8.069, 2, 1.633, 8, 2, 10, 8]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.5, 0.129, 2, 0.533, 0.469, 2, 0.567, 0.949, 2, 0.6, 1.5, 2, 0.633, 2.051, 2, 0.667, 2.531, 2, 0.7, 2.871, 2, 0.733, 3, 2, 0.767, 2.884, 2, 0.8, 2.553, 2, 0.833, 2.034, 2, 0.867, 1.354, 2, 0.9, 0.555, 2, 0.933, -0.365, 2, 0.967, -1.365, 2, 1, -2.419, 2, 1.033, -3.5, 2, 1.067, -4.581, 2, 1.1, -5.635, 2, 1.133, -6.635, 2, 1.167, -7.555, 2, 1.2, -8.354, 2, 1.233, -9.034, 2, 1.267, -9.553, 2, 1.3, -9.884, 2, 1.333, -10, 2, 1.367, -9.931, 2, 1.4, -9.747, 2, 1.433, -9.48, 2, 1.467, -9.163, 2, 1.5, -8.837, 2, 1.533, -8.52, 2, 1.567, -8.253, 2, 1.6, -8.069, 2, 1.633, -8, 2, 10, -8]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.5, -0.129, 2, 0.533, -0.469, 2, 0.567, -0.949, 2, 0.6, -1.5, 2, 0.633, -2.051, 2, 0.667, -2.531, 2, 0.7, -2.871, 2, 0.733, -3, 2, 0.767, -2.884, 2, 0.8, -2.553, 2, 0.833, -2.034, 2, 0.867, -1.354, 2, 0.9, -0.555, 2, 0.933, 0.365, 2, 0.967, 1.365, 2, 1, 2.419, 2, 1.033, 3.5, 2, 1.067, 4.581, 2, 1.1, 5.635, 2, 1.133, 6.635, 2, 1.167, 7.555, 2, 1.2, 8.354, 2, 1.233, 9.034, 2, 1.267, 9.553, 2, 1.3, 9.884, 2, 1.333, 10, 2, 1.367, 9.931, 2, 1.4, 9.747, 2, 1.433, 9.48, 2, 1.467, 9.163, 2, 1.5, 8.837, 2, 1.533, 8.52, 2, 1.567, 8.253, 2, 1.6, 8.069, 2, 1.633, 8, 2, 10, 8]}, {"Target": "Parameter", "Id": "ParamShenTiQianHou", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Paramzuodatui", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 2, 0.067, 0, 2, 0.133, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 0.233, 0, 2, 0.267, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 0.5, 0.004, 2, 0.533, 0.015, 2, 0.567, 0.028, 2, 0.6, 0.038, 2, 0.633, 0.043, 2, 0.667, 0.035, 2, 0.7, 0.013, 2, 0.733, -0.018, 2, 0.767, -0.055, 2, 0.8, -0.093, 2, 0.833, -0.13, 2, 0.867, -0.162, 2, 0.9, -0.183, 2, 0.933, -0.191, 2, 0.967, -0.186, 2, 1, -0.173, 2, 1.033, -0.151, 2, 1.067, -0.125, 2, 1.1, -0.093, 2, 1.133, -0.059, 2, 1.167, -0.024, 2, 1.2, 0.012, 2, 1.233, 0.046, 2, 1.267, 0.078, 2, 1.3, 0.104, 2, 1.333, 0.125, 2, 1.367, 0.139, 2, 1.4, 0.144, 2, 1.433, 0.135, 2, 1.467, 0.112, 2, 1.5, 0.078, 2, 1.533, 0.037, 2, 1.567, -0.008, 2, 1.6, -0.052, 2, 1.633, -0.093, 2, 1.667, -0.128, 2, 1.7, -0.151, 2, 1.733, -0.159, 2, 1.767, -0.154, 2, 1.8, -0.14, 2, 1.833, -0.119, 2, 1.867, -0.095, 2, 1.9, -0.068, 2, 1.933, -0.041, 2, 1.967, -0.016, 2, 2, 0.005, 2, 2.033, 0.019, 2, 2.067, 0.024, 2, 2.1, 0.021, 2, 2.133, 0.015, 2, 2.167, 0.006, 2, 2.2, -0.005, 2, 2.233, -0.016, 2, 2.267, -0.028, 2, 2.3, -0.039, 2, 2.333, -0.048, 2, 2.367, -0.054, 2, 2.4, -0.056, 2, 2.433, -0.054, 2, 2.467, -0.05, 2, 2.5, -0.042, 2, 2.533, -0.033, 2, 2.567, -0.023, 2, 2.6, -0.013, 2, 2.633, -0.003, 2, 2.667, 0.006, 2, 2.7, 0.013, 2, 2.733, 0.018, 2, 2.767, 0.02, 2, 2.8, 0.017, 2, 2.833, 0.01, 2, 2.867, 0, 2, 2.9, -0.013, 2, 2.933, -0.027, 2, 2.967, -0.041, 2, 3, -0.053, 2, 3.033, -0.064, 2, 3.067, -0.071, 2, 3.1, -0.074, 2, 3.133, -0.072, 2, 3.167, -0.066, 2, 3.2, -0.058, 2, 3.233, -0.048, 2, 3.267, -0.037, 2, 3.3, -0.027, 2, 3.333, -0.019, 2, 3.367, -0.013, 2, 3.4, -0.011, 2, 3.433, -0.012, 2, 3.467, -0.014, 2, 3.5, -0.018, 2, 3.533, -0.022, 2, 3.567, -0.026, 2, 3.6, -0.03, 2, 3.633, -0.034, 2, 3.667, -0.038, 2, 3.7, -0.04, 2, 3.733, -0.041, 2, 3.767, -0.041, 2, 3.8, -0.04, 2, 3.833, -0.038, 2, 3.867, -0.036, 2, 3.9, -0.034, 2, 3.933, -0.032, 2, 3.967, -0.03, 2, 4, -0.029, 2, 4.033, -0.028, 2, 4.067, -0.028, 2, 4.1, -0.028, 2, 4.133, -0.028, 2, 4.167, -0.029, 2, 4.2, -0.03, 2, 4.233, -0.031, 2, 4.267, -0.032, 2, 4.3, -0.033, 2, 4.333, -0.034, 2, 4.367, -0.034, 2, 4.433, -0.034, 2, 4.467, -0.033, 2, 4.5, -0.033, 2, 4.533, -0.033, 2, 4.567, -0.032, 2, 4.6, -0.032, 2, 4.633, -0.031, 2, 4.667, -0.031, 2, 4.7, -0.031, 2, 4.733, -0.031, 2, 4.767, -0.032, 2, 4.8, -0.032, 2, 4.833, -0.032, 2, 4.867, -0.033, 2, 4.9, -0.033, 2, 4.933, -0.033, 2, 4.967, -0.033, 2, 5, -0.034, 2, 5.033, -0.034, 2, 5.067, -0.034, 2, 5.2, -0.034, 2, 5.233, -0.034, 2, 5.3, -0.034, 2, 5.333, -0.035, 2, 5.367, -0.036, 2, 5.4, -0.037, 2, 5.433, -0.039, 2, 5.467, -0.041, 2, 5.5, -0.043, 2, 5.533, -0.045, 2, 5.567, -0.048, 2, 5.6, -0.05, 2, 5.633, -0.053, 2, 5.667, -0.056, 2, 5.7, -0.058, 2, 5.733, -0.061, 2, 5.767, -0.063, 2, 5.8, -0.065, 2, 5.833, -0.067, 2, 5.867, -0.069, 2, 5.9, -0.07, 2, 5.933, -0.071, 2, 5.967, -0.072, 2, 6, -0.072, 2, 6.033, -0.069, 2, 6.067, -0.06, 2, 6.1, -0.048, 2, 6.133, -0.033, 2, 6.167, -0.017, 2, 6.2, -0.003, 2, 6.233, 0.01, 2, 6.267, 0.019, 2, 6.3, 0.022, 2, 6.333, 0.02, 2, 6.367, 0.014, 2, 6.4, 0.005, 2, 6.433, -0.006, 2, 6.467, -0.017, 2, 6.5, -0.029, 2, 6.533, -0.039, 2, 6.567, -0.048, 2, 6.6, -0.054, 2, 6.633, -0.056, 2, 6.667, -0.055, 2, 6.7, -0.051, 2, 6.733, -0.047, 2, 6.767, -0.041, 2, 6.8, -0.035, 2, 6.833, -0.03, 2, 6.867, -0.025, 2, 6.9, -0.021, 2, 6.933, -0.02, 2, 7, -0.021, 2, 7.033, -0.022, 2, 7.067, -0.024, 2, 7.1, -0.027, 2, 7.133, -0.03, 2, 7.167, -0.032, 2, 7.2, -0.035, 2, 7.233, -0.036, 2, 7.267, -0.037, 2, 7.3, -0.036, 2, 7.333, -0.036, 2, 7.367, -0.035, 2, 7.4, -0.034, 2, 7.433, -0.033, 2, 7.467, -0.032, 2, 7.5, -0.031, 2, 7.533, -0.03, 2, 7.567, -0.029, 2, 7.6, -0.029, 2, 7.633, -0.029, 2, 7.667, -0.029, 2, 7.7, -0.03, 2, 7.733, -0.03, 2, 7.767, -0.031, 2, 7.8, -0.032, 2, 7.833, -0.032, 2, 7.867, -0.032, 2, 7.9, -0.033, 2, 7.967, -0.032, 2, 8, -0.032, 2, 8.033, -0.032, 2, 8.067, -0.032, 2, 8.1, -0.031, 2, 8.133, -0.031, 2, 8.167, -0.031, 2, 8.2, -0.031, 2, 8.3, -0.031, 2, 8.333, -0.031, 2, 8.367, -0.031, 2, 8.4, -0.031, 2, 8.433, -0.031, 2, 8.467, -0.032, 2, 8.5, -0.032, 2, 8.567, -0.032, 2, 8.6, -0.032, 2, 8.7, -0.032, 2, 8.733, -0.031, 2, 8.833, -0.031, 2, 9, -0.031, 2, 9.133, -0.032, 2, 9.333, -0.031, 2, 9.4, -0.031, 2, 9.433, -0.031, 2, 10, -0.031]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 2, 0.033, 0, 2, 0.133, 0, 2, 0.2, 0, 2, 0.233, 0, 2, 0.3, 0, 2, 0.5, 0.005, 2, 0.533, 0.018, 2, 0.567, 0.033, 2, 0.6, 0.046, 2, 0.633, 0.051, 2, 0.667, 0.042, 2, 0.7, 0.019, 2, 0.733, -0.014, 2, 0.767, -0.054, 2, 0.8, -0.095, 2, 0.833, -0.135, 2, 0.867, -0.169, 2, 0.9, -0.192, 2, 0.933, -0.2, 2, 0.967, -0.196, 2, 1, -0.183, 2, 1.033, -0.163, 2, 1.067, -0.138, 2, 1.1, -0.108, 2, 1.133, -0.076, 2, 1.167, -0.043, 2, 1.2, -0.009, 2, 1.233, 0.023, 2, 1.267, 0.053, 2, 1.3, 0.077, 2, 1.333, 0.097, 2, 1.367, 0.11, 2, 1.4, 0.115, 2, 1.433, 0.107, 2, 1.467, 0.085, 2, 1.5, 0.053, 2, 1.533, 0.014, 2, 1.567, -0.028, 2, 1.6, -0.071, 2, 1.633, -0.11, 2, 1.667, -0.142, 2, 1.7, -0.164, 2, 1.733, -0.172, 2, 1.767, -0.166, 2, 1.8, -0.149, 2, 1.833, -0.126, 2, 1.867, -0.098, 2, 1.9, -0.069, 2, 1.933, -0.041, 2, 1.967, -0.018, 2, 2, -0.002, 2, 2.033, 0.004, 2, 2.067, 0.001, 2, 2.1, -0.007, 2, 2.133, -0.019, 2, 2.167, -0.033, 2, 2.2, -0.047, 2, 2.233, -0.059, 2, 2.267, -0.068, 2, 2.3, -0.071, 2, 2.333, -0.07, 2, 2.367, -0.066, 2, 2.4, -0.061, 2, 2.433, -0.055, 2, 2.467, -0.047, 2, 2.5, -0.039, 2, 2.533, -0.031, 2, 2.567, -0.023, 2, 2.6, -0.015, 2, 2.633, -0.009, 2, 2.667, -0.004, 2, 2.7, 0, 2, 2.733, 0.001, 2, 2.767, -0.002, 2, 2.8, -0.008, 2, 2.833, -0.019, 2, 2.867, -0.031, 2, 2.9, -0.044, 2, 2.933, -0.057, 2, 2.967, -0.069, 2, 3, -0.08, 2, 3.033, -0.086, 2, 3.067, -0.089, 2, 3.1, -0.087, 2, 3.133, -0.081, 2, 3.167, -0.073, 2, 3.2, -0.063, 2, 3.233, -0.053, 2, 3.267, -0.044, 2, 3.3, -0.036, 2, 3.333, -0.03, 2, 3.367, -0.028, 2, 3.4, -0.029, 2, 3.433, -0.031, 2, 3.467, -0.035, 2, 3.5, -0.04, 2, 3.533, -0.044, 2, 3.567, -0.049, 2, 3.6, -0.052, 2, 3.633, -0.055, 2, 3.667, -0.056, 2, 3.7, -0.055, 2, 3.733, -0.054, 2, 3.767, -0.053, 2, 3.8, -0.051, 2, 3.833, -0.049, 2, 3.867, -0.047, 2, 3.9, -0.045, 2, 3.933, -0.044, 2, 3.967, -0.044, 2, 4, -0.044, 2, 4.033, -0.044, 2, 4.067, -0.045, 2, 4.1, -0.046, 2, 4.133, -0.047, 2, 4.167, -0.048, 2, 4.2, -0.049, 2, 4.233, -0.049, 2, 4.267, -0.049, 2, 4.3, -0.049, 2, 4.333, -0.049, 2, 4.367, -0.049, 2, 4.4, -0.048, 2, 4.433, -0.048, 2, 4.467, -0.047, 2, 4.5, -0.047, 2, 4.533, -0.047, 2, 4.6, -0.047, 2, 4.633, -0.047, 2, 4.667, -0.047, 2, 4.7, -0.048, 2, 4.733, -0.048, 2, 4.767, -0.048, 2, 4.8, -0.049, 2, 4.833, -0.049, 2, 4.867, -0.049, 2, 4.9, -0.049, 2, 5, -0.049, 2, 5.067, -0.049, 2, 5.133, -0.049, 2, 5.167, -0.05, 2, 5.2, -0.051, 2, 5.233, -0.052, 2, 5.267, -0.053, 2, 5.3, -0.055, 2, 5.333, -0.056, 2, 5.367, -0.058, 2, 5.4, -0.06, 2, 5.433, -0.062, 2, 5.467, -0.065, 2, 5.5, -0.067, 2, 5.533, -0.069, 2, 5.567, -0.072, 2, 5.6, -0.074, 2, 5.633, -0.076, 2, 5.667, -0.079, 2, 5.7, -0.081, 2, 5.733, -0.083, 2, 5.767, -0.085, 2, 5.8, -0.087, 2, 5.833, -0.088, 2, 5.867, -0.089, 2, 5.9, -0.091, 2, 5.933, -0.091, 2, 5.967, -0.092, 2, 6, -0.092, 2, 6.033, -0.088, 2, 6.067, -0.076, 2, 6.1, -0.059, 2, 6.133, -0.04, 2, 6.167, -0.021, 2, 6.2, -0.004, 2, 6.233, 0.007, 2, 6.267, 0.012, 2, 6.3, 0.009, 2, 6.333, 0.001, 2, 6.367, -0.01, 2, 6.4, -0.024, 2, 6.433, -0.038, 2, 6.467, -0.052, 2, 6.5, -0.063, 2, 6.533, -0.071, 2, 6.567, -0.074, 2, 6.6, -0.072, 2, 6.633, -0.069, 2, 6.667, -0.064, 2, 6.7, -0.058, 2, 6.733, -0.051, 2, 6.767, -0.045, 2, 6.8, -0.04, 2, 6.833, -0.036, 2, 6.867, -0.035, 2, 6.9, -0.036, 2, 6.933, -0.037, 2, 6.967, -0.04, 2, 7, -0.042, 2, 7.033, -0.045, 2, 7.067, -0.048, 2, 7.1, -0.05, 2, 7.133, -0.052, 2, 7.167, -0.053, 2, 7.2, -0.052, 2, 7.233, -0.052, 2, 7.267, -0.051, 2, 7.3, -0.049, 2, 7.333, -0.048, 2, 7.367, -0.047, 2, 7.4, -0.046, 2, 7.433, -0.045, 2, 7.467, -0.045, 2, 7.533, -0.045, 2, 7.567, -0.045, 2, 7.6, -0.046, 2, 7.633, -0.046, 2, 7.667, -0.047, 2, 7.7, -0.048, 2, 7.733, -0.048, 2, 7.767, -0.048, 2, 7.833, -0.048, 2, 7.867, -0.048, 2, 7.9, -0.048, 2, 7.933, -0.047, 2, 7.967, -0.047, 2, 8, -0.047, 2, 8.033, -0.047, 2, 8.067, -0.047, 2, 8.133, -0.047, 2, 8.167, -0.047, 2, 8.2, -0.047, 2, 8.233, -0.047, 2, 8.267, -0.047, 2, 8.3, -0.047, 2, 8.367, -0.047, 2, 8.4, -0.047, 2, 8.5, -0.047, 2, 8.533, -0.047, 2, 8.633, -0.047, 2, 8.767, -0.047, 2, 8.9, -0.047, 2, 8.967, -0.047, 2, 9, -0.047, 2, 9.067, -0.047, 2, 9.167, -0.047, 2, 9.2, -0.047, 2, 9.233, -0.047, 2, 9.267, -0.047, 2, 9.367, -0.047, 2, 9.4, -0.047, 2, 9.467, -0.047, 2, 9.5, -0.047, 2, 9.533, -0.047, 2, 9.567, -0.047, 2, 9.6, -0.047, 2, 9.633, -0.047, 2, 9.767, -0.047, 2, 9.8, -0.047, 2, 9.833, -0.047, 2, 10, -0.047]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0, 2, 0.1, 0, 2, 0.133, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 0.267, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 0.5, 0.004, 2, 0.533, 0.014, 2, 0.567, 0.025, 2, 0.6, 0.035, 2, 0.633, 0.039, 2, 0.667, 0.033, 2, 0.7, 0.018, 2, 0.733, -0.005, 2, 0.767, -0.032, 2, 0.8, -0.062, 2, 0.833, -0.092, 2, 0.867, -0.12, 2, 0.9, -0.143, 2, 0.933, -0.158, 2, 0.967, -0.164, 2, 1, -0.16, 2, 1.033, -0.149, 2, 1.067, -0.131, 2, 1.1, -0.11, 2, 1.133, -0.084, 2, 1.167, -0.056, 2, 1.2, -0.027, 2, 1.233, 0.002, 2, 1.267, 0.03, 2, 1.3, 0.056, 2, 1.333, 0.078, 2, 1.367, 0.095, 2, 1.4, 0.106, 2, 1.433, 0.11, 2, 1.467, 0.103, 2, 1.5, 0.082, 2, 1.533, 0.052, 2, 1.567, 0.015, 2, 1.6, -0.025, 2, 1.633, -0.065, 2, 1.667, -0.102, 2, 1.7, -0.133, 2, 1.733, -0.153, 2, 1.767, -0.161, 2, 1.8, -0.157, 2, 1.833, -0.147, 2, 1.867, -0.131, 2, 1.9, -0.112, 2, 1.933, -0.09, 2, 1.967, -0.068, 2, 2, -0.046, 2, 2.033, -0.027, 2, 2.067, -0.011, 2, 2.1, -0.001, 2, 2.133, 0.003, 2, 2.167, 0.001, 2, 2.2, -0.004, 2, 2.233, -0.013, 2, 2.267, -0.023, 2, 2.3, -0.033, 2, 2.333, -0.044, 2, 2.367, -0.054, 2, 2.4, -0.062, 2, 2.433, -0.068, 2, 2.467, -0.07, 2, 2.5, -0.068, 2, 2.533, -0.063, 2, 2.567, -0.055, 2, 2.6, -0.046, 2, 2.633, -0.035, 2, 2.667, -0.025, 2, 2.7, -0.016, 2, 2.733, -0.008, 2, 2.767, -0.003, 2, 2.8, -0.001, 2, 2.833, -0.004, 2, 2.867, -0.01, 2, 2.9, -0.019, 2, 2.933, -0.03, 2, 2.967, -0.042, 2, 3, -0.055, 2, 3.033, -0.066, 2, 3.067, -0.075, 2, 3.1, -0.081, 2, 3.133, -0.084, 2, 3.167, -0.082, 2, 3.2, -0.078, 2, 3.233, -0.072, 2, 3.267, -0.065, 2, 3.3, -0.057, 2, 3.333, -0.049, 2, 3.367, -0.041, 2, 3.4, -0.035, 2, 3.433, -0.031, 2, 3.467, -0.029, 2, 3.5, -0.03, 2, 3.533, -0.032, 2, 3.567, -0.034, 2, 3.6, -0.037, 2, 3.633, -0.041, 2, 3.667, -0.044, 2, 3.7, -0.048, 2, 3.733, -0.051, 2, 3.767, -0.053, 2, 3.8, -0.055, 2, 3.833, -0.056, 2, 3.867, -0.055, 2, 3.9, -0.054, 2, 3.933, -0.053, 2, 3.967, -0.051, 2, 4, -0.05, 2, 4.033, -0.048, 2, 4.067, -0.046, 2, 4.1, -0.045, 2, 4.133, -0.044, 2, 4.167, -0.044, 2, 4.2, -0.044, 2, 4.233, -0.044, 2, 4.267, -0.045, 2, 4.3, -0.046, 2, 4.333, -0.046, 2, 4.367, -0.047, 2, 4.4, -0.048, 2, 4.433, -0.049, 2, 4.467, -0.049, 2, 4.5, -0.049, 2, 4.567, -0.049, 2, 4.6, -0.049, 2, 4.633, -0.049, 2, 4.667, -0.048, 2, 4.7, -0.048, 2, 4.733, -0.048, 2, 4.767, -0.047, 2, 4.8, -0.047, 2, 4.867, -0.047, 2, 4.9, -0.048, 2, 4.933, -0.048, 2, 4.967, -0.048, 2, 5, -0.048, 2, 5.033, -0.049, 2, 5.067, -0.049, 2, 5.1, -0.049, 2, 5.133, -0.049, 2, 5.167, -0.05, 2, 5.2, -0.05, 2, 5.233, -0.05, 2, 5.333, -0.05, 2, 5.367, -0.05, 2, 5.4, -0.05, 2, 5.5, -0.05, 2, 5.533, -0.051, 2, 5.567, -0.052, 2, 5.6, -0.054, 2, 5.633, -0.056, 2, 5.667, -0.059, 2, 5.7, -0.061, 2, 5.733, -0.064, 2, 5.767, -0.067, 2, 5.8, -0.07, 2, 5.833, -0.072, 2, 5.867, -0.075, 2, 5.9, -0.077, 2, 5.933, -0.079, 2, 5.967, -0.08, 2, 6, -0.081, 2, 6.033, -0.081, 2, 6.067, -0.079, 2, 6.1, -0.071, 2, 6.133, -0.061, 2, 6.167, -0.049, 2, 6.2, -0.036, 2, 6.233, -0.024, 2, 6.267, -0.014, 2, 6.3, -0.007, 2, 6.333, -0.004, 2, 6.367, -0.006, 2, 6.4, -0.01, 2, 6.433, -0.018, 2, 6.467, -0.026, 2, 6.5, -0.036, 2, 6.533, -0.045, 2, 6.567, -0.054, 2, 6.6, -0.061, 2, 6.633, -0.066, 2, 6.667, -0.068, 2, 6.7, -0.067, 2, 6.733, -0.065, 2, 6.767, -0.061, 2, 6.8, -0.057, 2, 6.833, -0.053, 2, 6.867, -0.048, 2, 6.9, -0.044, 2, 6.933, -0.041, 2, 6.967, -0.038, 2, 7, -0.038, 2, 7.067, -0.038, 2, 7.1, -0.039, 2, 7.133, -0.041, 2, 7.167, -0.043, 2, 7.2, -0.045, 2, 7.233, -0.047, 2, 7.267, -0.049, 2, 7.3, -0.05, 2, 7.333, -0.051, 2, 7.367, -0.052, 2, 7.4, -0.052, 2, 7.433, -0.051, 2, 7.467, -0.05, 2, 7.5, -0.049, 2, 7.533, -0.048, 2, 7.567, -0.047, 2, 7.6, -0.046, 2, 7.633, -0.046, 2, 7.667, -0.045, 2, 7.7, -0.045, 2, 7.767, -0.045, 2, 7.8, -0.045, 2, 7.833, -0.046, 2, 7.867, -0.046, 2, 7.9, -0.047, 2, 7.933, -0.047, 2, 7.967, -0.048, 2, 8, -0.048, 2, 8.033, -0.048, 2, 8.133, -0.048, 2, 8.167, -0.048, 2, 8.2, -0.048, 2, 8.233, -0.047, 2, 8.267, -0.047, 2, 8.3, -0.047, 2, 8.333, -0.047, 2, 8.367, -0.047, 2, 8.4, -0.047, 2, 8.433, -0.047, 2, 8.5, -0.047, 2, 8.533, -0.047, 2, 8.567, -0.047, 2, 8.6, -0.047, 2, 8.633, -0.047, 2, 8.667, -0.047, 2, 8.7, -0.047, 2, 8.733, -0.047, 2, 8.8, -0.047, 2, 8.833, -0.047, 2, 8.867, -0.047, 2, 8.9, -0.047, 2, 8.967, -0.047, 2, 9.033, -0.047, 2, 9.2, -0.047, 2, 9.367, -0.047, 2, 9.4, -0.047, 2, 9.433, -0.047, 2, 9.467, -0.047, 2, 9.5, -0.047, 2, 9.533, -0.047, 2, 9.567, -0.047, 2, 9.6, -0.047, 2, 9.667, -0.047, 2, 9.7, -0.047, 2, 9.833, -0.047, 2, 9.867, -0.047, 2, 9.933, -0.047, 2, 9.967, -0.047, 2, 10, -0.047]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0.022, 2, 0.533, 0.075, 2, 0.567, 0.139, 2, 0.6, 0.191, 2, 0.633, 0.213, 2, 0.667, 0.181, 2, 0.7, 0.095, 2, 0.733, -0.026, 2, 0.767, -0.165, 2, 0.8, -0.304, 2, 0.833, -0.426, 2, 0.867, -0.512, 2, 0.9, -0.544, 2, 0.933, -0.53, 2, 0.967, -0.489, 2, 1, -0.425, 2, 1.033, -0.346, 2, 1.067, -0.252, 2, 1.1, -0.15, 2, 1.133, -0.043, 2, 1.167, 0.063, 2, 1.2, 0.165, 2, 1.233, 0.26, 2, 1.267, 0.339, 2, 1.3, 0.402, 2, 1.333, 0.443, 2, 1.367, 0.458, 2, 1.4, 0.441, 2, 1.433, 0.398, 2, 1.467, 0.332, 2, 1.5, 0.253, 2, 1.533, 0.168, 2, 1.567, 0.082, 2, 1.6, 0.003, 2, 1.633, -0.063, 2, 1.667, -0.106, 2, 1.7, -0.122, 2, 1.733, -0.108, 2, 1.767, -0.07, 2, 1.8, -0.016, 2, 1.833, 0.046, 2, 1.867, 0.107, 2, 1.9, 0.161, 2, 1.933, 0.199, 2, 1.967, 0.214, 2, 2, 0.207, 2, 2.033, 0.191, 2, 2.067, 0.167, 2, 2.1, 0.141, 2, 2.133, 0.114, 2, 2.167, 0.09, 2, 2.2, 0.074, 2, 2.233, 0.068, 2, 2.267, 0.07, 2, 2.3, 0.076, 2, 2.333, 0.084, 2, 2.367, 0.094, 2, 2.4, 0.104, 2, 2.433, 0.114, 2, 2.467, 0.123, 2, 2.5, 0.129, 2, 2.533, 0.131, 2, 2.567, 0.13, 2, 2.6, 0.127, 2, 2.633, 0.122, 2, 2.667, 0.117, 2, 2.7, 0.112, 2, 2.733, 0.108, 2, 2.767, 0.104, 2, 2.8, 0.103, 2, 2.833, 0.104, 2, 2.867, 0.105, 2, 2.9, 0.107, 2, 2.933, 0.109, 2, 2.967, 0.112, 2, 3, 0.113, 2, 3.033, 0.115, 2, 3.067, 0.115, 2, 3.1, 0.115, 2, 3.133, 0.115, 2, 3.167, 0.114, 2, 3.2, 0.113, 2, 3.233, 0.112, 2, 3.267, 0.111, 2, 3.3, 0.11, 2, 3.333, 0.11, 2, 3.4, 0.11, 2, 3.433, 0.111, 2, 3.467, 0.111, 2, 3.5, 0.111, 2, 3.533, 0.112, 2, 3.567, 0.112, 2, 3.6, 0.112, 2, 3.667, 0.112, 2, 3.7, 0.112, 2, 3.733, 0.112, 2, 3.767, 0.112, 2, 3.8, 0.112, 2, 3.833, 0.111, 2, 3.867, 0.111, 2, 3.967, 0.111, 2, 4, 0.112, 2, 4.033, 0.112, 2, 4.1, 0.112, 2, 4.2, 0.112, 2, 4.233, 0.112, 2, 4.3, 0.112, 2, 4.4, 0.112, 2, 4.5, 0.112, 2, 4.567, 0.112, 2, 4.6, 0.112, 2, 4.633, 0.112, 2, 4.667, 0.112, 2, 4.733, 0.112, 2, 4.767, 0.112, 2, 4.867, 0.112, 2, 4.9, 0.112, 2, 4.967, 0.112, 2, 5, 0.112, 2, 5.033, 0.112, 2, 5.067, 0.112, 2, 5.1, 0.112, 2, 5.133, 0.112, 2, 5.167, 0.112, 2, 5.2, 0.112, 2, 5.233, 0.112, 2, 5.3, 0.112, 2, 5.333, 0.112, 2, 5.433, 0.112, 2, 5.467, 0.112, 2, 5.5, 0.112, 2, 5.533, 0.112, 2, 5.6, 0.112, 2, 5.633, 0.112, 2, 5.667, 0.112, 2, 5.733, 0.112, 2, 5.767, 0.112, 2, 5.833, 0.112, 2, 5.867, 0.112, 2, 6, 0.112, 2, 6.033, 0.112, 2, 6.1, 0.112, 2, 6.133, 0.112, 2, 6.4, 0.112, 2, 6.433, 0.112, 2, 6.667, 0.112, 2, 6.7, 0.112, 2, 6.9, 0.112, 2, 6.933, 0.112, 2, 7, 0.112, 2, 7.033, 0.112, 2, 7.067, 0.112, 2, 7.1, 0.112, 2, 7.2, 0.112, 2, 7.233, 0.112, 2, 7.267, 0.112, 2, 7.3, 0.112, 2, 7.367, 0.112, 2, 7.4, 0.112, 2, 7.467, 0.112, 2, 7.5, 0.112, 2, 7.533, 0.112, 2, 7.567, 0.112, 2, 7.6, 0.112, 2, 7.667, 0.112, 2, 7.7, 0.112, 2, 7.733, 0.112, 2, 7.767, 0.112, 2, 7.833, 0.112, 2, 7.867, 0.112, 2, 7.9, 0.112, 2, 7.933, 0.112, 2, 8.2, 0.112, 2, 8.233, 0.112, 2, 8.333, 0.112, 2, 8.367, 0.112, 2, 8.633, 0.112, 2, 8.667, 0.112, 2, 8.767, 0.112, 2, 8.8, 0.112, 2, 10, 0.112]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.067, 0, 2, 0.133, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 0.233, 0, 2, 0.267, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.5, -0.006, 2, 0.533, -0.018, 2, 0.567, -0.034, 2, 0.6, -0.047, 2, 0.633, -0.052, 2, 0.7, -0.045, 2, 0.733, -0.025, 2, 0.767, 0.003, 2, 0.8, 0.037, 2, 0.833, 0.072, 2, 0.867, 0.105, 2, 0.9, 0.134, 2, 0.933, 0.153, 2, 0.967, 0.161, 2, 1, 0.155, 2, 1.033, 0.139, 2, 1.067, 0.115, 2, 1.1, 0.084, 2, 1.133, 0.047, 2, 1.167, 0.008, 2, 1.2, -0.033, 2, 1.233, -0.075, 2, 1.267, -0.114, 2, 1.3, -0.151, 2, 1.333, -0.181, 2, 1.367, -0.206, 2, 1.4, -0.222, 2, 1.433, -0.227, 2, 1.467, -0.22, 2, 1.5, -0.201, 2, 1.533, -0.172, 2, 1.567, -0.136, 2, 1.6, -0.096, 2, 1.633, -0.055, 2, 1.667, -0.015, 2, 1.7, 0.02, 2, 1.733, 0.05, 2, 1.767, 0.069, 2, 1.8, 0.076, 2, 1.833, 0.072, 2, 1.867, 0.061, 2, 1.9, 0.045, 2, 1.933, 0.025, 2, 1.967, 0.003, 2, 2, -0.02, 2, 2.033, -0.043, 2, 2.067, -0.063, 2, 2.1, -0.079, 2, 2.133, -0.09, 2, 2.167, -0.094, 2, 2.2, -0.092, 2, 2.233, -0.088, 2, 2.267, -0.081, 2, 2.3, -0.073, 2, 2.333, -0.063, 2, 2.367, -0.053, 2, 2.4, -0.043, 2, 2.433, -0.034, 2, 2.467, -0.025, 2, 2.5, -0.019, 2, 2.533, -0.014, 2, 2.567, -0.013, 2, 2.6, -0.014, 2, 2.633, -0.016, 2, 2.667, -0.02, 2, 2.7, -0.024, 2, 2.733, -0.03, 2, 2.767, -0.035, 2, 2.8, -0.04, 2, 2.833, -0.045, 2, 2.867, -0.048, 2, 2.9, -0.051, 2, 2.933, -0.052, 2, 2.967, -0.051, 2, 3, -0.05, 2, 3.033, -0.048, 2, 3.067, -0.046, 2, 3.1, -0.044, 2, 3.133, -0.041, 2, 3.167, -0.039, 2, 3.2, -0.037, 2, 3.233, -0.035, 2, 3.267, -0.034, 2, 3.3, -0.033, 2, 3.367, -0.033, 2, 3.4, -0.034, 2, 3.433, -0.035, 2, 3.467, -0.036, 2, 3.5, -0.037, 2, 3.533, -0.038, 2, 3.567, -0.039, 2, 3.6, -0.04, 2, 3.633, -0.041, 2, 3.667, -0.042, 2, 3.7, -0.042, 2, 3.733, -0.042, 2, 3.767, -0.042, 2, 3.8, -0.041, 2, 3.833, -0.041, 2, 3.867, -0.04, 2, 3.9, -0.04, 2, 3.933, -0.039, 2, 3.967, -0.038, 2, 4, -0.038, 2, 4.033, -0.038, 2, 4.067, -0.038, 2, 4.133, -0.038, 2, 4.167, -0.038, 2, 4.2, -0.038, 2, 4.233, -0.038, 2, 4.267, -0.039, 2, 4.3, -0.039, 2, 4.333, -0.039, 2, 4.367, -0.04, 2, 4.4, -0.04, 2, 4.433, -0.04, 2, 4.5, -0.04, 2, 4.533, -0.04, 2, 4.567, -0.04, 2, 4.6, -0.039, 2, 4.633, -0.039, 2, 4.667, -0.039, 2, 4.7, -0.039, 2, 4.733, -0.039, 2, 4.767, -0.039, 2, 4.8, -0.039, 2, 4.833, -0.039, 2, 4.933, -0.039, 2, 5, -0.039, 2, 5.033, -0.039, 2, 5.1, -0.039, 2, 5.4, -0.039, 2, 5.433, -0.039, 2, 5.467, -0.039, 2, 5.5, -0.039, 2, 5.533, -0.039, 2, 5.567, -0.039, 2, 5.733, -0.039, 2, 5.767, -0.039, 2, 5.8, -0.039, 2, 5.833, -0.039, 2, 5.867, -0.039, 2, 5.967, -0.039, 2, 6, -0.039, 2, 6.133, -0.039, 2, 6.233, -0.039, 2, 6.267, -0.039, 2, 6.3, -0.039, 2, 6.367, -0.039, 2, 6.433, -0.039, 2, 6.533, -0.039, 2, 6.667, -0.039, 2, 6.7, -0.039, 2, 6.733, -0.039, 2, 6.767, -0.039, 2, 7, -0.039, 2, 7.033, -0.039, 2, 7.1, -0.039, 2, 7.233, -0.039, 2, 7.267, -0.039, 2, 7.333, -0.039, 2, 7.4, -0.039, 2, 7.633, -0.039, 2, 7.667, -0.039, 2, 7.733, -0.039, 2, 7.767, -0.039, 2, 7.8, -0.039, 2, 7.833, -0.039, 2, 7.867, -0.039, 2, 8.033, -0.039, 2, 8.067, -0.039, 2, 8.133, -0.039, 2, 8.267, -0.039, 2, 8.3, -0.039, 2, 8.333, -0.039, 2, 8.367, -0.039, 2, 8.4, -0.039, 2, 8.433, -0.039, 2, 8.6, -0.039, 2, 8.633, -0.039, 2, 8.667, -0.039, 2, 8.733, -0.039, 2, 8.833, -0.039, 2, 8.867, -0.039, 2, 8.933, -0.039, 2, 8.967, -0.039, 2, 9, -0.039, 2, 9.033, -0.039, 2, 9.067, -0.039, 2, 9.1, -0.039, 2, 9.2, -0.039, 2, 9.233, -0.039, 2, 9.533, -0.039, 2, 9.6, -0.039, 2, 9.733, -0.039, 2, 9.767, -0.039, 2, 9.833, -0.039, 2, 9.867, -0.039, 2, 10, -0.039]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh0", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh0", "Segments": [0, 0, 2, 0.5, 0.061, 2, 0.533, 0.208, 2, 0.567, 0.387, 2, 0.6, 0.534, 2, 0.633, 0.596, 2, 0.667, 0.506, 2, 0.7, 0.264, 2, 0.733, -0.101, 2, 0.767, -0.537, 2, 0.8, -1.01, 2, 0.833, -1.484, 2, 0.867, -1.919, 2, 0.9, -2.284, 2, 0.933, -2.527, 2, 0.967, -2.616, 2, 1, -2.569, 2, 1.033, -2.436, 2, 1.067, -2.227, 2, 1.1, -1.962, 2, 1.133, -1.645, 2, 1.167, -1.3, 2, 1.2, -0.936, 2, 1.233, -0.564, 2, 1.267, -0.2, 2, 1.3, 0.145, 2, 1.333, 0.462, 2, 1.367, 0.727, 2, 1.4, 0.936, 2, 1.433, 1.069, 2, 1.467, 1.116, 2, 1.5, 1.034, 2, 1.533, 0.815, 2, 1.567, 0.498, 2, 1.6, 0.122, 2, 1.633, -0.267, 2, 1.667, -0.643, 2, 1.7, -0.96, 2, 1.733, -1.18, 2, 1.767, -1.261, 2, 1.8, -1.222, 2, 1.833, -1.117, 2, 1.867, -0.969, 2, 1.9, -0.799, 2, 1.933, -0.629, 2, 1.967, -0.481, 2, 2, -0.376, 2, 2.033, -0.336, 2, 2.067, -0.345, 2, 2.1, -0.37, 2, 2.133, -0.404, 2, 2.167, -0.444, 2, 2.2, -0.483, 2, 2.233, -0.518, 2, 2.267, -0.542, 2, 2.3, -0.552, 2, 2.333, -0.542, 2, 2.367, -0.515, 2, 2.4, -0.473, 2, 2.433, -0.421, 2, 2.467, -0.358, 2, 2.5, -0.291, 2, 2.533, -0.221, 2, 2.567, -0.15, 2, 2.6, -0.083, 2, 2.633, -0.021, 2, 2.667, 0.032, 2, 2.7, 0.074, 2, 2.733, 0.1, 2, 2.767, 0.11, 2, 2.8, 0.088, 2, 2.833, 0.029, 2, 2.867, -0.061, 2, 2.9, -0.167, 2, 2.933, -0.283, 2, 2.967, -0.399, 2, 3, -0.506, 2, 3.033, -0.595, 2, 3.067, -0.655, 2, 3.1, -0.677, 2, 3.133, -0.663, 2, 3.167, -0.626, 2, 3.2, -0.575, 2, 3.233, -0.516, 2, 3.267, -0.457, 2, 3.3, -0.405, 2, 3.333, -0.369, 2, 3.367, -0.355, 2, 3.4, -0.358, 2, 3.433, -0.367, 2, 3.467, -0.379, 2, 3.5, -0.394, 2, 3.533, -0.411, 2, 3.567, -0.427, 2, 3.6, -0.442, 2, 3.633, -0.455, 2, 3.667, -0.464, 2, 3.7, -0.467, 2, 3.733, -0.466, 2, 3.767, -0.464, 2, 3.8, -0.461, 2, 3.833, -0.458, 2, 3.867, -0.455, 2, 3.9, -0.454, 2, 3.967, -0.455, 2, 4, -0.456, 2, 4.033, -0.459, 2, 4.067, -0.462, 2, 4.1, -0.466, 2, 4.133, -0.471, 2, 4.167, -0.477, 2, 4.2, -0.484, 2, 4.233, -0.491, 2, 4.267, -0.499, 2, 4.3, -0.508, 2, 4.333, -0.517, 2, 4.367, -0.527, 2, 4.4, -0.538, 2, 4.433, -0.549, 2, 4.467, -0.56, 2, 4.5, -0.573, 2, 4.533, -0.585, 2, 4.567, -0.598, 2, 4.6, -0.611, 2, 4.633, -0.625, 2, 4.667, -0.639, 2, 4.7, -0.654, 2, 4.733, -0.668, 2, 4.767, -0.683, 2, 4.8, -0.698, 2, 4.833, -0.713, 2, 4.867, -0.729, 2, 4.9, -0.744, 2, 4.933, -0.76, 2, 4.967, -0.775, 2, 5, -0.79, 2, 5.033, -0.806, 2, 5.067, -0.821, 2, 5.1, -0.837, 2, 5.133, -0.852, 2, 5.167, -0.867, 2, 5.2, -0.882, 2, 5.233, -0.896, 2, 5.267, -0.911, 2, 5.3, -0.925, 2, 5.333, -0.939, 2, 5.367, -0.952, 2, 5.4, -0.965, 2, 5.433, -0.977, 2, 5.467, -0.99, 2, 5.5, -1.001, 2, 5.533, -1.012, 2, 5.567, -1.023, 2, 5.6, -1.033, 2, 5.633, -1.042, 2, 5.667, -1.051, 2, 5.7, -1.059, 2, 5.733, -1.066, 2, 5.767, -1.073, 2, 5.8, -1.079, 2, 5.833, -1.084, 2, 5.867, -1.088, 2, 5.9, -1.091, 2, 5.933, -1.094, 2, 5.967, -1.095, 2, 6, -1.096, 2, 6.033, -1.06, 2, 6.067, -0.964, 2, 6.1, -0.825, 2, 6.133, -0.66, 2, 6.167, -0.49, 2, 6.2, -0.325, 2, 6.233, -0.186, 2, 6.267, -0.09, 2, 6.3, -0.054, 2, 6.333, -0.072, 2, 6.367, -0.121, 2, 6.4, -0.191, 2, 6.433, -0.275, 2, 6.467, -0.361, 2, 6.5, -0.445, 2, 6.533, -0.516, 2, 6.567, -0.564, 2, 6.6, -0.583, 2, 6.633, -0.578, 2, 6.667, -0.565, 2, 6.7, -0.546, 2, 6.733, -0.524, 2, 6.767, -0.501, 2, 6.8, -0.478, 2, 6.833, -0.46, 2, 6.867, -0.447, 2, 6.9, -0.442, 2, 6.933, -0.443, 2, 6.967, -0.448, 2, 7, -0.454, 2, 7.033, -0.46, 2, 7.067, -0.467, 2, 7.1, -0.473, 2, 7.133, -0.477, 2, 7.167, -0.479, 2, 7.233, -0.479, 2, 7.267, -0.478, 2, 7.3, -0.476, 2, 7.333, -0.474, 2, 7.367, -0.472, 2, 7.4, -0.471, 2, 7.433, -0.47, 2, 7.467, -0.469, 2, 7.5, -0.469, 2, 7.533, -0.469, 2, 7.567, -0.47, 2, 7.6, -0.47, 2, 7.633, -0.471, 2, 7.667, -0.471, 2, 7.7, -0.471, 2, 7.733, -0.472, 2, 7.767, -0.472, 2, 7.833, -0.472, 2, 7.867, -0.472, 2, 7.9, -0.471, 2, 7.933, -0.471, 2, 7.967, -0.471, 2, 8, -0.471, 2, 8.033, -0.471, 2, 8.067, -0.471, 2, 8.1, -0.471, 2, 8.133, -0.471, 2, 8.167, -0.471, 2, 8.267, -0.471, 2, 8.433, -0.471, 2, 8.5, -0.471, 2, 8.533, -0.471, 2, 8.633, -0.471, 2, 8.667, -0.471, 2, 8.7, -0.471, 2, 8.733, -0.471, 2, 8.9, -0.471, 2, 8.933, -0.471, 2, 8.967, -0.471, 2, 9, -0.471, 2, 9.133, -0.471, 2, 9.167, -0.471, 2, 9.233, -0.471, 2, 9.367, -0.471, 2, 9.4, -0.471, 2, 9.833, -0.471, 2, 9.867, -0.471, 2, 9.933, -0.471, 2, 9.967, -0.471, 2, 10, -0.471]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh0", "Segments": [0, 0, 2, 0.067, 0, 2, 0.5, -0.051, 2, 0.533, -0.164, 2, 0.567, -0.277, 2, 0.6, -0.328, 2, 0.633, -0.243, 2, 0.667, -0.021, 2, 0.7, 0.286, 2, 0.733, 0.611, 2, 0.767, 0.918, 2, 0.8, 1.139, 2, 0.833, 1.225, 2, 0.867, 1.146, 2, 0.9, 0.935, 2, 0.933, 0.63, 2, 0.967, 0.269, 2, 1, -0.105, 2, 1.033, -0.466, 2, 1.067, -0.772, 2, 1.1, -0.982, 2, 1.133, -1.061, 2, 1.167, -1.033, 2, 1.2, -0.953, 2, 1.233, -0.829, 2, 1.267, -0.67, 2, 1.3, -0.481, 2, 1.333, -0.274, 2, 1.367, -0.056, 2, 1.4, 0.166, 2, 1.433, 0.383, 2, 1.467, 0.59, 2, 1.5, 0.779, 2, 1.533, 0.938, 2, 1.567, 1.062, 2, 1.6, 1.142, 2, 1.633, 1.17, 2, 1.667, 1.079, 2, 1.7, 0.84, 2, 1.733, 0.501, 2, 1.767, 0.113, 2, 1.8, -0.275, 2, 1.833, -0.614, 2, 1.867, -0.853, 2, 1.9, -0.944, 2, 1.933, -0.885, 2, 1.967, -0.729, 2, 2, -0.509, 2, 2.033, -0.256, 2, 2.067, -0.003, 2, 2.1, 0.217, 2, 2.133, 0.373, 2, 2.167, 0.432, 2, 2.2, 0.411, 2, 2.233, 0.354, 2, 2.267, 0.273, 2, 2.3, 0.176, 2, 2.333, 0.076, 2, 2.367, -0.021, 2, 2.4, -0.103, 2, 2.433, -0.159, 2, 2.467, -0.18, 2, 2.5, -0.167, 2, 2.533, -0.143, 2, 2.567, -0.129, 2, 2.6, -0.144, 2, 2.633, -0.171, 2, 2.667, -0.186, 2, 2.7, -0.168, 2, 2.733, -0.119, 2, 2.767, -0.048, 2, 2.8, 0.036, 2, 2.833, 0.122, 2, 2.867, 0.206, 2, 2.9, 0.276, 2, 2.933, 0.325, 2, 2.967, 0.344, 2, 3, 0.315, 2, 3.033, 0.242, 2, 3.067, 0.137, 2, 3.1, 0.017, 2, 3.133, -0.102, 2, 3.167, -0.207, 2, 3.2, -0.281, 2, 3.233, -0.309, 2, 3.267, -0.293, 2, 3.3, -0.251, 2, 3.333, -0.189, 2, 3.367, -0.116, 2, 3.4, -0.041, 2, 3.433, 0.032, 2, 3.467, 0.093, 2, 3.5, 0.135, 2, 3.533, 0.151, 2, 3.567, 0.142, 2, 3.6, 0.119, 2, 3.633, 0.085, 2, 3.667, 0.047, 2, 3.7, 0.009, 2, 3.733, -0.025, 2, 3.767, -0.048, 2, 3.8, -0.057, 2, 3.833, -0.055, 2, 3.867, -0.047, 2, 3.9, -0.037, 2, 3.933, -0.024, 2, 3.967, -0.011, 2, 4, 0.001, 2, 4.033, 0.012, 2, 4.067, 0.019, 2, 4.1, 0.022, 2, 4.133, 0.021, 2, 4.167, 0.018, 2, 4.2, 0.014, 2, 4.233, 0.01, 2, 4.267, 0.005, 2, 4.3, 0.001, 2, 4.333, -0.003, 2, 4.367, -0.005, 2, 4.4, -0.006, 2, 4.433, -0.006, 2, 4.467, -0.005, 2, 4.5, -0.004, 2, 4.533, -0.002, 2, 4.567, -0.001, 2, 4.6, 0.001, 2, 4.633, 0.003, 2, 4.667, 0.004, 2, 4.7, 0.005, 2, 4.733, 0.005, 2, 4.767, 0.005, 2, 4.8, 0.005, 2, 4.833, 0.004, 2, 4.867, 0.003, 2, 4.9, 0.003, 2, 4.933, 0.002, 2, 4.967, 0.002, 2, 5, 0.002, 2, 5.033, 0.002, 2, 5.067, 0.002, 2, 5.1, 0.002, 2, 5.133, 0.003, 2, 5.167, 0.003, 2, 5.2, 0.004, 2, 5.233, 0.004, 2, 5.3, 0.004, 2, 5.333, 0.004, 2, 5.367, 0.004, 2, 5.4, 0.004, 2, 5.433, 0.004, 2, 5.467, 0.004, 2, 5.5, 0.004, 2, 5.533, 0.003, 2, 5.567, 0.004, 2, 5.6, 0.004, 2, 5.633, 0.004, 2, 5.667, 0.004, 2, 5.7, 0.004, 2, 5.733, 0.005, 2, 5.767, 0.005, 2, 5.8, 0.004, 2, 5.833, 0.04, 2, 5.867, 0.118, 2, 5.9, 0.197, 2, 5.933, 0.232, 2, 5.967, 0.2, 2, 6, 0.115, 2, 6.033, -0.005, 2, 6.067, -0.143, 2, 6.1, -0.28, 2, 6.133, -0.4, 2, 6.167, -0.485, 2, 6.2, -0.517, 2, 6.233, -0.476, 2, 6.267, -0.367, 2, 6.3, -0.212, 2, 6.333, -0.035, 2, 6.367, 0.142, 2, 6.4, 0.296, 2, 6.433, 0.405, 2, 6.467, 0.447, 2, 6.5, 0.418, 2, 6.533, 0.342, 2, 6.567, 0.235, 2, 6.6, 0.112, 2, 6.633, -0.011, 2, 6.667, -0.118, 2, 6.7, -0.194, 2, 6.733, -0.222, 2, 6.767, -0.212, 2, 6.8, -0.183, 2, 6.833, -0.142, 2, 6.867, -0.093, 2, 6.9, -0.043, 2, 6.933, 0.006, 2, 6.967, 0.047, 2, 7, 0.076, 2, 7.033, 0.086, 2, 7.067, 0.081, 2, 7.1, 0.068, 2, 7.133, 0.049, 2, 7.167, 0.028, 2, 7.2, 0.006, 2, 7.233, -0.013, 2, 7.267, -0.026, 2, 7.3, -0.031, 2, 7.333, -0.029, 2, 7.367, -0.026, 2, 7.4, -0.02, 2, 7.433, -0.014, 2, 7.467, -0.007, 2, 7.5, 0, 2, 7.533, 0.005, 2, 7.567, 0.009, 2, 7.6, 0.01, 2, 7.633, 0.01, 2, 7.667, 0.009, 2, 7.7, 0.007, 2, 7.733, 0.005, 2, 7.767, 0.002, 2, 7.8, 0, 2, 7.833, -0.002, 2, 7.867, -0.003, 2, 7.9, -0.003, 2, 7.933, -0.003, 2, 7.967, -0.003, 2, 8, -0.002, 2, 8.033, -0.001, 2, 8.067, 0, 2, 8.1, 0, 2, 8.133, 0.001, 2, 8.167, 0.001, 2, 8.233, 0.001, 2, 8.267, 0.001, 2, 8.3, 0, 2, 8.333, 0, 2, 8.367, 0, 2, 8.4, 0, 2, 8.433, 0, 2, 8.467, 0, 2, 8.5, 0, 2, 8.567, 0, 2, 8.6, 0, 2, 8.667, 0, 2, 8.733, 0, 2, 8.8, 0, 2, 8.833, 0, 2, 8.9, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh0", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0, 2, 0.1, 0, 2, 0.133, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 0.233, 0, 2, 0.267, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.367, 0, 2, 0.5, -0.018, 2, 0.533, -0.064, 2, 0.567, -0.124, 2, 0.6, -0.184, 2, 0.633, -0.229, 2, 0.667, -0.247, 2, 0.7, -0.183, 2, 0.733, -0.012, 2, 0.767, 0.229, 2, 0.8, 0.506, 2, 0.833, 0.782, 2, 0.867, 1.023, 2, 0.9, 1.194, 2, 0.933, 1.259, 2, 0.967, 1.163, 2, 1, 0.909, 2, 1.033, 0.54, 2, 1.067, 0.103, 2, 1.1, -0.349, 2, 1.133, -0.786, 2, 1.167, -1.154, 2, 1.2, -1.409, 2, 1.233, -1.504, 2, 1.267, -1.463, 2, 1.3, -1.348, 2, 1.333, -1.169, 2, 1.367, -0.946, 2, 1.4, -0.68, 2, 1.433, -0.392, 2, 1.467, -0.092, 2, 1.5, 0.208, 2, 1.533, 0.495, 2, 1.567, 0.762, 2, 1.6, 0.985, 2, 1.633, 1.164, 2, 1.667, 1.279, 2, 1.7, 1.32, 2, 1.733, 1.23, 2, 1.767, 0.99, 2, 1.8, 0.643, 2, 1.833, 0.232, 2, 1.867, -0.193, 2, 1.9, -0.605, 2, 1.933, -0.952, 2, 1.967, -1.192, 2, 2, -1.281, 2, 2.033, -1.211, 2, 2.067, -1.023, 2, 2.1, -0.751, 2, 2.133, -0.428, 2, 2.167, -0.095, 2, 2.2, 0.228, 2, 2.233, 0.5, 2, 2.267, 0.688, 2, 2.3, 0.758, 2, 2.333, 0.718, 2, 2.367, 0.612, 2, 2.4, 0.459, 2, 2.433, 0.277, 2, 2.467, 0.089, 2, 2.5, -0.093, 2, 2.533, -0.247, 2, 2.567, -0.353, 2, 2.6, -0.393, 2, 2.633, -0.379, 2, 2.667, -0.341, 2, 2.7, -0.283, 2, 2.733, -0.209, 2, 2.767, -0.124, 2, 2.8, -0.034, 2, 2.833, 0.061, 2, 2.867, 0.152, 2, 2.9, 0.237, 2, 2.933, 0.31, 2, 2.967, 0.368, 2, 3, 0.406, 2, 3.033, 0.42, 2, 3.067, 0.391, 2, 3.1, 0.314, 2, 3.133, 0.203, 2, 3.167, 0.071, 2, 3.2, -0.065, 2, 3.233, -0.197, 2, 3.267, -0.308, 2, 3.3, -0.385, 2, 3.333, -0.413, 2, 3.367, -0.39, 2, 3.4, -0.328, 2, 3.433, -0.238, 2, 3.467, -0.131, 2, 3.5, -0.021, 2, 3.533, 0.086, 2, 3.567, 0.176, 2, 3.6, 0.239, 2, 3.633, 0.262, 2, 3.667, 0.249, 2, 3.7, 0.213, 2, 3.733, 0.161, 2, 3.767, 0.1, 2, 3.8, 0.037, 2, 3.833, -0.025, 2, 3.867, -0.076, 2, 3.9, -0.112, 2, 3.933, -0.125, 2, 3.967, -0.118, 2, 4, -0.097, 2, 4.033, -0.068, 2, 4.067, -0.035, 2, 4.1, -0.002, 2, 4.133, 0.026, 2, 4.167, 0.047, 2, 4.2, 0.055, 2, 4.233, 0.052, 2, 4.267, 0.045, 2, 4.3, 0.035, 2, 4.333, 0.023, 2, 4.367, 0.011, 2, 4.4, -0.001, 2, 4.433, -0.011, 2, 4.467, -0.018, 2, 4.5, -0.021, 2, 4.533, -0.02, 2, 4.567, -0.017, 2, 4.6, -0.012, 2, 4.633, -0.007, 2, 4.667, -0.002, 2, 4.7, 0.003, 2, 4.733, 0.007, 2, 4.767, 0.01, 2, 4.8, 0.011, 2, 4.833, 0.011, 2, 4.867, 0.01, 2, 4.9, 0.008, 2, 4.933, 0.006, 2, 4.967, 0.004, 2, 5, 0.002, 2, 5.033, 0.001, 2, 5.067, 0, 2, 5.1, -0.001, 2, 5.133, -0.001, 2, 5.167, 0, 2, 5.2, 0.001, 2, 5.233, 0.001, 2, 5.267, 0.002, 2, 5.3, 0.003, 2, 5.333, 0.004, 2, 5.367, 0.004, 2, 5.4, 0.005, 2, 5.433, 0.005, 2, 5.467, 0.005, 2, 5.5, 0.005, 2, 5.533, 0.004, 2, 5.567, 0.004, 2, 5.6, 0.004, 2, 5.633, 0.004, 2, 5.667, 0.004, 2, 5.733, 0.009, 2, 5.767, 0.024, 2, 5.8, 0.047, 2, 5.833, 0.074, 2, 5.867, 0.104, 2, 5.9, 0.133, 2, 5.933, 0.161, 2, 5.967, 0.183, 2, 6, 0.199, 2, 6.033, 0.204, 2, 6.067, 0.172, 2, 6.1, 0.086, 2, 6.133, -0.035, 2, 6.167, -0.174, 2, 6.2, -0.314, 2, 6.233, -0.435, 2, 6.267, -0.521, 2, 6.3, -0.553, 2, 6.333, -0.505, 2, 6.367, -0.377, 2, 6.4, -0.196, 2, 6.433, 0.012, 2, 6.467, 0.219, 2, 6.5, 0.4, 2, 6.533, 0.528, 2, 6.567, 0.577, 2, 6.6, 0.536, 2, 6.633, 0.43, 2, 6.667, 0.279, 2, 6.7, 0.106, 2, 6.733, -0.066, 2, 6.767, -0.217, 2, 6.8, -0.324, 2, 6.833, -0.364, 2, 6.867, -0.345, 2, 6.9, -0.295, 2, 6.933, -0.222, 2, 6.967, -0.135, 2, 7, -0.045, 2, 7.033, 0.041, 2, 7.067, 0.115, 2, 7.1, 0.165, 2, 7.133, 0.184, 2, 7.167, 0.175, 2, 7.2, 0.151, 2, 7.233, 0.115, 2, 7.267, 0.074, 2, 7.3, 0.031, 2, 7.333, -0.011, 2, 7.367, -0.046, 2, 7.4, -0.07, 2, 7.433, -0.079, 2, 7.467, -0.076, 2, 7.5, -0.065, 2, 7.533, -0.051, 2, 7.567, -0.033, 2, 7.6, -0.015, 2, 7.633, 0.002, 2, 7.667, 0.017, 2, 7.7, 0.027, 2, 7.733, 0.031, 2, 7.767, 0.029, 2, 7.8, 0.024, 2, 7.833, 0.018, 2, 7.867, 0.01, 2, 7.9, 0.002, 2, 7.933, -0.005, 2, 7.967, -0.01, 2, 8, -0.011, 2, 8.033, -0.011, 2, 8.067, -0.01, 2, 8.1, -0.007, 2, 8.133, -0.005, 2, 8.167, -0.002, 2, 8.2, 0, 2, 8.233, 0.002, 2, 8.267, 0.004, 2, 8.3, 0.004, 2, 8.333, 0.004, 2, 8.367, 0.003, 2, 8.4, 0.003, 2, 8.433, 0.002, 2, 8.467, 0.001, 2, 8.5, 0, 2, 8.533, -0.001, 2, 8.567, -0.001, 2, 8.6, -0.001, 2, 8.633, -0.001, 2, 8.667, -0.001, 2, 8.7, -0.001, 2, 8.733, -0.001, 2, 8.767, 0, 2, 8.8, 0, 2, 8.833, 0, 2, 8.867, 0, 2, 8.9, 0, 2, 8.933, 0, 2, 8.967, 0, 2, 9, 0, 2, 9.033, 0, 2, 9.067, 0, 2, 9.1, 0, 2, 9.133, 0, 2, 9.167, 0, 2, 9.2, 0, 2, 9.233, 0, 2, 9.267, 0, 2, 9.3, 0, 2, 9.333, 0, 2, 9.467, 0, 2, 9.533, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh0", "Segments": [0, 0.003, 2, 0.033, 0.003, 2, 0.067, 0.002, 2, 0.1, 0.001, 2, 0.133, -0.001, 2, 0.167, -0.001, 2, 0.2, -0.002, 2, 0.233, -0.002, 2, 0.267, -0.001, 2, 0.3, -0.001, 2, 0.333, -0.001, 2, 0.367, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.467, 0.001, 2, 0.5, -0.008, 2, 0.533, -0.032, 2, 0.567, -0.065, 2, 0.6, -0.103, 2, 0.633, -0.142, 2, 0.667, -0.175, 2, 0.7, -0.198, 2, 0.733, -0.207, 2, 0.767, -0.141, 2, 0.8, 0.035, 2, 0.833, 0.283, 2, 0.867, 0.568, 2, 0.9, 0.852, 2, 0.933, 1.1, 2, 0.967, 1.276, 2, 1, 1.343, 2, 1.033, 1.25, 2, 1.067, 1, 2, 1.1, 0.622, 2, 1.133, 0.172, 2, 1.167, -0.317, 2, 1.2, -0.807, 2, 1.233, -1.257, 2, 1.267, -1.634, 2, 1.3, -1.885, 2, 1.333, -1.977, 2, 1.367, -1.904, 2, 1.4, -1.71, 2, 1.433, -1.41, 2, 1.467, -1.034, 2, 1.5, -0.616, 2, 1.533, -0.164, 2, 1.567, 0.289, 2, 1.6, 0.706, 2, 1.633, 1.082, 2, 1.667, 1.382, 2, 1.7, 1.577, 2, 1.733, 1.649, 2, 1.767, 1.57, 2, 1.8, 1.358, 2, 1.833, 1.037, 2, 1.867, 0.647, 2, 1.9, 0.204, 2, 1.933, -0.245, 2, 1.967, -0.688, 2, 2, -1.078, 2, 2.033, -1.4, 2, 2.067, -1.611, 2, 2.1, -1.69, 2, 2.133, -1.591, 2, 2.167, -1.323, 2, 2.2, -0.937, 2, 2.233, -0.478, 2, 2.267, -0.005, 2, 2.3, 0.454, 2, 2.333, 0.841, 2, 2.367, 1.108, 2, 2.4, 1.208, 2, 2.433, 1.14, 2, 2.467, 0.96, 2, 2.5, 0.698, 2, 2.533, 0.389, 2, 2.567, 0.069, 2, 2.6, -0.241, 2, 2.633, -0.502, 2, 2.667, -0.683, 2, 2.7, -0.75, 2, 2.733, -0.724, 2, 2.767, -0.654, 2, 2.8, -0.546, 2, 2.833, -0.41, 2, 2.867, -0.259, 2, 2.9, -0.096, 2, 2.933, 0.068, 2, 2.967, 0.218, 2, 3, 0.354, 2, 3.033, 0.462, 2, 3.067, 0.533, 2, 3.1, 0.559, 2, 3.133, 0.528, 2, 3.167, 0.443, 2, 3.2, 0.316, 2, 3.233, 0.164, 2, 3.267, -0.001, 2, 3.3, -0.166, 2, 3.333, -0.317, 2, 3.367, -0.444, 2, 3.4, -0.529, 2, 3.433, -0.56, 2, 3.467, -0.526, 2, 3.5, -0.437, 2, 3.533, -0.307, 2, 3.567, -0.153, 2, 3.6, 0.006, 2, 3.633, 0.159, 2, 3.667, 0.289, 2, 3.7, 0.379, 2, 3.733, 0.412, 2, 3.767, 0.39, 2, 3.8, 0.33, 2, 3.833, 0.244, 2, 3.867, 0.142, 2, 3.9, 0.036, 2, 3.933, -0.067, 2, 3.967, -0.153, 2, 4, -0.213, 2, 4.033, -0.235, 2, 4.067, -0.223, 2, 4.1, -0.19, 2, 4.133, -0.143, 2, 4.167, -0.088, 2, 4.2, -0.03, 2, 4.233, 0.026, 2, 4.267, 0.073, 2, 4.3, 0.105, 2, 4.333, 0.117, 2, 4.367, 0.112, 2, 4.4, 0.096, 2, 4.433, 0.074, 2, 4.467, 0.047, 2, 4.5, 0.019, 2, 4.533, -0.007, 2, 4.567, -0.03, 2, 4.6, -0.045, 2, 4.633, -0.051, 2, 4.667, -0.048, 2, 4.7, -0.039, 2, 4.733, -0.027, 2, 4.767, -0.013, 2, 4.8, 0.001, 2, 4.833, 0.014, 2, 4.867, 0.022, 2, 4.9, 0.026, 2, 4.967, 0.024, 2, 5, 0.021, 2, 5.033, 0.015, 2, 5.067, 0.009, 2, 5.1, 0.004, 2, 5.133, -0.002, 2, 5.167, -0.005, 2, 5.2, -0.007, 2, 5.233, -0.006, 2, 5.267, -0.005, 2, 5.3, -0.003, 2, 5.333, -0.001, 2, 5.367, 0.002, 2, 5.4, 0.004, 2, 5.433, 0.006, 2, 5.467, 0.007, 2, 5.5, 0.008, 2, 5.567, 0.007, 2, 5.6, 0.007, 2, 5.633, 0.006, 2, 5.667, 0.005, 2, 5.7, 0.004, 2, 5.733, 0.003, 2, 5.767, 0.003, 2, 5.8, 0.003, 2, 5.833, 0.009, 2, 5.867, 0.027, 2, 5.9, 0.052, 2, 5.933, 0.082, 2, 5.967, 0.114, 2, 6, 0.144, 2, 6.033, 0.169, 2, 6.067, 0.187, 2, 6.1, 0.193, 2, 6.133, 0.166, 2, 6.167, 0.094, 2, 6.2, -0.012, 2, 6.233, -0.136, 2, 6.267, -0.265, 2, 6.3, -0.389, 2, 6.333, -0.494, 2, 6.367, -0.567, 2, 6.4, -0.594, 2, 6.433, -0.537, 2, 6.467, -0.387, 2, 6.5, -0.175, 2, 6.533, 0.069, 2, 6.567, 0.312, 2, 6.6, 0.524, 2, 6.633, 0.675, 2, 6.667, 0.732, 2, 6.7, 0.687, 2, 6.733, 0.568, 2, 6.767, 0.396, 2, 6.8, 0.192, 2, 6.833, -0.019, 2, 6.867, -0.223, 2, 6.9, -0.395, 2, 6.933, -0.514, 2, 6.967, -0.558, 2, 7, -0.52, 2, 7.033, -0.42, 2, 7.067, -0.278, 2, 7.1, -0.115, 2, 7.133, 0.048, 2, 7.167, 0.19, 2, 7.2, 0.29, 2, 7.233, 0.328, 2, 7.267, 0.311, 2, 7.3, 0.266, 2, 7.333, 0.2, 2, 7.367, 0.121, 2, 7.4, 0.04, 2, 7.433, -0.038, 2, 7.467, -0.104, 2, 7.5, -0.15, 2, 7.533, -0.167, 2, 7.567, -0.159, 2, 7.6, -0.136, 2, 7.633, -0.104, 2, 7.667, -0.065, 2, 7.7, -0.026, 2, 7.733, 0.013, 2, 7.767, 0.045, 2, 7.8, 0.067, 2, 7.833, 0.076, 2, 7.867, 0.072, 2, 7.9, 0.062, 2, 7.933, 0.048, 2, 7.967, 0.031, 2, 8, 0.013, 2, 8.033, -0.004, 2, 8.067, -0.018, 2, 8.1, -0.028, 2, 8.133, -0.032, 2, 8.167, -0.03, 2, 8.2, -0.026, 2, 8.233, -0.02, 2, 8.267, -0.013, 2, 8.3, -0.006, 2, 8.333, 0.001, 2, 8.367, 0.007, 2, 8.4, 0.011, 2, 8.433, 0.012, 2, 8.467, 0.012, 2, 8.5, 0.01, 2, 8.533, 0.007, 2, 8.567, 0.004, 2, 8.6, 0.001, 2, 8.633, -0.002, 2, 8.667, -0.004, 2, 8.7, -0.005, 2, 8.733, -0.004, 2, 8.767, -0.004, 2, 8.8, -0.003, 2, 8.833, -0.002, 2, 8.867, -0.001, 2, 8.9, 0, 2, 8.933, 0.001, 2, 8.967, 0.001, 2, 9, 0.002, 2, 9.067, 0.002, 2, 9.1, 0.001, 2, 9.133, 0.001, 2, 9.167, 0, 2, 9.2, 0, 2, 9.233, 0, 2, 9.267, -0.001, 2, 9.367, -0.001, 2, 9.4, 0, 2, 9.433, 0, 2, 9.467, 0, 2, 9.5, 0, 2, 9.567, 0, 2, 9.7, 0, 2, 9.767, 0, 2, 9.833, 0, 2, 9.9, 0, 2, 9.933, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1", "Segments": [0, 0, 2, 0.5, 0.061, 2, 0.533, 0.208, 2, 0.567, 0.387, 2, 0.6, 0.534, 2, 0.633, 0.596, 2, 0.667, 0.506, 2, 0.7, 0.264, 2, 0.733, -0.101, 2, 0.767, -0.537, 2, 0.8, -1.01, 2, 0.833, -1.484, 2, 0.867, -1.919, 2, 0.9, -2.284, 2, 0.933, -2.527, 2, 0.967, -2.616, 2, 1, -2.569, 2, 1.033, -2.436, 2, 1.067, -2.227, 2, 1.1, -1.962, 2, 1.133, -1.645, 2, 1.167, -1.3, 2, 1.2, -0.936, 2, 1.233, -0.564, 2, 1.267, -0.2, 2, 1.3, 0.145, 2, 1.333, 0.462, 2, 1.367, 0.727, 2, 1.4, 0.936, 2, 1.433, 1.069, 2, 1.467, 1.116, 2, 1.5, 1.034, 2, 1.533, 0.815, 2, 1.567, 0.498, 2, 1.6, 0.122, 2, 1.633, -0.267, 2, 1.667, -0.643, 2, 1.7, -0.96, 2, 1.733, -1.18, 2, 1.767, -1.261, 2, 1.8, -1.222, 2, 1.833, -1.117, 2, 1.867, -0.969, 2, 1.9, -0.799, 2, 1.933, -0.629, 2, 1.967, -0.481, 2, 2, -0.376, 2, 2.033, -0.336, 2, 2.067, -0.345, 2, 2.1, -0.37, 2, 2.133, -0.404, 2, 2.167, -0.444, 2, 2.2, -0.483, 2, 2.233, -0.518, 2, 2.267, -0.542, 2, 2.3, -0.552, 2, 2.333, -0.542, 2, 2.367, -0.515, 2, 2.4, -0.473, 2, 2.433, -0.421, 2, 2.467, -0.358, 2, 2.5, -0.291, 2, 2.533, -0.221, 2, 2.567, -0.15, 2, 2.6, -0.083, 2, 2.633, -0.021, 2, 2.667, 0.032, 2, 2.7, 0.074, 2, 2.733, 0.1, 2, 2.767, 0.11, 2, 2.8, 0.088, 2, 2.833, 0.029, 2, 2.867, -0.061, 2, 2.9, -0.167, 2, 2.933, -0.283, 2, 2.967, -0.399, 2, 3, -0.506, 2, 3.033, -0.595, 2, 3.067, -0.655, 2, 3.1, -0.677, 2, 3.133, -0.663, 2, 3.167, -0.626, 2, 3.2, -0.575, 2, 3.233, -0.516, 2, 3.267, -0.457, 2, 3.3, -0.405, 2, 3.333, -0.369, 2, 3.367, -0.355, 2, 3.4, -0.358, 2, 3.433, -0.367, 2, 3.467, -0.379, 2, 3.5, -0.394, 2, 3.533, -0.411, 2, 3.567, -0.427, 2, 3.6, -0.442, 2, 3.633, -0.455, 2, 3.667, -0.464, 2, 3.7, -0.467, 2, 3.733, -0.466, 2, 3.767, -0.464, 2, 3.8, -0.461, 2, 3.833, -0.458, 2, 3.867, -0.455, 2, 3.9, -0.454, 2, 3.967, -0.455, 2, 4, -0.456, 2, 4.033, -0.459, 2, 4.067, -0.462, 2, 4.1, -0.466, 2, 4.133, -0.471, 2, 4.167, -0.477, 2, 4.2, -0.484, 2, 4.233, -0.491, 2, 4.267, -0.499, 2, 4.3, -0.508, 2, 4.333, -0.517, 2, 4.367, -0.527, 2, 4.4, -0.538, 2, 4.433, -0.549, 2, 4.467, -0.56, 2, 4.5, -0.573, 2, 4.533, -0.585, 2, 4.567, -0.598, 2, 4.6, -0.611, 2, 4.633, -0.625, 2, 4.667, -0.639, 2, 4.7, -0.654, 2, 4.733, -0.668, 2, 4.767, -0.683, 2, 4.8, -0.698, 2, 4.833, -0.713, 2, 4.867, -0.729, 2, 4.9, -0.744, 2, 4.933, -0.76, 2, 4.967, -0.775, 2, 5, -0.79, 2, 5.033, -0.806, 2, 5.067, -0.821, 2, 5.1, -0.837, 2, 5.133, -0.852, 2, 5.167, -0.867, 2, 5.2, -0.882, 2, 5.233, -0.896, 2, 5.267, -0.911, 2, 5.3, -0.925, 2, 5.333, -0.939, 2, 5.367, -0.952, 2, 5.4, -0.965, 2, 5.433, -0.977, 2, 5.467, -0.99, 2, 5.5, -1.001, 2, 5.533, -1.012, 2, 5.567, -1.023, 2, 5.6, -1.033, 2, 5.633, -1.042, 2, 5.667, -1.051, 2, 5.7, -1.059, 2, 5.733, -1.066, 2, 5.767, -1.073, 2, 5.8, -1.079, 2, 5.833, -1.084, 2, 5.867, -1.088, 2, 5.9, -1.091, 2, 5.933, -1.094, 2, 5.967, -1.095, 2, 6, -1.096, 2, 6.033, -1.06, 2, 6.067, -0.964, 2, 6.1, -0.825, 2, 6.133, -0.66, 2, 6.167, -0.49, 2, 6.2, -0.325, 2, 6.233, -0.186, 2, 6.267, -0.09, 2, 6.3, -0.054, 2, 6.333, -0.072, 2, 6.367, -0.121, 2, 6.4, -0.191, 2, 6.433, -0.275, 2, 6.467, -0.361, 2, 6.5, -0.445, 2, 6.533, -0.516, 2, 6.567, -0.564, 2, 6.6, -0.583, 2, 6.633, -0.578, 2, 6.667, -0.565, 2, 6.7, -0.546, 2, 6.733, -0.524, 2, 6.767, -0.501, 2, 6.8, -0.478, 2, 6.833, -0.46, 2, 6.867, -0.447, 2, 6.9, -0.442, 2, 6.933, -0.443, 2, 6.967, -0.448, 2, 7, -0.454, 2, 7.033, -0.46, 2, 7.067, -0.467, 2, 7.1, -0.473, 2, 7.133, -0.477, 2, 7.167, -0.479, 2, 7.233, -0.479, 2, 7.267, -0.478, 2, 7.3, -0.476, 2, 7.333, -0.474, 2, 7.367, -0.472, 2, 7.4, -0.471, 2, 7.433, -0.47, 2, 7.467, -0.469, 2, 7.5, -0.469, 2, 7.533, -0.469, 2, 7.567, -0.47, 2, 7.6, -0.47, 2, 7.633, -0.471, 2, 7.667, -0.471, 2, 7.7, -0.471, 2, 7.733, -0.472, 2, 7.767, -0.472, 2, 7.833, -0.472, 2, 7.867, -0.472, 2, 7.9, -0.471, 2, 7.933, -0.471, 2, 7.967, -0.471, 2, 8, -0.471, 2, 8.033, -0.471, 2, 8.067, -0.471, 2, 8.1, -0.471, 2, 8.133, -0.471, 2, 8.167, -0.471, 2, 8.267, -0.471, 2, 8.433, -0.471, 2, 8.5, -0.471, 2, 8.533, -0.471, 2, 8.633, -0.471, 2, 8.667, -0.471, 2, 8.7, -0.471, 2, 8.733, -0.471, 2, 8.9, -0.471, 2, 8.933, -0.471, 2, 8.967, -0.471, 2, 9, -0.471, 2, 9.133, -0.471, 2, 9.167, -0.471, 2, 9.233, -0.471, 2, 9.367, -0.471, 2, 9.4, -0.471, 2, 9.833, -0.471, 2, 9.867, -0.471, 2, 9.933, -0.471, 2, 9.967, -0.471, 2, 10, -0.471]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1", "Segments": [0, 0, 2, 0.067, 0, 2, 0.5, -0.051, 2, 0.533, -0.164, 2, 0.567, -0.277, 2, 0.6, -0.328, 2, 0.633, -0.243, 2, 0.667, -0.021, 2, 0.7, 0.286, 2, 0.733, 0.611, 2, 0.767, 0.918, 2, 0.8, 1.139, 2, 0.833, 1.225, 2, 0.867, 1.146, 2, 0.9, 0.935, 2, 0.933, 0.63, 2, 0.967, 0.269, 2, 1, -0.105, 2, 1.033, -0.466, 2, 1.067, -0.772, 2, 1.1, -0.982, 2, 1.133, -1.061, 2, 1.167, -1.033, 2, 1.2, -0.953, 2, 1.233, -0.829, 2, 1.267, -0.67, 2, 1.3, -0.481, 2, 1.333, -0.274, 2, 1.367, -0.056, 2, 1.4, 0.166, 2, 1.433, 0.383, 2, 1.467, 0.59, 2, 1.5, 0.779, 2, 1.533, 0.938, 2, 1.567, 1.062, 2, 1.6, 1.142, 2, 1.633, 1.17, 2, 1.667, 1.079, 2, 1.7, 0.84, 2, 1.733, 0.501, 2, 1.767, 0.113, 2, 1.8, -0.275, 2, 1.833, -0.614, 2, 1.867, -0.853, 2, 1.9, -0.944, 2, 1.933, -0.885, 2, 1.967, -0.729, 2, 2, -0.509, 2, 2.033, -0.256, 2, 2.067, -0.003, 2, 2.1, 0.217, 2, 2.133, 0.373, 2, 2.167, 0.432, 2, 2.2, 0.411, 2, 2.233, 0.354, 2, 2.267, 0.273, 2, 2.3, 0.176, 2, 2.333, 0.076, 2, 2.367, -0.021, 2, 2.4, -0.103, 2, 2.433, -0.159, 2, 2.467, -0.18, 2, 2.5, -0.167, 2, 2.533, -0.143, 2, 2.567, -0.129, 2, 2.6, -0.144, 2, 2.633, -0.171, 2, 2.667, -0.186, 2, 2.7, -0.168, 2, 2.733, -0.119, 2, 2.767, -0.048, 2, 2.8, 0.036, 2, 2.833, 0.122, 2, 2.867, 0.206, 2, 2.9, 0.276, 2, 2.933, 0.325, 2, 2.967, 0.344, 2, 3, 0.315, 2, 3.033, 0.242, 2, 3.067, 0.137, 2, 3.1, 0.017, 2, 3.133, -0.102, 2, 3.167, -0.207, 2, 3.2, -0.281, 2, 3.233, -0.309, 2, 3.267, -0.293, 2, 3.3, -0.251, 2, 3.333, -0.189, 2, 3.367, -0.116, 2, 3.4, -0.041, 2, 3.433, 0.032, 2, 3.467, 0.093, 2, 3.5, 0.135, 2, 3.533, 0.151, 2, 3.567, 0.142, 2, 3.6, 0.119, 2, 3.633, 0.085, 2, 3.667, 0.047, 2, 3.7, 0.009, 2, 3.733, -0.025, 2, 3.767, -0.048, 2, 3.8, -0.057, 2, 3.833, -0.055, 2, 3.867, -0.047, 2, 3.9, -0.037, 2, 3.933, -0.024, 2, 3.967, -0.011, 2, 4, 0.001, 2, 4.033, 0.012, 2, 4.067, 0.019, 2, 4.1, 0.022, 2, 4.133, 0.021, 2, 4.167, 0.018, 2, 4.2, 0.014, 2, 4.233, 0.01, 2, 4.267, 0.005, 2, 4.3, 0.001, 2, 4.333, -0.003, 2, 4.367, -0.005, 2, 4.4, -0.006, 2, 4.433, -0.006, 2, 4.467, -0.005, 2, 4.5, -0.004, 2, 4.533, -0.002, 2, 4.567, -0.001, 2, 4.6, 0.001, 2, 4.633, 0.003, 2, 4.667, 0.004, 2, 4.7, 0.005, 2, 4.733, 0.005, 2, 4.767, 0.005, 2, 4.8, 0.005, 2, 4.833, 0.004, 2, 4.867, 0.003, 2, 4.9, 0.003, 2, 4.933, 0.002, 2, 4.967, 0.002, 2, 5, 0.002, 2, 5.033, 0.002, 2, 5.067, 0.002, 2, 5.1, 0.002, 2, 5.133, 0.003, 2, 5.167, 0.003, 2, 5.2, 0.004, 2, 5.233, 0.004, 2, 5.3, 0.004, 2, 5.333, 0.004, 2, 5.367, 0.004, 2, 5.4, 0.004, 2, 5.433, 0.004, 2, 5.467, 0.004, 2, 5.5, 0.004, 2, 5.533, 0.003, 2, 5.567, 0.004, 2, 5.6, 0.004, 2, 5.633, 0.004, 2, 5.667, 0.004, 2, 5.7, 0.004, 2, 5.733, 0.005, 2, 5.767, 0.005, 2, 5.8, 0.004, 2, 5.833, 0.04, 2, 5.867, 0.118, 2, 5.9, 0.197, 2, 5.933, 0.232, 2, 5.967, 0.2, 2, 6, 0.115, 2, 6.033, -0.005, 2, 6.067, -0.143, 2, 6.1, -0.28, 2, 6.133, -0.4, 2, 6.167, -0.485, 2, 6.2, -0.517, 2, 6.233, -0.476, 2, 6.267, -0.367, 2, 6.3, -0.212, 2, 6.333, -0.035, 2, 6.367, 0.142, 2, 6.4, 0.296, 2, 6.433, 0.405, 2, 6.467, 0.447, 2, 6.5, 0.418, 2, 6.533, 0.342, 2, 6.567, 0.235, 2, 6.6, 0.112, 2, 6.633, -0.011, 2, 6.667, -0.118, 2, 6.7, -0.194, 2, 6.733, -0.222, 2, 6.767, -0.212, 2, 6.8, -0.183, 2, 6.833, -0.142, 2, 6.867, -0.093, 2, 6.9, -0.043, 2, 6.933, 0.006, 2, 6.967, 0.047, 2, 7, 0.076, 2, 7.033, 0.086, 2, 7.067, 0.081, 2, 7.1, 0.068, 2, 7.133, 0.049, 2, 7.167, 0.028, 2, 7.2, 0.006, 2, 7.233, -0.013, 2, 7.267, -0.026, 2, 7.3, -0.031, 2, 7.333, -0.029, 2, 7.367, -0.026, 2, 7.4, -0.02, 2, 7.433, -0.014, 2, 7.467, -0.007, 2, 7.5, 0, 2, 7.533, 0.005, 2, 7.567, 0.009, 2, 7.6, 0.01, 2, 7.633, 0.01, 2, 7.667, 0.009, 2, 7.7, 0.007, 2, 7.733, 0.005, 2, 7.767, 0.002, 2, 7.8, 0, 2, 7.833, -0.002, 2, 7.867, -0.003, 2, 7.9, -0.003, 2, 7.933, -0.003, 2, 7.967, -0.003, 2, 8, -0.002, 2, 8.033, -0.001, 2, 8.067, 0, 2, 8.1, 0, 2, 8.133, 0.001, 2, 8.167, 0.001, 2, 8.233, 0.001, 2, 8.267, 0.001, 2, 8.3, 0, 2, 8.333, 0, 2, 8.367, 0, 2, 8.4, 0, 2, 8.433, 0, 2, 8.467, 0, 2, 8.5, 0, 2, 8.567, 0, 2, 8.6, 0, 2, 8.667, 0, 2, 8.733, 0, 2, 8.8, 0, 2, 8.833, 0, 2, 8.9, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0, 2, 0.1, 0, 2, 0.133, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 0.233, 0, 2, 0.267, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.367, 0, 2, 0.5, -0.018, 2, 0.533, -0.064, 2, 0.567, -0.124, 2, 0.6, -0.184, 2, 0.633, -0.229, 2, 0.667, -0.247, 2, 0.7, -0.183, 2, 0.733, -0.012, 2, 0.767, 0.229, 2, 0.8, 0.506, 2, 0.833, 0.782, 2, 0.867, 1.023, 2, 0.9, 1.194, 2, 0.933, 1.259, 2, 0.967, 1.163, 2, 1, 0.909, 2, 1.033, 0.54, 2, 1.067, 0.103, 2, 1.1, -0.349, 2, 1.133, -0.786, 2, 1.167, -1.154, 2, 1.2, -1.409, 2, 1.233, -1.504, 2, 1.267, -1.463, 2, 1.3, -1.348, 2, 1.333, -1.169, 2, 1.367, -0.946, 2, 1.4, -0.68, 2, 1.433, -0.392, 2, 1.467, -0.092, 2, 1.5, 0.208, 2, 1.533, 0.495, 2, 1.567, 0.762, 2, 1.6, 0.985, 2, 1.633, 1.164, 2, 1.667, 1.279, 2, 1.7, 1.32, 2, 1.733, 1.23, 2, 1.767, 0.99, 2, 1.8, 0.643, 2, 1.833, 0.232, 2, 1.867, -0.193, 2, 1.9, -0.605, 2, 1.933, -0.952, 2, 1.967, -1.192, 2, 2, -1.281, 2, 2.033, -1.211, 2, 2.067, -1.023, 2, 2.1, -0.751, 2, 2.133, -0.428, 2, 2.167, -0.095, 2, 2.2, 0.228, 2, 2.233, 0.5, 2, 2.267, 0.688, 2, 2.3, 0.758, 2, 2.333, 0.718, 2, 2.367, 0.612, 2, 2.4, 0.459, 2, 2.433, 0.277, 2, 2.467, 0.089, 2, 2.5, -0.093, 2, 2.533, -0.247, 2, 2.567, -0.353, 2, 2.6, -0.393, 2, 2.633, -0.379, 2, 2.667, -0.341, 2, 2.7, -0.283, 2, 2.733, -0.209, 2, 2.767, -0.124, 2, 2.8, -0.034, 2, 2.833, 0.061, 2, 2.867, 0.152, 2, 2.9, 0.237, 2, 2.933, 0.31, 2, 2.967, 0.368, 2, 3, 0.406, 2, 3.033, 0.42, 2, 3.067, 0.391, 2, 3.1, 0.314, 2, 3.133, 0.203, 2, 3.167, 0.071, 2, 3.2, -0.065, 2, 3.233, -0.197, 2, 3.267, -0.308, 2, 3.3, -0.385, 2, 3.333, -0.413, 2, 3.367, -0.39, 2, 3.4, -0.328, 2, 3.433, -0.238, 2, 3.467, -0.131, 2, 3.5, -0.021, 2, 3.533, 0.086, 2, 3.567, 0.176, 2, 3.6, 0.239, 2, 3.633, 0.262, 2, 3.667, 0.249, 2, 3.7, 0.213, 2, 3.733, 0.161, 2, 3.767, 0.1, 2, 3.8, 0.037, 2, 3.833, -0.025, 2, 3.867, -0.076, 2, 3.9, -0.112, 2, 3.933, -0.125, 2, 3.967, -0.118, 2, 4, -0.097, 2, 4.033, -0.068, 2, 4.067, -0.035, 2, 4.1, -0.002, 2, 4.133, 0.026, 2, 4.167, 0.047, 2, 4.2, 0.055, 2, 4.233, 0.052, 2, 4.267, 0.045, 2, 4.3, 0.035, 2, 4.333, 0.023, 2, 4.367, 0.011, 2, 4.4, -0.001, 2, 4.433, -0.011, 2, 4.467, -0.018, 2, 4.5, -0.021, 2, 4.533, -0.02, 2, 4.567, -0.017, 2, 4.6, -0.012, 2, 4.633, -0.007, 2, 4.667, -0.002, 2, 4.7, 0.003, 2, 4.733, 0.007, 2, 4.767, 0.01, 2, 4.8, 0.011, 2, 4.833, 0.011, 2, 4.867, 0.01, 2, 4.9, 0.008, 2, 4.933, 0.006, 2, 4.967, 0.004, 2, 5, 0.002, 2, 5.033, 0.001, 2, 5.067, 0, 2, 5.1, -0.001, 2, 5.133, -0.001, 2, 5.167, 0, 2, 5.2, 0.001, 2, 5.233, 0.001, 2, 5.267, 0.002, 2, 5.3, 0.003, 2, 5.333, 0.004, 2, 5.367, 0.004, 2, 5.4, 0.005, 2, 5.433, 0.005, 2, 5.467, 0.005, 2, 5.5, 0.005, 2, 5.533, 0.004, 2, 5.567, 0.004, 2, 5.6, 0.004, 2, 5.633, 0.004, 2, 5.667, 0.004, 2, 5.733, 0.009, 2, 5.767, 0.024, 2, 5.8, 0.047, 2, 5.833, 0.074, 2, 5.867, 0.104, 2, 5.9, 0.133, 2, 5.933, 0.161, 2, 5.967, 0.183, 2, 6, 0.199, 2, 6.033, 0.204, 2, 6.067, 0.172, 2, 6.1, 0.086, 2, 6.133, -0.035, 2, 6.167, -0.174, 2, 6.2, -0.314, 2, 6.233, -0.435, 2, 6.267, -0.521, 2, 6.3, -0.553, 2, 6.333, -0.505, 2, 6.367, -0.377, 2, 6.4, -0.196, 2, 6.433, 0.012, 2, 6.467, 0.219, 2, 6.5, 0.4, 2, 6.533, 0.528, 2, 6.567, 0.577, 2, 6.6, 0.536, 2, 6.633, 0.43, 2, 6.667, 0.279, 2, 6.7, 0.106, 2, 6.733, -0.066, 2, 6.767, -0.217, 2, 6.8, -0.324, 2, 6.833, -0.364, 2, 6.867, -0.345, 2, 6.9, -0.295, 2, 6.933, -0.222, 2, 6.967, -0.135, 2, 7, -0.045, 2, 7.033, 0.041, 2, 7.067, 0.115, 2, 7.1, 0.165, 2, 7.133, 0.184, 2, 7.167, 0.175, 2, 7.2, 0.151, 2, 7.233, 0.115, 2, 7.267, 0.074, 2, 7.3, 0.031, 2, 7.333, -0.011, 2, 7.367, -0.046, 2, 7.4, -0.07, 2, 7.433, -0.079, 2, 7.467, -0.076, 2, 7.5, -0.065, 2, 7.533, -0.051, 2, 7.567, -0.033, 2, 7.6, -0.015, 2, 7.633, 0.002, 2, 7.667, 0.017, 2, 7.7, 0.027, 2, 7.733, 0.031, 2, 7.767, 0.029, 2, 7.8, 0.024, 2, 7.833, 0.018, 2, 7.867, 0.01, 2, 7.9, 0.002, 2, 7.933, -0.005, 2, 7.967, -0.01, 2, 8, -0.011, 2, 8.033, -0.011, 2, 8.067, -0.01, 2, 8.1, -0.007, 2, 8.133, -0.005, 2, 8.167, -0.002, 2, 8.2, 0, 2, 8.233, 0.002, 2, 8.267, 0.004, 2, 8.3, 0.004, 2, 8.333, 0.004, 2, 8.367, 0.003, 2, 8.4, 0.003, 2, 8.433, 0.002, 2, 8.467, 0.001, 2, 8.5, 0, 2, 8.533, -0.001, 2, 8.567, -0.001, 2, 8.6, -0.001, 2, 8.633, -0.001, 2, 8.667, -0.001, 2, 8.7, -0.001, 2, 8.733, -0.001, 2, 8.767, 0, 2, 8.8, 0, 2, 8.833, 0, 2, 8.867, 0, 2, 8.9, 0, 2, 8.933, 0, 2, 8.967, 0, 2, 9, 0, 2, 9.033, 0, 2, 9.067, 0, 2, 9.1, 0, 2, 9.133, 0, 2, 9.167, 0, 2, 9.2, 0, 2, 9.233, 0, 2, 9.267, 0, 2, 9.3, 0, 2, 9.333, 0, 2, 9.467, 0, 2, 9.533, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1", "Segments": [0, 0.003, 2, 0.033, 0.003, 2, 0.067, 0.002, 2, 0.1, 0.001, 2, 0.133, -0.001, 2, 0.167, -0.001, 2, 0.2, -0.002, 2, 0.233, -0.002, 2, 0.267, -0.001, 2, 0.3, -0.001, 2, 0.333, -0.001, 2, 0.367, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.467, 0.001, 2, 0.5, -0.008, 2, 0.533, -0.032, 2, 0.567, -0.065, 2, 0.6, -0.103, 2, 0.633, -0.142, 2, 0.667, -0.175, 2, 0.7, -0.198, 2, 0.733, -0.207, 2, 0.767, -0.141, 2, 0.8, 0.035, 2, 0.833, 0.283, 2, 0.867, 0.568, 2, 0.9, 0.852, 2, 0.933, 1.1, 2, 0.967, 1.276, 2, 1, 1.343, 2, 1.033, 1.25, 2, 1.067, 1, 2, 1.1, 0.622, 2, 1.133, 0.172, 2, 1.167, -0.317, 2, 1.2, -0.807, 2, 1.233, -1.257, 2, 1.267, -1.634, 2, 1.3, -1.885, 2, 1.333, -1.977, 2, 1.367, -1.904, 2, 1.4, -1.71, 2, 1.433, -1.41, 2, 1.467, -1.034, 2, 1.5, -0.616, 2, 1.533, -0.164, 2, 1.567, 0.289, 2, 1.6, 0.706, 2, 1.633, 1.082, 2, 1.667, 1.382, 2, 1.7, 1.577, 2, 1.733, 1.649, 2, 1.767, 1.57, 2, 1.8, 1.358, 2, 1.833, 1.037, 2, 1.867, 0.647, 2, 1.9, 0.204, 2, 1.933, -0.245, 2, 1.967, -0.688, 2, 2, -1.078, 2, 2.033, -1.4, 2, 2.067, -1.611, 2, 2.1, -1.69, 2, 2.133, -1.591, 2, 2.167, -1.323, 2, 2.2, -0.937, 2, 2.233, -0.478, 2, 2.267, -0.005, 2, 2.3, 0.454, 2, 2.333, 0.841, 2, 2.367, 1.108, 2, 2.4, 1.208, 2, 2.433, 1.14, 2, 2.467, 0.96, 2, 2.5, 0.698, 2, 2.533, 0.389, 2, 2.567, 0.069, 2, 2.6, -0.241, 2, 2.633, -0.502, 2, 2.667, -0.683, 2, 2.7, -0.75, 2, 2.733, -0.724, 2, 2.767, -0.654, 2, 2.8, -0.546, 2, 2.833, -0.41, 2, 2.867, -0.259, 2, 2.9, -0.096, 2, 2.933, 0.068, 2, 2.967, 0.218, 2, 3, 0.354, 2, 3.033, 0.462, 2, 3.067, 0.533, 2, 3.1, 0.559, 2, 3.133, 0.528, 2, 3.167, 0.443, 2, 3.2, 0.316, 2, 3.233, 0.164, 2, 3.267, -0.001, 2, 3.3, -0.166, 2, 3.333, -0.317, 2, 3.367, -0.444, 2, 3.4, -0.529, 2, 3.433, -0.56, 2, 3.467, -0.526, 2, 3.5, -0.437, 2, 3.533, -0.307, 2, 3.567, -0.153, 2, 3.6, 0.006, 2, 3.633, 0.159, 2, 3.667, 0.289, 2, 3.7, 0.379, 2, 3.733, 0.412, 2, 3.767, 0.39, 2, 3.8, 0.33, 2, 3.833, 0.244, 2, 3.867, 0.142, 2, 3.9, 0.036, 2, 3.933, -0.067, 2, 3.967, -0.153, 2, 4, -0.213, 2, 4.033, -0.235, 2, 4.067, -0.223, 2, 4.1, -0.19, 2, 4.133, -0.143, 2, 4.167, -0.088, 2, 4.2, -0.03, 2, 4.233, 0.026, 2, 4.267, 0.073, 2, 4.3, 0.105, 2, 4.333, 0.117, 2, 4.367, 0.112, 2, 4.4, 0.096, 2, 4.433, 0.074, 2, 4.467, 0.047, 2, 4.5, 0.019, 2, 4.533, -0.007, 2, 4.567, -0.03, 2, 4.6, -0.045, 2, 4.633, -0.051, 2, 4.667, -0.048, 2, 4.7, -0.039, 2, 4.733, -0.027, 2, 4.767, -0.013, 2, 4.8, 0.001, 2, 4.833, 0.014, 2, 4.867, 0.022, 2, 4.9, 0.026, 2, 4.967, 0.024, 2, 5, 0.021, 2, 5.033, 0.015, 2, 5.067, 0.009, 2, 5.1, 0.004, 2, 5.133, -0.002, 2, 5.167, -0.005, 2, 5.2, -0.007, 2, 5.233, -0.006, 2, 5.267, -0.005, 2, 5.3, -0.003, 2, 5.333, -0.001, 2, 5.367, 0.002, 2, 5.4, 0.004, 2, 5.433, 0.006, 2, 5.467, 0.007, 2, 5.5, 0.008, 2, 5.567, 0.007, 2, 5.6, 0.007, 2, 5.633, 0.006, 2, 5.667, 0.005, 2, 5.7, 0.004, 2, 5.733, 0.003, 2, 5.767, 0.003, 2, 5.8, 0.003, 2, 5.833, 0.009, 2, 5.867, 0.027, 2, 5.9, 0.052, 2, 5.933, 0.082, 2, 5.967, 0.114, 2, 6, 0.144, 2, 6.033, 0.169, 2, 6.067, 0.187, 2, 6.1, 0.193, 2, 6.133, 0.166, 2, 6.167, 0.094, 2, 6.2, -0.012, 2, 6.233, -0.136, 2, 6.267, -0.265, 2, 6.3, -0.389, 2, 6.333, -0.494, 2, 6.367, -0.567, 2, 6.4, -0.594, 2, 6.433, -0.537, 2, 6.467, -0.387, 2, 6.5, -0.175, 2, 6.533, 0.069, 2, 6.567, 0.312, 2, 6.6, 0.524, 2, 6.633, 0.675, 2, 6.667, 0.732, 2, 6.7, 0.687, 2, 6.733, 0.568, 2, 6.767, 0.396, 2, 6.8, 0.192, 2, 6.833, -0.019, 2, 6.867, -0.223, 2, 6.9, -0.395, 2, 6.933, -0.514, 2, 6.967, -0.558, 2, 7, -0.52, 2, 7.033, -0.42, 2, 7.067, -0.278, 2, 7.1, -0.115, 2, 7.133, 0.048, 2, 7.167, 0.19, 2, 7.2, 0.29, 2, 7.233, 0.328, 2, 7.267, 0.311, 2, 7.3, 0.266, 2, 7.333, 0.2, 2, 7.367, 0.121, 2, 7.4, 0.04, 2, 7.433, -0.038, 2, 7.467, -0.104, 2, 7.5, -0.15, 2, 7.533, -0.167, 2, 7.567, -0.159, 2, 7.6, -0.136, 2, 7.633, -0.104, 2, 7.667, -0.065, 2, 7.7, -0.026, 2, 7.733, 0.013, 2, 7.767, 0.045, 2, 7.8, 0.067, 2, 7.833, 0.076, 2, 7.867, 0.072, 2, 7.9, 0.062, 2, 7.933, 0.048, 2, 7.967, 0.031, 2, 8, 0.013, 2, 8.033, -0.004, 2, 8.067, -0.018, 2, 8.1, -0.028, 2, 8.133, -0.032, 2, 8.167, -0.03, 2, 8.2, -0.026, 2, 8.233, -0.02, 2, 8.267, -0.013, 2, 8.3, -0.006, 2, 8.333, 0.001, 2, 8.367, 0.007, 2, 8.4, 0.011, 2, 8.433, 0.012, 2, 8.467, 0.012, 2, 8.5, 0.01, 2, 8.533, 0.007, 2, 8.567, 0.004, 2, 8.6, 0.001, 2, 8.633, -0.002, 2, 8.667, -0.004, 2, 8.7, -0.005, 2, 8.733, -0.004, 2, 8.767, -0.004, 2, 8.8, -0.003, 2, 8.833, -0.002, 2, 8.867, -0.001, 2, 8.9, 0, 2, 8.933, 0.001, 2, 8.967, 0.001, 2, 9, 0.002, 2, 9.067, 0.002, 2, 9.1, 0.001, 2, 9.133, 0.001, 2, 9.167, 0, 2, 9.2, 0, 2, 9.233, 0, 2, 9.267, -0.001, 2, 9.367, -0.001, 2, 9.4, 0, 2, 9.433, 0, 2, 9.467, 0, 2, 9.5, 0, 2, 9.567, 0, 2, 9.7, 0, 2, 9.767, 0, 2, 9.833, 0, 2, 9.9, 0, 2, 9.933, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh2", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh2", "Segments": [0, 0, 2, 0.5, 0.063, 2, 0.533, 0.213, 2, 0.567, 0.397, 2, 0.6, 0.547, 2, 0.633, 0.61, 2, 0.667, 0.527, 2, 0.7, 0.304, 2, 0.733, -0.035, 2, 0.767, -0.445, 2, 0.8, -0.912, 2, 0.833, -1.384, 2, 0.867, -1.851, 2, 0.9, -2.262, 2, 0.933, -2.601, 2, 0.967, -2.823, 2, 1, -2.907, 2, 1.033, -2.848, 2, 1.067, -2.682, 2, 1.1, -2.424, 2, 1.133, -2.103, 2, 1.167, -1.719, 2, 1.2, -1.304, 2, 1.233, -0.872, 2, 1.267, -0.44, 2, 1.3, -0.025, 2, 1.333, 0.359, 2, 1.367, 0.68, 2, 1.4, 0.938, 2, 1.433, 1.104, 2, 1.467, 1.163, 2, 1.5, 1.091, 2, 1.533, 0.895, 2, 1.567, 0.601, 2, 1.6, 0.25, 2, 1.633, -0.132, 2, 1.667, -0.514, 2, 1.7, -0.865, 2, 1.733, -1.16, 2, 1.767, -1.355, 2, 1.8, -1.427, 2, 1.833, -1.402, 2, 1.867, -1.332, 2, 1.9, -1.227, 2, 1.933, -1.102, 2, 1.967, -0.966, 2, 2, -0.83, 2, 2.033, -0.705, 2, 2.067, -0.6, 2, 2.1, -0.53, 2, 2.133, -0.504, 2, 2.167, -0.51, 2, 2.2, -0.527, 2, 2.233, -0.55, 2, 2.267, -0.578, 2, 2.3, -0.607, 2, 2.333, -0.635, 2, 2.367, -0.658, 2, 2.4, -0.675, 2, 2.433, -0.681, 2, 2.467, -0.665, 2, 2.5, -0.622, 2, 2.533, -0.556, 2, 2.567, -0.477, 2, 2.6, -0.387, 2, 2.633, -0.295, 2, 2.667, -0.205, 2, 2.7, -0.126, 2, 2.733, -0.06, 2, 2.767, -0.017, 2, 2.8, -0.001, 2, 2.833, -0.024, 2, 2.867, -0.086, 2, 2.9, -0.179, 2, 2.933, -0.291, 2, 2.967, -0.412, 2, 3, -0.533, 2, 3.033, -0.644, 2, 3.067, -0.738, 2, 3.1, -0.8, 2, 3.133, -0.823, 2, 3.167, -0.814, 2, 3.2, -0.791, 2, 3.233, -0.756, 2, 3.267, -0.715, 2, 3.3, -0.67, 2, 3.333, -0.625, 2, 3.367, -0.583, 2, 3.4, -0.548, 2, 3.433, -0.525, 2, 3.467, -0.517, 2, 3.5, -0.519, 2, 3.533, -0.525, 2, 3.567, -0.534, 2, 3.6, -0.545, 2, 3.633, -0.558, 2, 3.667, -0.571, 2, 3.7, -0.585, 2, 3.733, -0.598, 2, 3.767, -0.609, 2, 3.8, -0.618, 2, 3.833, -0.624, 2, 3.867, -0.626, 2, 3.9, -0.625, 2, 3.933, -0.624, 2, 3.967, -0.622, 2, 4, -0.62, 2, 4.033, -0.618, 2, 4.067, -0.618, 2, 4.133, -0.618, 2, 4.167, -0.62, 2, 4.2, -0.623, 2, 4.233, -0.627, 2, 4.267, -0.632, 2, 4.3, -0.638, 2, 4.333, -0.644, 2, 4.367, -0.652, 2, 4.4, -0.661, 2, 4.433, -0.67, 2, 4.467, -0.68, 2, 4.5, -0.692, 2, 4.533, -0.703, 2, 4.567, -0.715, 2, 4.6, -0.728, 2, 4.633, -0.742, 2, 4.667, -0.756, 2, 4.7, -0.77, 2, 4.733, -0.785, 2, 4.767, -0.801, 2, 4.8, -0.817, 2, 4.833, -0.833, 2, 4.867, -0.849, 2, 4.9, -0.866, 2, 4.933, -0.883, 2, 4.967, -0.899, 2, 5, -0.917, 2, 5.033, -0.934, 2, 5.067, -0.951, 2, 5.1, -0.968, 2, 5.133, -0.985, 2, 5.167, -1.003, 2, 5.2, -1.019, 2, 5.233, -1.036, 2, 5.267, -1.053, 2, 5.3, -1.069, 2, 5.333, -1.086, 2, 5.367, -1.101, 2, 5.4, -1.117, 2, 5.433, -1.132, 2, 5.467, -1.146, 2, 5.5, -1.16, 2, 5.533, -1.174, 2, 5.567, -1.187, 2, 5.6, -1.199, 2, 5.633, -1.211, 2, 5.667, -1.222, 2, 5.7, -1.232, 2, 5.733, -1.241, 2, 5.767, -1.25, 2, 5.8, -1.258, 2, 5.833, -1.265, 2, 5.867, -1.27, 2, 5.9, -1.275, 2, 5.933, -1.279, 2, 5.967, -1.282, 2, 6, -1.284, 2, 6.033, -1.284, 2, 6.067, -1.249, 2, 6.1, -1.152, 2, 6.133, -1.013, 2, 6.167, -0.848, 2, 6.2, -0.677, 2, 6.233, -0.512, 2, 6.267, -0.373, 2, 6.3, -0.276, 2, 6.333, -0.24, 2, 6.367, -0.254, 2, 6.4, -0.291, 2, 6.433, -0.346, 2, 6.467, -0.412, 2, 6.5, -0.484, 2, 6.533, -0.556, 2, 6.567, -0.622, 2, 6.6, -0.678, 2, 6.633, -0.715, 2, 6.667, -0.728, 2, 6.7, -0.725, 2, 6.733, -0.715, 2, 6.767, -0.701, 2, 6.8, -0.684, 2, 6.833, -0.665, 2, 6.867, -0.647, 2, 6.9, -0.63, 2, 6.933, -0.616, 2, 6.967, -0.606, 2, 7, -0.603, 2, 7.033, -0.603, 2, 7.067, -0.606, 2, 7.1, -0.609, 2, 7.133, -0.612, 2, 7.167, -0.617, 2, 7.2, -0.621, 2, 7.233, -0.625, 2, 7.267, -0.629, 2, 7.3, -0.632, 2, 7.333, -0.634, 2, 7.367, -0.635, 2, 7.4, -0.635, 2, 7.433, -0.634, 2, 7.467, -0.633, 2, 7.5, -0.632, 2, 7.533, -0.631, 2, 7.567, -0.63, 2, 7.6, -0.628, 2, 7.633, -0.627, 2, 7.667, -0.627, 2, 7.7, -0.627, 2, 7.733, -0.627, 2, 7.767, -0.627, 2, 7.8, -0.627, 2, 7.833, -0.627, 2, 7.867, -0.628, 2, 7.9, -0.628, 2, 7.933, -0.628, 2, 7.967, -0.629, 2, 8, -0.629, 2, 8.033, -0.629, 2, 8.067, -0.629, 2, 8.133, -0.629, 2, 8.167, -0.629, 2, 8.2, -0.628, 2, 8.233, -0.628, 2, 8.3, -0.628, 2, 8.433, -0.628, 2, 8.467, -0.628, 2, 8.5, -0.628, 2, 8.633, -0.628, 2, 8.667, -0.628, 2, 8.733, -0.628, 2, 8.767, -0.628, 2, 8.967, -0.628, 2, 9, -0.628, 2, 9.067, -0.628, 2, 9.1, -0.628, 2, 9.167, -0.628, 2, 9.233, -0.628, 2, 9.267, -0.628, 2, 9.3, -0.628, 2, 9.5, -0.628, 2, 9.533, -0.628, 2, 9.567, -0.628, 2, 9.6, -0.628, 2, 9.767, -0.628, 2, 9.8, -0.628, 2, 9.967, -0.628, 2, 10, -0.628]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh2", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0, 2, 0.1, 0, 2, 0.2, 0, 2, 0.267, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.5, -0.057, 2, 0.533, -0.183, 2, 0.567, -0.308, 2, 0.6, -0.365, 2, 0.633, -0.287, 2, 0.667, -0.082, 2, 0.7, 0.208, 2, 0.733, 0.541, 2, 0.767, 0.873, 2, 0.8, 1.164, 2, 0.833, 1.369, 2, 0.867, 1.447, 2, 0.9, 1.379, 2, 0.933, 1.199, 2, 0.967, 0.925, 2, 1, 0.593, 2, 1.033, 0.215, 2, 1.067, -0.168, 2, 1.1, -0.545, 2, 1.133, -0.877, 2, 1.167, -1.152, 2, 1.2, -1.332, 2, 1.233, -1.399, 2, 1.267, -1.348, 2, 1.3, -1.2, 2, 1.333, -0.979, 2, 1.367, -0.696, 2, 1.4, -0.37, 2, 1.433, -0.023, 2, 1.467, 0.342, 2, 1.5, 0.689, 2, 1.533, 1.015, 2, 1.567, 1.298, 2, 1.6, 1.519, 2, 1.633, 1.667, 2, 1.667, 1.719, 2, 1.7, 1.623, 2, 1.733, 1.365, 2, 1.767, 0.992, 2, 1.8, 0.551, 2, 1.833, 0.094, 2, 1.867, -0.347, 2, 1.9, -0.72, 2, 1.933, -0.978, 2, 1.967, -1.074, 2, 2, -1.032, 2, 2.033, -0.918, 2, 2.067, -0.746, 2, 2.1, -0.541, 2, 2.133, -0.318, 2, 2.167, -0.095, 2, 2.2, 0.11, 2, 2.233, 0.281, 2, 2.267, 0.396, 2, 2.3, 0.438, 2, 2.333, 0.421, 2, 2.367, 0.377, 2, 2.4, 0.31, 2, 2.433, 0.225, 2, 2.467, 0.13, 2, 2.5, 0.028, 2, 2.533, -0.074, 2, 2.567, -0.168, 2, 2.6, -0.253, 2, 2.633, -0.321, 2, 2.667, -0.365, 2, 2.7, -0.381, 2, 2.733, -0.357, 2, 2.767, -0.291, 2, 2.8, -0.191, 2, 2.833, -0.073, 2, 2.867, 0.056, 2, 2.9, 0.185, 2, 2.933, 0.304, 2, 2.967, 0.403, 2, 3, 0.469, 2, 3.033, 0.493, 2, 3.067, 0.458, 2, 3.1, 0.364, 2, 3.133, 0.232, 2, 3.167, 0.08, 2, 3.2, -0.072, 2, 3.233, -0.204, 2, 3.267, -0.298, 2, 3.3, -0.334, 2, 3.333, -0.32, 2, 3.367, -0.283, 2, 3.4, -0.228, 2, 3.433, -0.162, 2, 3.467, -0.091, 2, 3.5, -0.019, 2, 3.533, 0.047, 2, 3.567, 0.102, 2, 3.6, 0.139, 2, 3.633, 0.152, 2, 3.667, 0.147, 2, 3.7, 0.131, 2, 3.733, 0.108, 2, 3.767, 0.08, 2, 3.8, 0.05, 2, 3.833, 0.019, 2, 3.867, -0.009, 2, 3.9, -0.032, 2, 3.933, -0.047, 2, 3.967, -0.053, 2, 4, -0.051, 2, 4.033, -0.046, 2, 4.067, -0.037, 2, 4.1, -0.028, 2, 4.133, -0.017, 2, 4.167, -0.006, 2, 4.2, 0.004, 2, 4.233, 0.012, 2, 4.267, 0.018, 2, 4.3, 0.02, 2, 4.333, 0.019, 2, 4.367, 0.017, 2, 4.4, 0.014, 2, 4.433, 0.01, 2, 4.467, 0.006, 2, 4.5, 0.002, 2, 4.533, -0.001, 2, 4.567, -0.003, 2, 4.6, -0.004, 2, 4.633, -0.003, 2, 4.667, -0.003, 2, 4.7, -0.001, 2, 4.733, 0, 2, 4.767, 0.001, 2, 4.8, 0.003, 2, 4.833, 0.004, 2, 4.867, 0.005, 2, 4.9, 0.006, 2, 4.933, 0.006, 2, 4.967, 0.006, 2, 5, 0.006, 2, 5.033, 0.005, 2, 5.067, 0.005, 2, 5.1, 0.005, 2, 5.133, 0.004, 2, 5.167, 0.004, 2, 5.2, 0.004, 2, 5.233, 0.004, 2, 5.267, 0.004, 2, 5.333, 0.005, 2, 5.367, 0.005, 2, 5.4, 0.005, 2, 5.533, 0.006, 2, 5.567, 0.006, 2, 5.6, 0.006, 2, 5.633, 0.006, 2, 5.667, 0.006, 2, 5.7, 0.006, 2, 5.733, 0.007, 2, 5.767, 0.007, 2, 5.8, 0.006, 2, 5.833, 0.033, 2, 5.867, 0.095, 2, 5.9, 0.171, 2, 5.933, 0.234, 2, 5.967, 0.26, 2, 6, 0.223, 2, 6.033, 0.126, 2, 6.067, -0.012, 2, 6.1, -0.169, 2, 6.133, -0.327, 2, 6.167, -0.464, 2, 6.2, -0.561, 2, 6.233, -0.598, 2, 6.267, -0.562, 2, 6.3, -0.467, 2, 6.333, -0.329, 2, 6.367, -0.165, 2, 6.4, 0.004, 2, 6.433, 0.168, 2, 6.467, 0.306, 2, 6.5, 0.402, 2, 6.533, 0.437, 2, 6.567, 0.415, 2, 6.6, 0.356, 2, 6.633, 0.271, 2, 6.667, 0.17, 2, 6.7, 0.065, 2, 6.733, -0.036, 2, 6.767, -0.122, 2, 6.8, -0.181, 2, 6.833, -0.203, 2, 6.867, -0.195, 2, 6.9, -0.174, 2, 6.933, -0.142, 2, 6.967, -0.104, 2, 7, -0.063, 2, 7.033, -0.022, 2, 7.067, 0.016, 2, 7.1, 0.047, 2, 7.133, 0.069, 2, 7.167, 0.076, 2, 7.2, 0.073, 2, 7.233, 0.066, 2, 7.267, 0.054, 2, 7.3, 0.04, 2, 7.333, 0.025, 2, 7.367, 0.01, 2, 7.4, -0.004, 2, 7.433, -0.015, 2, 7.467, -0.023, 2, 7.5, -0.026, 2, 7.533, -0.025, 2, 7.567, -0.022, 2, 7.6, -0.018, 2, 7.633, -0.014, 2, 7.667, -0.009, 2, 7.7, -0.004, 2, 7.733, 0.001, 2, 7.767, 0.005, 2, 7.8, 0.007, 2, 7.833, 0.008, 2, 7.867, 0.008, 2, 7.9, 0.007, 2, 7.933, 0.006, 2, 7.967, 0.004, 2, 8, 0.003, 2, 8.033, 0.001, 2, 8.067, 0, 2, 8.1, -0.001, 2, 8.133, -0.002, 2, 8.167, -0.003, 2, 8.233, -0.002, 2, 8.267, -0.002, 2, 8.3, -0.002, 2, 8.333, -0.001, 2, 8.367, -0.001, 2, 8.4, 0, 2, 8.433, 0, 2, 8.467, 0.001, 2, 8.5, 0.001, 2, 8.567, 0.001, 2, 8.6, 0.001, 2, 8.633, 0, 2, 8.667, 0, 2, 8.7, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 8.8, 0, 2, 8.867, 0, 2, 8.9, 0, 2, 8.967, 0, 2, 9.033, 0, 2, 9.133, 0, 2, 9.2, 0, 2, 9.233, 0, 2, 9.3, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh2", "Segments": [0, -0.002, 2, 0.033, -0.002, 2, 0.067, -0.002, 2, 0.1, -0.002, 2, 0.133, -0.001, 2, 0.167, -0.001, 2, 0.2, 0, 2, 0.233, 0, 2, 0.267, 0.001, 2, 0.3, 0.001, 2, 0.333, 0.001, 2, 0.4, -0.006, 2, 0.433, -0.026, 2, 0.467, -0.055, 2, 0.5, -0.09, 2, 0.533, -0.128, 2, 0.567, -0.166, 2, 0.6, -0.201, 2, 0.633, -0.231, 2, 0.667, -0.25, 2, 0.7, -0.258, 2, 0.733, -0.184, 2, 0.767, 0.008, 2, 0.8, 0.281, 2, 0.833, 0.593, 2, 0.867, 0.906, 2, 0.9, 1.179, 2, 0.933, 1.371, 2, 0.967, 1.444, 2, 1, 1.364, 2, 1.033, 1.151, 2, 1.067, 0.826, 2, 1.1, 0.432, 2, 1.133, -0.015, 2, 1.167, -0.469, 2, 1.2, -0.917, 2, 1.233, -1.31, 2, 1.267, -1.635, 2, 1.3, -1.849, 2, 1.333, -1.929, 2, 1.367, -1.862, 2, 1.4, -1.67, 2, 1.433, -1.385, 2, 1.467, -1.019, 2, 1.5, -0.598, 2, 1.533, -0.15, 2, 1.567, 0.322, 2, 1.6, 0.77, 2, 1.633, 1.191, 2, 1.667, 1.557, 2, 1.7, 1.843, 2, 1.733, 2.034, 2, 1.767, 2.101, 2, 1.8, 1.998, 2, 1.833, 1.721, 2, 1.867, 1.302, 2, 1.9, 0.803, 2, 1.933, 0.26, 2, 1.967, -0.283, 2, 2, -0.782, 2, 2.033, -1.201, 2, 2.067, -1.479, 2, 2.1, -1.581, 2, 2.133, -1.514, 2, 2.167, -1.333, 2, 2.2, -1.059, 2, 2.233, -0.732, 2, 2.267, -0.377, 2, 2.3, -0.022, 2, 2.333, 0.305, 2, 2.367, 0.579, 2, 2.4, 0.76, 2, 2.433, 0.827, 2, 2.467, 0.794, 2, 2.5, 0.705, 2, 2.533, 0.569, 2, 2.567, 0.405, 2, 2.6, 0.218, 2, 2.633, 0.028, 2, 2.667, -0.159, 2, 2.7, -0.323, 2, 2.733, -0.459, 2, 2.767, -0.548, 2, 2.8, -0.582, 2, 2.833, -0.548, 2, 2.867, -0.456, 2, 2.9, -0.318, 2, 2.933, -0.154, 2, 2.967, 0.025, 2, 3, 0.204, 2, 3.033, 0.368, 2, 3.067, 0.506, 2, 3.1, 0.598, 2, 3.133, 0.632, 2, 3.167, 0.593, 2, 3.2, 0.491, 2, 3.233, 0.343, 2, 3.267, 0.167, 2, 3.3, -0.014, 2, 3.333, -0.189, 2, 3.367, -0.338, 2, 3.4, -0.44, 2, 3.433, -0.478, 2, 3.467, -0.457, 2, 3.5, -0.401, 2, 3.533, -0.316, 2, 3.567, -0.214, 2, 3.6, -0.104, 2, 3.633, 0.006, 2, 3.667, 0.108, 2, 3.7, 0.193, 2, 3.733, 0.249, 2, 3.767, 0.27, 2, 3.8, 0.259, 2, 3.833, 0.23, 2, 3.867, 0.186, 2, 3.9, 0.133, 2, 3.933, 0.075, 2, 3.967, 0.018, 2, 4, -0.035, 2, 4.033, -0.08, 2, 4.067, -0.109, 2, 4.1, -0.12, 2, 4.133, -0.115, 2, 4.167, -0.103, 2, 4.2, -0.083, 2, 4.233, -0.06, 2, 4.267, -0.035, 2, 4.3, -0.01, 2, 4.333, 0.013, 2, 4.367, 0.032, 2, 4.4, 0.045, 2, 4.433, 0.05, 2, 4.467, 0.048, 2, 4.5, 0.043, 2, 4.533, 0.036, 2, 4.567, 0.027, 2, 4.6, 0.018, 2, 4.633, 0.008, 2, 4.667, -0.001, 2, 4.7, -0.008, 2, 4.733, -0.013, 2, 4.767, -0.015, 2, 4.8, -0.014, 2, 4.833, -0.012, 2, 4.867, -0.009, 2, 4.9, -0.006, 2, 4.933, -0.002, 2, 4.967, 0.002, 2, 5, 0.005, 2, 5.033, 0.008, 2, 5.067, 0.01, 2, 5.1, 0.011, 2, 5.133, 0.011, 2, 5.167, 0.01, 2, 5.2, 0.009, 2, 5.233, 0.008, 2, 5.267, 0.006, 2, 5.3, 0.005, 2, 5.333, 0.004, 2, 5.367, 0.003, 2, 5.4, 0.003, 2, 5.467, 0.003, 2, 5.5, 0.003, 2, 5.533, 0.004, 2, 5.567, 0.004, 2, 5.6, 0.005, 2, 5.633, 0.006, 2, 5.667, 0.006, 2, 5.7, 0.007, 2, 5.733, 0.007, 2, 5.767, 0.007, 2, 5.833, 0.016, 2, 5.867, 0.041, 2, 5.9, 0.075, 2, 5.933, 0.114, 2, 5.967, 0.153, 2, 6, 0.187, 2, 6.033, 0.212, 2, 6.067, 0.221, 2, 6.1, 0.193, 2, 6.133, 0.12, 2, 6.167, 0.013, 2, 6.2, -0.114, 2, 6.233, -0.245, 2, 6.267, -0.371, 2, 6.3, -0.478, 2, 6.333, -0.552, 2, 6.367, -0.579, 2, 6.4, -0.54, 2, 6.433, -0.435, 2, 6.467, -0.283, 2, 6.5, -0.103, 2, 6.533, 0.082, 2, 6.567, 0.262, 2, 6.6, 0.414, 2, 6.633, 0.519, 2, 6.667, 0.558, 2, 6.7, 0.528, 2, 6.733, 0.445, 2, 6.767, 0.326, 2, 6.8, 0.185, 2, 6.833, 0.039, 2, 6.867, -0.102, 2, 6.9, -0.221, 2, 6.933, -0.303, 2, 6.967, -0.334, 2, 7, -0.32, 2, 7.033, -0.283, 2, 7.067, -0.227, 2, 7.1, -0.16, 2, 7.133, -0.087, 2, 7.167, -0.015, 2, 7.2, 0.052, 2, 7.233, 0.108, 2, 7.267, 0.145, 2, 7.3, 0.159, 2, 7.333, 0.153, 2, 7.367, 0.136, 2, 7.4, 0.11, 2, 7.433, 0.08, 2, 7.467, 0.047, 2, 7.5, 0.014, 2, 7.533, -0.017, 2, 7.567, -0.042, 2, 7.6, -0.059, 2, 7.633, -0.066, 2, 7.667, -0.063, 2, 7.7, -0.058, 2, 7.733, -0.049, 2, 7.767, -0.038, 2, 7.8, -0.026, 2, 7.833, -0.014, 2, 7.867, -0.002, 2, 7.9, 0.008, 2, 7.933, 0.017, 2, 7.967, 0.023, 2, 8, 0.025, 2, 8.033, 0.024, 2, 8.067, 0.021, 2, 8.1, 0.017, 2, 8.133, 0.013, 2, 8.167, 0.008, 2, 8.2, 0.003, 2, 8.233, -0.002, 2, 8.267, -0.005, 2, 8.3, -0.008, 2, 8.333, -0.009, 2, 8.367, -0.009, 2, 8.4, -0.008, 2, 8.433, -0.006, 2, 8.467, -0.005, 2, 8.5, -0.003, 2, 8.533, -0.001, 2, 8.567, 0, 2, 8.6, 0.002, 2, 8.633, 0.003, 2, 8.667, 0.003, 2, 8.7, 0.003, 2, 8.733, 0.002, 2, 8.767, 0.002, 2, 8.8, 0.001, 2, 8.833, 0.001, 2, 8.867, 0, 2, 8.9, 0, 2, 8.933, -0.001, 2, 8.967, -0.001, 2, 9.067, -0.001, 2, 9.1, -0.001, 2, 9.133, -0.001, 2, 9.167, 0, 2, 9.2, 0, 2, 9.233, 0, 2, 9.267, 0, 2, 9.3, 0, 2, 9.333, 0, 2, 9.367, 0, 2, 9.433, 0, 2, 9.467, 0, 2, 9.533, 0, 2, 9.6, 0, 2, 9.633, 0, 2, 9.667, 0, 2, 9.7, 0, 2, 9.8, 0, 2, 9.833, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh2", "Segments": [0, -0.004, 2, 0.033, -0.005, 2, 0.067, -0.008, 2, 0.1, -0.009, 2, 0.133, -0.009, 2, 0.167, -0.008, 2, 0.2, -0.006, 2, 0.233, -0.005, 2, 0.267, -0.003, 2, 0.3, -0.001, 2, 0.333, 0, 2, 0.367, 0.002, 2, 0.4, 0.003, 2, 0.433, 0.003, 2, 0.467, -0.003, 2, 0.5, -0.019, 2, 0.533, -0.043, 2, 0.567, -0.072, 2, 0.6, -0.103, 2, 0.633, -0.134, 2, 0.667, -0.163, 2, 0.7, -0.187, 2, 0.733, -0.203, 2, 0.767, -0.209, 2, 0.8, -0.161, 2, 0.833, -0.031, 2, 0.867, 0.165, 2, 0.9, 0.4, 2, 0.933, 0.654, 2, 0.967, 0.909, 2, 1, 1.143, 2, 1.033, 1.34, 2, 1.067, 1.47, 2, 1.1, 1.518, 2, 1.133, 1.423, 2, 1.167, 1.17, 2, 1.2, 0.784, 2, 1.233, 0.318, 2, 1.267, -0.213, 2, 1.3, -0.751, 2, 1.333, -1.281, 2, 1.367, -1.748, 2, 1.4, -2.133, 2, 1.433, -2.386, 2, 1.467, -2.481, 2, 1.5, -2.377, 2, 1.533, -2.099, 2, 1.567, -1.668, 2, 1.6, -1.127, 2, 1.633, -0.528, 2, 1.667, 0.121, 2, 1.7, 0.771, 2, 1.733, 1.37, 2, 1.767, 1.91, 2, 1.8, 2.341, 2, 1.833, 2.62, 2, 1.867, 2.724, 2, 1.9, 2.585, 2, 1.933, 2.209, 2, 1.967, 1.642, 2, 2, 0.966, 2, 2.033, 0.231, 2, 2.067, -0.504, 2, 2.1, -1.18, 2, 2.133, -1.747, 2, 2.167, -2.124, 2, 2.2, -2.262, 2, 2.233, -2.176, 2, 2.267, -1.945, 2, 2.3, -1.593, 2, 2.333, -1.168, 2, 2.367, -0.683, 2, 2.4, -0.193, 2, 2.433, 0.291, 2, 2.467, 0.717, 2, 2.5, 1.068, 2, 2.533, 1.299, 2, 2.567, 1.386, 2, 2.6, 1.321, 2, 2.633, 1.145, 2, 2.667, 0.879, 2, 2.7, 0.562, 2, 2.733, 0.218, 2, 2.767, -0.126, 2, 2.8, -0.443, 2, 2.833, -0.709, 2, 2.867, -0.885, 2, 2.9, -0.95, 2, 2.933, -0.899, 2, 2.967, -0.761, 2, 3, -0.554, 2, 3.033, -0.306, 2, 3.067, -0.037, 2, 3.1, 0.232, 2, 3.133, 0.48, 2, 3.167, 0.688, 2, 3.2, 0.825, 2, 3.233, 0.876, 2, 3.267, 0.832, 2, 3.3, 0.714, 2, 3.333, 0.534, 2, 3.367, 0.321, 2, 3.4, 0.089, 2, 3.433, -0.143, 2, 3.467, -0.357, 2, 3.5, -0.536, 2, 3.533, -0.655, 2, 3.567, -0.698, 2, 3.6, -0.667, 2, 3.633, -0.58, 2, 3.667, -0.45, 2, 3.7, -0.295, 2, 3.733, -0.126, 2, 3.767, 0.042, 2, 3.8, 0.198, 2, 3.833, 0.328, 2, 3.867, 0.414, 2, 3.9, 0.446, 2, 3.933, 0.427, 2, 3.967, 0.376, 2, 4, 0.299, 2, 4.033, 0.207, 2, 4.067, 0.107, 2, 4.1, 0.007, 2, 4.133, -0.085, 2, 4.167, -0.162, 2, 4.2, -0.213, 2, 4.233, -0.232, 2, 4.267, -0.222, 2, 4.3, -0.197, 2, 4.333, -0.158, 2, 4.367, -0.112, 2, 4.4, -0.062, 2, 4.433, -0.012, 2, 4.467, 0.035, 2, 4.5, 0.073, 2, 4.533, 0.099, 2, 4.567, 0.108, 2, 4.6, 0.104, 2, 4.633, 0.093, 2, 4.667, 0.076, 2, 4.7, 0.056, 2, 4.733, 0.034, 2, 4.767, 0.012, 2, 4.8, -0.009, 2, 4.833, -0.026, 2, 4.867, -0.037, 2, 4.9, -0.041, 2, 4.933, -0.039, 2, 4.967, -0.035, 2, 5, -0.027, 2, 5.033, -0.019, 2, 5.067, -0.009, 2, 5.1, 0, 2, 5.133, 0.009, 2, 5.167, 0.016, 2, 5.2, 0.021, 2, 5.233, 0.022, 2, 5.267, 0.022, 2, 5.3, 0.02, 2, 5.333, 0.017, 2, 5.367, 0.014, 2, 5.4, 0.01, 2, 5.433, 0.007, 2, 5.467, 0.004, 2, 5.5, 0.001, 2, 5.533, -0.001, 2, 5.567, -0.001, 2, 5.6, 0, 2, 5.633, 0.006, 2, 5.667, 0.014, 2, 5.7, 0.024, 2, 5.733, 0.037, 2, 5.767, 0.051, 2, 5.8, 0.067, 2, 5.833, 0.083, 2, 5.867, 0.1, 2, 5.9, 0.117, 2, 5.933, 0.133, 2, 5.967, 0.149, 2, 6, 0.163, 2, 6.033, 0.176, 2, 6.067, 0.186, 2, 6.1, 0.195, 2, 6.133, 0.2, 2, 6.167, 0.202, 2, 6.2, 0.173, 2, 6.233, 0.098, 2, 6.267, -0.011, 2, 6.3, -0.14, 2, 6.333, -0.274, 2, 6.367, -0.403, 2, 6.4, -0.512, 2, 6.433, -0.588, 2, 6.467, -0.616, 2, 6.5, -0.579, 2, 6.533, -0.48, 2, 6.567, -0.331, 2, 6.6, -0.153, 2, 6.633, 0.04, 2, 6.667, 0.234, 2, 6.7, 0.411, 2, 6.733, 0.561, 2, 6.767, 0.66, 2, 6.8, 0.696, 2, 6.833, 0.655, 2, 6.867, 0.544, 2, 6.9, 0.383, 2, 6.933, 0.193, 2, 6.967, -0.004, 2, 7, -0.194, 2, 7.033, -0.354, 2, 7.067, -0.465, 2, 7.1, -0.507, 2, 7.133, -0.485, 2, 7.167, -0.425, 2, 7.2, -0.335, 2, 7.233, -0.227, 2, 7.267, -0.111, 2, 7.3, 0.006, 2, 7.333, 0.114, 2, 7.367, 0.204, 2, 7.4, 0.264, 2, 7.433, 0.286, 2, 7.467, 0.274, 2, 7.5, 0.242, 2, 7.533, 0.194, 2, 7.567, 0.137, 2, 7.6, 0.074, 2, 7.633, 0.012, 2, 7.667, -0.045, 2, 7.7, -0.094, 2, 7.733, -0.125, 2, 7.767, -0.137, 2, 7.8, -0.133, 2, 7.833, -0.12, 2, 7.867, -0.101, 2, 7.9, -0.078, 2, 7.933, -0.052, 2, 7.967, -0.026, 2, 8, 0.001, 2, 8.033, 0.024, 2, 8.067, 0.043, 2, 8.1, 0.055, 2, 8.133, 0.06, 2, 8.167, 0.057, 2, 8.2, 0.051, 2, 8.233, 0.042, 2, 8.267, 0.03, 2, 8.3, 0.018, 2, 8.333, 0.005, 2, 8.367, -0.006, 2, 8.4, -0.015, 2, 8.433, -0.022, 2, 8.467, -0.024, 2, 8.5, -0.023, 2, 8.533, -0.021, 2, 8.567, -0.017, 2, 8.6, -0.012, 2, 8.633, -0.007, 2, 8.667, -0.003, 2, 8.7, 0.002, 2, 8.733, 0.006, 2, 8.767, 0.008, 2, 8.8, 0.009, 2, 8.833, 0.009, 2, 8.867, 0.008, 2, 8.9, 0.006, 2, 8.933, 0.005, 2, 8.967, 0.003, 2, 9, 0.001, 2, 9.033, -0.001, 2, 9.067, -0.002, 2, 9.1, -0.003, 2, 9.133, -0.003, 2, 9.167, -0.003, 2, 9.2, -0.003, 2, 9.233, -0.002, 2, 9.267, -0.002, 2, 9.3, -0.001, 2, 9.333, 0, 2, 9.367, 0, 2, 9.4, 0.001, 2, 9.433, 0.001, 2, 9.467, 0.001, 2, 9.5, 0.001, 2, 9.533, 0.001, 2, 9.567, 0.001, 2, 9.6, 0.001, 2, 9.633, 0, 2, 9.667, 0, 2, 9.7, 0, 2, 9.733, 0, 2, 9.767, 0, 2, 9.9, 0, 2, 9.933, 0, 2, 9.967, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh3", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh3", "Segments": [0, 0, 2, 0.5, 0.058, 2, 0.533, 0.197, 2, 0.567, 0.366, 2, 0.6, 0.504, 2, 0.633, 0.562, 2, 0.667, 0.494, 2, 0.7, 0.31, 2, 0.733, 0.031, 2, 0.767, -0.306, 2, 0.8, -0.69, 2, 0.833, -1.079, 2, 0.867, -1.463, 2, 0.9, -1.801, 2, 0.933, -2.08, 2, 0.967, -2.263, 2, 1, -2.332, 2, 1.033, -2.286, 2, 1.067, -2.16, 2, 1.1, -1.964, 2, 1.133, -1.719, 2, 1.167, -1.427, 2, 1.2, -1.111, 2, 1.233, -0.782, 2, 1.267, -0.453, 2, 1.3, -0.137, 2, 1.333, 0.155, 2, 1.367, 0.4, 2, 1.4, 0.596, 2, 1.433, 0.722, 2, 1.467, 0.767, 2, 1.5, 0.696, 2, 1.533, 0.503, 2, 1.567, 0.225, 2, 1.6, -0.105, 2, 1.633, -0.446, 2, 1.667, -0.776, 2, 1.7, -1.055, 2, 1.733, -1.247, 2, 1.767, -1.319, 2, 1.8, -1.291, 2, 1.833, -1.217, 2, 1.867, -1.109, 2, 1.9, -0.982, 2, 1.933, -0.85, 2, 1.967, -0.723, 2, 2, -0.615, 2, 2.033, -0.541, 2, 2.067, -0.513, 2, 2.1, -0.521, 2, 2.133, -0.541, 2, 2.167, -0.57, 2, 2.2, -0.604, 2, 2.233, -0.637, 2, 2.267, -0.666, 2, 2.3, -0.687, 2, 2.333, -0.694, 2, 2.367, -0.685, 2, 2.4, -0.657, 2, 2.433, -0.616, 2, 2.467, -0.564, 2, 2.5, -0.503, 2, 2.533, -0.439, 2, 2.567, -0.371, 2, 2.6, -0.306, 2, 2.633, -0.246, 2, 2.667, -0.193, 2, 2.7, -0.152, 2, 2.733, -0.125, 2, 2.767, -0.115, 2, 2.8, -0.134, 2, 2.833, -0.187, 2, 2.867, -0.265, 2, 2.9, -0.359, 2, 2.933, -0.461, 2, 2.967, -0.563, 2, 3, -0.657, 2, 3.033, -0.736, 2, 3.067, -0.788, 2, 3.1, -0.807, 2, 3.133, -0.797, 2, 3.167, -0.771, 2, 3.2, -0.734, 2, 3.233, -0.69, 2, 3.267, -0.644, 2, 3.3, -0.6, 2, 3.333, -0.562, 2, 3.367, -0.536, 2, 3.4, -0.527, 2, 3.433, -0.529, 2, 3.467, -0.537, 2, 3.5, -0.548, 2, 3.533, -0.561, 2, 3.567, -0.576, 2, 3.6, -0.59, 2, 3.633, -0.603, 2, 3.667, -0.614, 2, 3.7, -0.622, 2, 3.733, -0.625, 2, 3.767, -0.624, 2, 3.8, -0.623, 2, 3.833, -0.621, 2, 3.867, -0.619, 2, 3.9, -0.617, 2, 3.933, -0.615, 2, 3.967, -0.615, 2, 4, -0.615, 2, 4.033, -0.616, 2, 4.067, -0.618, 2, 4.1, -0.621, 2, 4.133, -0.625, 2, 4.167, -0.629, 2, 4.2, -0.634, 2, 4.233, -0.64, 2, 4.267, -0.647, 2, 4.3, -0.654, 2, 4.333, -0.661, 2, 4.367, -0.67, 2, 4.4, -0.678, 2, 4.433, -0.688, 2, 4.467, -0.697, 2, 4.5, -0.708, 2, 4.533, -0.718, 2, 4.567, -0.729, 2, 4.6, -0.741, 2, 4.633, -0.752, 2, 4.667, -0.765, 2, 4.7, -0.777, 2, 4.733, -0.79, 2, 4.767, -0.802, 2, 4.8, -0.815, 2, 4.833, -0.829, 2, 4.867, -0.842, 2, 4.9, -0.855, 2, 4.933, -0.869, 2, 4.967, -0.882, 2, 5, -0.896, 2, 5.033, -0.91, 2, 5.067, -0.923, 2, 5.1, -0.937, 2, 5.133, -0.95, 2, 5.167, -0.963, 2, 5.2, -0.977, 2, 5.233, -0.99, 2, 5.267, -1.002, 2, 5.3, -1.015, 2, 5.333, -1.027, 2, 5.367, -1.04, 2, 5.4, -1.051, 2, 5.433, -1.063, 2, 5.467, -1.074, 2, 5.5, -1.084, 2, 5.533, -1.095, 2, 5.567, -1.104, 2, 5.6, -1.114, 2, 5.633, -1.122, 2, 5.667, -1.131, 2, 5.7, -1.138, 2, 5.733, -1.145, 2, 5.767, -1.152, 2, 5.8, -1.158, 2, 5.833, -1.163, 2, 5.867, -1.167, 2, 5.9, -1.171, 2, 5.933, -1.174, 2, 5.967, -1.176, 2, 6, -1.177, 2, 6.033, -1.177, 2, 6.067, -1.138, 2, 6.1, -1.036, 2, 6.133, -0.89, 2, 6.167, -0.724, 2, 6.2, -0.557, 2, 6.233, -0.412, 2, 6.267, -0.309, 2, 6.3, -0.27, 2, 6.333, -0.286, 2, 6.367, -0.328, 2, 6.4, -0.388, 2, 6.433, -0.46, 2, 6.467, -0.534, 2, 6.5, -0.605, 2, 6.533, -0.666, 2, 6.567, -0.708, 2, 6.6, -0.723, 2, 6.633, -0.719, 2, 6.667, -0.708, 2, 6.7, -0.692, 2, 6.733, -0.673, 2, 6.767, -0.653, 2, 6.8, -0.634, 2, 6.833, -0.618, 2, 6.867, -0.607, 2, 6.9, -0.603, 2, 6.933, -0.604, 2, 6.967, -0.606, 2, 7, -0.61, 2, 7.033, -0.614, 2, 7.067, -0.619, 2, 7.1, -0.624, 2, 7.133, -0.628, 2, 7.167, -0.632, 2, 7.2, -0.634, 2, 7.233, -0.635, 2, 7.267, -0.635, 2, 7.3, -0.634, 2, 7.333, -0.633, 2, 7.367, -0.631, 2, 7.4, -0.63, 2, 7.433, -0.629, 2, 7.467, -0.628, 2, 7.5, -0.627, 2, 7.533, -0.626, 2, 7.567, -0.627, 2, 7.6, -0.627, 2, 7.633, -0.627, 2, 7.667, -0.628, 2, 7.7, -0.628, 2, 7.733, -0.628, 2, 7.767, -0.629, 2, 7.8, -0.629, 2, 7.9, -0.629, 2, 7.933, -0.629, 2, 7.967, -0.628, 2, 8, -0.628, 2, 8.033, -0.628, 2, 8.067, -0.628, 2, 8.2, -0.628, 2, 8.233, -0.628, 2, 8.267, -0.628, 2, 8.433, -0.628, 2, 8.467, -0.628, 2, 8.533, -0.628, 2, 8.567, -0.628, 2, 8.7, -0.628, 2, 8.733, -0.628, 2, 8.767, -0.628, 2, 9.033, -0.628, 2, 9.067, -0.628, 2, 9.167, -0.628, 2, 9.2, -0.628, 2, 9.233, -0.628, 2, 9.3, -0.628, 2, 9.533, -0.628, 2, 9.567, -0.628, 2, 9.6, -0.628, 2, 9.733, -0.628, 2, 9.767, -0.628, 2, 9.833, -0.628, 2, 9.9, -0.628, 2, 9.967, -0.628, 2, 10, -0.628]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh3", "Segments": [0, 0, 2, 0.067, 0, 2, 0.1, 0, 2, 0.133, 0, 2, 0.167, 0, 2, 0.5, -0.049, 2, 0.533, -0.156, 2, 0.567, -0.263, 2, 0.6, -0.312, 2, 0.633, -0.233, 2, 0.667, -0.029, 2, 0.7, 0.254, 2, 0.733, 0.553, 2, 0.767, 0.836, 2, 0.8, 1.04, 2, 0.833, 1.119, 2, 0.867, 1.062, 2, 0.9, 0.907, 2, 0.933, 0.675, 2, 0.967, 0.397, 2, 1, 0.095, 2, 1.033, -0.207, 2, 1.067, -0.484, 2, 1.1, -0.717, 2, 1.133, -0.871, 2, 1.167, -0.928, 2, 1.2, -0.899, 2, 1.233, -0.816, 2, 1.267, -0.687, 2, 1.3, -0.526, 2, 1.333, -0.334, 2, 1.367, -0.127, 2, 1.4, 0.089, 2, 1.433, 0.305, 2, 1.467, 0.512, 2, 1.5, 0.704, 2, 1.533, 0.865, 2, 1.567, 0.994, 2, 1.6, 1.077, 2, 1.633, 1.106, 2, 1.667, 1.022, 2, 1.7, 0.802, 2, 1.733, 0.49, 2, 1.767, 0.133, 2, 1.8, -0.224, 2, 1.833, -0.536, 2, 1.867, -0.756, 2, 1.9, -0.84, 2, 1.933, -0.798, 2, 1.967, -0.686, 2, 2, -0.523, 2, 2.033, -0.331, 2, 2.067, -0.132, 2, 2.1, 0.061, 2, 2.133, 0.223, 2, 2.167, 0.335, 2, 2.2, 0.377, 2, 2.233, 0.359, 2, 2.267, 0.31, 2, 2.3, 0.238, 2, 2.333, 0.154, 2, 2.367, 0.067, 2, 2.4, -0.018, 2, 2.433, -0.089, 2, 2.467, -0.138, 2, 2.5, -0.157, 2, 2.533, -0.152, 2, 2.567, -0.148, 2, 2.6, -0.161, 2, 2.633, -0.185, 2, 2.667, -0.198, 2, 2.7, -0.183, 2, 2.733, -0.144, 2, 2.767, -0.084, 2, 2.8, -0.013, 2, 2.833, 0.065, 2, 2.867, 0.142, 2, 2.9, 0.214, 2, 2.933, 0.273, 2, 2.967, 0.313, 2, 3, 0.328, 2, 3.033, 0.302, 2, 3.067, 0.234, 2, 3.1, 0.138, 2, 3.133, 0.027, 2, 3.167, -0.083, 2, 3.2, -0.179, 2, 3.233, -0.247, 2, 3.267, -0.273, 2, 3.3, -0.255, 2, 3.333, -0.209, 2, 3.367, -0.144, 2, 3.4, -0.069, 2, 3.433, 0.006, 2, 3.467, 0.071, 2, 3.5, 0.117, 2, 3.533, 0.134, 2, 3.567, 0.128, 2, 3.6, 0.111, 2, 3.633, 0.086, 2, 3.667, 0.057, 2, 3.7, 0.027, 2, 3.733, -0.002, 2, 3.767, -0.027, 2, 3.8, -0.044, 2, 3.833, -0.05, 2, 3.867, -0.048, 2, 3.9, -0.041, 2, 3.933, -0.032, 2, 3.967, -0.021, 2, 4, -0.01, 2, 4.033, 0.001, 2, 4.067, 0.01, 2, 4.1, 0.016, 2, 4.133, 0.019, 2, 4.167, 0.018, 2, 4.2, 0.016, 2, 4.233, 0.012, 2, 4.267, 0.009, 2, 4.3, 0.005, 2, 4.333, 0.001, 2, 4.367, -0.002, 2, 4.4, -0.005, 2, 4.433, -0.005, 2, 4.467, -0.005, 2, 4.5, -0.004, 2, 4.533, -0.003, 2, 4.567, -0.002, 2, 4.6, 0, 2, 4.633, 0.001, 2, 4.667, 0.003, 2, 4.7, 0.004, 2, 4.733, 0.005, 2, 4.767, 0.005, 2, 4.8, 0.005, 2, 4.833, 0.005, 2, 4.867, 0.004, 2, 4.9, 0.003, 2, 4.933, 0.003, 2, 4.967, 0.002, 2, 5, 0.002, 2, 5.033, 0.002, 2, 5.067, 0.002, 2, 5.133, 0.002, 2, 5.167, 0.002, 2, 5.2, 0.003, 2, 5.233, 0.003, 2, 5.267, 0.003, 2, 5.333, 0.003, 2, 5.367, 0.003, 2, 5.4, 0.004, 2, 5.433, 0.004, 2, 5.467, 0.004, 2, 5.5, 0.003, 2, 5.533, 0.003, 2, 5.567, 0.004, 2, 5.6, 0.004, 2, 5.633, 0.004, 2, 5.667, 0.004, 2, 5.7, 0.003, 2, 5.733, 0.005, 2, 5.767, 0.004, 2, 5.8, 0.004, 2, 5.833, 0.035, 2, 5.867, 0.104, 2, 5.9, 0.173, 2, 5.933, 0.204, 2, 5.967, 0.181, 2, 6, 0.119, 2, 6.033, 0.03, 2, 6.067, -0.077, 2, 6.1, -0.186, 2, 6.133, -0.293, 2, 6.167, -0.382, 2, 6.2, -0.444, 2, 6.233, -0.467, 2, 6.267, -0.42, 2, 6.3, -0.297, 2, 6.333, -0.127, 2, 6.367, 0.053, 2, 6.4, 0.224, 2, 6.433, 0.346, 2, 6.467, 0.394, 2, 6.5, 0.374, 2, 6.533, 0.32, 2, 6.567, 0.242, 2, 6.6, 0.15, 2, 6.633, 0.055, 2, 6.667, -0.037, 2, 6.7, -0.115, 2, 6.733, -0.169, 2, 6.767, -0.189, 2, 6.8, -0.18, 2, 6.833, -0.156, 2, 6.867, -0.121, 2, 6.9, -0.079, 2, 6.933, -0.036, 2, 6.967, 0.005, 2, 7, 0.04, 2, 7.033, 0.064, 2, 7.067, 0.073, 2, 7.1, 0.07, 2, 7.133, 0.061, 2, 7.167, 0.048, 2, 7.2, 0.032, 2, 7.233, 0.016, 2, 7.267, 0, 2, 7.3, -0.013, 2, 7.333, -0.022, 2, 7.367, -0.026, 2, 7.4, -0.025, 2, 7.433, -0.022, 2, 7.467, -0.017, 2, 7.5, -0.011, 2, 7.533, -0.006, 2, 7.567, 0, 2, 7.6, 0.004, 2, 7.633, 0.007, 2, 7.667, 0.009, 2, 7.7, 0.008, 2, 7.733, 0.007, 2, 7.767, 0.006, 2, 7.8, 0.004, 2, 7.833, 0.002, 2, 7.867, 0, 2, 7.9, -0.001, 2, 7.933, -0.002, 2, 7.967, -0.003, 2, 8, -0.003, 2, 8.033, -0.002, 2, 8.067, -0.002, 2, 8.1, -0.001, 2, 8.133, -0.001, 2, 8.167, 0, 2, 8.2, 0, 2, 8.233, 0.001, 2, 8.267, 0.001, 2, 8.3, 0.001, 2, 8.333, 0.001, 2, 8.367, 0, 2, 8.4, 0, 2, 8.433, 0, 2, 8.467, 0, 2, 8.5, 0, 2, 8.567, 0, 2, 8.6, 0, 2, 8.667, 0, 2, 8.733, 0, 2, 8.8, 0, 2, 8.867, 0, 2, 8.9, 0, 2, 8.967, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh3", "Segments": [0, 0.001, 2, 0.033, 0.001, 2, 0.067, 0, 2, 0.1, 0, 2, 0.133, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 0.233, 0, 2, 0.3, 0, 2, 0.367, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.5, -0.018, 2, 0.533, -0.06, 2, 0.567, -0.117, 2, 0.6, -0.174, 2, 0.633, -0.217, 2, 0.667, -0.234, 2, 0.7, -0.175, 2, 0.733, -0.017, 2, 0.767, 0.206, 2, 0.8, 0.462, 2, 0.833, 0.718, 2, 0.867, 0.941, 2, 0.9, 1.099, 2, 0.933, 1.159, 2, 0.967, 1.09, 2, 1, 0.901, 2, 1.033, 0.617, 2, 1.067, 0.279, 2, 1.1, -0.089, 2, 1.133, -0.457, 2, 1.167, -0.796, 2, 1.2, -1.08, 2, 1.233, -1.268, 2, 1.267, -1.338, 2, 1.3, -1.294, 2, 1.333, -1.169, 2, 1.367, -0.982, 2, 1.4, -0.743, 2, 1.433, -0.468, 2, 1.467, -0.175, 2, 1.5, 0.133, 2, 1.533, 0.426, 2, 1.567, 0.701, 2, 1.6, 0.941, 2, 1.633, 1.127, 2, 1.667, 1.252, 2, 1.7, 1.296, 2, 1.733, 1.211, 2, 1.767, 0.985, 2, 1.8, 0.658, 2, 1.833, 0.271, 2, 1.867, -0.13, 2, 1.9, -0.518, 2, 1.933, -0.845, 2, 1.967, -1.071, 2, 2, -1.155, 2, 2.033, -1.104, 2, 2.067, -0.967, 2, 2.1, -0.759, 2, 2.133, -0.512, 2, 2.167, -0.243, 2, 2.2, 0.025, 2, 2.233, 0.272, 2, 2.267, 0.48, 2, 2.3, 0.617, 2, 2.333, 0.668, 2, 2.367, 0.632, 2, 2.4, 0.537, 2, 2.433, 0.398, 2, 2.467, 0.234, 2, 2.5, 0.065, 2, 2.533, -0.099, 2, 2.567, -0.237, 2, 2.6, -0.333, 2, 2.633, -0.369, 2, 2.667, -0.356, 2, 2.7, -0.318, 2, 2.733, -0.263, 2, 2.767, -0.192, 2, 2.8, -0.11, 2, 2.833, -0.022, 2, 2.867, 0.069, 2, 2.9, 0.157, 2, 2.933, 0.239, 2, 2.967, 0.31, 2, 3, 0.365, 2, 3.033, 0.403, 2, 3.067, 0.416, 2, 3.1, 0.388, 2, 3.133, 0.315, 2, 3.167, 0.209, 2, 3.2, 0.083, 2, 3.233, -0.047, 2, 3.267, -0.173, 2, 3.3, -0.279, 2, 3.333, -0.353, 2, 3.367, -0.38, 2, 3.4, -0.359, 2, 3.433, -0.303, 2, 3.467, -0.221, 2, 3.5, -0.124, 2, 3.533, -0.023, 2, 3.567, 0.074, 2, 3.6, 0.155, 2, 3.633, 0.212, 2, 3.667, 0.233, 2, 3.7, 0.221, 2, 3.733, 0.19, 2, 3.767, 0.144, 2, 3.8, 0.089, 2, 3.833, 0.033, 2, 3.867, -0.021, 2, 3.9, -0.067, 2, 3.933, -0.099, 2, 3.967, -0.11, 2, 4, -0.105, 2, 4.033, -0.09, 2, 4.067, -0.069, 2, 4.1, -0.044, 2, 4.133, -0.018, 2, 4.167, 0.007, 2, 4.2, 0.028, 2, 4.233, 0.043, 2, 4.267, 0.048, 2, 4.3, 0.046, 2, 4.333, 0.04, 2, 4.367, 0.031, 2, 4.4, 0.02, 2, 4.433, 0.01, 2, 4.467, -0.001, 2, 4.5, -0.009, 2, 4.533, -0.016, 2, 4.567, -0.018, 2, 4.6, -0.017, 2, 4.633, -0.014, 2, 4.667, -0.01, 2, 4.7, -0.006, 2, 4.733, -0.001, 2, 4.767, 0.003, 2, 4.8, 0.007, 2, 4.833, 0.009, 2, 4.867, 0.01, 2, 4.9, 0.01, 2, 4.933, 0.009, 2, 4.967, 0.008, 2, 5, 0.006, 2, 5.033, 0.004, 2, 5.067, 0.002, 2, 5.1, 0.001, 2, 5.133, 0, 2, 5.167, 0, 2, 5.2, 0, 2, 5.233, 0, 2, 5.267, 0.001, 2, 5.3, 0.002, 2, 5.333, 0.003, 2, 5.367, 0.003, 2, 5.4, 0.004, 2, 5.433, 0.005, 2, 5.467, 0.005, 2, 5.533, 0.005, 2, 5.567, 0.004, 2, 5.6, 0.004, 2, 5.633, 0.004, 2, 5.667, 0.004, 2, 5.7, 0.004, 2, 5.8, 0.011, 2, 5.833, 0.031, 2, 5.867, 0.06, 2, 5.9, 0.092, 2, 5.933, 0.125, 2, 5.967, 0.153, 2, 6, 0.173, 2, 6.033, 0.181, 2, 6.067, 0.152, 2, 6.1, 0.077, 2, 6.133, -0.031, 2, 6.167, -0.153, 2, 6.2, -0.276, 2, 6.233, -0.383, 2, 6.267, -0.459, 2, 6.3, -0.488, 2, 6.333, -0.454, 2, 6.367, -0.363, 2, 6.4, -0.232, 2, 6.433, -0.076, 2, 6.467, 0.084, 2, 6.5, 0.24, 2, 6.533, 0.371, 2, 6.567, 0.462, 2, 6.6, 0.495, 2, 6.633, 0.461, 2, 6.667, 0.369, 2, 6.7, 0.239, 2, 6.733, 0.09, 2, 6.767, -0.059, 2, 6.8, -0.189, 2, 6.833, -0.281, 2, 6.867, -0.316, 2, 6.9, -0.299, 2, 6.933, -0.256, 2, 6.967, -0.193, 2, 7, -0.118, 2, 7.033, -0.041, 2, 7.067, 0.034, 2, 7.1, 0.097, 2, 7.133, 0.14, 2, 7.167, 0.156, 2, 7.2, 0.149, 2, 7.233, 0.128, 2, 7.267, 0.098, 2, 7.3, 0.063, 2, 7.333, 0.026, 2, 7.367, -0.009, 2, 7.4, -0.039, 2, 7.433, -0.059, 2, 7.467, -0.067, 2, 7.5, -0.064, 2, 7.533, -0.055, 2, 7.567, -0.043, 2, 7.6, -0.028, 2, 7.633, -0.013, 2, 7.667, 0.002, 2, 7.7, 0.014, 2, 7.733, 0.023, 2, 7.767, 0.026, 2, 7.8, 0.025, 2, 7.833, 0.022, 2, 7.867, 0.017, 2, 7.9, 0.011, 2, 7.933, 0.005, 2, 7.967, 0, 2, 8, -0.005, 2, 8.033, -0.008, 2, 8.067, -0.01, 2, 8.1, -0.009, 2, 8.133, -0.008, 2, 8.167, -0.006, 2, 8.2, -0.004, 2, 8.233, -0.002, 2, 8.267, 0, 2, 8.3, 0.002, 2, 8.333, 0.003, 2, 8.367, 0.003, 2, 8.433, 0.003, 2, 8.467, 0.003, 2, 8.5, 0.002, 2, 8.533, 0.001, 2, 8.567, 0, 2, 8.6, 0, 2, 8.633, -0.001, 2, 8.667, -0.001, 2, 8.733, -0.001, 2, 8.767, -0.001, 2, 8.8, -0.001, 2, 8.833, 0, 2, 8.867, 0, 2, 8.9, 0, 2, 8.933, 0, 2, 8.967, 0, 2, 9.033, 0, 2, 9.1, 0, 2, 9.133, 0, 2, 9.167, 0, 2, 9.233, 0, 2, 9.267, 0, 2, 9.3, 0, 2, 9.433, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh3", "Segments": [0, 0.004, 2, 0.033, 0.004, 2, 0.067, 0.004, 2, 0.1, 0.004, 2, 0.133, 0.003, 2, 0.167, 0.002, 2, 0.2, 0.001, 2, 0.233, 0, 2, 0.267, -0.001, 2, 0.3, -0.001, 2, 0.333, -0.002, 2, 0.367, -0.001, 2, 0.4, -0.001, 2, 0.433, -0.001, 2, 0.467, 0, 2, 0.5, -0.009, 2, 0.533, -0.031, 2, 0.567, -0.062, 2, 0.6, -0.098, 2, 0.633, -0.134, 2, 0.667, -0.165, 2, 0.7, -0.188, 2, 0.733, -0.196, 2, 0.767, -0.146, 2, 0.8, -0.014, 2, 0.833, 0.178, 2, 0.867, 0.405, 2, 0.9, 0.64, 2, 0.933, 0.867, 2, 0.967, 1.059, 2, 1, 1.192, 2, 1.033, 1.241, 2, 1.067, 1.157, 2, 1.1, 0.929, 2, 1.133, 0.585, 2, 1.167, 0.176, 2, 1.2, -0.27, 2, 1.233, -0.715, 2, 1.267, -1.125, 2, 1.3, -1.469, 2, 1.333, -1.697, 2, 1.367, -1.781, 2, 1.4, -1.712, 2, 1.433, -1.527, 2, 1.467, -1.241, 2, 1.5, -0.883, 2, 1.533, -0.486, 2, 1.567, -0.055, 2, 1.6, 0.375, 2, 1.633, 0.773, 2, 1.667, 1.131, 2, 1.7, 1.416, 2, 1.733, 1.601, 2, 1.767, 1.67, 2, 1.8, 1.579, 2, 1.833, 1.333, 2, 1.867, 0.961, 2, 1.9, 0.518, 2, 1.933, 0.036, 2, 1.967, -0.445, 2, 2, -0.888, 2, 2.033, -1.26, 2, 2.067, -1.507, 2, 2.1, -1.597, 2, 2.133, -1.523, 2, 2.167, -1.32, 2, 2.2, -1.014, 2, 2.233, -0.649, 2, 2.267, -0.252, 2, 2.3, 0.145, 2, 2.333, 0.51, 2, 2.367, 0.816, 2, 2.4, 1.019, 2, 2.433, 1.094, 2, 2.467, 1.032, 2, 2.5, 0.867, 2, 2.533, 0.629, 2, 2.567, 0.346, 2, 2.6, 0.054, 2, 2.633, -0.229, 2, 2.667, -0.467, 2, 2.7, -0.632, 2, 2.733, -0.694, 2, 2.767, -0.668, 2, 2.8, -0.6, 2, 2.833, -0.495, 2, 2.867, -0.363, 2, 2.9, -0.216, 2, 2.933, -0.057, 2, 2.967, 0.101, 2, 3, 0.248, 2, 3.033, 0.38, 2, 3.067, 0.485, 2, 3.1, 0.553, 2, 3.133, 0.579, 2, 3.167, 0.548, 2, 3.2, 0.464, 2, 3.233, 0.338, 2, 3.267, 0.188, 2, 3.3, 0.024, 2, 3.333, -0.139, 2, 3.367, -0.289, 2, 3.4, -0.416, 2, 3.433, -0.499, 2, 3.467, -0.53, 2, 3.5, -0.499, 2, 3.533, -0.415, 2, 3.567, -0.294, 2, 3.6, -0.151, 2, 3.633, -0.003, 2, 3.667, 0.14, 2, 3.7, 0.261, 2, 3.733, 0.345, 2, 3.767, 0.376, 2, 3.8, 0.356, 2, 3.833, 0.302, 2, 3.867, 0.223, 2, 3.9, 0.131, 2, 3.933, 0.035, 2, 3.967, -0.057, 2, 4, -0.135, 2, 4.033, -0.189, 2, 4.067, -0.209, 2, 4.1, -0.199, 2, 4.133, -0.17, 2, 4.167, -0.128, 2, 4.2, -0.079, 2, 4.233, -0.028, 2, 4.267, 0.022, 2, 4.3, 0.064, 2, 4.333, 0.092, 2, 4.367, 0.103, 2, 4.4, 0.098, 2, 4.433, 0.084, 2, 4.467, 0.065, 2, 4.5, 0.041, 2, 4.533, 0.017, 2, 4.567, -0.006, 2, 4.6, -0.026, 2, 4.633, -0.039, 2, 4.667, -0.044, 2, 4.7, -0.042, 2, 4.733, -0.036, 2, 4.767, -0.027, 2, 4.8, -0.016, 2, 4.833, -0.005, 2, 4.867, 0.005, 2, 4.9, 0.014, 2, 4.933, 0.02, 2, 4.967, 0.022, 2, 5.033, 0.021, 2, 5.067, 0.018, 2, 5.1, 0.014, 2, 5.133, 0.009, 2, 5.167, 0.003, 2, 5.2, -0.001, 2, 5.233, -0.004, 2, 5.267, -0.005, 2, 5.333, -0.005, 2, 5.367, -0.004, 2, 5.4, -0.002, 2, 5.433, 0, 2, 5.467, 0.002, 2, 5.5, 0.004, 2, 5.533, 0.006, 2, 5.567, 0.007, 2, 5.6, 0.007, 2, 5.633, 0.007, 2, 5.667, 0.006, 2, 5.7, 0.005, 2, 5.733, 0.004, 2, 5.767, 0.004, 2, 5.8, 0.004, 2, 5.867, 0.009, 2, 5.9, 0.024, 2, 5.933, 0.046, 2, 5.967, 0.072, 2, 6, 0.099, 2, 6.033, 0.125, 2, 6.067, 0.147, 2, 6.1, 0.162, 2, 6.133, 0.168, 2, 6.167, 0.138, 2, 6.2, 0.059, 2, 6.233, -0.052, 2, 6.267, -0.18, 2, 6.3, -0.307, 2, 6.333, -0.419, 2, 6.367, -0.497, 2, 6.4, -0.527, 2, 6.433, -0.487, 2, 6.467, -0.38, 2, 6.5, -0.225, 2, 6.533, -0.041, 2, 6.567, 0.148, 2, 6.6, 0.332, 2, 6.633, 0.487, 2, 6.667, 0.594, 2, 6.7, 0.634, 2, 6.733, 0.596, 2, 6.767, 0.493, 2, 6.8, 0.344, 2, 6.833, 0.168, 2, 6.867, -0.014, 2, 6.9, -0.19, 2, 6.933, -0.338, 2, 6.967, -0.441, 2, 7, -0.479, 2, 7.033, -0.453, 2, 7.067, -0.383, 2, 7.1, -0.282, 2, 7.133, -0.161, 2, 7.167, -0.037, 2, 7.2, 0.083, 2, 7.233, 0.185, 2, 7.267, 0.255, 2, 7.3, 0.281, 2, 7.333, 0.266, 2, 7.367, 0.228, 2, 7.4, 0.171, 2, 7.433, 0.105, 2, 7.467, 0.036, 2, 7.5, -0.031, 2, 7.533, -0.087, 2, 7.567, -0.126, 2, 7.6, -0.141, 2, 7.633, -0.134, 2, 7.667, -0.115, 2, 7.7, -0.088, 2, 7.733, -0.055, 2, 7.767, -0.022, 2, 7.8, 0.01, 2, 7.833, 0.037, 2, 7.867, 0.056, 2, 7.9, 0.063, 2, 7.933, 0.06, 2, 7.967, 0.052, 2, 8, 0.04, 2, 8.033, 0.026, 2, 8.067, 0.011, 2, 8.1, -0.003, 2, 8.133, -0.015, 2, 8.167, -0.023, 2, 8.2, -0.026, 2, 8.233, -0.025, 2, 8.267, -0.022, 2, 8.3, -0.017, 2, 8.333, -0.011, 2, 8.367, -0.005, 2, 8.4, 0.001, 2, 8.433, 0.006, 2, 8.467, 0.009, 2, 8.5, 0.01, 2, 8.533, 0.01, 2, 8.567, 0.009, 2, 8.6, 0.007, 2, 8.633, 0.004, 2, 8.667, 0.002, 2, 8.7, 0, 2, 8.733, -0.002, 2, 8.767, -0.003, 2, 8.8, -0.004, 2, 8.833, -0.004, 2, 8.867, -0.003, 2, 8.9, -0.003, 2, 8.933, -0.002, 2, 8.967, -0.001, 2, 9, 0, 2, 9.033, 0.001, 2, 9.067, 0.001, 2, 9.1, 0.001, 2, 9.167, 0.001, 2, 9.2, 0.001, 2, 9.233, 0.001, 2, 9.267, 0, 2, 9.3, 0, 2, 9.333, 0, 2, 9.367, 0, 2, 9.4, 0, 2, 9.467, 0, 2, 9.5, 0, 2, 9.533, 0, 2, 9.567, 0, 2, 9.6, 0, 2, 9.633, 0, 2, 9.7, 0, 2, 9.767, 0, 2, 9.8, 0, 2, 9.833, 0, 2, 9.867, 0, 2, 9.967, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh46", "Segments": [0, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh46", "Segments": [0, 0, 2, 0.5, 0.031, 2, 0.533, 0.106, 2, 0.567, 0.206, 2, 0.6, 0.305, 2, 0.633, 0.38, 2, 0.667, 0.411, 2, 0.7, 0.35, 2, 0.733, 0.187, 2, 0.767, -0.061, 2, 0.8, -0.361, 2, 0.833, -0.703, 2, 0.867, -1.049, 2, 0.9, -1.391, 2, 0.933, -1.692, 2, 0.967, -1.94, 2, 1, -2.103, 2, 1.033, -2.164, 2, 1.067, -2.12, 2, 1.1, -1.997, 2, 1.133, -1.806, 2, 1.167, -1.567, 2, 1.2, -1.282, 2, 1.233, -0.975, 2, 1.267, -0.654, 2, 1.3, -0.333, 2, 1.333, -0.026, 2, 1.367, 0.259, 2, 1.4, 0.498, 2, 1.433, 0.689, 2, 1.467, 0.812, 2, 1.5, 0.856, 2, 1.533, 0.812, 2, 1.567, 0.696, 2, 1.6, 0.518, 2, 1.633, 0.303, 2, 1.667, 0.058, 2, 1.7, -0.19, 2, 1.733, -0.434, 2, 1.767, -0.649, 2, 1.8, -0.827, 2, 1.833, -0.944, 2, 1.867, -0.987, 2, 1.9, -0.973, 2, 1.933, -0.936, 2, 1.967, -0.879, 2, 2, -0.81, 2, 2.033, -0.732, 2, 2.067, -0.653, 2, 2.1, -0.574, 2, 2.133, -0.505, 2, 2.167, -0.449, 2, 2.2, -0.411, 2, 2.233, -0.397, 2, 2.267, -0.4, 2, 2.3, -0.408, 2, 2.333, -0.418, 2, 2.367, -0.431, 2, 2.4, -0.445, 2, 2.433, -0.458, 2, 2.467, -0.469, 2, 2.5, -0.476, 2, 2.533, -0.479, 2, 2.567, -0.464, 2, 2.6, -0.423, 2, 2.633, -0.363, 2, 2.667, -0.293, 2, 2.7, -0.22, 2, 2.733, -0.15, 2, 2.767, -0.09, 2, 2.8, -0.049, 2, 2.833, -0.034, 2, 2.867, -0.049, 2, 2.9, -0.091, 2, 2.933, -0.153, 2, 2.967, -0.228, 2, 3, -0.309, 2, 3.033, -0.39, 2, 3.067, -0.464, 2, 3.1, -0.527, 2, 3.133, -0.568, 2, 3.167, -0.584, 2, 3.2, -0.579, 2, 3.233, -0.568, 2, 3.267, -0.55, 2, 3.3, -0.529, 2, 3.333, -0.505, 2, 3.367, -0.481, 2, 3.4, -0.457, 2, 3.433, -0.436, 2, 3.467, -0.418, 2, 3.5, -0.407, 2, 3.533, -0.402, 2, 3.567, -0.403, 2, 3.6, -0.406, 2, 3.633, -0.41, 2, 3.667, -0.416, 2, 3.7, -0.422, 2, 3.733, -0.429, 2, 3.767, -0.437, 2, 3.8, -0.444, 2, 3.833, -0.451, 2, 3.867, -0.458, 2, 3.9, -0.463, 2, 3.933, -0.467, 2, 3.967, -0.47, 2, 4, -0.471, 2, 4.033, -0.471, 2, 4.067, -0.47, 2, 4.1, -0.47, 2, 4.133, -0.469, 2, 4.167, -0.468, 2, 4.2, -0.468, 2, 4.233, -0.468, 2, 4.267, -0.47, 2, 4.3, -0.472, 2, 4.333, -0.475, 2, 4.367, -0.479, 2, 4.4, -0.483, 2, 4.433, -0.489, 2, 4.467, -0.494, 2, 4.5, -0.501, 2, 4.533, -0.508, 2, 4.567, -0.516, 2, 4.6, -0.524, 2, 4.633, -0.533, 2, 4.667, -0.543, 2, 4.7, -0.552, 2, 4.733, -0.563, 2, 4.767, -0.573, 2, 4.8, -0.584, 2, 4.833, -0.596, 2, 4.867, -0.607, 2, 4.9, -0.619, 2, 4.933, -0.631, 2, 4.967, -0.643, 2, 5, -0.656, 2, 5.033, -0.668, 2, 5.067, -0.681, 2, 5.1, -0.693, 2, 5.133, -0.706, 2, 5.167, -0.719, 2, 5.2, -0.731, 2, 5.233, -0.743, 2, 5.267, -0.756, 2, 5.3, -0.768, 2, 5.333, -0.78, 2, 5.367, -0.792, 2, 5.4, -0.804, 2, 5.433, -0.815, 2, 5.467, -0.826, 2, 5.5, -0.836, 2, 5.533, -0.847, 2, 5.567, -0.857, 2, 5.6, -0.866, 2, 5.633, -0.875, 2, 5.667, -0.883, 2, 5.7, -0.891, 2, 5.733, -0.898, 2, 5.767, -0.905, 2, 5.8, -0.911, 2, 5.833, -0.916, 2, 5.867, -0.921, 2, 5.9, -0.924, 2, 5.933, -0.927, 2, 5.967, -0.93, 2, 6, -0.931, 2, 6.033, -0.931, 2, 6.067, -0.912, 2, 6.1, -0.859, 2, 6.133, -0.779, 2, 6.167, -0.683, 2, 6.2, -0.58, 2, 6.233, -0.476, 2, 6.267, -0.381, 2, 6.3, -0.301, 2, 6.333, -0.247, 2, 6.367, -0.228, 2, 6.4, -0.235, 2, 6.433, -0.254, 2, 6.467, -0.283, 2, 6.5, -0.319, 2, 6.533, -0.359, 2, 6.567, -0.399, 2, 6.6, -0.44, 2, 6.633, -0.475, 2, 6.667, -0.504, 2, 6.7, -0.523, 2, 6.733, -0.53, 2, 6.767, -0.529, 2, 6.8, -0.525, 2, 6.833, -0.519, 2, 6.867, -0.511, 2, 6.9, -0.503, 2, 6.933, -0.493, 2, 6.967, -0.484, 2, 7, -0.476, 2, 7.033, -0.468, 2, 7.067, -0.462, 2, 7.1, -0.458, 2, 7.133, -0.457, 2, 7.167, -0.457, 2, 7.2, -0.458, 2, 7.233, -0.46, 2, 7.267, -0.462, 2, 7.3, -0.464, 2, 7.333, -0.467, 2, 7.367, -0.469, 2, 7.4, -0.471, 2, 7.433, -0.473, 2, 7.467, -0.474, 2, 7.5, -0.475, 2, 7.567, -0.475, 2, 7.6, -0.474, 2, 7.633, -0.474, 2, 7.667, -0.473, 2, 7.7, -0.473, 2, 7.733, -0.472, 2, 7.767, -0.471, 2, 7.8, -0.471, 2, 7.833, -0.471, 2, 7.867, -0.47, 2, 7.967, -0.47, 2, 8, -0.471, 2, 8.033, -0.471, 2, 8.067, -0.471, 2, 8.1, -0.471, 2, 8.133, -0.471, 2, 8.167, -0.471, 2, 8.2, -0.471, 2, 8.267, -0.471, 2, 8.333, -0.471, 2, 8.433, -0.471, 2, 8.533, -0.471, 2, 8.8, -0.471, 2, 8.867, -0.471, 2, 9, -0.471, 2, 9.067, -0.471, 2, 9.1, -0.471, 2, 9.133, -0.471, 2, 9.167, -0.471, 2, 9.2, -0.471, 2, 9.233, -0.471, 2, 9.267, -0.471, 2, 9.3, -0.471, 2, 9.333, -0.471, 2, 9.467, -0.471, 2, 9.5, -0.471, 2, 9.7, -0.471, 2, 9.733, -0.471, 2, 9.833, -0.471, 2, 9.867, -0.471, 2, 10, -0.471]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh46", "Segments": [0, 0, 2, 0.033, 0, 2, 0.067, 0, 2, 0.1, 0, 2, 0.167, 0, 2, 0.233, 0, 2, 0.267, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, -0.027, 2, 0.533, -0.091, 2, 0.567, -0.169, 2, 0.6, -0.232, 2, 0.633, -0.259, 2, 0.667, -0.201, 2, 0.7, -0.047, 2, 0.733, 0.171, 2, 0.767, 0.42, 2, 0.8, 0.67, 2, 0.833, 0.888, 2, 0.867, 1.042, 2, 0.9, 1.1, 2, 0.933, 1.061, 2, 0.967, 0.948, 2, 1, 0.78, 2, 1.033, 0.565, 2, 1.067, 0.317, 2, 1.1, 0.053, 2, 1.133, -0.225, 2, 1.167, -0.489, 2, 1.2, -0.737, 2, 1.233, -0.952, 2, 1.267, -1.12, 2, 1.3, -1.233, 2, 1.333, -1.272, 2, 1.367, -1.208, 2, 1.4, -1.038, 2, 1.433, -0.778, 2, 1.467, -0.463, 2, 1.5, -0.106, 2, 1.533, 0.257, 2, 1.567, 0.615, 2, 1.6, 0.929, 2, 1.633, 1.189, 2, 1.667, 1.36, 2, 1.7, 1.424, 2, 1.733, 1.363, 2, 1.767, 1.2, 2, 1.8, 0.953, 2, 1.833, 0.659, 2, 1.867, 0.34, 2, 1.9, 0.02, 2, 1.933, -0.274, 2, 1.967, -0.52, 2, 2, -0.684, 2, 2.033, -0.744, 2, 2.067, -0.72, 2, 2.1, -0.656, 2, 2.133, -0.558, 2, 2.167, -0.439, 2, 2.2, -0.304, 2, 2.233, -0.168, 2, 2.267, -0.033, 2, 2.3, 0.085, 2, 2.333, 0.183, 2, 2.367, 0.247, 2, 2.4, 0.272, 2, 2.433, 0.256, 2, 2.467, 0.215, 2, 2.5, 0.152, 2, 2.533, 0.077, 2, 2.567, -0.004, 2, 2.6, -0.085, 2, 2.633, -0.159, 2, 2.667, -0.222, 2, 2.7, -0.264, 2, 2.733, -0.279, 2, 2.767, -0.261, 2, 2.8, -0.214, 2, 2.833, -0.142, 2, 2.867, -0.056, 2, 2.9, 0.038, 2, 2.933, 0.131, 2, 2.967, 0.217, 2, 3, 0.289, 2, 3.033, 0.336, 2, 3.067, 0.354, 2, 3.1, 0.338, 2, 3.133, 0.296, 2, 3.167, 0.233, 2, 3.2, 0.157, 2, 3.233, 0.075, 2, 3.267, -0.008, 2, 3.3, -0.084, 2, 3.333, -0.147, 2, 3.367, -0.189, 2, 3.4, -0.205, 2, 3.433, -0.197, 2, 3.467, -0.174, 2, 3.5, -0.141, 2, 3.533, -0.101, 2, 3.567, -0.058, 2, 3.6, -0.015, 2, 3.633, 0.025, 2, 3.667, 0.058, 2, 3.7, 0.081, 2, 3.733, 0.089, 2, 3.767, 0.086, 2, 3.8, 0.08, 2, 3.833, 0.07, 2, 3.867, 0.058, 2, 3.9, 0.045, 2, 3.933, 0.03, 2, 3.967, 0.015, 2, 4, 0.002, 2, 4.033, -0.01, 2, 4.067, -0.02, 2, 4.1, -0.026, 2, 4.133, -0.029, 2, 4.167, -0.028, 2, 4.2, -0.025, 2, 4.233, -0.021, 2, 4.267, -0.017, 2, 4.3, -0.012, 2, 4.333, -0.006, 2, 4.367, -0.001, 2, 4.4, 0.003, 2, 4.433, 0.007, 2, 4.467, 0.009, 2, 4.5, 0.01, 2, 4.533, 0.01, 2, 4.567, 0.009, 2, 4.6, 0.008, 2, 4.633, 0.007, 2, 4.667, 0.006, 2, 4.7, 0.005, 2, 4.733, 0.003, 2, 4.767, 0.002, 2, 4.8, 0.002, 2, 4.833, 0.001, 2, 4.9, 0.001, 2, 4.933, 0.002, 2, 4.967, 0.002, 2, 5, 0.002, 2, 5.033, 0.003, 2, 5.067, 0.003, 2, 5.1, 0.004, 2, 5.133, 0.004, 2, 5.167, 0.005, 2, 5.2, 0.005, 2, 5.233, 0.005, 2, 5.3, 0.005, 2, 5.367, 0.005, 2, 5.4, 0.005, 2, 5.433, 0.005, 2, 5.467, 0.005, 2, 5.5, 0.005, 2, 5.533, 0.005, 2, 5.567, 0.005, 2, 5.633, 0.005, 2, 5.667, 0.005, 2, 5.733, 0.006, 2, 5.767, 0.006, 2, 5.8, 0.006, 2, 5.833, 0.025, 2, 5.867, 0.07, 2, 5.9, 0.126, 2, 5.933, 0.171, 2, 5.967, 0.19, 2, 6, 0.169, 2, 6.033, 0.112, 2, 6.067, 0.03, 2, 6.1, -0.068, 2, 6.133, -0.168, 2, 6.167, -0.266, 2, 6.2, -0.348, 2, 6.233, -0.405, 2, 6.267, -0.426, 2, 6.3, -0.402, 2, 6.333, -0.337, 2, 6.367, -0.242, 2, 6.4, -0.131, 2, 6.433, -0.015, 2, 6.467, 0.097, 2, 6.5, 0.191, 2, 6.533, 0.256, 2, 6.567, 0.281, 2, 6.6, 0.271, 2, 6.633, 0.246, 2, 6.667, 0.207, 2, 6.7, 0.16, 2, 6.733, 0.106, 2, 6.767, 0.052, 2, 6.8, -0.001, 2, 6.833, -0.048, 2, 6.867, -0.087, 2, 6.9, -0.112, 2, 6.933, -0.122, 2, 6.967, -0.118, 2, 7, -0.107, 2, 7.033, -0.091, 2, 7.067, -0.072, 2, 7.1, -0.05, 2, 7.133, -0.028, 2, 7.167, -0.006, 2, 7.2, 0.013, 2, 7.233, 0.029, 2, 7.267, 0.039, 2, 7.3, 0.043, 2, 7.333, 0.042, 2, 7.367, 0.039, 2, 7.4, 0.034, 2, 7.433, 0.028, 2, 7.467, 0.022, 2, 7.5, 0.015, 2, 7.533, 0.008, 2, 7.567, 0.001, 2, 7.6, -0.005, 2, 7.633, -0.01, 2, 7.667, -0.013, 2, 7.7, -0.014, 2, 7.733, -0.013, 2, 7.767, -0.012, 2, 7.8, -0.011, 2, 7.833, -0.008, 2, 7.867, -0.006, 2, 7.9, -0.004, 2, 7.933, -0.001, 2, 7.967, 0.001, 2, 8, 0.003, 2, 8.033, 0.004, 2, 8.067, 0.004, 2, 8.1, 0.004, 2, 8.133, 0.004, 2, 8.167, 0.003, 2, 8.2, 0.002, 2, 8.233, 0.002, 2, 8.267, 0.001, 2, 8.3, 0, 2, 8.333, -0.001, 2, 8.367, -0.001, 2, 8.4, -0.001, 2, 8.5, -0.001, 2, 8.533, -0.001, 2, 8.567, -0.001, 2, 8.6, -0.001, 2, 8.633, 0, 2, 8.667, 0, 2, 8.7, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 8.8, 0, 2, 8.867, 0, 2, 8.933, 0, 2, 9, 0, 2, 9.033, 0, 2, 9.1, 0, 2, 9.133, 0, 2, 9.233, 0, 2, 9.3, 0, 2, 9.333, 0, 2, 9.367, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh46", "Segments": [0, 0.003, 2, 0.033, 0.003, 2, 0.067, 0.003, 2, 0.1, 0.002, 2, 0.133, 0.002, 2, 0.167, 0.001, 2, 0.2, 0.001, 2, 0.233, 0, 2, 0.267, 0, 2, 0.3, -0.001, 2, 0.333, -0.001, 2, 0.367, -0.001, 2, 0.4, -0.001, 2, 0.467, -0.001, 2, 0.5, -0.01, 2, 0.533, -0.036, 2, 0.567, -0.071, 2, 0.6, -0.108, 2, 0.633, -0.142, 2, 0.667, -0.168, 2, 0.7, -0.177, 2, 0.733, -0.142, 2, 0.767, -0.046, 2, 0.8, 0.098, 2, 0.833, 0.271, 2, 0.867, 0.458, 2, 0.9, 0.645, 2, 0.933, 0.817, 2, 0.967, 0.962, 2, 1, 1.058, 2, 1.033, 1.093, 2, 1.067, 1.039, 2, 1.1, 0.892, 2, 1.133, 0.666, 2, 1.167, 0.381, 2, 1.2, 0.066, 2, 1.233, -0.275, 2, 1.267, -0.617, 2, 1.3, -0.931, 2, 1.333, -1.216, 2, 1.367, -1.442, 2, 1.4, -1.589, 2, 1.433, -1.643, 2, 1.467, -1.576, 2, 1.5, -1.395, 2, 1.533, -1.115, 2, 1.567, -0.764, 2, 1.6, -0.376, 2, 1.633, 0.046, 2, 1.667, 0.467, 2, 1.7, 0.856, 2, 1.733, 1.207, 2, 1.767, 1.487, 2, 1.8, 1.668, 2, 1.833, 1.735, 2, 1.867, 1.667, 2, 1.9, 1.485, 2, 1.933, 1.209, 2, 1.967, 0.874, 2, 2, 0.493, 2, 2.033, 0.107, 2, 2.067, -0.274, 2, 2.1, -0.609, 2, 2.133, -0.886, 2, 2.167, -1.067, 2, 2.2, -1.135, 2, 2.233, -1.096, 2, 2.267, -0.99, 2, 2.3, -0.83, 2, 2.333, -0.635, 2, 2.367, -0.414, 2, 2.4, -0.19, 2, 2.433, 0.031, 2, 2.467, 0.225, 2, 2.5, 0.386, 2, 2.533, 0.491, 2, 2.567, 0.531, 2, 2.6, 0.506, 2, 2.633, 0.44, 2, 2.667, 0.339, 2, 2.7, 0.219, 2, 2.733, 0.089, 2, 2.767, -0.041, 2, 2.8, -0.161, 2, 2.833, -0.262, 2, 2.867, -0.328, 2, 2.9, -0.353, 2, 2.933, -0.328, 2, 2.967, -0.261, 2, 3, -0.163, 2, 3.033, -0.048, 2, 3.067, 0.071, 2, 3.1, 0.186, 2, 3.133, 0.283, 2, 3.167, 0.351, 2, 3.2, 0.376, 2, 3.233, 0.36, 2, 3.267, 0.319, 2, 3.3, 0.257, 2, 3.333, 0.181, 2, 3.367, 0.095, 2, 3.4, 0.007, 2, 3.433, -0.079, 2, 3.467, -0.155, 2, 3.5, -0.217, 2, 3.533, -0.259, 2, 3.567, -0.274, 2, 3.6, -0.262, 2, 3.633, -0.23, 2, 3.667, -0.182, 2, 3.7, -0.125, 2, 3.733, -0.062, 2, 3.767, 0, 2, 3.8, 0.058, 2, 3.833, 0.106, 2, 3.867, 0.138, 2, 3.9, 0.15, 2, 3.933, 0.145, 2, 3.967, 0.134, 2, 4, 0.116, 2, 4.033, 0.094, 2, 4.067, 0.07, 2, 4.1, 0.043, 2, 4.133, 0.017, 2, 4.167, -0.008, 2, 4.2, -0.03, 2, 4.233, -0.047, 2, 4.267, -0.059, 2, 4.3, -0.063, 2, 4.333, -0.061, 2, 4.367, -0.055, 2, 4.4, -0.047, 2, 4.433, -0.036, 2, 4.467, -0.024, 2, 4.5, -0.013, 2, 4.533, -0.001, 2, 4.567, 0.01, 2, 4.6, 0.018, 2, 4.633, 0.024, 2, 4.667, 0.026, 2, 4.7, 0.025, 2, 4.733, 0.023, 2, 4.767, 0.021, 2, 4.8, 0.017, 2, 4.833, 0.013, 2, 4.867, 0.009, 2, 4.9, 0.005, 2, 4.933, 0.002, 2, 4.967, -0.001, 2, 5, -0.003, 2, 5.033, -0.004, 2, 5.067, -0.004, 2, 5.1, -0.003, 2, 5.133, -0.002, 2, 5.167, -0.001, 2, 5.2, 0, 2, 5.233, 0.002, 2, 5.267, 0.003, 2, 5.3, 0.004, 2, 5.333, 0.005, 2, 5.367, 0.006, 2, 5.4, 0.007, 2, 5.433, 0.007, 2, 5.467, 0.007, 2, 5.5, 0.007, 2, 5.533, 0.006, 2, 5.567, 0.006, 2, 5.6, 0.006, 2, 5.633, 0.005, 2, 5.667, 0.005, 2, 5.7, 0.005, 2, 5.8, 0.009, 2, 5.833, 0.021, 2, 5.867, 0.038, 2, 5.9, 0.059, 2, 5.933, 0.082, 2, 5.967, 0.104, 2, 6, 0.125, 2, 6.033, 0.143, 2, 6.067, 0.154, 2, 6.1, 0.159, 2, 6.133, 0.139, 2, 6.167, 0.088, 2, 6.2, 0.013, 2, 6.233, -0.076, 2, 6.267, -0.167, 2, 6.3, -0.256, 2, 6.333, -0.33, 2, 6.367, -0.382, 2, 6.4, -0.401, 2, 6.433, -0.38, 2, 6.467, -0.323, 2, 6.5, -0.237, 2, 6.533, -0.135, 2, 6.567, -0.023, 2, 6.6, 0.088, 2, 6.633, 0.191, 2, 6.667, 0.277, 2, 6.7, 0.334, 2, 6.733, 0.355, 2, 6.767, 0.342, 2, 6.8, 0.307, 2, 6.833, 0.253, 2, 6.867, 0.188, 2, 6.9, 0.114, 2, 6.933, 0.039, 2, 6.967, -0.034, 2, 7, -0.099, 2, 7.033, -0.153, 2, 7.067, -0.188, 2, 7.1, -0.201, 2, 7.133, -0.194, 2, 7.167, -0.176, 2, 7.2, -0.148, 2, 7.233, -0.114, 2, 7.267, -0.075, 2, 7.3, -0.036, 2, 7.333, 0.003, 2, 7.367, 0.037, 2, 7.4, 0.065, 2, 7.433, 0.083, 2, 7.467, 0.09, 2, 7.5, 0.087, 2, 7.533, 0.079, 2, 7.567, 0.067, 2, 7.6, 0.053, 2, 7.633, 0.036, 2, 7.667, 0.019, 2, 7.7, 0.002, 2, 7.733, -0.012, 2, 7.767, -0.024, 2, 7.8, -0.032, 2, 7.833, -0.035, 2, 7.867, -0.034, 2, 7.9, -0.032, 2, 7.933, -0.028, 2, 7.967, -0.023, 2, 8, -0.017, 2, 8.033, -0.011, 2, 8.067, -0.005, 2, 8.1, 0, 2, 8.133, 0.005, 2, 8.167, 0.009, 2, 8.2, 0.012, 2, 8.233, 0.013, 2, 8.267, 0.012, 2, 8.3, 0.011, 2, 8.333, 0.01, 2, 8.367, 0.008, 2, 8.4, 0.005, 2, 8.433, 0.003, 2, 8.467, 0.001, 2, 8.5, -0.001, 2, 8.533, -0.003, 2, 8.567, -0.004, 2, 8.6, -0.004, 2, 8.633, -0.004, 2, 8.667, -0.004, 2, 8.7, -0.003, 2, 8.733, -0.003, 2, 8.767, -0.002, 2, 8.8, -0.001, 2, 8.833, 0, 2, 8.867, 0, 2, 8.9, 0.001, 2, 8.933, 0.001, 2, 8.967, 0.001, 2, 9.033, 0.001, 2, 9.067, 0.001, 2, 9.1, 0.001, 2, 9.133, 0.001, 2, 9.167, 0, 2, 9.2, 0, 2, 9.233, 0, 2, 9.267, 0, 2, 9.3, 0, 2, 9.367, 0, 2, 9.4, 0, 2, 9.467, 0, 2, 9.5, 0, 2, 9.533, 0, 2, 9.6, 0, 2, 9.633, 0, 2, 9.767, 0, 2, 9.8, 0, 2, 9.833, 0, 2, 9.867, 0, 2, 9.9, 0, 2, 9.933, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh46", "Segments": [0, -0.002, 2, 0.033, 0, 2, 0.067, 0.004, 2, 0.1, 0.008, 2, 0.133, 0.012, 2, 0.167, 0.014, 2, 0.2, 0.012, 2, 0.233, 0.008, 2, 0.267, 0.001, 2, 0.3, -0.008, 2, 0.333, -0.019, 2, 0.367, -0.032, 2, 0.4, -0.046, 2, 0.433, -0.06, 2, 0.467, -0.075, 2, 0.5, -0.09, 2, 0.533, -0.106, 2, 0.567, -0.12, 2, 0.6, -0.134, 2, 0.633, -0.146, 2, 0.667, -0.157, 2, 0.7, -0.166, 2, 0.733, -0.173, 2, 0.767, -0.178, 2, 0.8, -0.179, 2, 0.833, -0.14, 2, 0.867, -0.034, 2, 0.9, 0.126, 2, 0.933, 0.321, 2, 0.967, 0.542, 2, 1, 0.766, 2, 1.033, 0.987, 2, 1.067, 1.182, 2, 1.1, 1.343, 2, 1.133, 1.448, 2, 1.167, 1.488, 2, 1.2, 1.405, 2, 1.233, 1.183, 2, 1.267, 0.841, 2, 1.3, 0.411, 2, 1.333, -0.066, 2, 1.367, -0.583, 2, 1.4, -1.099, 2, 1.433, -1.576, 2, 1.467, -2.006, 2, 1.5, -2.349, 2, 1.533, -2.57, 2, 1.567, -2.653, 2, 1.6, -2.541, 2, 1.633, -2.242, 2, 1.667, -1.781, 2, 1.7, -1.201, 2, 1.733, -0.558, 2, 1.767, 0.139, 2, 1.8, 0.835, 2, 1.833, 1.478, 2, 1.867, 2.058, 2, 1.9, 2.52, 2, 1.933, 2.819, 2, 1.967, 2.93, 2, 2, 2.809, 2, 2.033, 2.485, 2, 2.067, 1.991, 2, 2.1, 1.394, 2, 2.133, 0.714, 2, 2.167, 0.025, 2, 2.2, -0.655, 2, 2.233, -1.252, 2, 2.267, -1.746, 2, 2.3, -2.07, 2, 2.333, -2.191, 2, 2.367, -2.111, 2, 2.4, -1.897, 2, 2.433, -1.572, 2, 2.467, -1.178, 2, 2.5, -0.73, 2, 2.533, -0.277, 2, 2.567, 0.171, 2, 2.6, 0.565, 2, 2.633, 0.89, 2, 2.667, 1.104, 2, 2.7, 1.184, 2, 2.733, 1.14, 2, 2.767, 1.023, 2, 2.8, 0.845, 2, 2.833, 0.63, 2, 2.867, 0.384, 2, 2.9, 0.136, 2, 2.933, -0.109, 2, 2.967, -0.325, 2, 3, -0.503, 2, 3.033, -0.62, 2, 3.067, -0.664, 2, 3.1, -0.62, 2, 3.133, -0.505, 2, 3.167, -0.338, 2, 3.2, -0.14, 2, 3.233, 0.065, 2, 3.267, 0.263, 2, 3.3, 0.431, 2, 3.333, 0.546, 2, 3.367, 0.589, 2, 3.4, 0.56, 2, 3.433, 0.479, 2, 3.467, 0.358, 2, 3.5, 0.214, 2, 3.533, 0.057, 2, 3.567, -0.099, 2, 3.6, -0.244, 2, 3.633, -0.364, 2, 3.667, -0.445, 2, 3.7, -0.474, 2, 3.733, -0.456, 2, 3.767, -0.406, 2, 3.8, -0.331, 2, 3.833, -0.24, 2, 3.867, -0.136, 2, 3.9, -0.031, 2, 3.933, 0.073, 2, 3.967, 0.164, 2, 4, 0.239, 2, 4.033, 0.288, 2, 4.067, 0.307, 2, 4.1, 0.296, 2, 4.133, 0.267, 2, 4.167, 0.222, 2, 4.2, 0.168, 2, 4.233, 0.107, 2, 4.267, 0.045, 2, 4.3, -0.017, 2, 4.333, -0.071, 2, 4.367, -0.115, 2, 4.4, -0.144, 2, 4.433, -0.155, 2, 4.467, -0.151, 2, 4.5, -0.139, 2, 4.533, -0.12, 2, 4.567, -0.096, 2, 4.6, -0.07, 2, 4.633, -0.042, 2, 4.667, -0.013, 2, 4.7, 0.013, 2, 4.733, 0.037, 2, 4.767, 0.055, 2, 4.8, 0.068, 2, 4.833, 0.072, 2, 4.867, 0.07, 2, 4.9, 0.064, 2, 4.933, 0.055, 2, 4.967, 0.044, 2, 5, 0.032, 2, 5.033, 0.019, 2, 5.067, 0.007, 2, 5.1, -0.004, 2, 5.133, -0.013, 2, 5.167, -0.019, 2, 5.2, -0.021, 2, 5.233, -0.02, 2, 5.267, -0.018, 2, 5.3, -0.014, 2, 5.333, -0.01, 2, 5.367, -0.005, 2, 5.4, 0, 2, 5.433, 0.005, 2, 5.467, 0.009, 2, 5.5, 0.013, 2, 5.533, 0.015, 2, 5.567, 0.016, 2, 5.6, 0.016, 2, 5.633, 0.015, 2, 5.667, 0.013, 2, 5.7, 0.011, 2, 5.733, 0.01, 2, 5.767, 0.008, 2, 5.8, 0.007, 2, 5.833, 0.007, 2, 5.867, 0.011, 2, 5.9, 0.023, 2, 5.933, 0.04, 2, 5.967, 0.062, 2, 6, 0.086, 2, 6.033, 0.111, 2, 6.067, 0.135, 2, 6.1, 0.156, 2, 6.133, 0.174, 2, 6.167, 0.186, 2, 6.2, 0.19, 2, 6.233, 0.169, 2, 6.267, 0.114, 2, 6.3, 0.029, 2, 6.333, -0.071, 2, 6.367, -0.181, 2, 6.4, -0.29, 2, 6.433, -0.391, 2, 6.467, -0.475, 2, 6.5, -0.531, 2, 6.533, -0.552, 2, 6.567, -0.525, 2, 6.6, -0.453, 2, 6.633, -0.343, 2, 6.667, -0.21, 2, 6.7, -0.059, 2, 6.733, 0.094, 2, 6.767, 0.246, 2, 6.8, 0.378, 2, 6.833, 0.488, 2, 6.867, 0.56, 2, 6.9, 0.587, 2, 6.933, 0.564, 2, 6.967, 0.501, 2, 7, 0.406, 2, 7.033, 0.291, 2, 7.067, 0.159, 2, 7.1, 0.027, 2, 7.133, -0.105, 2, 7.167, -0.22, 2, 7.2, -0.315, 2, 7.233, -0.378, 2, 7.267, -0.401, 2, 7.3, -0.387, 2, 7.333, -0.348, 2, 7.367, -0.288, 2, 7.4, -0.217, 2, 7.433, -0.135, 2, 7.467, -0.052, 2, 7.5, 0.03, 2, 7.533, 0.102, 2, 7.567, 0.161, 2, 7.6, 0.2, 2, 7.633, 0.215, 2, 7.667, 0.207, 2, 7.7, 0.187, 2, 7.733, 0.157, 2, 7.767, 0.121, 2, 7.8, 0.079, 2, 7.833, 0.037, 2, 7.867, -0.004, 2, 7.9, -0.041, 2, 7.933, -0.071, 2, 7.967, -0.091, 2, 8, -0.098, 2, 8.033, -0.095, 2, 8.067, -0.086, 2, 8.1, -0.073, 2, 8.133, -0.057, 2, 8.167, -0.038, 2, 8.2, -0.02, 2, 8.233, -0.001, 2, 8.267, 0.015, 2, 8.3, 0.028, 2, 8.333, 0.037, 2, 8.367, 0.04, 2, 8.4, 0.039, 2, 8.433, 0.036, 2, 8.467, 0.031, 2, 8.5, 0.026, 2, 8.533, 0.019, 2, 8.567, 0.012, 2, 8.6, 0.005, 2, 8.633, -0.001, 2, 8.667, -0.007, 2, 8.7, -0.011, 2, 8.733, -0.014, 2, 8.767, -0.015, 2, 8.8, -0.015, 2, 8.833, -0.014, 2, 8.867, -0.012, 2, 8.9, -0.009, 2, 8.933, -0.006, 2, 8.967, -0.003, 2, 9, -0.001, 2, 9.033, 0.002, 2, 9.067, 0.004, 2, 9.1, 0.005, 2, 9.133, 0.006, 2, 9.167, 0.005, 2, 9.2, 0.005, 2, 9.233, 0.004, 2, 9.267, 0.003, 2, 9.3, 0.002, 2, 9.333, 0.001, 2, 9.367, 0, 2, 9.4, -0.001, 2, 9.433, -0.001, 2, 9.467, -0.002, 2, 9.5, -0.002, 2, 9.567, -0.002, 2, 9.6, -0.002, 2, 9.633, -0.001, 2, 9.667, -0.001, 2, 9.7, -0.001, 2, 9.733, 0, 2, 9.767, 0, 2, 9.8, 0, 2, 9.833, 0.001, 2, 9.867, 0.001, 2, 9.967, 0.001, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh46", "Segments": [0, -0.087, 2, 0.033, -0.082, 2, 0.067, -0.071, 2, 0.1, -0.054, 2, 0.133, -0.035, 2, 0.167, -0.014, 2, 0.2, 0.005, 2, 0.233, 0.022, 2, 0.267, 0.033, 2, 0.3, 0.038, 2, 0.333, 0.036, 2, 0.367, 0.031, 2, 0.4, 0.022, 2, 0.433, 0.012, 2, 0.467, -0.001, 2, 0.5, -0.016, 2, 0.533, -0.031, 2, 0.567, -0.047, 2, 0.6, -0.064, 2, 0.633, -0.08, 2, 0.667, -0.095, 2, 0.7, -0.11, 2, 0.733, -0.122, 2, 0.767, -0.133, 2, 0.8, -0.141, 2, 0.833, -0.147, 2, 0.867, -0.148, 2, 0.9, -0.114, 2, 0.933, -0.023, 2, 0.967, 0.118, 2, 1, 0.295, 2, 1.033, 0.492, 2, 1.067, 0.705, 2, 1.1, 0.917, 2, 1.133, 1.114, 2, 1.167, 1.291, 2, 1.2, 1.432, 2, 1.233, 1.524, 2, 1.267, 1.558, 2, 1.3, 1.464, 2, 1.333, 1.213, 2, 1.367, 0.825, 2, 1.4, 0.338, 2, 1.433, -0.201, 2, 1.467, -0.786, 2, 1.5, -1.371, 2, 1.533, -1.911, 2, 1.567, -2.398, 2, 1.6, -2.785, 2, 1.633, -3.036, 2, 1.667, -3.13, 2, 1.7, -3.017, 2, 1.733, -2.694, 2, 1.767, -2.211, 2, 1.8, -1.593, 2, 1.833, -0.881, 2, 1.867, -0.122, 2, 1.9, 0.674, 2, 1.933, 1.433, 2, 1.967, 2.145, 2, 2, 2.763, 2, 2.033, 3.246, 2, 2.067, 3.569, 2, 2.1, 3.682, 2, 2.133, 3.522, 2, 2.167, 3.095, 2, 2.2, 2.444, 2, 2.233, 1.656, 2, 2.267, 0.76, 2, 2.3, -0.147, 2, 2.333, -1.044, 2, 2.367, -1.831, 2, 2.4, -2.482, 2, 2.433, -2.909, 2, 2.467, -3.069, 2, 2.5, -2.97, 2, 2.533, -2.706, 2, 2.567, -2.297, 2, 2.6, -1.783, 2, 2.633, -1.214, 2, 2.667, -0.597, 2, 2.7, 0.02, 2, 2.733, 0.589, 2, 2.767, 1.102, 2, 2.8, 1.511, 2, 2.833, 1.776, 2, 2.867, 1.875, 2, 2.9, 1.795, 2, 2.933, 1.576, 2, 2.967, 1.247, 2, 3, 0.854, 2, 3.033, 0.427, 2, 3.067, 0, 2, 3.1, -0.392, 2, 3.133, -0.722, 2, 3.167, -0.94, 2, 3.2, -1.021, 2, 3.233, -0.972, 2, 3.267, -0.838, 2, 3.3, -0.637, 2, 3.333, -0.397, 2, 3.367, -0.137, 2, 3.4, 0.124, 2, 3.433, 0.364, 2, 3.467, 0.565, 2, 3.5, 0.698, 2, 3.533, 0.747, 2, 3.567, 0.709, 2, 3.6, 0.606, 2, 3.633, 0.451, 2, 3.667, 0.267, 2, 3.7, 0.066, 2, 3.733, -0.135, 2, 3.767, -0.32, 2, 3.8, -0.475, 2, 3.833, -0.578, 2, 3.867, -0.616, 2, 3.9, -0.591, 2, 3.933, -0.524, 2, 3.967, -0.421, 2, 4, -0.298, 2, 4.033, -0.157, 2, 4.067, -0.015, 2, 4.1, 0.126, 2, 4.133, 0.249, 2, 4.167, 0.351, 2, 4.2, 0.418, 2, 4.233, 0.443, 2, 4.267, 0.427, 2, 4.3, 0.382, 2, 4.333, 0.315, 2, 4.367, 0.233, 2, 4.4, 0.14, 2, 4.433, 0.045, 2, 4.467, -0.048, 2, 4.5, -0.13, 2, 4.533, -0.197, 2, 4.567, -0.242, 2, 4.6, -0.258, 2, 4.633, -0.249, 2, 4.667, -0.224, 2, 4.7, -0.186, 2, 4.733, -0.14, 2, 4.767, -0.088, 2, 4.8, -0.036, 2, 4.833, 0.017, 2, 4.867, 0.062, 2, 4.9, 0.1, 2, 4.933, 0.125, 2, 4.967, 0.134, 2, 5, 0.13, 2, 5.033, 0.118, 2, 5.067, 0.1, 2, 5.1, 0.079, 2, 5.133, 0.054, 2, 5.167, 0.029, 2, 5.2, 0.004, 2, 5.233, -0.017, 2, 5.267, -0.035, 2, 5.3, -0.047, 2, 5.333, -0.051, 2, 5.367, -0.05, 2, 5.4, -0.045, 2, 5.433, -0.039, 2, 5.467, -0.03, 2, 5.5, -0.021, 2, 5.533, -0.011, 2, 5.567, 0, 2, 5.6, 0.009, 2, 5.633, 0.017, 2, 5.667, 0.024, 2, 5.7, 0.028, 2, 5.733, 0.03, 2, 5.767, 0.029, 2, 5.8, 0.026, 2, 5.833, 0.022, 2, 5.867, 0.018, 2, 5.9, 0.015, 2, 5.933, 0.012, 2, 5.967, 0.011, 2, 6, 0.015, 2, 6.033, 0.028, 2, 6.067, 0.047, 2, 6.1, 0.069, 2, 6.133, 0.093, 2, 6.167, 0.118, 2, 6.2, 0.14, 2, 6.233, 0.159, 2, 6.267, 0.172, 2, 6.3, 0.176, 2, 6.333, 0.158, 2, 6.367, 0.11, 2, 6.4, 0.037, 2, 6.433, -0.051, 2, 6.467, -0.152, 2, 6.5, -0.254, 2, 6.533, -0.355, 2, 6.567, -0.443, 2, 6.6, -0.516, 2, 6.633, -0.564, 2, 6.667, -0.582, 2, 6.7, -0.551, 2, 6.733, -0.469, 2, 6.767, -0.343, 2, 6.8, -0.191, 2, 6.833, -0.018, 2, 6.867, 0.157, 2, 6.9, 0.33, 2, 6.933, 0.482, 2, 6.967, 0.608, 2, 7, 0.691, 2, 7.033, 0.722, 2, 7.067, 0.691, 2, 7.1, 0.609, 2, 7.133, 0.484, 2, 7.167, 0.334, 2, 7.2, 0.162, 2, 7.233, -0.012, 2, 7.267, -0.184, 2, 7.3, -0.334, 2, 7.333, -0.459, 2, 7.367, -0.541, 2, 7.4, -0.572, 2, 7.433, -0.55, 2, 7.467, -0.492, 2, 7.5, -0.403, 2, 7.533, -0.296, 2, 7.567, -0.173, 2, 7.6, -0.05, 2, 7.633, 0.072, 2, 7.667, 0.18, 2, 7.7, 0.268, 2, 7.733, 0.327, 2, 7.767, 0.348, 2, 7.8, 0.338, 2, 7.833, 0.31, 2, 7.867, 0.266, 2, 7.9, 0.211, 2, 7.933, 0.15, 2, 7.967, 0.084, 2, 8, 0.018, 2, 8.033, -0.042, 2, 8.067, -0.097, 2, 8.1, -0.141, 2, 8.133, -0.169, 2, 8.167, -0.18, 2, 8.2, -0.174, 2, 8.233, -0.157, 2, 8.267, -0.132, 2, 8.3, -0.101, 2, 8.333, -0.066, 2, 8.367, -0.031, 2, 8.4, 0.004, 2, 8.433, 0.035, 2, 8.467, 0.061, 2, 8.5, 0.077, 2, 8.533, 0.083, 2, 8.567, 0.081, 2, 8.6, 0.073, 2, 8.633, 0.062, 2, 8.667, 0.048, 2, 8.7, 0.032, 2, 8.733, 0.016, 2, 8.767, 0, 2, 8.8, -0.013, 2, 8.833, -0.025, 2, 8.867, -0.032, 2, 8.9, -0.035, 2, 8.933, -0.034, 2, 8.967, -0.032, 2, 9, -0.028, 2, 9.033, -0.022, 2, 9.067, -0.017, 2, 9.1, -0.011, 2, 9.133, -0.004, 2, 9.167, 0.001, 2, 9.2, 0.006, 2, 9.233, 0.01, 2, 9.267, 0.013, 2, 9.3, 0.014, 2, 9.333, 0.014, 2, 9.367, 0.012, 2, 9.4, 0.01, 2, 9.433, 0.008, 2, 9.467, 0.006, 2, 9.5, 0.003, 2, 9.533, 0, 2, 9.567, -0.002, 2, 9.6, -0.004, 2, 9.633, -0.005, 2, 9.667, -0.005, 2, 9.7, -0.005, 2, 9.733, -0.005, 2, 9.767, -0.004, 2, 9.8, -0.003, 2, 9.833, -0.002, 2, 9.867, -0.001, 2, 9.9, 0, 2, 9.933, 0.001, 2, 9.967, 0.001, 2, 10, 0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh46", "Segments": [0, -0.199, 2, 0.033, -0.22, 2, 0.067, -0.259, 2, 0.1, -0.28, 2, 0.133, -0.271, 2, 0.167, -0.246, 2, 0.2, -0.208, 2, 0.233, -0.162, 2, 0.267, -0.109, 2, 0.3, -0.056, 2, 0.333, -0.004, 2, 0.367, 0.043, 2, 0.4, 0.081, 2, 0.433, 0.106, 2, 0.467, 0.115, 2, 0.5, 0.111, 2, 0.533, 0.101, 2, 0.567, 0.084, 2, 0.6, 0.063, 2, 0.633, 0.038, 2, 0.667, 0.01, 2, 0.7, -0.019, 2, 0.733, -0.048, 2, 0.767, -0.077, 2, 0.8, -0.105, 2, 0.833, -0.13, 2, 0.867, -0.151, 2, 0.9, -0.168, 2, 0.933, -0.179, 2, 0.967, -0.182, 2, 1, -0.138, 2, 1.033, -0.02, 2, 1.067, 0.163, 2, 1.1, 0.392, 2, 1.133, 0.646, 2, 1.167, 0.922, 2, 1.2, 1.198, 2, 1.233, 1.452, 2, 1.267, 1.681, 2, 1.3, 1.864, 2, 1.333, 1.982, 2, 1.367, 2.026, 2, 1.4, 1.918, 2, 1.433, 1.605, 2, 1.467, 1.14, 2, 1.5, 0.543, 2, 1.533, -0.144, 2, 1.567, -0.876, 2, 1.6, -1.644, 2, 1.633, -2.376, 2, 1.667, -3.063, 2, 1.7, -3.66, 2, 1.733, -4.125, 2, 1.767, -4.438, 2, 1.8, -4.546, 2, 1.833, -4.34, 2, 1.867, -3.786, 2, 1.9, -2.931, 2, 1.933, -1.858, 2, 1.967, -0.668, 2, 2, 0.622, 2, 2.033, 1.912, 2, 2.067, 3.101, 2, 2.1, 4.175, 2, 2.133, 5.03, 2, 2.167, 5.583, 2, 2.2, 5.79, 2, 2.233, 5.57, 2, 2.267, 4.979, 2, 2.3, 4.067, 2, 2.333, 2.922, 2, 2.367, 1.654, 2, 2.4, 0.278, 2, 2.433, -1.097, 2, 2.467, -2.366, 2, 2.5, -3.511, 2, 2.533, -4.422, 2, 2.567, -5.013, 2, 2.6, -5.233, 2, 2.633, -5.058, 2, 2.667, -4.587, 2, 2.7, -3.861, 2, 2.733, -2.95, 2, 2.767, -1.94, 2, 2.8, -0.844, 2, 2.833, 0.251, 2, 2.867, 1.261, 2, 2.9, 2.173, 2, 2.933, 2.899, 2, 2.967, 3.369, 2, 3, 3.544, 2, 3.033, 3.413, 2, 3.067, 3.062, 2, 3.1, 2.527, 2, 3.133, 1.88, 2, 3.167, 1.144, 2, 3.2, 0.399, 2, 3.233, -0.337, 2, 3.267, -0.984, 2, 3.3, -1.519, 2, 3.333, -1.869, 2, 3.367, -2.001, 2, 3.4, -1.91, 2, 3.433, -1.663, 2, 3.467, -1.291, 2, 3.5, -0.847, 2, 3.533, -0.364, 2, 3.567, 0.119, 2, 3.6, 0.563, 2, 3.633, 0.935, 2, 3.667, 1.183, 2, 3.7, 1.274, 2, 3.733, 1.21, 2, 3.767, 1.039, 2, 3.8, 0.78, 2, 3.833, 0.471, 2, 3.867, 0.136, 2, 3.9, -0.2, 2, 3.933, -0.508, 2, 3.967, -0.767, 2, 4, -0.939, 2, 4.033, -1.002, 2, 4.067, -0.96, 2, 4.1, -0.848, 2, 4.133, -0.678, 2, 4.167, -0.472, 2, 4.2, -0.237, 2, 4.233, 0, 2, 4.267, 0.234, 2, 4.3, 0.44, 2, 4.333, 0.611, 2, 4.367, 0.722, 2, 4.4, 0.764, 2, 4.433, 0.735, 2, 4.467, 0.655, 2, 4.5, 0.533, 2, 4.533, 0.387, 2, 4.567, 0.219, 2, 4.6, 0.05, 2, 4.633, -0.117, 2, 4.667, -0.264, 2, 4.7, -0.385, 2, 4.733, -0.465, 2, 4.767, -0.495, 2, 4.8, -0.473, 2, 4.833, -0.413, 2, 4.867, -0.322, 2, 4.9, -0.214, 2, 4.933, -0.097, 2, 4.967, 0.02, 2, 5, 0.128, 2, 5.033, 0.218, 2, 5.067, 0.278, 2, 5.1, 0.3, 2, 5.133, 0.29, 2, 5.167, 0.263, 2, 5.2, 0.222, 2, 5.233, 0.172, 2, 5.267, 0.115, 2, 5.3, 0.058, 2, 5.333, 0.001, 2, 5.367, -0.049, 2, 5.4, -0.09, 2, 5.433, -0.117, 2, 5.467, -0.127, 2, 5.5, -0.123, 2, 5.533, -0.113, 2, 5.567, -0.097, 2, 5.6, -0.076, 2, 5.633, -0.053, 2, 5.667, -0.029, 2, 5.7, -0.004, 2, 5.733, 0.018, 2, 5.767, 0.039, 2, 5.8, 0.055, 2, 5.833, 0.065, 2, 5.867, 0.069, 2, 5.9, 0.067, 2, 5.933, 0.06, 2, 5.967, 0.051, 2, 6, 0.041, 2, 6.033, 0.032, 2, 6.067, 0.026, 2, 6.1, 0.023, 2, 6.133, 0.029, 2, 6.167, 0.046, 2, 6.2, 0.07, 2, 6.233, 0.099, 2, 6.267, 0.128, 2, 6.3, 0.157, 2, 6.333, 0.181, 2, 6.367, 0.198, 2, 6.4, 0.204, 2, 6.433, 0.181, 2, 6.467, 0.118, 2, 6.5, 0.024, 2, 6.533, -0.091, 2, 6.567, -0.221, 2, 6.6, -0.353, 2, 6.633, -0.483, 2, 6.667, -0.598, 2, 6.7, -0.692, 2, 6.733, -0.755, 2, 6.767, -0.778, 2, 6.8, -0.741, 2, 6.833, -0.641, 2, 6.867, -0.486, 2, 6.9, -0.292, 2, 6.933, -0.077, 2, 6.967, 0.156, 2, 7, 0.389, 2, 7.033, 0.604, 2, 7.067, 0.798, 2, 7.1, 0.952, 2, 7.133, 1.052, 2, 7.167, 1.089, 2, 7.2, 1.04, 2, 7.233, 0.91, 2, 7.267, 0.711, 2, 7.3, 0.47, 2, 7.333, 0.196, 2, 7.367, -0.081, 2, 7.4, -0.355, 2, 7.433, -0.596, 2, 7.467, -0.795, 2, 7.5, -0.925, 2, 7.533, -0.974, 2, 7.567, -0.942, 2, 7.6, -0.854, 2, 7.633, -0.719, 2, 7.667, -0.548, 2, 7.7, -0.36, 2, 7.733, -0.156, 2, 7.767, 0.049, 2, 7.8, 0.237, 2, 7.833, 0.407, 2, 7.867, 0.543, 2, 7.9, 0.63, 2, 7.933, 0.663, 2, 7.967, 0.638, 2, 8, 0.572, 2, 8.033, 0.471, 2, 8.067, 0.349, 2, 8.1, 0.209, 2, 8.133, 0.068, 2, 8.167, -0.071, 2, 8.2, -0.193, 2, 8.233, -0.294, 2, 8.267, -0.36, 2, 8.3, -0.385, 2, 8.333, -0.371, 2, 8.367, -0.335, 2, 8.4, -0.279, 2, 8.433, -0.211, 2, 8.467, -0.134, 2, 8.5, -0.056, 2, 8.533, 0.021, 2, 8.567, 0.089, 2, 8.6, 0.145, 2, 8.633, 0.181, 2, 8.667, 0.195, 2, 8.7, 0.189, 2, 8.733, 0.174, 2, 8.767, 0.15, 2, 8.8, 0.121, 2, 8.833, 0.088, 2, 8.867, 0.052, 2, 8.9, 0.016, 2, 8.933, -0.017, 2, 8.967, -0.046, 2, 9, -0.07, 2, 9.033, -0.085, 2, 9.067, -0.091, 2, 9.1, -0.088, 2, 9.133, -0.08, 2, 9.167, -0.067, 2, 9.2, -0.052, 2, 9.233, -0.035, 2, 9.267, -0.017, 2, 9.3, 0, 2, 9.333, 0.015, 2, 9.367, 0.028, 2, 9.4, 0.036, 2, 9.433, 0.039, 2, 9.467, 0.038, 2, 9.5, 0.035, 2, 9.533, 0.031, 2, 9.567, 0.025, 2, 9.6, 0.019, 2, 9.633, 0.012, 2, 9.667, 0.005, 2, 9.7, -0.002, 2, 9.733, -0.007, 2, 9.767, -0.012, 2, 9.8, -0.015, 2, 9.833, -0.016, 2, 9.867, -0.015, 2, 9.9, -0.014, 2, 9.933, -0.012, 2, 9.967, -0.009, 2, 10, -0.006]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh0_Skinning", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh1_Skinning", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh2_Skinning", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh3_Skinning", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh46_Skinning", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 10, 1]}]}