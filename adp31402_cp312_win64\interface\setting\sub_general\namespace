All variable names follow the rules below.

* <object>            :  The names you will call
* [*statements*]      :  The tags
  -  [NonFixe]  ->   The widget that will change
  -  [Fixed]    ->   The widget that won't change
* {*statements*}      :  The code blocks

- Pivot             ->    pivot_<object>
- [CARDS] Card      ->    card_<object>
- RadioButton       ->    single_<object>
- Checkbox          ->    check_<object>
- Slider            ->    scale_<object>
- LineEdit          ->    input_<object>
- PushButton        ->    click_<object>
- Combo             ->    select_<object>
- [NonFixed] Label  ->    show_<object>
- [Fixed]    Label  ->    {QLabel.setGeometry()}
