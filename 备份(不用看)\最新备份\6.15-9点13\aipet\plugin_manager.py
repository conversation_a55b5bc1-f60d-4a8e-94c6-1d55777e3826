import os
import importlib.util
import json
import re
from typing import List, Dict, Any, Optional

class PluginManager:
    def __init__(self, plugin_dir="plugins"):
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.plugin_dir = os.path.join(self.base_dir, plugin_dir)
        self.tools: Dict[str, callable] = {}
        self.tool_definitions: List[Dict[str, Any]] = []
        self.placeholder_resolvers: List[callable] = []
        
        if not os.path.exists(self.plugin_dir):
            print(f"Creating plugin directory: {self.plugin_dir}")
            os.makedirs(self.plugin_dir)

    def discover_and_load_plugins(self):
        print(f"🔍 Discovering plugins in: {self.plugin_dir}")
        for filename in os.listdir(self.plugin_dir):
            if filename.endswith(".py") and not filename.startswith("__"):
                plugin_name = os.path.splitext(filename)[0]
                plugin_path = os.path.join(self.plugin_dir, filename)
                self._load_plugin(plugin_name, plugin_path)
        print(f"✅ Plugin discovery finished. Loaded tools: {list(self.tools.keys())}")
        print(f"✅ Loaded {len(self.placeholder_resolvers)} placeholder resolver plugins.")

    def _load_plugin(self, plugin_name: str, entry_point: str):
        try:
            spec = importlib.util.spec_from_file_location(f"plugins.{plugin_name}", entry_point)
            plugin_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(plugin_module)
            
            loaded_something = False
            if hasattr(plugin_module, "get_tools"):
                tools = plugin_module.get_tools()
                for tool_name, tool_data in tools.items():
                    self.tools[tool_name] = tool_data["function"]
                    self.tool_definitions.append(tool_data["definition"])
                print(f"  - Loaded tools from plugin '{plugin_name}' successfully.")
                loaded_something = True

            if hasattr(plugin_module, "resolve_placeholder"):
                self.placeholder_resolvers.append(plugin_module.resolve_placeholder)
                print(f"  - Loaded placeholder resolver from plugin '{plugin_name}' successfully.")
                loaded_something = True

            if not loaded_something:
                print(f"  - ⚠️  Plugin '{plugin_name}' has no 'get_tools' or 'resolve_placeholder' function. Skipping.")

        except Exception as e:
            print(f"  - ❌ Failed to load plugin '{plugin_name}': {e}")

    def resolve_placeholders_in_text(self, text: str) -> str:
        """
        Resolves all placeholders like {{placeholder_name}} in the given text.
        """
        def replace_match(match):
            placeholder_name = match.group(1)
            for resolver in self.placeholder_resolvers:
                resolved_value = resolver(placeholder_name)
                if resolved_value is not None:
                    return str(resolved_value)
            # If no resolver found, return the original placeholder
            return match.group(0)

        # Using a non-greedy match for placeholders: {{...}}
        return re.sub(r"\{\{(.+?)\}\}", replace_match, text)

    def get_all_tool_definitions(self) -> Optional[List[Dict[str, Any]]]:
        return self.tool_definitions if self.tool_definitions else None

    def execute_tool(self, tool_name: str, tool_args_str: str) -> str:
        print(f"Executing tool: {tool_name} with args: {tool_args_str}")
        if tool_name not in self.tools:
            return json.dumps({"error": f"Tool '{tool_name}' not found."})
        
        try:
            tool_function = self.tools[tool_name]
            tool_args = json.loads(tool_args_str)
            result = tool_function(**tool_args)
            if not isinstance(result, str):
                result = json.dumps(result, ensure_ascii=False, indent=2)
            return result
        except json.JSONDecodeError:
            return json.dumps({"error": "Invalid JSON arguments. Expected a valid JSON string."})
        except TypeError as e:
            return json.dumps({"error": f"Incorrect arguments for tool '{tool_name}': {e}"})
        except Exception as e:
            import traceback
            traceback.print_exc()
            return json.dumps({"error": f"Exception during tool '{tool_name}' execution: {str(e)}"}) 