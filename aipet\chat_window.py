import sys
import re
import html
import os
import json
import logging
import configparser
import threading
import requests
import tempfile
import uuid
import time
import gc
import hashlib
import shutil
from datetime import datetime
from functools import partial
from typing import Optional, Any, Dict, List

from PyQt5.QtCore import (Qt, pyqtSignal, QEvent, QObject, QSize, QUrl, QTimer, 
                          QPoint, QRect, QRectF, QParallelAnimationGroup, QSequentialAnimationGroup, 
                          QMimeData, pyqtSlot, QPropertyAnimation, QEasingCurve, QBuffer, QIODevice)
from PyQt5.QtGui import (QMouseEvent, QIcon, QKeySequence, QColor, QPalette, QPixmap,
                         QImageReader, QImage, QTextCursor, QTextDocument, QFont,
                         QTextBlockFormat, QTextCharFormat, QKeyEvent, QBrush,
                         QFontMetrics, QPainter, QPainterPath, QLinearGradient, QRadialGradient, QPen)
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QApplication, 
                             QFileDialog, QLabel, QInputDialog, QLineEdit, QListWidget, 
                             QListWidgetItem, QDialog, QDialogButtonBox, QMenu, QAction,
                             QFrame, QGraphicsDropShadowEffect, QSizePolicy, QStackedWidget,
                             QActionGroup, QGraphicsOpacityEffect, QComboBox, QShortcut,
                             QSpacerItem, QToolButton, QProgressBar, QTextEdit, QScrollArea)
from PyQt5.QtMultimedia import QAudioInput, QAudioFormat, QAudioDeviceInfo, QAudio

from aipet.chat_commands import ChatCommandParser, TTSCommandHandler, CommandResult
from aipet.tts_settings_dialog import TTSSettingsDialog
from aipet.websocket_client import VCPLogClient, VCPLogMessageHandler
from aipet.push_message_widget import CollapsiblePushMessageWidget
from aipet.ui.modern_vcplog_system import VCPLogNotificationBar

from aipet.assistant_manager import AssistantManager
from aipet.assistant_config import AssistantConfig
from aipet.new_assistant_dialog import NewAssistantDialog
from aipet.manage_assistants_dialog import ManageAssistantsDialog

from aipet.utils import load_companion_config
from aipet.ui.theme import ThemeManager
from aipet.ui.animations import AnimationManager
from aipet.ui.widgets import TypingIndicator, ModernButton, SmartScrollArea
from aipet.ui.input_area import ModernInputArea
from aipet.ui.message_bubble import MessageBubble

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChatWindow(QWidget):
    """现代化聊天窗口"""
    
    # 信号定义 - 保持与原始代码兼容
    send_composed_message_signal = pyqtSignal(str, object, object)
    system_prompt_changed_signal = pyqtSignal(str)
    topic_selected_signal = pyqtSignal(str)
    new_topic_requested_signal = pyqtSignal()
    model_selected_signal = pyqtSignal(str)
    topic_deleted_signal = pyqtSignal(str)  # 新增：删除话题信号
    window_hidden_signal = pyqtSignal()
    update_image_signal = pyqtSignal(str, str)  # message_id, image_path
    
    # 新增Live2D模型相关信号
    live2d_model_switch_requested = pyqtSignal(str)  # 请求切换模型信号
    live2d_model_switch_completed = pyqtSignal(str, bool)  # 模型切换完成信号
    
    # --- 自定义信号 ---
    send_message_signal = pyqtSignal(str, str) # message, image_path
    message_composed = pyqtSignal(str, str) # text, image_path
    
    # +++ 新增信号，用于请求打开TTS设置对话框 +++
    open_settings_signal = pyqtSignal()
    
    # 助手管理相关的信号
    switch_assistant_signal = pyqtSignal(str) # assistant_name
    save_system_prompt_signal = pyqtSignal(str, str) # assistant_name, new_prompt
    
    def __init__(self, parent=None, app_controller=None):
        super().__init__(parent)
        self.app_controller = app_controller
        self._is_forcing_close = False # 强制关闭标志
        self._is_immune_to_close = False # 新增：关闭事件免疫标志
        
        # 窗口状态
        self.is_dragging = False
        self.drag_start_position = None
        self.is_recording = False
        
        # 窗口调整大小相关
        self.border_width = 15
        self.is_resizing = False
        self.resize_edge = None
        
        # 消息管理
        self.active_message_elements: Dict[str, Any] = {}
        self.last_history_sender_was_ai: bool = False
        self.processed_images = {}
        
        # 历史记录加载优化
        self.full_history: List[Dict] = []
        self.current_history_offset = 0
        self.is_loading_history = False
        self.HISTORY_BATCH_SIZE = 20 # 每次加载的历史数量
        
        # 组件
        self.theme_manager = ThemeManager()
        self.animation_manager = AnimationManager()
        self.typing_indicator = None
        self.live2d_widget = None # 为助手管理器准备
        
        # AssistantManager 初始化
        self.assistant_manager = AssistantManager(config_dir="aipet")
        current_assistant = self.assistant_manager.get_current_assistant()
        self.system_prompt = current_assistant.system_prompt if current_assistant else ""
        # 初始化后，如果 app_controller 存在，则通知其系统提示已更改
        if self.app_controller and hasattr(self.app_controller, 'handle_system_prompt_changed'):
            self.app_controller.handle_system_prompt_changed(self.system_prompt)

        # SSE客户端 - 默认连接
        # self.sse_client = SSEClientWrapper(self)
        self.sse_url = "http://localhost:6005/companion/sse"
        self.sse_connected = False
        
        # VCPLog客户端 - 用于接收VCP工具调用日志推送
        # 从配置文件读取WebSocket服务器地址和密钥
        vcplog_server_url = os.getenv('VCPLOG_SERVER_URL', 'ws://***************:6005')
        vcp_key = os.getenv('VCP_Key', '123456')

        self.vcplog_client = VCPLogClient(vcplog_server_url, vcp_key, self.app_controller)
        self.vcplog_client.vcp_log_received.connect(self.handle_vcp_log_message)
        self.vcplog_client.agent_message_received.connect(self.handle_agent_message)  # 🆕 新增Agent消息处理
        self.vcplog_client.connection_status_changed.connect(self.handle_vcplog_status_change)
        self.vcplog_client.error_occurred.connect(self.handle_vcplog_error)
        
        # UI组件
        # 初始化命令解析器
        self.command_parser = ChatCommandParser()
        self.tts_command_handler = TTSCommandHandler(self.command_parser)
        self.message_container = None
        self.message_layout = None
        self.input_area = None
        self.scroll_area = None
        self.status_label = None
        self.push_message_widget = None  # 推送消息折叠组件（保留兼容性）
        self.vcplog_notification_bar = None  # 现代化VCPLog通知栏
        
        # 游戏伴侣模式相关初始化
        self.companion_config = load_companion_config()  # 加载配置
        self.game_companion_timer = QTimer(self)
        self.game_companion_timer.timeout.connect(self._trigger_timed_companion_action)
        self.is_game_companion_active = False
        self.companion_interval_seconds = self.companion_config['interval_seconds']  # 从配置文件读取
        
        # 设置窗口
        self.setup_window()
        self.setup_ui()
        self.setup_animations()
        self.connect_signals()
        
        # 应用主题
        self.apply_theme()
        
        # 连接图片更新信号
        self.update_image_signal.connect(self._update_image_in_bubble_safe)
        
        # 自动连接SSE
        self._auto_connect_sse()
        
        # self.app_controller 引用已由构造函数参数 app_controller 处理
        self.scroll_area.verticalScrollBar().valueChanged.connect(self.on_scroll_changed)

    def force_close(self):
        """强制关闭窗口，绕过隐藏到托盘的逻辑"""
        self._is_forcing_close = True
        self.close()

    def set_app_controller(self, app_controller):
        """设置AppController引用"""
        self.app_controller = app_controller

    def update_asr_button_state(self, is_asr_on: bool):
        """由AppController调用，更新ASR按钮的视觉状态"""
        if self.input_area and hasattr(self.input_area, 'tool_buttons'):
            asr_btn = self.input_area.tool_buttons.get("asr_voice_input")
            if asr_btn:
                if is_asr_on:
                    asr_btn.setText("🎙️") # 正在录音/监听的图标
                    asr_btn.setToolTip("停止语音输入")
                    # 可以添加闪烁等视觉效果
                else:
                    asr_btn.setText("🎤") # 默认图标
                    asr_btn.setToolTip("开始语音输入")
            else:
                logger.warning("ChatWindow: 未找到ASR按钮 (asr_voice_input) 来更新状态。")
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WA_NoSystemBackground, True)
        self.setAutoFillBackground(False)
        
        # 设置窗口大小和位置
        self.resize(450, 650)
        self.center_on_screen()
        
        # 设置窗口图标和标题
        self.setWindowTitle("AI 智能助手")
    
    def center_on_screen(self):
        """将窗口居中"""
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            x = (screen_geometry.width() - self.width()) // 2
            y = (screen_geometry.height() - self.height()) // 2
            self.move(x, y)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        self.main_container = QFrame(self)
        self.main_container.setObjectName("mainContainer")
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.main_container)
        
        # 容器布局
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # 标题栏
        self.title_bar = self.create_title_bar()
        container_layout.addWidget(self.title_bar)
        
        # 聊天区域
        self.chat_area = self.create_chat_area()
        container_layout.addWidget(self.chat_area, 1)
        
        # 输入区域
        # self.input_area = ModernInputArea(self, self.theme_manager) # 原来的代码
        self.input_area = ModernInputArea(self, self.theme_manager, self.app_controller) # <--- 传入 self.app_controller
        container_layout.addWidget(self.input_area)
        
        # 添加阴影效果
        self.add_shadow_effect()
    
    def create_title_bar(self) -> QWidget:
        """创建标题栏"""
        title_bar = QFrame()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(60)
        
        layout = QHBoxLayout(title_bar)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(15)
        
        # 左侧：头像和标题
        left_layout = QHBoxLayout()
        left_layout.setSpacing(12)
        
        # AI头像
        self.avatar_label = QLabel("🤖")
        self.avatar_label.setFixedSize(40, 40)
        self.avatar_label.setAlignment(Qt.AlignCenter)
        self.avatar_label.setObjectName("avatarLabel")
        
        # 标题和状态
        title_status_layout = QVBoxLayout()
        title_status_layout.setSpacing(2)
        
        self.title_label = QLabel("AI 智能助手")
        self.title_label.setObjectName("titleLabel")
        
        self.status_label = QLabel("● 在线")
        self.status_label.setObjectName("statusLabel")
        
        title_status_layout.addWidget(self.title_label)
        title_status_layout.addWidget(self.status_label)
        
        left_layout.addWidget(self.avatar_label)
        left_layout.addLayout(title_status_layout)
        
        # 右侧：操作按钮
        right_layout = QHBoxLayout()
        right_layout.setSpacing(8)
        
        # 主题切换按钮
        self.theme_button = ModernButton(self.theme_manager.get_color("theme_icon"))
        self.theme_button.setFixedSize(32, 32)
        self.theme_button.setToolTip("切换主题")
        self.theme_button.clicked.connect(self.toggle_theme)
        
        # 菜单按钮
        self.menu_button = ModernButton("☰")
        self.menu_button.setFixedSize(32, 32)
        self.menu_button.setToolTip("菜单")
        self.menu_button.clicked.connect(self.show_menu)
        
        # 最小化按钮
        self.minimize_button = ModernButton("—")
        self.minimize_button.setFixedSize(32, 32)
        self.minimize_button.setToolTip("最小化")
        self.minimize_button.clicked.connect(self.animated_hide)
        
        right_layout.addWidget(self.theme_button)
        right_layout.addWidget(self.menu_button)
        right_layout.addWidget(self.minimize_button)
        
        layout.addLayout(left_layout)
        layout.addStretch()
        layout.addLayout(right_layout)
        
        return title_bar
    
    def create_chat_area(self) -> QWidget:
        """创建聊天区域"""
        # 创建聊天区域容器
        chat_container = QWidget()
        chat_main_layout = QHBoxLayout(chat_container)
        chat_main_layout.setContentsMargins(0, 0, 0, 0)
        chat_main_layout.setSpacing(0)

        # 左侧：聊天消息区域
        chat_left_container = QWidget()
        chat_layout = QVBoxLayout(chat_left_container)
        chat_layout.setContentsMargins(0, 0, 0, 0)
        chat_layout.setSpacing(0)

        # 创建推送消息折叠组件（保留兼容性）
        self.push_message_widget = CollapsiblePushMessageWidget(self, max_messages=50)
        self.push_message_widget.message_cleared.connect(self._on_push_messages_cleared)
        self.push_message_widget.expand_state_changed.connect(self._on_push_expand_state_changed)

        # 将推送消息组件添加到聊天区域顶部
        chat_layout.addWidget(self.push_message_widget)

        # 智能滚动区域（正常聊天消息）
        self.scroll_area = SmartScrollArea(self)
        self.scroll_area.enable_scroll_animation()

        # 消息容器
        self.message_container = QWidget()
        self.message_layout = QVBoxLayout(self.message_container)
        self.message_layout.setContentsMargins(10, 10, 10, 10)
        self.message_layout.setSpacing(12)

        # 添加弹性空间，使消息从底部开始
        self.message_layout.addStretch()

        self.scroll_area.setWidget(self.message_container)

        # 将正常聊天区域添加到左侧容器
        chat_layout.addWidget(self.scroll_area, 1)  # 占用剩余空间

        # 右侧：现代化VCPLog通知栏
        self.vcplog_notification_bar = VCPLogNotificationBar(self)

        # 配置现代化通知系统
        self.vcplog_notification_bar.configure(
            toast_enabled=True,          # 启用Toast通知
            sidebar_enabled=True,        # 启用侧边栏
            smart_display=True,          # 智能显示模式
            max_items=100,              # 最大项目数
            max_toasts=5                # 最大Toast数
        )

        # 连接现代化通知栏信号
        self.vcplog_notification_bar.status_changed.connect(self.on_vcplog_status_changed)
        self.vcplog_notification_bar.toggle_requested.connect(self.on_vcplog_toggle_requested)

        # 添加到主布局
        chat_main_layout.addWidget(chat_left_container, 1)  # 聊天区域占主要空间
        chat_main_layout.addWidget(self.vcplog_notification_bar)  # 通知栏在右侧

        # 初始隐藏通知栏侧边栏
        # 检查当前状态，如果显示则切换为隐藏
        if self.vcplog_notification_bar.is_sidebar_visible():
            self.vcplog_notification_bar.toggle_sidebar()

        return chat_container
    
    def add_shadow_effect(self):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(25)
        shadow.setXOffset(0)
        shadow.setYOffset(8)
        shadow.setColor(QColor(0, 0, 0, 60))
        self.main_container.setGraphicsEffect(shadow)
    
    def setup_animations(self):
        """设置动画"""
        # 窗口显示动画
        self.show_animation = QPropertyAnimation(self, b"windowOpacity")
        self.show_animation.setDuration(300)
        self.show_animation.setStartValue(0.0)
        self.show_animation.setEndValue(1.0)
        self.show_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 窗口隐藏动画
        self.hide_animation = QPropertyAnimation(self, b"windowOpacity")
        self.hide_animation.setDuration(250)
        self.hide_animation.setStartValue(1.0)
        self.hide_animation.setEndValue(0.0)
        self.hide_animation.setEasingCurve(QEasingCurve.InCubic)
        self.hide_animation.finished.connect(self.hide)
        
        # 主题切换动画
        self.theme_transition = QPropertyAnimation(self.main_container, b"geometry")
        self.theme_transition.setDuration(200)
        self.theme_transition.setEasingCurve(QEasingCurve.OutCubic)
    
    def connect_signals(self):
        """连接信号槽"""
        # 输入区域信号
        if self.input_area:
            self.input_area.sendMessage.connect(self.handle_send_message)
            self.input_area.voiceRecordingToggled.connect(self.handle_voice_recording)
            
            # 连接游戏伴侣模式信号
            if hasattr(self.input_area, 'gameCompanionModeToggled'):
                self.input_area.gameCompanionModeToggled.connect(self.handle_game_companion_toggle)
            
            # 模型选择信号
            if hasattr(self.input_area, 'model_selector'):
                self.input_area.model_selector.currentTextChanged.connect(self.on_model_changed)
        
        # SSE客户端信号
        # self.sse_client.dataReceived.connect(self._handle_sse_data)
        # self.sse_client.statusChanged.connect(self._handle_sse_connection_status)
        # self.sse_client.errorOccurred.connect(self._handle_sse_error)
        
        # 自动启动VCPLog连接
        if hasattr(self, 'vcplog_client'):
            # 延迟启动，确保UI完全初始化
            QTimer.singleShot(1000, self.start_vcplog_connection)
    
    def _on_push_messages_cleared(self):
        """推送消息清空时的处理"""
        logger.info("推送消息已清空")
    
    def _on_push_expand_state_changed(self, is_expanded: bool):
        """推送消息展开状态变化处理"""
        logger.debug(f"推送消息区域{'展开' if is_expanded else '折叠'}")
        
        # 可以在这里添加其他UI调整逻辑
        # 比如调整聊天区域的高度等
    
    def apply_theme(self):
        """应用主题"""
        # 应用窗口样式
        window_style = self.theme_manager.apply_window_style(self)
        
        # 获取主题颜色
        theme = self.theme_manager.get_current_theme()
        
        # 主容器样式
        main_container_style = f"""
        QFrame#mainContainer {{
            background-color: {theme['window_bg']};
            border-radius: 20px;
            border: 1px solid {theme['border_color']};
        }}
        """
        
        # 标题栏样式
        title_bar_style = f"""
        QFrame#titleBar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {theme['window_bg']}, 
                stop:1 rgba(0,0,0,0.05));
            border-radius: 20px 20px 0 0;
            border-bottom: 1px solid {theme['separator_color']};
        }}
        QLabel#avatarLabel {{
            background-color: {theme['primary']};
            border-radius: 20px;
            font-size: 20px;
            color: {theme['text_inverse']};
        }}
        QLabel#titleLabel {{
            color: {theme['text_primary']};
            font-size: 16px;
            font-weight: 600;
        }}
        QLabel#statusLabel {{
            color: {theme['online_color']};
            font-size: 12px;
        }}
        """
        
        # 聊天区域样式
        chat_area_style = self.theme_manager.apply_chat_area_style()
        
        # 合并所有样式
        full_style = window_style + main_container_style + title_bar_style + chat_area_style
        self.setStyleSheet(full_style)
        
        # 应用按钮样式
        if hasattr(self, 'theme_button'):
            self.theme_button.set_style_type("tool", self.theme_manager)
        if hasattr(self, 'menu_button'):
            self.menu_button.set_style_type("tool", self.theme_manager)
        if hasattr(self, 'minimize_button'):
            self.minimize_button.set_style_type("tool", self.theme_manager)
        
        # 更新主题图标
        if hasattr(self, 'theme_button'):
            self.theme_button.setText(theme.get('theme_icon', '🌗'))
        
        # 更新推送消息组件主题
        if hasattr(self, 'push_message_widget') and self.push_message_widget:
            is_dark_theme = self.theme_manager.current_theme == "dark"
            self.push_message_widget.update_theme(is_dark_theme)

        # 更新现代化VCPLog通知栏主题
        if hasattr(self, 'vcplog_notification_bar') and self.vcplog_notification_bar:
            is_dark_theme = self.theme_manager.current_theme == "dark"
            self.vcplog_notification_bar.update_theme(is_dark_theme)
        
    
    def toggle_theme(self):
        """切换主题"""
        old_theme = self.theme_manager.current_theme
        new_theme = self.theme_manager.toggle_theme()
        
        logger.info(f"Theme changed from {old_theme} to {new_theme}")
        
        # 重新应用主题
        self.apply_theme()
        
        # 如果有输入区域，也需要重新应用样式
        if self.input_area:
            self.input_area.apply_styling()
        
        # 刷新所有现有的消息气泡
        self.refresh_message_bubbles()
        
        # 显示主题切换提示
        self.show_theme_changed_notification(new_theme)
    
    def refresh_message_bubbles(self):
        """刷新所有消息气泡的样式"""
        for i in range(self.message_layout.count() - 1):  # -1 因为最后一个是stretch
            item = self.message_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, MessageBubble):
                    widget.theme_manager = self.theme_manager
                    widget.apply_styling()
    
    def show_theme_changed_notification(self, theme_name: str):
        """显示主题切换通知"""
        theme_names = {"dark": "深色主题", "light": "浅色主题", "auto": "自动主题"}
        display_name = theme_names.get(theme_name, theme_name)
        
        # 创建临时通知气泡
        notification = MessageBubble("系统", f"已切换到{display_name}", role="system")
        self.add_message_bubble(notification)
        
        # 3秒后自动移除
        QTimer.singleShot(3000, lambda: self.remove_message_bubble(notification))
    
    def show_menu(self):
        """显示菜单"""
        menu = QMenu(self)
        
        # 设置菜单样式
        theme = self.theme_manager.get_current_theme()
        menu.setStyleSheet(f"""
            QMenu {{
                background-color: {theme['window_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 12px;
                padding: 8px;
                color: {theme['text_primary']};
            }}
            QMenu::item {{
                padding: 8px 16px;
                border-radius: 6px;
                margin: 2px;
            }}
            QMenu::item:selected {{
                background-color: {theme['primary']};
                color: {theme['text_inverse']};
            }}
            QMenu::separator {{
                height: 1px;
                background-color: {theme['separator_color']};
                margin: 4px 8px;
            }}
        """)
        
        # 菜单项
        new_topic_action = menu.addAction("✨ 新对话")
        new_topic_action.triggered.connect(self.new_topic_requested_signal.emit)
        
        history_action = menu.addAction("📜 历史记录")
        history_action.triggered.connect(self.show_history)
        
        menu.addSeparator()
        
        # 模型选择子菜单 (AI Model)
        model_menu = menu.addMenu("🤖 选择模型")
        if self.app_controller and hasattr(self.app_controller, 'available_models'):
            current_model = self.app_controller.data_manager.api_model_name
            model_group = QActionGroup(model_menu)
            
            for model_name in self.app_controller.available_models:
                action = model_menu.addAction(model_name)
                action.setCheckable(True)
                action.setChecked(model_name == current_model)
                action.triggered.connect(lambda checked, m=model_name: self.model_selected_signal.emit(m))
                model_group.addAction(action)
        
        # ===== 统一的助手管理菜单 =====
        assistant_submenu = self._create_assistant_submenu() # 调用新的方法创建子菜单
        menu.addMenu(assistant_submenu)
        
        # ===== 移除旧的 Live2D模型选择菜单 =====
        # live2d_menu = menu.addMenu("🎭 Live2D模型")
        # self._populate_live2d_models_menu(live2d_menu)
        
        menu.addSeparator()
        
        settings_action = menu.addAction("⚙️ 设置")
        settings_action.triggered.connect(self.show_settings)
        
        about_action = menu.addAction("ℹ️ 关于")
        about_action.triggered.connect(self.show_about)
        
        # 显示菜单
        button_pos = self.menu_button.mapToGlobal(QPoint(0, self.menu_button.height() + 5))
        menu.exec_(button_pos)
    
    def show_settings(self):
        """显示设置对话框"""
        # current_prompt = "" # 已移除
        # if self.app_controller and hasattr(self.app_controller, 'system_prompt'): # 已移除
        #     current_prompt = self.app_controller.system_prompt or "" # 已移除
        
        # 创建设置对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("设置")
        dialog.setModal(True)
        dialog.resize(350, 150) # 调整对话框大小
        
        layout = QVBoxLayout(dialog)
        
        # 系统提示词设置 - 已移除
        # prompt_label = QLabel("系统提示词:")
        # prompt_edit = QTextEdit()
        # prompt_edit.setPlainText(current_prompt)
        # prompt_edit.setMaximumHeight(150)
        # 
        # layout.addWidget(prompt_label)
        # layout.addWidget(prompt_edit)

        # 添加占位符标签
        placeholder_label = QLabel("更多设置项待添加...")
        placeholder_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(placeholder_label)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok) # 只保留Ok按钮
        button_box.accepted.connect(dialog.accept)
        # button_box.rejected.connect(dialog.reject) # Cancel按钮不再需要
        layout.addWidget(button_box)
        
        # 应用对话框样式
        theme = self.theme_manager.get_current_theme()
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {theme['window_bg']};
                color: {theme['text_primary']};
            }}
            QLabel {{ /* Style for placeholder */
                color: {theme['text_secondary']};
                padding: 10px;
            }}
            QTextEdit {{ /* Keep style for potential future use, though not currently used */
                background-color: {theme['input_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                padding: 8px;
                color: {theme['text_primary']};
            }}
        """)
        
        # 旧的信号发送逻辑 - 已移除
        # if dialog.exec_() == QDialog.Accepted:
        #     new_prompt = prompt_edit.toPlainText().strip()
        #     self.system_prompt_changed_signal.emit(new_prompt)
        
        dialog.exec_() # 仅仅显示对话框
    
    def show_about(self):
        """显示关于对话框"""
        from PyQt5.QtWidgets import QMessageBox
        
        msg = QMessageBox(self)
        msg.setWindowTitle("关于")
        msg.setIconPixmap(QPixmap())  # 可以设置图标
        msg.setText("AI 智能助手")
        msg.setInformativeText(
            "一个现代化的AI聊天助手\n"
            "支持多种AI模型和丰富的交互功能\n\n"
            "版本: 2.0\n"
            "基于 PyQt5 开发"
        )
        
        # 应用主题样式
        theme = self.theme_manager.get_current_theme()
        msg.setStyleSheet(f"""
            QMessageBox {{
                background-color: {theme['window_bg']};
                color: {theme['text_primary']};
            }}
        """)
        
        msg.exec_()
    
    def show_history(self):
        """显示历史记录"""
        if not self.app_controller or not hasattr(self.app_controller, 'get_all_topics_for_display'):
            self.add_system_message("无法加载历史记录")
            return
        
        topics = self.app_controller.get_all_topics_for_display()
        if not topics:
            self.add_system_message("没有历史对话记录")
            return
        
        # 创建历史记录对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("历史对话")
        dialog.setModal(False)
        dialog.resize(500, 600)
        
        layout = QVBoxLayout(dialog)
        
        # 搜索框
        search_edit = QLineEdit()
        search_edit.setPlaceholderText("搜索对话...")
        layout.addWidget(search_edit)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        for topic_data in topics:
            title = topic_data.get("title", "无标题")
            last_updated = topic_data.get("last_updated", "")
            topic_id = topic_data.get("id")
            
            # 格式化时间
            if last_updated:
                try:
                    dt = datetime.fromisoformat(last_updated.replace("Z", "+00:00"))
                    time_str = dt.strftime("%m-%d %H:%M")
                except:
                    time_str = last_updated
            else:
                time_str = "未知时间"
            
            # 创建话题项容器
            topic_frame = QFrame()
            topic_frame.setFrameStyle(QFrame.Box)
            topic_layout = QHBoxLayout(topic_frame)
            
            # 话题信息
            info_layout = QVBoxLayout()
            title_label = QLabel(title)
            title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
            time_label = QLabel(time_str)
            time_label.setStyleSheet("color: gray; font-size: 12px;")
            
            info_layout.addWidget(title_label)
            info_layout.addWidget(time_label)
            topic_layout.addLayout(info_layout)
            
            # 按钮容器
            button_layout = QVBoxLayout()
            
            # 选择按钮
            select_btn = QPushButton("选择")
            select_btn.clicked.connect(lambda checked, tid=topic_id: self._select_topic_by_id(tid, dialog))
            select_btn.setFixedSize(60, 30)
            
            # 删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.clicked.connect(lambda checked, tid=topic_id, ttitle=title: self._confirm_delete_topic(tid, ttitle, dialog))
            delete_btn.setFixedSize(60, 30)
            delete_btn.setStyleSheet("QPushButton { background-color: #FF3B30; color: white; }")
            
            button_layout.addWidget(select_btn)
            button_layout.addWidget(delete_btn)
            topic_layout.addLayout(button_layout)
            
            scroll_layout.addWidget(topic_frame)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 搜索功能
        def filter_topics(text):
            for i in range(scroll_layout.count()):
                frame = scroll_layout.itemAt(i).widget()
                if isinstance(frame, QFrame):
                    title_label = frame.findChild(QLabel)
                    if title_label:
                        visible = text.lower() in title_label.text().lower()
                        frame.setVisible(visible)
        
        search_edit.textChanged.connect(filter_topics)
        
        # 应用样式
        self._apply_dialog_style(dialog)
        
        dialog.show()
    
    def _select_topic(self, item: QListWidgetItem, dialog: QDialog):
        """选择话题"""
        topic_id = item.data(Qt.UserRole)
        if topic_id:
            self.topic_selected_signal.emit(topic_id)
            dialog.accept()
    
    def _select_topic_by_id(self, topic_id: str, dialog: QDialog):
        """通过ID选择话题"""
        if topic_id:
            self.topic_selected_signal.emit(topic_id)
            dialog.accept()
    
    def _confirm_delete_topic(self, topic_id: str, title: str, dialog: QDialog):
        """确认删除话题"""
        from PyQt5.QtWidgets import QMessageBox
        
        # 创建确认对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("确认删除")
        msg_box.setText(f"确定要删除对话 '{title}' 吗？")
        msg_box.setInformativeText("此操作无法撤销。")
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg_box.setDefaultButton(QMessageBox.No)
        
        # 设置按钮文本
        yes_button = msg_box.button(QMessageBox.Yes)
        no_button = msg_box.button(QMessageBox.No)
        yes_button.setText("删除")
        no_button.setText("取消")
        
        # 应用样式
        theme = self.theme_manager.get_current_theme()
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {theme['window_bg']};
                color: {theme['text_primary']};
            }}
            QMessageBox QPushButton {{
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                min-width: 60px;
            }}
            QMessageBox QPushButton[text="删除"] {{
                background-color: #FF3B30;
                color: white;
                border: none;
            }}
            QMessageBox QPushButton[text="取消"] {{
                background-color: {theme['input_bg']};
                color: {theme['text_primary']};
                border: 1px solid {theme['border_color']};
            }}
        """)
        
        # 显示确认对话框
        result = msg_box.exec_()
        
        if result == QMessageBox.Yes:
            # 发出删除信号
            self.topic_deleted_signal.emit(topic_id)
            # 关闭历史对话框并重新打开以刷新列表
            dialog.accept()
            # 延迟重新打开历史对话框
            QTimer.singleShot(100, self.show_history)
    
    def _apply_dialog_style(self, dialog: QDialog):
        """应用对话框样式"""
        theme = self.theme_manager.get_current_theme()
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {theme['window_bg']};
                color: {theme['text_primary']};
            }}
            QLineEdit {{
                background-color: {theme['input_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                padding: 8px;
                color: {theme['text_primary']};
            }}
            QListWidget {{
                background-color: {theme['chat_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                color: {theme['text_primary']};
            }}
            QListWidget::item {{
                padding: 12px;
                border-radius: 6px;
                margin: 2px;
            }}
            QListWidget::item:selected {{
                background-color: {theme['primary']};
                color: {theme['text_inverse']};
            }}
            QListWidget::item:hover {{
                background-color: {theme['border_color']};
            }}
        """)
    def _display_command_result(self, result: CommandResult):
        """
        显示命令执行结果
        
        Args:
            result: 命令执行结果
        """
        # 使用新的 _add_system_message 方法来显示结果
        if result.success:
            self._add_system_message(result.message, "success")
        else:
            self._add_system_message(result.message, "error")

    def _add_system_message(self, message: str, message_type: str = "info"):
        """
        添加带特定类型的系统消息到聊天窗口
        
        Args:
            message: 消息内容
            message_type: 消息类型 ("info", "success", "error", "warning")
        """
        sender_prefix = ""
        actual_sender = "系统" # 默认 MessageBubble sender for system messages

        if message_type == "success":
            sender_prefix = "✅ "
        elif message_type == "error":
            sender_prefix = "❌ "
            actual_sender = "SystemError" # This is recognized by MessageBubble as is_system
        elif message_type == "warning":
            sender_prefix = "⚠️ "
        else: # info
            sender_prefix = "ℹ️ "

        formatted_message = sender_prefix + message
        
        system_bubble = MessageBubble(actual_sender, formatted_message, datetime.now(), self, role="system")
        self.add_message_bubble(system_bubble)
        
        logger.info(f"System message ({message_type}) added to chat: {formatted_message}")

    def _handle_normal_message(self, text: str, image_path: Optional[str], attachment_path: Optional[str]):
        """
        处理普通聊天消息的发送逻辑 (发送信号并显示打字指示器)
        用户消息气泡应在此方法调用前已添加。
        """
        if self.app_controller:
            self.send_composed_message_signal.emit(text,
                                                 image_path if image_path else None,
                                                 attachment_path if attachment_path else None)
            self.show_typing_indicator()
        else:
            logger.warning("AppController not set in ChatWindow, cannot process normal message via signal.")

    def handle_send_message(self, text: str, image_path: str, attachment_path: str):
        """处理用户输入（包括普通消息和命令）"""
        
        original_text = text
        original_image_path = image_path
        original_attachment_path = attachment_path

        # 检查是否有任何输入内容
        if not text.strip() and not original_image_path and not original_attachment_path:
            return

        # 用户输入的消息气泡应该总是先显示
        # (确保即使文本为空但有附件时也显示用户气泡)
        if text or original_image_path or original_attachment_path: # 只有当有内容时才创建气泡
            user_bubble = MessageBubble("User", text, datetime.now(), self, role="user")
            if original_image_path and os.path.exists(original_image_path):
                user_bubble.set_image(original_image_path)
            self.add_message_bubble(user_bubble)

        # 首先尝试处理命令 (仅当有文本输入时)
        if text.strip(): # 命令通常是纯文本
            if not hasattr(self, 'command_parser'): # 防御性检查
                logger.error("Command parser not initialized in ChatWindow.")
                # 如果 command_parser 未初始化，则作为普通消息处理
                self._handle_normal_message(original_text, original_image_path, original_attachment_path)
                return

            command_result = self.command_parser.execute_command(text, self.app_controller)
            if command_result: # Assumes execute_command returns a CommandResult object or None/False
                self._display_command_result(command_result)
                
                # 特殊处理成功的TTS切换命令
                if (command_result.success and
                    command_result.data and
                    isinstance(command_result.data, dict) and
                    'service_id' in command_result.data):
                    logger.info(f"TTS service switched successfully via command: {command_result.data}")
                return # 命令已处理，结束

        # 如果不是命令 (或者 command_parser 未初始化，或者 execute_command 返回 None/False)
        self._handle_normal_message(original_text, original_image_path, original_attachment_path)
    
    def on_model_changed(self, model_name: str):
        """模型改变时的处理"""
        if model_name:
            self.model_selected_signal.emit(model_name)
            self.add_system_message(f"已切换到模型: {model_name}")
    
    def handle_voice_recording(self, is_recording: bool):
        """处理语音录制"""
        if is_recording:
            self.add_system_message("开始录音...")
            self.status_label.setText("🎤 录音中")
            self.status_label.setStyleSheet("color: #FF3B30;")
        else:
            self.add_system_message("录音结束")
            self.status_label.setText("● 在线")
            theme = self.theme_manager.get_current_theme()
            self.status_label.setStyleSheet(f"color: {theme['online_color']};")
    
    def handle_game_companion_toggle(self, activate: bool):
        """处理游戏伴侣模式切换"""
        self.is_game_companion_active = activate
        
        if activate:
            # 启动定时器
            self.game_companion_timer.start(self.companion_interval_seconds * 1000)  # 转为毫秒
            
            # 显示启动消息
            self.add_system_message(
                f"🎮 游戏伴侣模式已开启 (每 {self.companion_interval_seconds} 秒分析一次屏幕)"
            )
            logger.info(f"游戏伴侣模式已开启，截图间隔: {self.companion_interval_seconds}s")
        else:
            # 停止定时器
            self.game_companion_timer.stop()
            
            # 显示停止消息
            self.add_system_message("🛑 游戏伴侣模式已关闭")
            logger.info("游戏伴侣模式已关闭")
    
    def _trigger_timed_companion_action(self):
        """游戏伴侣定时分析核心方法"""
        if not self.is_game_companion_active or not self.app_controller:
            return
        
        logger.info("游戏伴侣：定时器触发，执行全屏截图并发送")
        
        # 调用AppController的全屏截图方法
        if hasattr(self.app_controller, 'capture_full_screen_and_get_path'):
            screenshot_path = self.app_controller.capture_full_screen_and_get_path()
            
            if screenshot_path and os.path.exists(screenshot_path):
                logger.info(f"游戏伴侣：获取到全屏截图 {screenshot_path}，准备发送")
                
                # 从配置文件读取提示词
                prompt_text = self.companion_config['prompt']
                
                # 通过现有消息处理流程发送
                self.app_controller._handle_composed_message(
                    message_text=prompt_text,
                    image_path=screenshot_path,
                    audio_data_url=None
                )
            else:
                logger.warning("游戏伴侣：自动全屏截图失败或未返回有效路径")
                self.add_system_message("⚠️ 游戏伴侣：截屏失败")
        else:
            logger.error("游戏伴侣：AppController 缺少 capture_full_screen_and_get_path 方法")
            self.add_system_message("❌ 游戏伴侣：截图功能配置错误")
            # 发生错误时自动关闭模式
            self.handle_game_companion_toggle(False)
            if self.input_area and self.input_area.game_companion_button:
                self.input_area.game_companion_button.setChecked(False)
                self.input_area.toggle_game_companion_mode()
    
    # === 消息管理方法 ===
    
    def add_message_bubble(self, bubble: MessageBubble):
        """添加消息气泡"""
        # 插入到stretch之前
        index = self.message_layout.count() - 1
        self.message_layout.insertWidget(index, bubble)
        
        # 自动滚动到底部
        QTimer.singleShot(100, self.scroll_area.scroll_to_bottom)
    
    def remove_message_bubble(self, bubble: MessageBubble): # 或者 bubble: QWidget
        """移除消息气泡"""
        # 检查 bubble 是否有效并且确实在布局中
        if bubble is None: # 如果 bubble 是 None，直接返回
            return

        found_and_removed = False
        for i in range(self.message_layout.count()):
            item = self.message_layout.itemAt(i)
            if item and item.widget() == bubble:
                # 从布局中移除项目
                self.message_layout.takeAt(i) # 或者 item.widget().setParent(None)
                # 彻底删除控件
                bubble.deleteLater()
                found_and_removed = True
                break # 找到并移除后就可以退出了

        if not found_and_removed:
            # 如果代码走到这里，说明传入的 bubble 不在布局中
            # 这可能是正常情况（比如它已经被移除了），也可能是逻辑问题
            # 可以选择性地打印一个警告，但对于 typing_indicator 来说，可能多次调用 hide 是正常的
            # logger.warning(f"Attempted to remove a bubble that was not found in the layout: {bubble}")
            pass
    
    def add_system_message(self, text: str):
        """添加系统消息"""
        system_bubble = MessageBubble("系统", text, datetime.now(), self, role="system")
        self.add_message_bubble(system_bubble)
    
    def show_typing_indicator(self):
        """显示打字指示器"""
        if self.typing_indicator:
            return
        
        self.typing_indicator = TypingIndicator(self)
        self.add_message_bubble(self.typing_indicator)
        self.typing_indicator.start_animation()
    
    def hide_typing_indicator(self):
        """隐藏打字指示器"""
        if self.typing_indicator:
            self.remove_message_bubble(self.typing_indicator)
            self.typing_indicator = None
    
    # === VCPLog消息处理方法 ===
    
    def handle_vcp_log_message(self, message_data: Dict[str, Any]):
        """处理VCPLog接收到的VCP工具调用日志"""
        try:
            if VCPLogMessageHandler.is_vcp_log_message(message_data):
                # 优先使用现代化VCPLog通知栏
                if hasattr(self, 'vcplog_notification_bar') and self.vcplog_notification_bar:
                    self.vcplog_notification_bar.add_vcp_message(message_data)

                    # 记录日志
                    data = message_data.get('data', {})
                    tool_name = data.get('tool_name', 'Unknown')
                    status = data.get('status', 'unknown')
                    logger.info(f"VCP日志已添加到现代化通知栏 (工具: {tool_name}, 状态: {status})")

                # 兼容性：同时添加到旧的推送消息组件
                elif hasattr(self, 'push_message_widget') and self.push_message_widget:
                    self.push_message_widget.add_vcp_message(message_data)

                    # 记录日志
                    data = message_data.get('data', {})
                    tool_name = data.get('tool_name', 'Unknown')
                    status = data.get('status', 'unknown')
                    logger.info(f"VCP日志已添加到推送消息区域 (工具: {tool_name}, 状态: {status})")
                else:
                    # 降级处理：如果通知组件都不可用，使用原来的方式
                    formatted_message = VCPLogMessageHandler.format_vcp_log_message(message_data)
                    vcp_bubble = MessageBubble(
                        formatted_message['sender'],
                        formatted_message['content'],
                        datetime.now(),
                        self
                    )
                    self.add_message_bubble(vcp_bubble)
                    logger.warning("通知组件不可用，VCP消息显示在正常聊天区域")

        except Exception as e:
            logger.error(f"处理VCP日志消息时出错: {e}")
    
    def handle_vcplog_status_change(self, is_connected: bool):
        """处理VCPLog连接状态变化"""
        status_text = "已连接" if is_connected else "未连接"
        logger.info(f"VCPLog状态: {status_text}")

        # 更新现代化通知栏的连接状态
        if hasattr(self, 'vcplog_notification_bar') and self.vcplog_notification_bar:
            if is_connected:
                self.vcplog_notification_bar.update_connection_status("connected", "已连接")
            else:
                self.vcplog_notification_bar.update_connection_status("disconnected", "连接断开")

        # 在界面上显示连接状态
        if hasattr(self, 'status_label') and self.status_label:
            if is_connected:
                self.status_label.setText("● VCPLog已连接")
                self.status_label.setStyleSheet("color: #4CAF50;")
            else:
                self.status_label.setText("● 在线")
                theme = self.theme_manager.get_current_theme()
                self.status_label.setStyleSheet(f"color: {theme['online_color']};")

    def handle_vcplog_error(self, error_message: str):
        """处理VCPLog错误"""
        logger.error(f"VCPLog错误: {error_message}")

        # 更新现代化通知栏的错误状态
        if hasattr(self, 'vcplog_notification_bar') and self.vcplog_notification_bar:
            self.vcplog_notification_bar.update_connection_status("error", f"连接错误: {error_message}")

        # 在界面上显示错误消息
        self.add_system_message(f"VCPLog连接错误: {error_message}")
    
    def handle_agent_message(self, message_data: Dict[str, Any]):
        """处理AgentMessage插件推送的AI通知消息 🆕"""
        try:
            from websocket_client import AgentMessageHandler

            if AgentMessageHandler.is_agent_message(message_data):
                # 优先使用现代化VCPLog通知栏
                if hasattr(self, 'vcplog_notification_bar') and self.vcplog_notification_bar:
                    # 将Agent消息转换为VCP消息格式
                    vcp_format_data = {
                        'type': 'agent_message',
                        'data': message_data
                    }
                    self.vcplog_notification_bar.add_vcp_message(vcp_format_data)

                    # 记录日志
                    message_content = message_data.get('message', '')
                    recipient = message_data.get('recipient', 'AI助手')
                    logger.info(f"Agent消息已添加到现代化通知栏 (发送者: {recipient}, 内容: {message_content[:50]}...)")

                    # 可选：显示桌面通知
                    if hasattr(self.app_controller, 'show_desktop_notification'):
                        self.app_controller.show_desktop_notification(
                            title=f"来自 {recipient} 的消息",
                            message=message_content[:100]
                        )

                # 兼容性：同时添加到旧的推送消息组件
                elif hasattr(self, 'push_message_widget') and self.push_message_widget:
                    self.push_message_widget.add_agent_message(message_data)

                    # 记录日志
                    message_content = message_data.get('message', '')
                    recipient = message_data.get('recipient', 'AI助手')
                    logger.info(f"Agent消息已添加到推送消息区域 (发送者: {recipient}, 内容: {message_content[:50]}...)")

                    # 可选：显示桌面通知
                    if hasattr(self.app_controller, 'show_desktop_notification'):
                        self.app_controller.show_desktop_notification(
                            title=f"来自 {recipient} 的消息",
                            message=message_content[:100]
                        )
                else:
                    # 降级处理：如果通知组件都不可用，使用原来的方式
                    formatted_message = AgentMessageHandler.format_agent_message(message_data)
                    agent_bubble = MessageBubble(
                        formatted_message['sender'],
                        formatted_message['content'],
                        datetime.now(),
                        self
                    )
                    self.add_message_bubble(agent_bubble)
                    logger.warning("通知组件不可用，Agent消息显示在正常聊天区域")

        except Exception as e:
            logger.error(f"处理Agent消息时出错: {e}")
    
    def start_vcplog_connection(self):
        """启动VCPLog连接"""
        if hasattr(self, 'vcplog_client'):
            # 尝试从配置或环境变量获取VCP_Key
            vcp_key = self._get_vcp_key_from_config()
            if vcp_key:
                self.vcplog_client.update_vcp_key(vcp_key)
                self.vcplog_client.connect()
                logger.info("正在启动VCPLog连接...")
            else:
                logger.warning("VCP_Key未配置，无法连接VCPLog服务")
                self.add_system_message("⚠️ VCP_Key未配置，无法连接VCPLog服务")
    
    def stop_vcplog_connection(self):
        """停止VCPLog连接"""
        if hasattr(self, 'vcplog_client'):
            self.vcplog_client.disconnect()
            logger.info("已停止VCPLog连接")
    
    def _get_vcp_key_from_config(self) -> str:
        """从配置中获取VCP_Key"""
        try:
            print("DEBUG: 开始获取VCP_Key")
            # 优先从app_controller的配置中获取
            if (self.app_controller and
                hasattr(self.app_controller, 'config') and
                hasattr(self.app_controller.config, 'get')):
                vcp_key = self.app_controller.config.get('VCP_Key', '')
                print(f"DEBUG: 从app_controller获取VCP_Key: '{vcp_key}'")
                if vcp_key:
                    return vcp_key
            
            # 尝试从环境变量获取
            import os
            vcp_key = os.getenv('VCP_Key', '')
            print(f"DEBUG: 从环境变量获取VCP_Key: '{vcp_key}'")
            if vcp_key:
                return vcp_key
                
            # 尝试从配置文件获取
            config_paths = [
                'config.env',
                'Plugin/VCPLog/config.env',
                os.path.join(os.path.dirname(__file__), '..', 'config.env'),
                os.path.join(os.path.dirname(__file__), 'config.env')
            ]
            
            print(f"DEBUG: 配置文件搜索路径: {config_paths}")
            
            for config_path in config_paths:
                print(f"DEBUG: 检查配置文件: {config_path}")
                if os.path.exists(config_path):
                    print(f"DEBUG: 配置文件存在: {config_path}")
                    with open(config_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(f"DEBUG: 配置文件内容: '{content}'")
                        import re
                        match = re.search(r'VCP_Key\s*=\s*(.+)', content)
                        if match:
                            result = match.group(1).strip().strip('"').strip("'")
                            print(f"DEBUG: 正则匹配到VCP_Key: '{result}'")
                            return result
                        else:
                            print("DEBUG: 正则匹配失败")
                else:
                    print(f"DEBUG: 配置文件不存在: {config_path}")
                            
        except Exception as e:
            print(f"DEBUG: 获取VCP_Key时出错: {e}")
            logger.warning(f"获取VCP_Key时出错: {e}")
        
        print("DEBUG: 所有方法都未获取到VCP_Key，返回空字符串")
        return ""
    
    def cleanup_on_app_exit(self):
        """应用程序真正退出时的清理方法"""
        logger.info("应用程序退出，清理VCPLog连接...")
        if hasattr(self, 'vcplog_client'):
            self.vcplog_client.disconnect()
        logger.info("VCPLog连接已清理")

    # === 现代化VCPLog通知栏控制方法 ===

    def toggle_vcplog_sidebar(self):
        """切换VCPLog侧边栏"""
        if hasattr(self, 'vcplog_notification_bar') and self.vcplog_notification_bar:
            return self.vcplog_notification_bar.toggle_sidebar()
        return False

    def clear_vcplog_notifications(self):
        """清空VCPLog通知"""
        if hasattr(self, 'vcplog_notification_bar') and self.vcplog_notification_bar:
            self.vcplog_notification_bar.clear_messages()

    def get_vcplog_message_count(self) -> int:
        """获取VCPLog消息数量"""
        if hasattr(self, 'vcplog_notification_bar') and self.vcplog_notification_bar:
            return self.vcplog_notification_bar.get_message_count()
        return 0

    @pyqtSlot(str)
    def on_vcplog_status_changed(self, status: str):
        """VCPLog状态变化处理"""
        logger.info(f"VCPLog状态: {status}")
        # 可以在这里添加额外的状态处理逻辑

    @pyqtSlot()
    def on_vcplog_toggle_requested(self):
        """VCPLog切换请求处理"""
        logger.info("收到VCPLog侧边栏切换请求")
        # 可以在这里添加额外的切换逻辑
    
    # === 流式响应处理方法 ===
    
    def handle_app_ai_stream_start(self, message_id: str, sender_override: Optional[str] = None):
        """处理AI流式响应开始"""
        
        # 隐藏打字指示器
        self.hide_typing_indicator()
        
        sender = sender_override if sender_override else "AI"
        
        # 获取当前助手头像路径
        avatar_path = None
        current_assistant = self.assistant_manager.get_current_assistant()
        if current_assistant and current_assistant.avatar_path:
            avatar_path = self.assistant_manager.get_full_avatar_path(current_assistant.avatar_path)
        # 修正：助手名称用 current_assistant.name
        sender = current_assistant.name if current_assistant else (sender_override if sender_override else "AI")
        ai_bubble = MessageBubble(sender, "", datetime.now(), self, avatar_path=avatar_path, role="assistant")
        self.add_message_bubble(ai_bubble)
        
        # 存储到活动消息中
        self.active_message_elements[message_id] = {
            'bubble_instance': ai_bubble,
            'sender_role': sender,
            'full_content': "",
            'start_time': datetime.now()
        }
        
        self.last_history_sender_was_ai = (sender.lower() == "ai")
    
    def handle_app_ai_stream_chunk(self, message_id: str, text_chunk: str):
        """处理AI流式响应片段"""
        
        if message_id not in self.active_message_elements:
            logger.warning(f"Message ID {message_id} not found in active elements")
            return
        
        msg_info = self.active_message_elements[message_id]
        bubble_instance = msg_info.get('bubble_instance')
        
        if bubble_instance:
            # 累积内容
            msg_info['full_content'] += text_chunk
              # 在流式更新过程中，检查是否有完整的img标签
            full_content = msg_info['full_content']
            
            # 检查是否有完整的img标签，如果有就实时显示
            import re
            complete_img_pattern = r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>'
            complete_images = re.findall(complete_img_pattern, full_content)
            
            if complete_images:
                # 如果有完整的图片，使用混合内容更新
                bubble_instance.update_content(full_content)
            else:
                # 如果没有完整图片，只更新文本内容
                display_text = self._remove_img_tags(full_content)
                bubble_instance._update_streaming_text(display_text)
            
            # 自动滚动
            self.scroll_area.scroll_to_bottom()
    def handle_app_ai_image_display(self, message_id: str, local_path: str, original_url: str, width: Optional[int], height: Optional[int]):
        """处理AI图片显示"""
        # 暂时禁用旧的图片显示逻辑，因为我们现在使用混合内容显示
        return
        
        # 以下是旧的逻辑，已禁用
        if message_id not in self.active_message_elements:
            logger.warning(f"Message ID {message_id} not found for image display")
            return
        
        msg_info = self.active_message_elements[message_id]
        bubble_instance = msg_info.get('bubble_instance')
        
        if bubble_instance and os.path.exists(local_path):
            # 设置图片，使用指定的尺寸或默认最大宽度
            max_width = width if width and width > 0 else 250
            bubble_instance.set_image(local_path, max_width)
            
            # 自动滚动
            self.scroll_area.scroll_to_bottom()
    
    def handle_app_ai_raw_block(self, message_id: str, block_type: str, raw_block_content: str):
        """处理AI原始块内容"""
        logger.info(f"Raw block: {message_id}, type: {block_type}")
        
        if message_id not in self.active_message_elements:
            logger.warning(f"Message ID {message_id} not found for raw block")
            return
        
        msg_info = self.active_message_elements[message_id]
        bubble_instance = msg_info.get('bubble_instance')
        
        if bubble_instance:
            # 添加原始块到内容
            block_text = f"\n\n[{block_type}]\n{raw_block_content}\n[/{block_type}]"
            msg_info['full_content'] += block_text
            
            # 在流式过程中使用流式文本更新，避免重建导致图片消失
            display_text = self._remove_img_tags(msg_info['full_content'])
            bubble_instance._update_streaming_text(display_text)
            
            # 自动滚动
            self.scroll_area.scroll_to_bottom()
    
    def handle_ai_stream_finished(self, message_id: str, full_text_for_tts: str, original_message_obj: Optional[Any] = None, last_event_data: Optional[Dict[str, Any]] = None, metadata: Optional[Dict[str, Any]] = None):
        """处理AI流式响应结束"""
          # 确保隐藏打字指示器（多重保障）
        self.hide_typing_indicator()
        
        metadata = metadata or {}
        is_final = metadata.get("is_final_stream_end", False)
        is_error = metadata.get("is_error", False)
        
        if message_id in self.active_message_elements:
            msg_info = self.active_message_elements[message_id]
            bubble_instance = msg_info.get('bubble_instance')
            
            if bubble_instance:
                if is_final:
                    # 流式结束时，检查是否需要完整重建内容
                    full_content_with_images = msg_info['full_content']
                    
                    logger.info(f"🔥🔥🔥🔥🔥 完整内容长度: {len(full_content_with_images)}")
                    logger.info(f"🔥🔥🔥🔥🔥 完整内容前100字符: {full_content_with_images[:100]}")
                    
                    # 检查内容中是否有图片标签
                    import re
                    img_pattern = r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>'
                    has_images = bool(re.search(img_pattern, full_content_with_images))
                    
                    logger.info(f"🔥🔥🔥🔥🔥 检测到图片: {has_images}")
                    
                    if has_images:
                        logger.info(f"🔥🔥🔥🔥🔥 调用 _finalize_mixed_content")
                        # 如果有图片，使用智能更新避免清空已加载的图片
                        bubble_instance._finalize_mixed_content(full_content_with_images)
                    else:
                        logger.info(f"🔥🔥🔥🔥🔥 没有图片，只更新文本")
                        # 如果没有图片，直接更新文本即可
                        final_content = self._remove_img_tags(full_content_with_images)
                        bubble_instance._update_streaming_text(final_content)
                else:
                    # 非最终结束时，只更新文本内容
                    final_content = self._remove_img_tags(msg_info['full_content'])
                    bubble_instance._update_streaming_text(final_content)
                
                # 如果是错误，高亮显示
                if is_error:
                    bubble_instance.highlight_briefly(2000)
            
            # 如果是最终结束，清理活动消息
            if is_final:
                del self.active_message_elements[message_id]
        
        # 更新状态
        if is_final:
            sender_role = ""
            if message_id in self.active_message_elements:
                sender_role = self.active_message_elements[message_id].get('sender_role', "").lower()
            
            if sender_role == "ai":
                self.last_history_sender_was_ai = True
            elif sender_role == "user":
                self.last_history_sender_was_ai = False
        
        # 确保滚动到底部
        self.scroll_area.scroll_to_bottom()
    
    # === 图片处理方法 ===
    
    def _process_images_in_content(self, message_id: str, content: str):
        """处理内容中的图片"""
        # 查找所有img标签
        img_pattern = r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>'
        matches = re.findall(img_pattern, content, re.IGNORECASE)
        
        logger.debug(f"Found {len(matches)} images in content for {message_id}")
        
        for img_url in matches:
            process_key = f"{message_id}_{img_url}"
            
            if process_key in self.processed_images:
                continue
            
            self.processed_images[process_key] = True
            logger.debug(f"Processing new image: {img_url}")
            
            # 异步加载图片
            self._load_and_display_image(message_id, img_url)
    
    def _load_and_display_image(self, message_id: str, img_url: str):
        """异步加载并显示图片 - 添加请求缓存 (优化建议 4)"""
        # 检查缓存
        try:
            cache_key = hashlib.md5(img_url.encode()).hexdigest()
            # 使用应用特定的缓存目录可能更好，但暂时用系统临时目录
            cache_dir = os.path.join(tempfile.gettempdir(), "ai_pet_img_cache")
            os.makedirs(cache_dir, exist_ok=True) # 确保目录存在
            # 使用原始 URL 的一部分作为文件名可能有助于调试，但哈希值是唯一标识
            # 文件扩展名可能未知，可以尝试从 URL 推断或默认 .img
            file_ext = os.path.splitext(img_url)[1] or ".img"
            cache_path = os.path.join(cache_dir, f"{cache_key}{file_ext}")

            if os.path.exists(cache_path):
                logger.info(f"Image cache hit for {img_url} at {cache_path}")
                # 检查文件是否有效（例如，大小 > 0）
                if os.path.getsize(cache_path) > 0:
                    self.update_image_signal.emit(message_id, cache_path)
                    return # 缓存命中，直接返回
                else:
                    logger.warning(f"Cached image file is empty, removing: {cache_path}")
                    os.remove(cache_path) # 无效缓存文件，删除

        except Exception as e:
            logger.error(f"Error accessing image cache for {img_url}: {e}")
            # 缓存出错，继续尝试下载

        # --- 缓存未命中或无效，执行下载 ---
        def load_image_thread():
            temp_path = None # 初始化变量
            try:
                logger.debug(f"Loading image (cache miss): {img_url}")
                
                # 下载图片
                response = requests.get(img_url, timeout=10, headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
                response.raise_for_status()
                
                # 保存到临时文件
                # 使用 NamedTemporaryFile 来确保唯一性，但下载后需要复制到缓存路径
                with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as tmp_file:
                    tmp_file.write(response.content)
                    temp_path = tmp_file.name # 获取临时文件路径
                
                logger.debug(f"Image downloaded to temporary file: {temp_path}")

                # 下载后保存到缓存 (优化建议 4)
                try:
                    shutil.copy2(temp_path, cache_path) # 使用 copy2 保留元数据
                    logger.info(f"Image saved to cache: {cache_path}")
                except Exception as copy_err:
                    logger.error(f"Failed to save image to cache {cache_path}: {copy_err}")
                    # 即使缓存失败，仍然尝试使用临时文件显示
                
                # 在主线程中更新UI (使用缓存路径或临时路径)
                display_path = cache_path if os.path.exists(cache_path) else temp_path
                if display_path: # 确保路径有效
                    self.update_image_signal.emit(message_id, display_path)
                
            except Exception as e:
                logger.error(f"Failed to load image {img_url}: {e}")
            finally:
                # 清理临时文件（如果它不是最终显示的路径）
                if temp_path and os.path.exists(temp_path) and temp_path != display_path:
                    try:
                        os.remove(temp_path)
                        logger.debug(f"Temporary download file removed: {temp_path}")
                    except Exception as rm_err:
                        logger.warning(f"Failed to remove temporary download file {temp_path}: {rm_err}")

        # 在单独线程中加载
        threading.Thread(target=load_image_thread, daemon=True).start()
    
    def _update_image_in_bubble_safe(self, message_id: str, image_path: str):
        """线程安全的图片更新方法"""
        logger.debug(f"Updating image in bubble for {message_id}: {image_path}")
        
        msg_info = self.active_message_elements.get(message_id)
        if not msg_info:
            logger.warning(f"Message {message_id} not found for image update")
            return
        
        bubble_instance = msg_info.get('bubble_instance')
        if bubble_instance:
            try:
                # 设置图片
                bubble_instance.set_image(image_path, 200)
                
                # 滚动到底部
                self.scroll_area.scroll_to_bottom()
                
                logger.debug(f"Image successfully set for {message_id}")
                
            except Exception as e:
                logger.error(f"Failed to set image: {e}")
            finally:
                # 清理临时文件
                try:
                    if os.path.exists(image_path):
                        os.remove(image_path)
                        logger.debug(f"Temp file removed: {image_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove temp file: {e}")
    
    def _remove_img_tags(self, content: str) -> str:
        """移除HTML img标签（包括不完整的标签）"""
        import re
        # 移除完整的img标签
        content = re.sub(r'<img[^>]*>', '', content)
        # 移除不完整的img标签开始部分
        content = re.sub(r'<img[^>]*$', '', content)
        return content
    
    # === 历史记录处理 ===
    
    def clear_chat_display(self):
        """清空聊天显示 - 添加内存优化"""
        # 移除所有消息气泡（除了最后的stretch）
        logger.debug("Clearing chat display...")
        widgets_to_delete = []
        while self.message_layout.count() > 1:
            item = self.message_layout.takeAt(0)
            if item and item.widget():
                widgets_to_delete.append(item.widget())
        
        for widget in widgets_to_delete:
             widget.deleteLater() # 标记删除
        logger.debug(f"Marked {len(widgets_to_delete)} widgets for deletion.")

        # 清空相关数据
        self.active_message_elements.clear()
        self.last_history_sender_was_ai = False
        self.processed_images.clear()
        
        # 隐藏打字指示器
        self.hide_typing_indicator()
        
        # 显式调用垃圾回收 (优化建议 6)
        gc.collect()
        logger.info("Chat display cleared and GC collected.")
    
    def populate_with_history(self, history_list: List[Dict[str, Any]]):
        """用历史记录填充聊天显示 - 虚拟滚动实现"""
        self.clear_chat_display()
        logger.info(f"Populating chat with {len(history_list)} history entries using virtual scroll.")

        self.full_history = history_list
        
        if not self.full_history:
            return

        # 计算初始加载的条目
        initial_load_count = min(len(self.full_history), self.HISTORY_BATCH_SIZE)
        self.current_history_offset = len(self.full_history) - initial_load_count
        
        initial_batch = self.full_history[self.current_history_offset:]
        
        for entry in initial_batch:
            self._create_bubble_from_history_entry(entry)

        # 滚动到底部
        QTimer.singleShot(100, self.scroll_area.scroll_to_bottom)
        logger.info(f"Finished initial population of {len(initial_batch)} entries.")

    def load_previous_messages(self):
        """加载上一批历史消息"""
        if self.is_loading_history or self.current_history_offset <= 0:
            return

        self.is_loading_history = True
        logger.info(f"Loading previous messages. Current offset: {self.current_history_offset}")

        # 获取旧的滚动条最大值，用于后续恢复位置
        scroll_bar = self.scroll_area.verticalScrollBar()
        old_scroll_max = scroll_bar.maximum()

        # 计算要加载的下一批消息
        load_count = min(self.current_history_offset, self.HISTORY_BATCH_SIZE)
        start_index = self.current_history_offset - load_count
        batch = self.full_history[start_index:self.current_history_offset]
        
        # 将新消息插入到布局顶部
        for entry in reversed(batch): # 从旧到新插入
            self._create_bubble_from_history_entry(entry, insert_at_top=True)
            
        # 强制UI处理事件以更新滚动条范围
        QApplication.processEvents()

        # 恢复滚动条位置
        new_scroll_max = scroll_bar.maximum()
        scroll_bar.setValue(new_scroll_max - old_scroll_max)

        self.current_history_offset = start_index
        logger.info(f"Finished loading {len(batch)} previous messages. New offset: {self.current_history_offset}")
        
        # 使用QTimer重置标志位，确保UI稳定后再允许下一次加载
        QTimer.singleShot(100, lambda: setattr(self, 'is_loading_history', False))

    def _create_bubble_from_history_entry(self, entry: Dict[str, Any], insert_at_top: bool = False):
        """从单个历史条目创建并添加MessageBubble"""
        role = entry.get("role", "unknown").lower()
        content = entry.get("content", "")
        parts = entry.get("parts", [])

        # 创建气泡
        if role in ["ai", "assistant"]:
            assistant_id = entry.get("assistant_id")
            assistant_name = entry.get("assistant_name", "AI")
            avatar_path = None
            if assistant_id:
                assistant = self.assistant_manager.get_assistant(assistant_id)
                if assistant and assistant.avatar_path:
                    avatar_path = self.assistant_manager.get_full_avatar_path(assistant.avatar_path)
            bubble = MessageBubble(assistant_name, "", datetime.now(), self, avatar_path=avatar_path, role=role)
        else:
            sender = "User" if role == "user" else "AI"
            bubble = MessageBubble(sender, "", datetime.now(), self, role=role)
        
        # 添加到布局
        if insert_at_top:
            self.message_layout.insertWidget(0, bubble)
        else:
            self.add_message_bubble(bubble)

        # 提取并设置内容
        full_content_text_with_html = ""
        if isinstance(content, str) and content:
            full_content_text_with_html = content
        elif isinstance(parts, list) and parts:
            text_parts = []
            for part_entry in parts:
                if isinstance(part_entry, dict):
                    if part_entry.get("type") == "text":
                        text_parts.append(part_entry.get("text", ""))
            full_content_text_with_html = "\n".join(text_parts)

        full_content_text_with_html = full_content_text_with_html.strip()
        if full_content_text_with_html:
            bubble.update_content(full_content_text_with_html)
        
        # 这里我们不再处理图片加载，假设图片在update_content中处理
        # 或者后续有专门的逻辑处理图片懒加载
        
    def display_error_message(self, error_text: str, is_critical: bool = False):
        """显示错误消息"""
        # 出现错误时清理打字指示器
        self.hide_typing_indicator()
        
        logger.error(f"Error message: {error_text} (critical: {is_critical})")
        
        sender = "系统错误" if is_critical else "系统"
        error_bubble = MessageBubble(sender, error_text, datetime.now(), self, role="system")
        
        # 如果是严重错误，高亮显示
        if is_critical:
            error_bubble.highlight_briefly(3000)
        
        self.add_message_bubble(error_bubble)
    
    # === 窗口控制方法 ===
    
    def _update_cursor_shape(self, pos: QPoint):
        """根据鼠标位置更新光标形状"""
        if self.isMaximized() or self.isFullScreen():
            self.setCursor(Qt.ArrowCursor)
            return

        edges = self._get_edges(pos)
        if edges["top"] and edges["left"]:
            self.setCursor(Qt.SizeFDiagCursor)
        elif edges["top"] and edges["right"]:
            self.setCursor(Qt.SizeBDiagCursor)
        elif edges["bottom"] and edges["left"]:
            self.setCursor(Qt.SizeBDiagCursor)
        elif edges["bottom"] and edges["right"]:
            self.setCursor(Qt.SizeFDiagCursor)
        elif edges["top"] or edges["bottom"]:
            self.setCursor(Qt.SizeVerCursor)
        elif edges["left"] or edges["right"]:
            self.setCursor(Qt.SizeHorCursor)
        else:
            self.setCursor(Qt.ArrowCursor)

    def _get_edges(self, pos: QPoint) -> Dict[str, bool]:
        """获取鼠标所在的边缘"""
        rect = self.rect()
        return {
            "top": pos.y() < self.border_width,
            "bottom": pos.y() > rect.height() - self.border_width,
            "left": pos.x() < self.border_width,
            "right": pos.x() > rect.width() - self.border_width,
        }
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            pos = event.pos()
            # 检查是否点击在标题栏区域
            if hasattr(self, 'title_bar') and self.title_bar.geometry().contains(pos):
                self.is_dragging = True
                self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()
            # 检查是否点击在可调整大小的边框上
            elif not (self.isMaximized() or self.isFullScreen()):
                self.resize_edge = self._get_edges(pos)
                if any(self.resize_edge.values()):
                    self.is_resizing = True
                    self.resize_start_position = event.globalPos()
                    self.resize_start_geometry = self.geometry()
            
            event.accept()
            return
        
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        pos = event.pos()
        if self.is_dragging and event.buttons() & Qt.LeftButton:
            new_pos = event.globalPos() - self.drag_start_position
            self.move(new_pos)
            event.accept()
        elif self.is_resizing and event.buttons() & Qt.LeftButton:
            self._resize_window(event.globalPos())
            event.accept()
        else:
            self._update_cursor_shape(pos)
            super().mouseMoveEvent(event)
    
    def _resize_window(self, global_pos: QPoint):
        """根据鼠标位置调整窗口大小"""
        delta = global_pos - self.resize_start_position
        new_geo = QRect(self.resize_start_geometry)

        if self.resize_edge["left"]:
            new_geo.setLeft(self.resize_start_geometry.left() + delta.x())
        if self.resize_edge["right"]:
            new_geo.setRight(self.resize_start_geometry.right() + delta.x())
        if self.resize_edge["top"]:
            new_geo.setTop(self.resize_start_geometry.top() + delta.y())
        if self.resize_edge["bottom"]:
            new_geo.setBottom(self.resize_start_geometry.bottom() + delta.y())

        # 保持最小尺寸
        if new_geo.width() < self.minimumWidth():
            new_geo.setWidth(self.minimumWidth())
        if new_geo.height() < self.minimumHeight():
            new_geo.setHeight(self.minimumHeight())
            
        self.setGeometry(new_geo)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            self.is_resizing = False
            self.resize_edge = None
            self.setCursor(Qt.ArrowCursor)
            event.accept()
        else:
            super().mouseReleaseEvent(event)
    
    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        if hasattr(self, 'show_animation'):
            self.show_animation.start()
    
    def closeEvent(self, event):
        """窗口关闭事件 - 添加资源清理 (优化建议 8)"""
        # --- 新增：检查免疫标志 ---
        if self._is_immune_to_close:
            event.ignore() # 忽略事件，不做任何处理
            return

        # 检查是否有强制关闭的标志位
        if hasattr(self, '_is_forcing_close') and self._is_forcing_close:
            event.accept() # 强制关闭，接受事件
            return
            
        logger.info("Close event triggered. Hiding window instead of closing.")
        
        # 注意：这里不清理WebSocket连接，因为closeEvent只是隐藏窗口，不是退出程序
        # WebSocket连接应该保持，以便在后台继续接收陪玩消息
        
        # # --- 错误代码：当窗口只是隐藏时，不应该删除所有子控件 ---
        # # 清理所有子组件 (尤其是那些可能持有外部资源或定时器的)
        # children_to_delete = self.findChildren(QObject)
        # logger.debug(f"Found {len(children_to_delete)} child QObjects to deleteLater.")
        # for child in children_to_delete:
        #     # 避免删除自身或父对象（理论上 findChildren 不会返回自身）
        #     if child != self and child.parent() == self: # 只删除直接子对象？或者所有后代？
        #          # 检查是否有 deleteLater 方法，以防万一
        #          if hasattr(child, 'deleteLater'):
        #              try:
        #                  child.deleteLater()
        #              except Exception as e:
        #                  logger.warning(f"Error calling deleteLater on child {type(child)}: {e}")

        # 调用父类的 closeEvent 之前执行隐藏动画
        event.ignore() # 忽略原始关闭事件，使用动画隐藏
        self.animated_hide()
        logger.info("Hiding window. The close event was ignored and animated_hide() was called.")
        # super().closeEvent(event) # 不在这里调用 super，因为我们用动画隐藏替代了

    def hideEvent(self, event):
        """当窗口被隐藏时调用 (无论是通过 self.hide() 还是其他方式)"""
        super().hideEvent(event)
        self.window_hidden_signal.emit() # <--- 发出信号
        logger.info("ChatWindow hidden, emitted window_hidden_signal.")
    
    def animated_hide(self):
        """动画隐藏窗口"""
        if hasattr(self, 'hide_animation'):
            # 确保 hide_animation.finished 连接到 self.hide 或一个能发出信号的方法
            # 如果它直接连接到 self.hide，那么 hideEvent 会被调用
            self.hide_animation.start()
        else:
            self.hide() # 直接隐藏也会触发 hideEvent
    
    def show_window(self):
        """显示窗口"""
        self.show()
        self.raise_()
        self.activateWindow()
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 确保主容器大小匹配窗口
        if hasattr(self, 'main_container'):
            self.main_container.resize(self.size())

    def eventFilter(self, obj, event):
        """事件过滤器 - 添加性能监控 (优化建议 7)"""
        # 注意：这个 eventFilter 是 ChatWindow 级别的，可以捕获所有子控件的事件
        # 如果只想监控特定子控件，需要在该子控件上 installEventFilter
        start_time = time.time()
        
        # 调用父类的 eventFilter 来处理事件
        # 如果 ChatWindow 本身没有重写 eventFilter，这将调用 QWidget 的默认实现
        result = super().eventFilter(obj, event)
        
        processing_time = time.time() - start_time
        
        # 记录处理时间过长的操作
        if processing_time > 0.05:  # 超过 50ms
            try:
                # 尝试获取更详细的事件信息
                event_type = event.type()
                obj_info = f"Object: {type(obj).__name__}" if obj else "Object: None"
                logger.warning(f"Event processing took {processing_time:.4f}s: Type={event_type}, {obj_info}") # 使用 .4f 提高精度
            except Exception as log_e:
                # 防止日志记录本身出错
                logger.warning(f"Event processing took {processing_time:.4f}s (logging event details failed: {log_e})")

        return result
    
    # === 兼容性方法 ===
    
    def enable_capture_button(self):
        """启用截图按钮（兼容性方法）"""
        if self.input_area and hasattr(self.input_area, 'tool_buttons'):
            screenshot_btn = self.input_area.tool_buttons.get('screenshot')
            if screenshot_btn:
                screenshot_btn.setEnabled(True)
    
    def disable_capture_button(self):
        """禁用截图按钮（兼容性方法）"""
        if self.input_area and hasattr(self.input_area, 'tool_buttons'):
            screenshot_btn = self.input_area.tool_buttons.get('screenshot')
            if screenshot_btn:
                screenshot_btn.setEnabled(False)

    def set_selected_screenshot(self, image_path: str):
        """由 AppController 调用，设置截图结果到输入区域"""
        if self.input_area:
            self.input_area.selected_image_path = image_path
            self.input_area.selected_attachment_path = None # 清除可能的其他附件
            self.input_area.update_preview() # 调用 ModernInputArea 的方法来更新UI
            logger.info(f"ChatWindow: Screenshot set to input area: {image_path}")
        else:
            logger.warning("ChatWindow: input_area not found, cannot set screenshot.")

    # === SSE相关方法 ===
    
    def _handle_sse_data(self, sse_data: str):
        """处理从SSE客户端接收到的数据"""
        try:
            data = json.loads(sse_data)
            message_id = data.get("id", f"sse_stream_{uuid.uuid4().hex[:8]}")
            
            # 处理连接类型消息
            if data.get("type") == "connection":
                self._add_system_message(f"📡 {data.get('message', 'SSE连接事件')}")
                return
                
            # 处理心跳消息
            elif data.get("type") == "ping":
                logger.debug("SSE心跳消息")
                return
                
            # 处理AI流式消息（OpenAI格式）- 简化版本，参考companion_client.html
            elif "choices" in data and data["choices"] and "delta" in data["choices"][0]:
                delta = data["choices"][0].get("delta", {})
                ai_content = delta.get("content", "")
                
                if ai_content and ai_content.strip():
                    # 直接创建新的AI消息，不进行流式拼接
                    ai_bubble = MessageBubble("AI小爱", ai_content, datetime.now())
                    self.add_message_bubble(ai_bubble)
                    logger.info(f"接收到AI消息块: {ai_content[:50]}...")
                    
            # 处理流结束事件
            elif data.get("type") == "stream_end":
                stream_id = data.get("stream_id", message_id)
                if stream_id in self.active_message_elements:
                    full_text = self.active_message_elements.get(stream_id, {}).get('full_content', "")
                    self.handle_ai_stream_finished(
                        stream_id,
                        full_text_for_tts=full_text,
                        metadata={"is_final_stream_end": True}
                    )
                    
            # 处理其他类型的消息
            else:
                message_text = data.get("message", json.dumps(data, ensure_ascii=False, indent=2))
                self._add_system_message(f"📨 SSE: {message_text}")
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode SSE JSON data: {sse_data[:200]}...")
            # 检查是否是数据被截断的问题
            if "Unterminated string" in str(e) or len(sse_data) > 1000:
                logger.warning("可能收到不完整的SSE数据，忽略此消息")
                return
            
            # 尝试多种编码修复方法
            for encoding_method in ['latin1', 'iso-8859-1', 'cp1252']:
                try:
                    if isinstance(sse_data, str):
                        # 尝试不同的编码修复方法
                        fixed_data = sse_data.encode(encoding_method).decode('utf-8', errors='replace')
                        # 清理可能的JSON格式问题
                        fixed_data = fixed_data.replace('\ufffd', '?')  # 替换无法解码的字符
                        data = json.loads(fixed_data)
                        
                        # 如果成功解析，记录并处理
                        content_preview = data.get('choices', [{}])[0].get('delta', {}).get('content', '')[:30]
                        logger.info(f"使用 {encoding_method} 编码修复成功: {content_preview}...")
                        self._handle_sse_data(fixed_data)
                        return
                except Exception:
                    continue
            
            # 所有修复方法都失败了
            logger.error("所有编码修复方法都失败")
            self._add_system_message(f"⚠️ 收到无法解析的SSE消息: {sse_data[:50]}...", "error")
        except Exception as e:
            logger.error(f"Error processing SSE data: {e}")
            self._add_system_message(f"❌ 处理SSE消息时出错: {str(e)}", "error")
    
    def _handle_sse_connection_status(self, connected: bool, message: str):
        """处理SSE连接状态变化"""
        self.sse_connected = connected
        
        if connected:
            self._add_system_message(f"🎉 成功连接到SSE服务器！({message})", "success")
        else:
            self._add_system_message(f"🔌 与SSE服务器断开连接。({message})", "error")
    
    def _handle_sse_error(self, error_message: str):
        """处理SSE错误"""
        logger.error(f"SSE Error: {error_message}")
        self._add_system_message(f"❌ SSE错误: {error_message}", "error")
    
    def toggle_sse_connection(self):
        """切换SSE连接状态"""
        if self.sse_connected:
            # self.sse_client.disconnect_from_server()
            pass
        else:
            if self.sse_url:
                # self.sse_client.set_connection_params(self.sse_url)
                # self.sse_client.connect_to_server()
                pass
            else:
                self._add_system_message("❌ 请先设置SSE服务器地址", "error")
    
    def set_sse_url(self, url: str):
        """设置SSE服务器地址"""
        self.sse_url = url.strip()
        logger.info(f"SSE URL set to: {self.sse_url}")
    
    def _auto_connect_sse(self):
        """自动连接SSE服务器"""
        if self.sse_url:
            logger.info(f"Auto-connecting to SSE: {self.sse_url}")
            # self.sse_client.set_connection_params(self.sse_url)
            # self.sse_client.connect_to_server()

    # --- Assistant Management Methods ---
    def update_system_prompt(self, new_prompt: str):
        """更新系统提示词并发出信号"""
        self.system_prompt = new_prompt
        self.system_prompt_changed_signal.emit(self.system_prompt)
        # Optional: Add a system message to notify user if desired
        # self._add_system_message(f"助手系统提示词已更新。")
        logger.info(f"System prompt updated by assistant manager.")

    def _create_assistant_submenu(self) -> QMenu:
        """创建助手管理子菜单"""
        assistant_menu = QMenu("🤖 助手管理", self)
        assistant_menu.setObjectName("assistantMenu")

        current_assistant = self.assistant_manager.get_current_assistant()
        
        # 显示当前助手名称 (不可点击)
        current_assistant_action = QAction(f"当前: {current_assistant.name if current_assistant else '无'}", self)
        current_assistant_action.setEnabled(False)
        assistant_menu.addAction(current_assistant_action)
        assistant_menu.addSeparator()

        # 列出所有可用的助手
        assistant_group = QActionGroup(self)
        assistant_group.setExclusive(True)

        assistants = self.assistant_manager.get_all_assistants() # 更正: list_assistants -> get_all_assistants
        if not assistants:
            no_assistants_action = QAction("没有可用的助手", self)
            no_assistants_action.setEnabled(False)
            assistant_menu.addAction(no_assistants_action)
        else:
            for assistant_obj in assistants:
                action = QAction(assistant_obj.name, self)
                action.setCheckable(True)
                if current_assistant and assistant_obj.id == current_assistant.id:
                    action.setChecked(True)
                
                # 需要 functools.partial
                action.triggered.connect(partial(self._switch_assistant, assistant_obj.id))
                assistant_group.addAction(action)
                assistant_menu.addAction(action)

        assistant_menu.addSeparator()

        # 新建助手
        new_assistant_action = QAction("➕ 新建助手...", self)
        new_assistant_action.triggered.connect(self._show_new_assistant_dialog)
        assistant_menu.addAction(new_assistant_action)

        # 管理助手
        manage_assistants_action = QAction("⚙️ 管理助手...", self)
        manage_assistants_action.triggered.connect(self._show_manage_assistant_dialog)
        assistant_menu.addAction(manage_assistants_action)
        
        return assistant_menu

    def _switch_assistant(self, assistant_id: str):
        """切换助手"""
        print(f"DEBUG: ChatWindow._switch_assistant called. self.live2d_widget is: {self.live2d_widget}") # <--- 调试打印
        success = self.assistant_manager.switch_assistant(assistant_id, self.live2d_widget, self)
        if success:
            current_assistant = self.assistant_manager.get_current_assistant()
            if current_assistant: # 应该总是有，因为切换成功了
                self.update_system_prompt(current_assistant.system_prompt) # 更新 system_prompt 并触发信号
                 # 更新窗口标题等（如果需要）
                self.title_label.setText(f"{current_assistant.name}") # 更新标题栏的助手名称
                self._add_system_message(f"助手已切换为: {current_assistant.name}")
            else: # 后备，理论上不应发生
                self._add_system_message(f"切换到助手 {assistant_id}，但无法获取当前助手信息。")
        else:
            self._add_system_message(f"切换助手 {assistant_id} 失败。")
        
        # 可能需要刷新菜单以正确反映勾选状态，但这通常由QActionGroup处理
        # 如果 live2d_widget 为 None, assistant_manager.switch_assistant 内部会处理

    def _show_new_assistant_dialog(self):
        """显示新建助手对话框"""
        if not self.assistant_manager:
            self._add_system_message("错误: AssistantManager 未初始化，无法新建助手。")
            return

        dialog = NewAssistantDialog(self, self.assistant_manager)
        # 对话框的 accept() 方法内部处理了助手的创建和成功/失败消息
        # 如果成功，它会调用 super().accept() 关闭对话框
        # 菜单会在下次打开时自动刷新，因为 _create_assistant_submenu 会重新获取助手列表
        dialog.exec_() # exec_() 会阻塞直到对话框关闭

    def _show_manage_assistant_dialog(self):
        """显示管理助手对话框"""
        if not self.assistant_manager:
            self._add_system_message("错误: AssistantManager 未初始化，无法管理助手。")
            return

        dialog = ManageAssistantsDialog(self, self.assistant_manager)
        # ManageAssistantsDialog 内部处理助手的编辑和删除
        # 对话框关闭后，下次菜单打开时会刷新
        dialog.exec_()

    # --- End Assistant Management Methods ---

    @pyqtSlot(str, str)
    def update_gag_reference_fields(self, audio_path: str, text: str):
        """
        槽函数：由AppController调用，用于更新界面上显示的GAG-TTS参考设置。
        """
        # TODO: 将这里的 'self.gag_ref_audio_line_edit' 和 'self.gag_ref_text_line_edit'
        # 替换为实际的UI组件变量名。
        print(f"UI更新：接收到新的参考设置: Path='{audio_path}', Text='{text}'")
        # self.gag_ref_audio_line_edit.setText(audio_path)
        # self.gag_ref_text_line_edit.setText(text)
        pass

    def _init_ui(self):
        """
        初始化UI组件
        """
        # 添加输入历史缓存 (优化建议 2)
        self.history_cache = {}

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 20)
        layout.setSpacing(12)
        
        # 文件预览区域
        self.preview_area = self.create_preview_area()
        layout.addWidget(self.preview_area)
        
        # 工具栏
        self.toolbar = self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # 输入区域
        self.input_container = self.create_input_container()
        layout.addWidget(self.input_container)
        
        # 应用样式
        self.apply_styling()

    def create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # 工具按钮配置
        tools = [
            ("📎", "附件", self.select_attachment, "attachment"),
            ("🖼️", "图片", self.select_image, "image"),
            ("🎤", "语音输入", self.toggle_asr_button_action, "asr_voice_input"),
            ("😊", "表情", self.show_emoji_picker, "emoji"),
            ("📸", "截图", self.take_screenshot, "screenshot"),
            ("🗣️", "语音设置", self._show_tts_settings_dialog, "tts_settings"),
            ("🎮", "游戏伴侣", self.toggle_game_companion_mode, "game_companion"),
            ("✨", "划词助手", self.toggle_selection_assistant, "selection_assistant"),  # 新增
        ]

        self.tool_buttons = {}
        for icon, tooltip, callback, key in tools:
            btn = ModernButton(icon=icon)
            btn.setFixedSize(36, 36)
            btn.setToolTip(tooltip)
            btn.clicked.connect(callback)

            if hasattr(self, 'theme_manager') and self.theme_manager:
                btn.set_style_type("tool", self.theme_manager)

            layout.addWidget(btn)
            self.tool_buttons[key] = btn

        layout.addStretch()

        # 模型选择器（如果需要）
        if hasattr(self, 'create_model_selector'):
            self.model_selector = self.create_model_selector()
            layout.addWidget(self.model_selector)

        # 划词助手按钮特殊配置
        self.selection_assistant_button = self.tool_buttons.get("selection_assistant")
        if self.selection_assistant_button:
            self.selection_assistant_button.setCheckable(True)  # 设为可切换按钮

        return toolbar

    def create_preview_area(self) -> QWidget:
        """创建文件预览区域"""
        preview_area = QWidget()
        preview_area.setVisible(False)  # 默认隐藏
        return preview_area

    def create_input_container(self) -> QWidget:
        """创建输入容器"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)

        # 输入区域
        if hasattr(self, 'input_area'):
            layout.addWidget(self.input_area)

        return container

    def toggle_selection_assistant(self):
        """切换划词助手状态"""
        if self.app_controller and hasattr(self.app_controller, 'toggle_selection_assistant'):
            success = self.app_controller.toggle_selection_assistant()

            # 更新按钮状态
            if self.selection_assistant_button:
                is_enabled = self.app_controller.is_selection_assistant_enabled()
                self.selection_assistant_button.setChecked(is_enabled)

                # 更新按钮样式
                if is_enabled:
                    self.selection_assistant_button.setStyleSheet(
                        self.selection_assistant_button.styleSheet() +
                        "background-color: #4CAF50; color: white;"
                    )
                else:
                    # 重置样式
                    if hasattr(self, 'theme_manager') and self.theme_manager:
                        self.selection_assistant_button.set_style_type("tool", self.theme_manager)
        else:
            self.add_system_message("⚠️ 划词助手功能不可用")

    def set_close_immunity(self, immune: bool):
        """设置一个标志，以暂时忽略接下来可能收到的伪造 closeEvent"""
        self._is_immune_to_close = immune

    def on_scroll_changed(self, value):
        """滚动条位置变化时调用，用于实现无限滚动加载"""
        if not self.is_loading_history and value == 0 and self.current_history_offset > 0:
            self.load_previous_messages()


# === 测试代码 ===

if __name__ == "__main__":
    import sys
    
    # 设置高DPI支持
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = ChatWindow()
    
    # 添加测试消息
    def add_test_messages():
        # 用户消息
        user_bubble = MessageBubble("User", "你好！请介绍一下自己", datetime.now())
        window.add_message_bubble(user_bubble)
        
        # AI回复
        ai_bubble = MessageBubble("AI", 
            "你好！我是AI智能助手，很高兴为您服务！😊\n\n"
            "我可以帮助您：\n"
            "• 回答各种问题\n"
            "• 编写和调试代码\n" 
            "• 翻译文本\n"
            "• 创意写作\n"
            "• 分析图片内容\n\n"
            "有什么我可以帮助您的吗？", 
            datetime.now()
        )
        window.add_message_bubble(ai_bubble)
        
        # 系统消息
        system_bubble = MessageBubble("系统", "连接已建立，开始对话", datetime.now())
        window.add_message_bubble(system_bubble)
    
    # 延迟添加测试消息，让窗口动画完成
    QTimer.singleShot(500, add_test_messages)
    
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())