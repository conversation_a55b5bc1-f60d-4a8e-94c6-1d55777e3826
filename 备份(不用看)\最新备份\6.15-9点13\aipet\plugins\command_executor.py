import subprocess
import platform
import shlex
from typing import Dict, Any

# =====================================================================================
# !!! SECURITY WARNING !!!
# This plugin allows the AI to execute arbitrary shell commands on the user's system.
# This is EXTREMELY DANGEROUS and can lead to data loss, security breaches, or
# system instability if not handled with extreme care.
#
# For production or shared use, it is STRONGLY RECOMMENDED to implement additional
# safety measures, such as user confirmation before any command is executed.
# =====================================================================================


def execute_command(command: str) -> str:
    """
    Executes a shell command on the user's operating system and returns the output.

    Args:
        command: The command string to execute.

    Returns:
        A string containing the standard output and standard error of the command.
    """
    try:
        # A timeout is crucial to prevent the application from hanging on long-running commands.
        timeout_seconds = 30

        print(f"!!! EXECUTING DANGEROUS COMMAND: {command}")

        if platform.system() == "Windows":
            # On Windows, `shell=True` is often more reliable for commands that
            # users would type in a `cmd` prompt.
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout_seconds,
                encoding='utf-8',
                errors='ignore'
            )
        else:
            # On Linux/macOS, `shlex.split` is more robust and secure.
            args = shlex.split(command)
            result = subprocess.run(
                args,
                capture_output=True,
                text=True,
                timeout=timeout_seconds,
                encoding='utf-8',
                errors='ignore'
            )

        output = ""
        if result.stdout:
            output += f"STDOUT:\n{result.stdout}\n"
        if result.stderr:
            output += f"STDERR:\n{result.stderr}\n"

        if not output:
            return "Command executed successfully with no output."

        return output

    except FileNotFoundError:
        return f"Error: Command not found. Make sure it's in your system's PATH."
    except subprocess.TimeoutExpired:
        return f"Error: Command timed out after {timeout_seconds} seconds."
    except Exception as e:
        return f"Error: An unexpected error occurred while executing the command: {str(e)}"

def get_tools() -> Dict[str, Dict[str, Any]]:
    return {
        "execute_command": {
            "function": execute_command,
            "definition": {
                "type": "function",
                "function": {
                    "name": "execute_command",
                    "description": (
                        "Executes a shell command on the user's local machine and returns the output. "
                        "!!! DANGER !!! This tool can modify files, install software, or damage the system. "
                        "Use it with extreme caution. For simple file operations like read/write/list, prefer using the dedicated file tools. "
                        "Examples: `ls -l`, `pip install numpy`, `echo 'hello'`."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "command": {
                                "type": "string",
                                "description": "The shell command to be executed."
                            }
                        },
                        "required": ["command"]
                    }
                }
            }
        }
    } 