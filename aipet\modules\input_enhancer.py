"""
输入增强模块 - 基于 VCPChat 的输入处理功能
提供智能文件处理、剪贴板增强、自动文本处理等功能
"""

import os
import tempfile
import mimetypes
from typing import Optional, List, Dict, Any
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QMimeData, QUrl, QTimer
from PyQt5.QtGui import QClipboard, QPixmap
import logging

logger = logging.getLogger(__name__)

class InputEnhancer:
    """输入增强器"""
    
    # 常量定义
    LONG_TEXT_THRESHOLD = 2000  # 长文本阈值
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 最大文件大小 50MB
    SUPPORTED_IMAGE_FORMATS = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']
    SUPPORTED_TEXT_FORMATS = ['.txt', '.md', '.py', '.js', '.html', '.css', '.json']
    
    def __init__(self, input_area, app_controller=None):
        self.input_area = input_area
        self.app_controller = app_controller
        self.clipboard = QApplication.clipboard()
        self.last_clipboard_content = ""
        
        # 设置剪贴板监听
        self.clipboard.dataChanged.connect(self.on_clipboard_changed)
        
        # 拖拽状态
        self.drag_active = False
        
    def setup_enhanced_input(self):
        """设置增强输入功能"""
        if hasattr(self.input_area, 'input_field'):
            text_edit = self.input_area.input_field
            
            # 设置拖拽支持
            text_edit.setAcceptDrops(True)
            text_edit.dragEnterEvent = self.drag_enter_event
            text_edit.dragMoveEvent = self.drag_move_event
            text_edit.dragLeaveEvent = self.drag_leave_event
            text_edit.dropEvent = self.drop_event
            
            # 增强粘贴功能
            original_paste = text_edit.paste
            text_edit.paste = self.enhanced_paste
            
            logger.info("输入增强功能已设置")
    
    def drag_enter_event(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls() or event.mimeData().hasText():
            event.acceptProposedAction()
            self.drag_active = True
            self.add_drag_visual_feedback()
        else:
            event.ignore()
    
    def drag_move_event(self, event):
        """拖拽移动事件"""
        if self.drag_active:
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def drag_leave_event(self, event):
        """拖拽离开事件"""
        self.drag_active = False
        self.remove_drag_visual_feedback()
    
    def drop_event(self, event):
        """拖拽放下事件"""
        self.drag_active = False
        self.remove_drag_visual_feedback()
        
        mime_data = event.mimeData()
        
        if mime_data.hasUrls():
            self.handle_file_drop(mime_data.urls())
        elif mime_data.hasText():
            self.handle_text_drop(mime_data.text())
        
        event.acceptProposedAction()
    
    def handle_file_drop(self, urls: List[QUrl]):
        """处理文件拖拽"""
        for url in urls:
            if url.isLocalFile():
                file_path = url.toLocalFile()
                self.process_dropped_file(file_path)
    
    def process_dropped_file(self, file_path: str):
        """处理拖拽的文件"""
        try:
            if not os.path.exists(file_path):
                self.show_error("文件不存在")
                return
            
            file_size = os.path.getsize(file_path)
            if file_size > self.MAX_FILE_SIZE:
                self.show_error(f"文件太大，最大支持 {self.MAX_FILE_SIZE // (1024*1024)}MB")
                return
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in self.SUPPORTED_IMAGE_FORMATS:
                self.handle_image_file(file_path)
            elif file_ext in self.SUPPORTED_TEXT_FORMATS:
                self.handle_text_file(file_path)
            else:
                self.handle_general_file(file_path)
                
        except Exception as e:
            logger.error(f"处理文件失败: {e}")
            self.show_error(f"处理文件失败: {str(e)}")
    
    def handle_image_file(self, file_path: str):
        """处理图片文件"""
        if hasattr(self.input_area, 'set_image_attachment'):
            self.input_area.set_image_attachment(file_path)
            self.show_info(f"已添加图片: {os.path.basename(file_path)}")
        else:
            self.insert_file_reference(file_path)
    
    def handle_text_file(self, file_path: str):
        """处理文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if len(content) > self.LONG_TEXT_THRESHOLD:
                # 长文本作为附件
                if hasattr(self.input_area, 'set_file_attachment'):
                    self.input_area.set_file_attachment(file_path)
                    self.show_info(f"已添加文本文件: {os.path.basename(file_path)}")
                else:
                    self.insert_file_reference(file_path)
            else:
                # 短文本直接插入
                self.insert_text_content(content, os.path.basename(file_path))
                
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
                self.insert_text_content(content, os.path.basename(file_path))
            except:
                self.handle_general_file(file_path)
        except Exception as e:
            logger.error(f"读取文本文件失败: {e}")
            self.handle_general_file(file_path)
    
    def handle_general_file(self, file_path: str):
        """处理一般文件"""
        if hasattr(self.input_area, 'set_file_attachment'):
            self.input_area.set_file_attachment(file_path)
            self.show_info(f"已添加文件: {os.path.basename(file_path)}")
        else:
            self.insert_file_reference(file_path)
    
    def handle_text_drop(self, text: str):
        """处理文本拖拽"""
        if len(text) > self.LONG_TEXT_THRESHOLD:
            self.handle_long_text(text)
        else:
            self.insert_text(text)
    
    def enhanced_paste(self):
        """增强的粘贴功能"""
        mime_data = self.clipboard.mimeData()
        
        # 检查是否有图片
        if mime_data.hasImage():
            self.handle_image_paste(mime_data)
            return
        
        # 检查是否有文件
        if mime_data.hasUrls():
            self.handle_file_drop(mime_data.urls())
            return
        
        # 处理文本
        if mime_data.hasText():
            text = mime_data.text()
            if len(text) > self.LONG_TEXT_THRESHOLD:
                self.handle_long_text(text)
            else:
                self.insert_text(text)
            return
        
        # 默认粘贴
        if hasattr(self.input_area.input_field, 'paste'):
            self.input_area.input_field.paste()
    
    def handle_image_paste(self, mime_data: QMimeData):
        """处理图片粘贴"""
        try:
            image = self.clipboard.image()
            if not image.isNull():
                # 保存到临时文件
                temp_path = tempfile.mktemp(suffix='.png')
                if image.save(temp_path):
                    self.handle_image_file(temp_path)
                else:
                    self.show_error("保存剪贴板图片失败")
            else:
                self.show_error("剪贴板中没有有效图片")
        except Exception as e:
            logger.error(f"处理图片粘贴失败: {e}")
            self.show_error(f"处理图片失败: {str(e)}")
    
    def handle_long_text(self, text: str):
        """处理长文本"""
        reply = QMessageBox.question(
            self.input_area,
            "长文本处理",
            f"检测到长文本({len(text)}字符)，是否转换为文件附件？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            self.convert_text_to_file(text)
        else:
            self.insert_text(text)
    
    def convert_text_to_file(self, text: str):
        """将文本转换为文件"""
        try:
            # 创建临时文件
            temp_path = tempfile.mktemp(suffix='.txt')
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(text)
            
            self.handle_text_file(temp_path)
            
        except Exception as e:
            logger.error(f"转换文本为文件失败: {e}")
            self.show_error(f"转换失败: {str(e)}")
    
    def insert_text(self, text: str):
        """插入文本到输入框"""
        if hasattr(self.input_area, 'input_field'):
            cursor = self.input_area.input_field.textCursor()
            cursor.insertText(text)
    
    def insert_text_content(self, content: str, filename: str):
        """插入文本内容（带文件名标识）"""
        formatted_text = f"\n--- {filename} ---\n{content}\n--- 文件结束 ---\n"
        self.insert_text(formatted_text)
    
    def insert_file_reference(self, file_path: str):
        """插入文件引用"""
        file_ref = f"[文件: {os.path.basename(file_path)}]({file_path})"
        self.insert_text(file_ref)
    
    def on_clipboard_changed(self):
        """剪贴板内容变化处理"""
        try:
            current_content = self.clipboard.text()
            if current_content != self.last_clipboard_content:
                self.last_clipboard_content = current_content
                # 这里可以添加智能剪贴板处理逻辑
                
        except Exception as e:
            logger.debug(f"剪贴板监听错误: {e}")
    
    def add_drag_visual_feedback(self):
        """添加拖拽视觉反馈"""
        if hasattr(self.input_area, 'input_field'):
            self.input_area.input_field.setStyleSheet(
                self.input_area.input_field.styleSheet() + 
                "border: 2px dashed #4CAF50; background-color: rgba(76, 175, 80, 0.1);"
            )
    
    def remove_drag_visual_feedback(self):
        """移除拖拽视觉反馈"""
        if hasattr(self.input_area, 'apply_styling'):
            # 重新应用原始样式
            QTimer.singleShot(100, self.input_area.apply_styling)
    
    def show_info(self, message: str):
        """显示信息提示"""
        if hasattr(self.input_area, 'parent') and self.input_area.parent():
            QMessageBox.information(self.input_area.parent(), "信息", message)
        else:
            logger.info(message)
    
    def show_error(self, message: str):
        """显示错误提示"""
        if hasattr(self.input_area, 'parent') and self.input_area.parent():
            QMessageBox.warning(self.input_area.parent(), "错误", message)
        else:
            logger.error(message)
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件信息"""
        try:
            stat = os.stat(file_path)
            mime_type, _ = mimetypes.guess_type(file_path)
            
            return {
                'name': os.path.basename(file_path),
                'size': stat.st_size,
                'mime_type': mime_type or 'application/octet-stream',
                'extension': os.path.splitext(file_path)[1].lower(),
                'modified': stat.st_mtime
            }
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return {}
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        # 这里可以添加临时文件清理逻辑
        pass
