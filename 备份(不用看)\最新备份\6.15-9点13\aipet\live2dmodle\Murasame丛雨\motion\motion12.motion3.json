{"Version": 3, "Meta": {"Duration": 8, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 92, "TotalSegmentCount": 7444, "TotalPointCount": 7536, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 8, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 2.3, 1.312, 2, 2.333, 4.443, 2, 2.367, 8.26, 2, 2.4, 11.391, 2, 2.433, 12.703, 2, 2.467, 10.868, 2, 2.5, 6.031, 2, 2.533, -0.808, 2, 2.567, -8.649, 2, 2.6, -16.489, 2, 2.633, -23.328, 2, 2.667, -28.165, 2, 2.7, -30, 2, 2.733, -28.333, 2, 2.767, -23.805, 2, 2.8, -16.981, 2, 2.833, -8.846, 2, 2.867, 0, 2, 2.9, 8.846, 2, 2.933, 16.981, 2, 2.967, 23.805, 2, 3, 28.333, 2, 3.033, 30, 2, 3.067, 29.009, 2, 3.1, 26.155, 2, 3.133, 21.908, 2, 3.167, 16.46, 2, 3.2, 10.187, 2, 3.233, 3.508, 2, 3.267, -3.508, 2, 3.3, -10.187, 2, 3.333, -16.46, 2, 3.367, -21.908, 2, 3.4, -26.155, 2, 3.433, -29.009, 2, 3.467, -30, 2, 3.5, -29.129, 2, 3.533, -26.689, 2, 3.567, -22.882, 2, 3.6, -18.146, 2, 3.633, -12.484, 2, 3.667, -6.373, 2, 3.7, 0, 2, 3.733, 6.373, 2, 3.767, 12.484, 2, 3.8, 18.146, 2, 3.833, 22.882, 2, 3.867, 26.689, 2, 3.9, 29.129, 2, 3.933, 30, 2, 3.967, 29.248, 2, 4, 27.231, 2, 4.033, 24.116, 2, 4.067, 20.205, 2, 4.1, 15.872, 2, 4.133, 11.172, 2, 4.167, 6.473, 2, 4.2, 2.14, 2, 4.233, -1.771, 2, 4.267, -4.886, 2, 4.3, -6.903, 2, 4.333, -7.655, 2, 4.367, -7.442, 2, 4.4, -6.865, 2, 4.433, -5.994, 2, 4.467, -4.956, 2, 4.5, -3.828, 2, 4.533, -2.699, 2, 4.567, -1.661, 2, 4.6, -0.79, 2, 4.633, -0.213, 2, 4.667, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.167, 2.255, 2, 0.2, 7.726, 2, 0.233, 15, 2, 0.267, 22.274, 2, 0.3, 27.745, 2, 0.333, 30, 2, 0.367, 29.096, 2, 0.4, 26.673, 2, 0.433, 22.931, 2, 0.467, 18.232, 2, 0.5, 13.026, 2, 0.533, 7.38, 2, 0.567, 1.734, 2, 0.6, -3.472, 2, 0.633, -8.171, 2, 0.667, -11.913, 2, 0.7, -14.336, 2, 0.733, -15.24, 2, 0.767, -14.095, 2, 0.8, -11.315, 2, 0.833, -7.62, 2, 0.867, -3.925, 2, 0.9, -1.145, 2, 0.933, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.167, 0.925, 2, 0.2, 0.742, 2, 0.233, 0.5, 2, 0.267, 0.258, 2, 0.3, 0.075, 2, 0.333, 0, 2, 0.767, 0.075, 2, 0.8, 0.258, 2, 0.833, 0.5, 2, 0.867, 0.742, 2, 0.9, 0.925, 2, 0.933, 1, 2, 2.4, 0.925, 2, 2.433, 0.742, 2, 2.467, 0.5, 2, 2.5, 0.258, 2, 2.533, 0.075, 2, 2.567, 0, 2, 4.367, 0.075, 2, 4.4, 0.258, 2, 4.433, 0.5, 2, 4.467, 0.742, 2, 4.5, 0.925, 2, 4.533, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.167, 0.925, 2, 0.2, 0.742, 2, 0.233, 0.5, 2, 0.267, 0.258, 2, 0.3, 0.075, 2, 0.333, 0, 2, 0.767, 0.075, 2, 0.8, 0.258, 2, 0.833, 0.5, 2, 0.867, 0.742, 2, 0.9, 0.925, 2, 0.933, 1, 2, 2.4, 0.925, 2, 2.433, 0.742, 2, 2.467, 0.5, 2, 2.5, 0.258, 2, 2.533, 0.075, 2, 2.567, 0, 2, 4.367, 0.075, 2, 4.4, 0.258, 2, 4.433, 0.5, 2, 4.467, 0.742, 2, 4.5, 0.925, 2, 4.533, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangL", "Segments": [0, 0, 2, 0.167, 0.185, 2, 0.2, 0.518, 2, 0.233, 0.703, 2, 0.267, 0.564, 2, 0.3, 0.232, 2, 0.333, -0.173, 2, 0.367, -0.505, 2, 0.4, -0.644, 2, 0.433, -0.597, 2, 0.467, -0.475, 2, 0.5, -0.306, 2, 0.533, -0.127, 2, 0.567, 0.043, 2, 0.6, 0.165, 2, 0.633, 0.212, 2, 0.667, 0.141, 2, 0.7, -0.031, 2, 0.733, -0.26, 2, 0.767, -0.489, 2, 0.8, -0.661, 2, 0.833, -0.732, 2, 0.867, -0.59, 2, 0.9, -0.251, 2, 0.933, 0.162, 2, 0.967, 0.501, 2, 1, 0.643, 2, 1.033, 0.595, 2, 1.067, 0.474, 2, 1.1, 0.305, 2, 1.133, 0.126, 2, 1.167, -0.043, 2, 1.2, -0.165, 2, 1.233, -0.212, 2, 1.267, -0.189, 2, 1.3, -0.134, 2, 1.333, -0.062, 2, 1.367, 0.011, 2, 1.4, 0.066, 2, 1.433, 0.089, 2, 1.467, 0.079, 2, 1.5, 0.056, 2, 1.533, 0.025, 2, 1.567, -0.005, 2, 1.6, -0.028, 2, 1.633, -0.038, 2, 1.667, -0.034, 2, 1.7, -0.024, 2, 1.733, -0.011, 2, 1.767, 0.002, 2, 1.8, 0.012, 2, 1.833, 0.016, 2, 1.867, 0.014, 2, 1.9, 0.01, 2, 1.933, 0.005, 2, 1.967, -0.001, 2, 2, -0.005, 2, 2.033, -0.007, 2, 2.067, -0.006, 2, 2.1, -0.004, 2, 2.133, -0.002, 2, 2.167, 0, 2, 2.2, 0.002, 2, 2.233, 0.003, 2, 2.267, 0.003, 2, 2.3, 0.001, 2, 2.333, 0, 2, 2.367, 0, 2, 2.4, 0.184, 2, 2.433, 0.518, 2, 2.467, 0.702, 2, 2.5, 0.563, 2, 2.533, 0.231, 2, 2.567, -0.173, 2, 2.6, -0.505, 2, 2.633, -0.644, 2, 2.667, -0.597, 2, 2.7, -0.475, 2, 2.733, -0.306, 2, 2.767, -0.127, 2, 2.8, 0.043, 2, 2.833, 0.165, 2, 2.867, 0.212, 2, 2.9, 0.189, 2, 2.933, 0.135, 2, 2.967, 0.062, 2, 3, -0.011, 2, 3.033, -0.066, 2, 3.067, -0.089, 2, 3.1, -0.079, 2, 3.133, -0.056, 2, 3.167, -0.025, 2, 3.2, 0.005, 2, 3.233, 0.028, 2, 3.267, 0.038, 2, 3.3, 0.034, 2, 3.333, 0.024, 2, 3.367, 0.011, 2, 3.4, -0.002, 2, 3.433, -0.012, 2, 3.467, -0.016, 2, 3.5, -0.015, 2, 3.533, -0.01, 2, 3.567, -0.005, 2, 3.6, 0.001, 2, 3.633, 0.005, 2, 3.667, 0.007, 2, 3.7, 0.006, 2, 3.733, 0.004, 2, 3.767, 0.002, 2, 3.8, 0, 2, 3.833, -0.002, 2, 3.867, -0.003, 2, 3.9, -0.003, 2, 3.933, -0.002, 2, 3.967, -0.001, 2, 4, 0, 2, 4.033, 0.001, 2, 4.067, 0.001, 2, 4.1, 0.001, 2, 4.133, 0.001, 2, 4.167, 0, 2, 4.2, 0, 2, 4.233, 0, 2, 4.267, 0, 2, 4.333, 0, 2, 4.367, -0.185, 2, 4.4, -0.518, 2, 4.433, -0.703, 2, 4.467, -0.564, 2, 4.5, -0.232, 2, 4.533, 0.173, 2, 4.567, 0.505, 2, 4.6, 0.645, 2, 4.633, 0.597, 2, 4.667, 0.475, 2, 4.7, 0.306, 2, 4.733, 0.127, 2, 4.767, -0.043, 2, 4.8, -0.165, 2, 4.833, -0.212, 2, 4.867, -0.189, 2, 4.9, -0.135, 2, 4.933, -0.062, 2, 4.967, 0.011, 2, 5, 0.066, 2, 5.033, 0.089, 2, 5.067, 0.079, 2, 5.1, 0.056, 2, 5.133, 0.025, 2, 5.167, -0.005, 2, 5.2, -0.028, 2, 5.233, -0.038, 2, 5.267, -0.034, 2, 5.3, -0.024, 2, 5.333, -0.011, 2, 5.367, 0.002, 2, 5.4, 0.012, 2, 5.433, 0.016, 2, 5.467, 0.015, 2, 5.5, 0.01, 2, 5.533, 0.005, 2, 5.567, -0.001, 2, 5.6, -0.005, 2, 5.633, -0.007, 2, 5.667, -0.006, 2, 5.7, -0.004, 2, 5.733, -0.002, 2, 5.767, 0, 2, 5.8, 0.002, 2, 5.833, 0.003, 2, 5.867, 0.003, 2, 5.9, 0.002, 2, 5.933, 0.001, 2, 5.967, 0, 2, 6, -0.001, 2, 6.033, -0.001, 2, 6.067, -0.001, 2, 6.1, -0.001, 2, 6.133, 0, 2, 6.167, 0, 2, 6.2, 0, 2, 6.233, 0, 2, 6.3, 0, 2, 6.333, 0, 2, 6.367, 0, 2, 6.4, 0, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.733, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamYanZhuSuoFangR", "Segments": [0, 0, 2, 0.167, 0.185, 2, 0.2, 0.518, 2, 0.233, 0.703, 2, 0.267, 0.564, 2, 0.3, 0.232, 2, 0.333, -0.173, 2, 0.367, -0.505, 2, 0.4, -0.644, 2, 0.433, -0.597, 2, 0.467, -0.475, 2, 0.5, -0.306, 2, 0.533, -0.127, 2, 0.567, 0.043, 2, 0.6, 0.165, 2, 0.633, 0.212, 2, 0.667, 0.141, 2, 0.7, -0.031, 2, 0.733, -0.26, 2, 0.767, -0.489, 2, 0.8, -0.661, 2, 0.833, -0.732, 2, 0.867, -0.59, 2, 0.9, -0.251, 2, 0.933, 0.162, 2, 0.967, 0.501, 2, 1, 0.643, 2, 1.033, 0.595, 2, 1.067, 0.474, 2, 1.1, 0.305, 2, 1.133, 0.126, 2, 1.167, -0.043, 2, 1.2, -0.165, 2, 1.233, -0.212, 2, 1.267, -0.189, 2, 1.3, -0.134, 2, 1.333, -0.062, 2, 1.367, 0.011, 2, 1.4, 0.066, 2, 1.433, 0.089, 2, 1.467, 0.079, 2, 1.5, 0.056, 2, 1.533, 0.025, 2, 1.567, -0.005, 2, 1.6, -0.028, 2, 1.633, -0.038, 2, 1.667, -0.034, 2, 1.7, -0.024, 2, 1.733, -0.011, 2, 1.767, 0.002, 2, 1.8, 0.012, 2, 1.833, 0.016, 2, 1.867, 0.014, 2, 1.9, 0.01, 2, 1.933, 0.005, 2, 1.967, -0.001, 2, 2, -0.005, 2, 2.033, -0.007, 2, 2.067, -0.006, 2, 2.1, -0.004, 2, 2.133, -0.002, 2, 2.167, 0, 2, 2.2, 0.002, 2, 2.233, 0.003, 2, 2.267, 0.003, 2, 2.3, 0.001, 2, 2.333, 0, 2, 2.367, 0, 2, 2.4, 0.184, 2, 2.433, 0.518, 2, 2.467, 0.702, 2, 2.5, 0.563, 2, 2.533, 0.231, 2, 2.567, -0.173, 2, 2.6, -0.505, 2, 2.633, -0.644, 2, 2.667, -0.597, 2, 2.7, -0.475, 2, 2.733, -0.306, 2, 2.767, -0.127, 2, 2.8, 0.043, 2, 2.833, 0.165, 2, 2.867, 0.212, 2, 2.9, 0.189, 2, 2.933, 0.135, 2, 2.967, 0.062, 2, 3, -0.011, 2, 3.033, -0.066, 2, 3.067, -0.089, 2, 3.1, -0.079, 2, 3.133, -0.056, 2, 3.167, -0.025, 2, 3.2, 0.005, 2, 3.233, 0.028, 2, 3.267, 0.038, 2, 3.3, 0.034, 2, 3.333, 0.024, 2, 3.367, 0.011, 2, 3.4, -0.002, 2, 3.433, -0.012, 2, 3.467, -0.016, 2, 3.5, -0.015, 2, 3.533, -0.01, 2, 3.567, -0.005, 2, 3.6, 0.001, 2, 3.633, 0.005, 2, 3.667, 0.007, 2, 3.7, 0.006, 2, 3.733, 0.004, 2, 3.767, 0.002, 2, 3.8, 0, 2, 3.833, -0.002, 2, 3.867, -0.003, 2, 3.9, -0.003, 2, 3.933, -0.002, 2, 3.967, -0.001, 2, 4, 0, 2, 4.033, 0.001, 2, 4.067, 0.001, 2, 4.1, 0.001, 2, 4.133, 0.001, 2, 4.167, 0, 2, 4.2, 0, 2, 4.233, 0, 2, 4.267, 0, 2, 4.333, 0, 2, 4.367, -0.185, 2, 4.4, -0.518, 2, 4.433, -0.703, 2, 4.467, -0.564, 2, 4.5, -0.232, 2, 4.533, 0.173, 2, 4.567, 0.505, 2, 4.6, 0.645, 2, 4.633, 0.597, 2, 4.667, 0.475, 2, 4.7, 0.306, 2, 4.733, 0.127, 2, 4.767, -0.043, 2, 4.8, -0.165, 2, 4.833, -0.212, 2, 4.867, -0.189, 2, 4.9, -0.135, 2, 4.933, -0.062, 2, 4.967, 0.011, 2, 5, 0.066, 2, 5.033, 0.089, 2, 5.067, 0.079, 2, 5.1, 0.056, 2, 5.133, 0.025, 2, 5.167, -0.005, 2, 5.2, -0.028, 2, 5.233, -0.038, 2, 5.267, -0.034, 2, 5.3, -0.024, 2, 5.333, -0.011, 2, 5.367, 0.002, 2, 5.4, 0.012, 2, 5.433, 0.016, 2, 5.467, 0.015, 2, 5.5, 0.01, 2, 5.533, 0.005, 2, 5.567, -0.001, 2, 5.6, -0.005, 2, 5.633, -0.007, 2, 5.667, -0.006, 2, 5.7, -0.004, 2, 5.733, -0.002, 2, 5.767, 0, 2, 5.8, 0.002, 2, 5.833, 0.003, 2, 5.867, 0.003, 2, 5.9, 0.002, 2, 5.933, 0.001, 2, 5.967, 0, 2, 6, -0.001, 2, 6.033, -0.001, 2, 6.067, -0.001, 2, 6.1, -0.001, 2, 6.133, 0, 2, 6.167, 0, 2, 6.2, 0, 2, 6.233, 0, 2, 6.3, 0, 2, 6.333, 0, 2, 6.367, 0, 2, 6.4, 0, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.733, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamGaoGguangL", "Segments": [0, 0, 2, 0.167, 0.185, 2, 0.2, 0.518, 2, 0.233, 0.703, 2, 0.267, 0.564, 2, 0.3, 0.232, 2, 0.333, -0.173, 2, 0.367, -0.505, 2, 0.4, -0.644, 2, 0.433, -0.597, 2, 0.467, -0.475, 2, 0.5, -0.306, 2, 0.533, -0.127, 2, 0.567, 0.043, 2, 0.6, 0.165, 2, 0.633, 0.212, 2, 0.667, 0.141, 2, 0.7, -0.031, 2, 0.733, -0.26, 2, 0.767, -0.489, 2, 0.8, -0.661, 2, 0.833, -0.732, 2, 0.867, -0.59, 2, 0.9, -0.251, 2, 0.933, 0.162, 2, 0.967, 0.501, 2, 1, 0.643, 2, 1.033, 0.595, 2, 1.067, 0.474, 2, 1.1, 0.305, 2, 1.133, 0.126, 2, 1.167, -0.043, 2, 1.2, -0.165, 2, 1.233, -0.212, 2, 1.267, -0.189, 2, 1.3, -0.134, 2, 1.333, -0.062, 2, 1.367, 0.011, 2, 1.4, 0.066, 2, 1.433, 0.089, 2, 1.467, 0.079, 2, 1.5, 0.056, 2, 1.533, 0.025, 2, 1.567, -0.005, 2, 1.6, -0.028, 2, 1.633, -0.038, 2, 1.667, -0.034, 2, 1.7, -0.024, 2, 1.733, -0.011, 2, 1.767, 0.002, 2, 1.8, 0.012, 2, 1.833, 0.016, 2, 1.867, 0.014, 2, 1.9, 0.01, 2, 1.933, 0.005, 2, 1.967, -0.001, 2, 2, -0.005, 2, 2.033, -0.007, 2, 2.067, -0.006, 2, 2.1, -0.004, 2, 2.133, -0.002, 2, 2.167, 0, 2, 2.2, 0.002, 2, 2.233, 0.003, 2, 2.267, 0.003, 2, 2.3, 0.001, 2, 2.333, 0, 2, 2.367, 0, 2, 2.4, 0.184, 2, 2.433, 0.518, 2, 2.467, 0.702, 2, 2.5, 0.563, 2, 2.533, 0.231, 2, 2.567, -0.173, 2, 2.6, -0.505, 2, 2.633, -0.644, 2, 2.667, -0.597, 2, 2.7, -0.475, 2, 2.733, -0.306, 2, 2.767, -0.127, 2, 2.8, 0.043, 2, 2.833, 0.165, 2, 2.867, 0.212, 2, 2.9, 0.189, 2, 2.933, 0.135, 2, 2.967, 0.062, 2, 3, -0.011, 2, 3.033, -0.066, 2, 3.067, -0.089, 2, 3.1, -0.079, 2, 3.133, -0.056, 2, 3.167, -0.025, 2, 3.2, 0.005, 2, 3.233, 0.028, 2, 3.267, 0.038, 2, 3.3, 0.034, 2, 3.333, 0.024, 2, 3.367, 0.011, 2, 3.4, -0.002, 2, 3.433, -0.012, 2, 3.467, -0.016, 2, 3.5, -0.015, 2, 3.533, -0.01, 2, 3.567, -0.005, 2, 3.6, 0.001, 2, 3.633, 0.005, 2, 3.667, 0.007, 2, 3.7, 0.006, 2, 3.733, 0.004, 2, 3.767, 0.002, 2, 3.8, 0, 2, 3.833, -0.002, 2, 3.867, -0.003, 2, 3.9, -0.003, 2, 3.933, -0.002, 2, 3.967, -0.001, 2, 4, 0, 2, 4.033, 0.001, 2, 4.067, 0.001, 2, 4.1, 0.001, 2, 4.133, 0.001, 2, 4.167, 0, 2, 4.2, 0, 2, 4.233, 0, 2, 4.267, 0, 2, 4.333, 0, 2, 4.367, -0.185, 2, 4.4, -0.518, 2, 4.433, -0.703, 2, 4.467, -0.564, 2, 4.5, -0.232, 2, 4.533, 0.173, 2, 4.567, 0.505, 2, 4.6, 0.645, 2, 4.633, 0.597, 2, 4.667, 0.475, 2, 4.7, 0.306, 2, 4.733, 0.127, 2, 4.767, -0.043, 2, 4.8, -0.165, 2, 4.833, -0.212, 2, 4.867, -0.189, 2, 4.9, -0.135, 2, 4.933, -0.062, 2, 4.967, 0.011, 2, 5, 0.066, 2, 5.033, 0.089, 2, 5.067, 0.079, 2, 5.1, 0.056, 2, 5.133, 0.025, 2, 5.167, -0.005, 2, 5.2, -0.028, 2, 5.233, -0.038, 2, 5.267, -0.034, 2, 5.3, -0.024, 2, 5.333, -0.011, 2, 5.367, 0.002, 2, 5.4, 0.012, 2, 5.433, 0.016, 2, 5.467, 0.015, 2, 5.5, 0.01, 2, 5.533, 0.005, 2, 5.567, -0.001, 2, 5.6, -0.005, 2, 5.633, -0.007, 2, 5.667, -0.006, 2, 5.7, -0.004, 2, 5.733, -0.002, 2, 5.767, 0, 2, 5.8, 0.002, 2, 5.833, 0.003, 2, 5.867, 0.003, 2, 5.9, 0.002, 2, 5.933, 0.001, 2, 5.967, 0, 2, 6, -0.001, 2, 6.033, -0.001, 2, 6.067, -0.001, 2, 6.1, -0.001, 2, 6.133, 0, 2, 6.167, 0, 2, 6.2, 0, 2, 6.233, 0, 2, 6.3, 0, 2, 6.333, 0, 2, 6.367, 0, 2, 6.4, 0, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.733, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamGaoGuangR", "Segments": [0, 0, 2, 0.167, 0.185, 2, 0.2, 0.518, 2, 0.233, 0.703, 2, 0.267, 0.564, 2, 0.3, 0.232, 2, 0.333, -0.173, 2, 0.367, -0.505, 2, 0.4, -0.644, 2, 0.433, -0.597, 2, 0.467, -0.475, 2, 0.5, -0.306, 2, 0.533, -0.127, 2, 0.567, 0.043, 2, 0.6, 0.165, 2, 0.633, 0.212, 2, 0.667, 0.141, 2, 0.7, -0.031, 2, 0.733, -0.26, 2, 0.767, -0.489, 2, 0.8, -0.661, 2, 0.833, -0.732, 2, 0.867, -0.59, 2, 0.9, -0.251, 2, 0.933, 0.162, 2, 0.967, 0.501, 2, 1, 0.643, 2, 1.033, 0.595, 2, 1.067, 0.474, 2, 1.1, 0.305, 2, 1.133, 0.126, 2, 1.167, -0.043, 2, 1.2, -0.165, 2, 1.233, -0.212, 2, 1.267, -0.189, 2, 1.3, -0.134, 2, 1.333, -0.062, 2, 1.367, 0.011, 2, 1.4, 0.066, 2, 1.433, 0.089, 2, 1.467, 0.079, 2, 1.5, 0.056, 2, 1.533, 0.025, 2, 1.567, -0.005, 2, 1.6, -0.028, 2, 1.633, -0.038, 2, 1.667, -0.034, 2, 1.7, -0.024, 2, 1.733, -0.011, 2, 1.767, 0.002, 2, 1.8, 0.012, 2, 1.833, 0.016, 2, 1.867, 0.014, 2, 1.9, 0.01, 2, 1.933, 0.005, 2, 1.967, -0.001, 2, 2, -0.005, 2, 2.033, -0.007, 2, 2.067, -0.006, 2, 2.1, -0.004, 2, 2.133, -0.002, 2, 2.167, 0, 2, 2.2, 0.002, 2, 2.233, 0.003, 2, 2.267, 0.003, 2, 2.3, 0.001, 2, 2.333, 0, 2, 2.367, 0, 2, 2.4, 0.184, 2, 2.433, 0.518, 2, 2.467, 0.702, 2, 2.5, 0.563, 2, 2.533, 0.231, 2, 2.567, -0.173, 2, 2.6, -0.505, 2, 2.633, -0.644, 2, 2.667, -0.597, 2, 2.7, -0.475, 2, 2.733, -0.306, 2, 2.767, -0.127, 2, 2.8, 0.043, 2, 2.833, 0.165, 2, 2.867, 0.212, 2, 2.9, 0.189, 2, 2.933, 0.135, 2, 2.967, 0.062, 2, 3, -0.011, 2, 3.033, -0.066, 2, 3.067, -0.089, 2, 3.1, -0.079, 2, 3.133, -0.056, 2, 3.167, -0.025, 2, 3.2, 0.005, 2, 3.233, 0.028, 2, 3.267, 0.038, 2, 3.3, 0.034, 2, 3.333, 0.024, 2, 3.367, 0.011, 2, 3.4, -0.002, 2, 3.433, -0.012, 2, 3.467, -0.016, 2, 3.5, -0.015, 2, 3.533, -0.01, 2, 3.567, -0.005, 2, 3.6, 0.001, 2, 3.633, 0.005, 2, 3.667, 0.007, 2, 3.7, 0.006, 2, 3.733, 0.004, 2, 3.767, 0.002, 2, 3.8, 0, 2, 3.833, -0.002, 2, 3.867, -0.003, 2, 3.9, -0.003, 2, 3.933, -0.002, 2, 3.967, -0.001, 2, 4, 0, 2, 4.033, 0.001, 2, 4.067, 0.001, 2, 4.1, 0.001, 2, 4.133, 0.001, 2, 4.167, 0, 2, 4.2, 0, 2, 4.233, 0, 2, 4.267, 0, 2, 4.333, 0, 2, 4.367, -0.185, 2, 4.4, -0.518, 2, 4.433, -0.703, 2, 4.467, -0.564, 2, 4.5, -0.232, 2, 4.533, 0.173, 2, 4.567, 0.505, 2, 4.6, 0.645, 2, 4.633, 0.597, 2, 4.667, 0.475, 2, 4.7, 0.306, 2, 4.733, 0.127, 2, 4.767, -0.043, 2, 4.8, -0.165, 2, 4.833, -0.212, 2, 4.867, -0.189, 2, 4.9, -0.135, 2, 4.933, -0.062, 2, 4.967, 0.011, 2, 5, 0.066, 2, 5.033, 0.089, 2, 5.067, 0.079, 2, 5.1, 0.056, 2, 5.133, 0.025, 2, 5.167, -0.005, 2, 5.2, -0.028, 2, 5.233, -0.038, 2, 5.267, -0.034, 2, 5.3, -0.024, 2, 5.333, -0.011, 2, 5.367, 0.002, 2, 5.4, 0.012, 2, 5.433, 0.016, 2, 5.467, 0.015, 2, 5.5, 0.01, 2, 5.533, 0.005, 2, 5.567, -0.001, 2, 5.6, -0.005, 2, 5.633, -0.007, 2, 5.667, -0.006, 2, 5.7, -0.004, 2, 5.733, -0.002, 2, 5.767, 0, 2, 5.8, 0.002, 2, 5.833, 0.003, 2, 5.867, 0.003, 2, 5.9, 0.002, 2, 5.933, 0.001, 2, 5.967, 0, 2, 6, -0.001, 2, 6.033, -0.001, 2, 6.067, -0.001, 2, 6.1, -0.001, 2, 6.133, 0, 2, 6.167, 0, 2, 6.2, 0, 2, 6.233, 0, 2, 6.3, 0, 2, 6.333, 0, 2, 6.367, 0, 2, 6.4, 0, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.733, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamTeShuEyeChuXian", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamHeiHuaShadow", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamXianTiaoChuXian", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamTeShuZuiCX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.033, 0.075, 2, 0.067, 0.258, 2, 0.1, 0.5, 2, 0.133, 0.742, 2, 0.167, 0.925, 2, 0.2, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.033, 0.075, 2, 0.067, 0.258, 2, 0.1, 0.5, 2, 0.133, 0.742, 2, 0.167, 0.925, 2, 0.2, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 0.033, -0.075, 2, 0.067, -0.258, 2, 0.1, -0.5, 2, 0.133, -0.742, 2, 0.167, -0.925, 2, 0.2, -1, 2, 8, -1]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 0.033, -0.075, 2, 0.067, -0.258, 2, 0.1, -0.5, 2, 0.133, -0.742, 2, 0.167, -0.925, 2, 0.2, -1, 2, 8, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 2, 0.033, 0.038, 2, 0.067, 0.129, 2, 0.1, 0.25, 2, 0.133, 0.371, 2, 0.167, 0.462, 2, 0.2, 0.5, 2, 8, 0.5]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 2, 0.033, 0.038, 2, 0.067, 0.129, 2, 0.1, 0.25, 2, 0.133, 0.371, 2, 0.167, 0.462, 2, 0.2, 0.5, 2, 8, 0.5]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.3, 0.07, 2, 0.333, 0.255, 2, 0.367, 0.519, 2, 0.4, 0.814, 2, 0.433, 1, 2, 0.633, 0.798, 2, 0.667, 0.644, 2, 0.7, 1, 2, 0.767, 0.8, 2, 0.8, 0.196, 2, 0.833, 0.33, 2, 0.867, 0.644, 2, 0.9, 1, 2, 1.067, 0.989, 2, 1.1, 0.33, 2, 1.133, 0.032, 2, 1.167, 0.294, 2, 1.2, 0.871, 2, 1.233, 1, 2, 1.333, 0.346, 2, 1.367, 0.753, 2, 1.4, 1, 2, 1.433, 0.596, 2, 1.467, 0.032, 2, 1.5, 0.267, 2, 1.533, 0.502, 2, 1.567, 0.259, 2, 1.6, 0.016, 2, 1.633, 0.306, 2, 1.667, 0.945, 2, 1.7, 1, 2, 1.867, 0.868, 2, 1.9, 0.64, 2, 1.933, 0.55, 2, 1.967, 0.758, 2, 2, 1, 2, 2.167, 0.516, 2, 2.2, 0.258, 2, 2.233, 0.27, 2, 2.267, 0.282, 2, 2.3, 0.168, 2, 2.333, 0.054, 2, 2.367, 0.219, 2, 2.4, 0.384, 2, 2.433, 0.294, 2, 2.467, 0.204, 2, 2.5, 0.386, 2, 2.533, 0.774, 2, 2.567, 1, 2, 2.633, 0.679, 2, 2.667, 0.008, 2, 2.7, 0.431, 2, 2.733, 0.854, 2, 2.767, 0.729, 2, 2.8, 0.604, 2, 2.833, 1, 2, 2.9, 0.859, 2, 2.933, 0.236, 2, 2.967, 0.73, 2, 3, 1, 2, 3.133, 0.79, 2, 3.167, 0.497, 2, 3.2, 0.36, 2, 3.233, 0.565, 2, 3.267, 1, 2, 3.367, 0.941, 2, 3.4, 0.212, 2, 3.433, 0.295, 2, 3.467, 0.499, 2, 3.5, 0.771, 2, 3.533, 1, 2, 3.633, 0.726, 2, 3.667, 0.126, 2, 3.7, 0.188, 2, 3.733, 0.344, 2, 3.767, 0.553, 2, 3.8, 0.762, 2, 3.833, 0.918, 2, 3.867, 0.98, 2, 3.9, 0.909, 2, 3.933, 0.731, 2, 3.967, 0.493, 2, 4, 0.254, 2, 4.033, 0.074, 2, 4.067, 0, 2, 4.1, 0.98, 2, 4.133, 1, 2, 4.233, 0.98, 2, 4.267, 0.501, 2, 4.3, 0.149, 2, 4.333, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.033, 0.001, 2, 0.067, 0.003, 2, 0.1, 0.007, 2, 0.133, 0.013, 2, 0.167, 0.02, 2, 0.2, 0.028, 2, 0.233, 0.038, 2, 0.267, 0.049, 2, 0.3, 0.061, 2, 0.333, 0.074, 2, 0.367, 0.089, 2, 0.4, 0.104, 2, 0.433, 0.121, 2, 0.467, 0.138, 2, 0.5, 0.156, 2, 0.533, 0.175, 2, 0.567, 0.195, 2, 0.6, 0.216, 2, 0.633, 0.237, 2, 0.667, 0.259, 2, 0.7, 0.282, 2, 0.733, 0.305, 2, 0.767, 0.328, 2, 0.8, 0.352, 2, 0.833, 0.376, 2, 0.867, 0.401, 2, 0.9, 0.425, 2, 0.933, 0.45, 2, 0.967, 0.475, 2, 1, 0.5, 2, 1.033, 0.525, 2, 1.067, 0.55, 2, 1.1, 0.575, 2, 1.133, 0.599, 2, 1.167, 0.624, 2, 1.2, 0.648, 2, 1.233, 0.672, 2, 1.267, 0.695, 2, 1.3, 0.718, 2, 1.333, 0.741, 2, 1.367, 0.763, 2, 1.4, 0.784, 2, 1.433, 0.805, 2, 1.467, 0.825, 2, 1.5, 0.844, 2, 1.533, 0.862, 2, 1.567, 0.879, 2, 1.6, 0.896, 2, 1.633, 0.911, 2, 1.667, 0.926, 2, 1.7, 0.939, 2, 1.733, 0.951, 2, 1.767, 0.962, 2, 1.8, 0.972, 2, 1.833, 0.98, 2, 1.867, 0.987, 2, 1.9, 0.993, 2, 1.933, 0.997, 2, 1.967, 0.999, 2, 2, 1, 2, 2.033, 0.999, 2, 2.067, 0.997, 2, 2.1, 0.993, 2, 2.133, 0.987, 2, 2.167, 0.98, 2, 2.2, 0.972, 2, 2.233, 0.962, 2, 2.267, 0.951, 2, 2.3, 0.939, 2, 2.333, 0.926, 2, 2.367, 0.911, 2, 2.4, 0.896, 2, 2.433, 0.879, 2, 2.467, 0.862, 2, 2.5, 0.844, 2, 2.533, 0.825, 2, 2.567, 0.805, 2, 2.6, 0.784, 2, 2.633, 0.763, 2, 2.667, 0.741, 2, 2.7, 0.718, 2, 2.733, 0.695, 2, 2.767, 0.672, 2, 2.8, 0.648, 2, 2.833, 0.624, 2, 2.867, 0.599, 2, 2.9, 0.575, 2, 2.933, 0.55, 2, 2.967, 0.525, 2, 3, 0.5, 2, 3.033, 0.475, 2, 3.067, 0.45, 2, 3.1, 0.425, 2, 3.133, 0.401, 2, 3.167, 0.376, 2, 3.2, 0.352, 2, 3.233, 0.328, 2, 3.267, 0.305, 2, 3.3, 0.282, 2, 3.333, 0.259, 2, 3.367, 0.237, 2, 3.4, 0.216, 2, 3.433, 0.195, 2, 3.467, 0.175, 2, 3.5, 0.156, 2, 3.533, 0.138, 2, 3.567, 0.121, 2, 3.6, 0.104, 2, 3.633, 0.089, 2, 3.667, 0.074, 2, 3.7, 0.061, 2, 3.733, 0.049, 2, 3.767, 0.038, 2, 3.8, 0.028, 2, 3.833, 0.02, 2, 3.867, 0.013, 2, 3.9, 0.007, 2, 3.933, 0.003, 2, 3.967, 0.001, 2, 4, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.167, 0.276, 2, 0.2, 0.988, 2, 0.233, 1.976, 2, 0.267, 3.024, 2, 0.3, 4.012, 2, 0.333, 4.724, 2, 0.367, 5, 2, 0.4, 4.782, 2, 0.433, 4.172, 2, 0.467, 3.22, 2, 0.5, 2.036, 2, 0.533, 0.621, 2, 0.567, -0.907, 2, 0.6, -2.5, 2, 0.633, -4.093, 2, 0.667, -5.621, 2, 0.7, -7.036, 2, 0.733, -8.22, 2, 0.767, -9.172, 2, 0.8, -9.782, 2, 0.833, -10, 2, 0.867, -9.828, 2, 0.9, -9.367, 2, 0.933, -8.699, 2, 0.967, -7.909, 2, 1, -7.091, 2, 1.033, -6.301, 2, 1.067, -5.633, 2, 1.1, -5.172, 2, 1.133, -5, 2, 8, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamShenTiQianHou", "Segments": [0, 0, 2, 0.033, -0.552, 2, 0.067, -1.976, 2, 0.1, -3.952, 2, 0.133, -6.048, 2, 0.167, -8.024, 2, 0.2, -9.448, 2, 0.233, -10, 2, 0.267, -9.419, 2, 0.3, -7.792, 2, 0.333, -5.254, 2, 0.367, -2.097, 2, 0.4, 1.677, 2, 0.433, 5.752, 2, 0.467, 10, 2, 0.5, 14.248, 2, 0.533, 18.323, 2, 0.567, 22.097, 2, 0.6, 25.254, 2, 0.633, 27.792, 2, 0.667, 29.419, 2, 0.7, 30, 2, 0.733, 29.828, 2, 0.767, 29.367, 2, 0.8, 28.699, 2, 0.833, 27.909, 2, 0.867, 27.091, 2, 0.9, 26.301, 2, 0.933, 25.633, 2, 0.967, 25.172, 2, 1, 25, 2, 8, 25]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.601, 2, 0.067, 2.06, 2, 0.1, 4, 2, 0.133, 5.94, 2, 0.167, 7.399, 2, 0.2, 8, 2, 0.233, 7.241, 2, 0.267, 5.206, 2, 0.3, 2.062, 2, 0.333, -1.885, 2, 0.367, -6.258, 2, 0.4, -11, 2, 0.433, -15.742, 2, 0.467, -20.115, 2, 0.5, -24.062, 2, 0.533, -27.206, 2, 0.567, -29.241, 2, 0.6, -30, 2, 0.633, -29.828, 2, 0.667, -29.367, 2, 0.7, -28.699, 2, 0.733, -27.909, 2, 0.767, -27.091, 2, 0.8, -26.301, 2, 0.833, -25.633, 2, 0.867, -25.172, 2, 0.9, -25, 2, 2.167, -25.276, 2, 2.2, -25.988, 2, 2.233, -26.976, 2, 2.267, -28.024, 2, 2.3, -29.012, 2, 2.333, -29.724, 2, 2.367, -30, 2, 2.4, -29.241, 2, 2.433, -27.106, 2, 2.467, -23.749, 2, 2.5, -19.48, 2, 2.533, -14.392, 2, 2.567, -8.846, 2, 2.6, -2.984, 2, 2.633, 2.984, 2, 2.667, 8.846, 2, 2.7, 14.392, 2, 2.733, 19.48, 2, 2.767, 23.749, 2, 2.8, 27.106, 2, 2.833, 29.241, 2, 2.867, 30, 2, 2.9, 29.828, 2, 2.933, 29.367, 2, 2.967, 28.699, 2, 3, 27.909, 2, 3.033, 27.091, 2, 3.067, 26.301, 2, 3.1, 25.633, 2, 3.133, 25.172, 2, 3.167, 25, 2, 8, 25]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.233, 0.12, 2, 0.267, 0.441, 2, 0.3, 0.938, 2, 0.333, 1.561, 2, 0.367, 2.251, 2, 0.4, 3, 2, 0.433, 3.749, 2, 0.467, 4.439, 2, 0.5, 5.062, 2, 0.533, 5.559, 2, 0.567, 5.88, 2, 0.6, 6, 2, 0.633, 5.931, 2, 0.667, 5.747, 2, 0.7, 5.48, 2, 0.733, 5.163, 2, 0.767, 4.837, 2, 0.8, 4.52, 2, 0.833, 4.253, 2, 0.867, 4.069, 2, 0.9, 4, 2, 2.167, 4.11, 2, 2.2, 4.395, 2, 2.233, 4.79, 2, 2.267, 5.21, 2, 2.3, 5.605, 2, 2.333, 5.89, 2, 2.367, 6, 2, 2.4, 5.924, 2, 2.433, 5.711, 2, 2.467, 5.375, 2, 2.5, 4.948, 2, 2.533, 4.439, 2, 2.567, 3.885, 2, 2.6, 3.298, 2, 2.633, 2.702, 2, 2.667, 2.115, 2, 2.7, 1.561, 2, 2.733, 1.052, 2, 2.767, 0.625, 2, 2.8, 0.289, 2, 2.833, 0.076, 2, 2.867, 0, 2, 2.9, 0.034, 2, 2.933, 0.127, 2, 2.967, 0.26, 2, 3, 0.418, 2, 3.033, 0.582, 2, 3.067, 0.74, 2, 3.1, 0.873, 2, 3.133, 0.966, 2, 3.167, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.233, 0.12, 2, 0.267, 0.441, 2, 0.3, 0.938, 2, 0.333, 1.561, 2, 0.367, 2.251, 2, 0.4, 3, 2, 0.433, 3.749, 2, 0.467, 4.439, 2, 0.5, 5.062, 2, 0.533, 5.559, 2, 0.567, 5.88, 2, 0.6, 6, 2, 0.633, 5.931, 2, 0.667, 5.747, 2, 0.7, 5.48, 2, 0.733, 5.163, 2, 0.767, 4.837, 2, 0.8, 4.52, 2, 0.833, 4.253, 2, 0.867, 4.069, 2, 0.9, 4, 2, 2.167, 4.11, 2, 2.2, 4.395, 2, 2.233, 4.79, 2, 2.267, 5.21, 2, 2.3, 5.605, 2, 2.333, 5.89, 2, 2.367, 6, 2, 2.4, 5.924, 2, 2.433, 5.711, 2, 2.467, 5.375, 2, 2.5, 4.948, 2, 2.533, 4.439, 2, 2.567, 3.885, 2, 2.6, 3.298, 2, 2.633, 2.702, 2, 2.667, 2.115, 2, 2.7, 1.561, 2, 2.733, 1.052, 2, 2.767, 0.625, 2, 2.8, 0.289, 2, 2.833, 0.076, 2, 2.867, 0, 2, 2.9, 0.034, 2, 2.933, 0.127, 2, 2.967, 0.26, 2, 3, 0.418, 2, 3.033, 0.582, 2, 3.067, 0.74, 2, 3.1, 0.873, 2, 3.133, 0.966, 2, 3.167, 1, 2, 8, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.601, 2, 0.067, 2.06, 2, 0.1, 4, 2, 0.133, 5.94, 2, 0.167, 7.399, 2, 0.2, 8, 2, 0.233, 7.241, 2, 0.267, 5.206, 2, 0.3, 2.062, 2, 0.333, -1.885, 2, 0.367, -6.258, 2, 0.4, -11, 2, 0.433, -15.742, 2, 0.467, -20.115, 2, 0.5, -24.062, 2, 0.533, -27.206, 2, 0.567, -29.241, 2, 0.6, -30, 2, 0.633, -29.828, 2, 0.667, -29.367, 2, 0.7, -28.699, 2, 0.733, -27.909, 2, 0.767, -27.091, 2, 0.8, -26.301, 2, 0.833, -25.633, 2, 0.867, -25.172, 2, 0.9, -25, 2, 2.167, -25.276, 2, 2.2, -25.988, 2, 2.233, -26.976, 2, 2.267, -28.024, 2, 2.3, -29.012, 2, 2.333, -29.724, 2, 2.367, -30, 2, 8, -30]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Paramzuodatui", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 2, 0.033, 0.019, 2, 0.067, 0.064, 2, 0.1, 0.12, 2, 0.133, 0.165, 2, 0.167, 0.184, 2, 0.2, 0.161, 2, 0.233, 0.1, 2, 0.267, 0.011, 2, 0.3, -0.094, 2, 0.333, -0.203, 2, 0.367, -0.308, 2, 0.4, -0.396, 2, 0.433, -0.458, 2, 0.467, -0.481, 2, 0.5, -0.454, 2, 0.533, -0.381, 2, 0.567, -0.272, 2, 0.6, -0.141, 2, 0.633, 0.001, 2, 0.667, 0.143, 2, 0.7, 0.274, 2, 0.733, 0.383, 2, 0.767, 0.456, 2, 0.8, 0.483, 2, 0.833, 0.462, 2, 0.867, 0.406, 2, 0.9, 0.321, 2, 0.933, 0.22, 2, 0.967, 0.11, 2, 1, 0.001, 2, 1.033, -0.1, 2, 1.067, -0.185, 2, 1.1, -0.241, 2, 1.133, -0.262, 2, 1.167, -0.249, 2, 1.2, -0.214, 2, 1.233, -0.163, 2, 1.267, -0.102, 2, 1.3, -0.04, 2, 1.333, 0.02, 2, 1.367, 0.071, 2, 1.4, 0.106, 2, 1.433, 0.12, 2, 1.467, 0.115, 2, 1.5, 0.102, 2, 1.533, 0.082, 2, 1.567, 0.058, 2, 1.6, 0.032, 2, 1.633, 0.006, 2, 1.667, -0.018, 2, 1.7, -0.038, 2, 1.733, -0.051, 2, 1.767, -0.056, 2, 1.8, -0.053, 2, 1.833, -0.047, 2, 1.867, -0.038, 2, 1.9, -0.027, 2, 1.933, -0.015, 2, 1.967, -0.003, 2, 2, 0.008, 2, 2.033, 0.017, 2, 2.067, 0.023, 2, 2.1, 0.026, 2, 2.133, 0.019, 2, 2.167, 0.002, 2, 2.2, -0.023, 2, 2.233, -0.052, 2, 2.267, -0.082, 2, 2.3, -0.112, 2, 2.333, -0.136, 2, 2.367, -0.154, 2, 2.4, -0.16, 2, 2.433, -0.127, 2, 2.467, -0.041, 2, 2.5, 0.077, 2, 2.533, 0.203, 2, 2.567, 0.322, 2, 2.6, 0.407, 2, 2.633, 0.44, 2, 2.667, 0.392, 2, 2.7, 0.266, 2, 2.733, 0.088, 2, 2.767, -0.116, 2, 2.8, -0.32, 2, 2.833, -0.498, 2, 2.867, -0.624, 2, 2.9, -0.672, 2, 2.933, -0.625, 2, 2.967, -0.5, 2, 3, -0.32, 2, 3.033, -0.106, 2, 3.067, 0.115, 2, 3.1, 0.328, 2, 3.133, 0.509, 2, 3.167, 0.634, 2, 3.2, 0.68, 2, 3.233, 0.655, 2, 3.267, 0.588, 2, 3.3, 0.484, 2, 3.333, 0.353, 2, 3.367, 0.209, 2, 3.4, 0.052, 2, 3.433, -0.105, 2, 3.467, -0.249, 2, 3.5, -0.38, 2, 3.533, -0.483, 2, 3.567, -0.551, 2, 3.6, -0.576, 2, 3.633, -0.556, 2, 3.667, -0.503, 2, 3.7, -0.42, 2, 3.733, -0.317, 2, 3.767, -0.202, 2, 3.8, -0.078, 2, 3.833, 0.046, 2, 3.867, 0.161, 2, 3.9, 0.264, 2, 3.933, 0.347, 2, 3.967, 0.4, 2, 4, 0.42, 2, 4.033, 0.405, 2, 4.067, 0.366, 2, 4.1, 0.305, 2, 4.133, 0.228, 2, 4.167, 0.143, 2, 4.2, 0.051, 2, 4.233, -0.041, 2, 4.267, -0.126, 2, 4.3, -0.203, 2, 4.333, -0.264, 2, 4.367, -0.303, 2, 4.4, -0.318, 2, 4.433, -0.304, 2, 4.467, -0.267, 2, 4.5, -0.211, 2, 4.533, -0.144, 2, 4.567, -0.071, 2, 4.6, 0.002, 2, 4.633, 0.069, 2, 4.667, 0.125, 2, 4.7, 0.162, 2, 4.733, 0.176, 2, 4.767, 0.169, 2, 4.8, 0.149, 2, 4.833, 0.12, 2, 4.867, 0.085, 2, 4.9, 0.047, 2, 4.933, 0.009, 2, 4.967, -0.026, 2, 5, -0.055, 2, 5.033, -0.075, 2, 5.067, -0.082, 2, 5.1, -0.078, 2, 5.133, -0.069, 2, 5.167, -0.056, 2, 5.2, -0.04, 2, 5.233, -0.022, 2, 5.267, -0.004, 2, 5.3, 0.012, 2, 5.333, 0.025, 2, 5.367, 0.034, 2, 5.4, 0.038, 2, 5.433, 0.036, 2, 5.467, 0.032, 2, 5.5, 0.026, 2, 5.533, 0.018, 2, 5.567, 0.01, 2, 5.6, 0.002, 2, 5.633, -0.005, 2, 5.667, -0.012, 2, 5.7, -0.016, 2, 5.733, -0.017, 2, 5.767, -0.016, 2, 5.8, -0.014, 2, 5.833, -0.011, 2, 5.867, -0.007, 2, 5.9, -0.003, 2, 5.933, 0.001, 2, 5.967, 0.005, 2, 6, 0.007, 2, 6.033, 0.008, 2, 6.067, 0.008, 2, 6.1, 0.007, 2, 6.133, 0.006, 2, 6.167, 0.004, 2, 6.2, 0.002, 2, 6.233, 0, 2, 6.267, -0.001, 2, 6.3, -0.003, 2, 6.333, -0.003, 2, 6.367, -0.004, 2, 6.4, -0.004, 2, 6.433, -0.003, 2, 6.467, -0.002, 2, 6.5, -0.001, 2, 6.533, -0.001, 2, 6.567, 0, 2, 6.6, 0.001, 2, 6.633, 0.002, 2, 6.667, 0.002, 2, 6.733, 0.002, 2, 6.767, 0.001, 2, 6.8, 0.001, 2, 6.833, 0.001, 2, 6.867, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, -0.001, 2, 7, -0.001, 2, 7.067, -0.001, 2, 7.1, -0.001, 2, 7.133, 0, 2, 7.167, 0, 2, 7.2, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.333, 0, 2, 7.4, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.633, 0, 2, 7.667, 0, 2, 7.7, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.8, 0, 2, 7.833, 0, 2, 7.9, 0, 2, 7.933, 0, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 2, 0.033, 0.022, 2, 0.067, 0.073, 2, 0.1, 0.136, 2, 0.133, 0.187, 2, 0.167, 0.209, 2, 0.2, 0.177, 2, 0.233, 0.092, 2, 0.267, -0.028, 2, 0.3, -0.166, 2, 0.333, -0.304, 2, 0.367, -0.424, 2, 0.4, -0.51, 2, 0.433, -0.542, 2, 0.467, -0.512, 2, 0.5, -0.432, 2, 0.533, -0.311, 2, 0.567, -0.166, 2, 0.6, -0.009, 2, 0.633, 0.148, 2, 0.667, 0.292, 2, 0.7, 0.414, 2, 0.733, 0.494, 2, 0.767, 0.524, 2, 0.8, 0.496, 2, 0.833, 0.421, 2, 0.867, 0.314, 2, 0.9, 0.186, 2, 0.933, 0.054, 2, 0.967, -0.073, 2, 1, -0.181, 2, 1.033, -0.256, 2, 1.067, -0.283, 2, 1.1, -0.269, 2, 1.133, -0.231, 2, 1.167, -0.177, 2, 1.2, -0.112, 2, 1.233, -0.045, 2, 1.267, 0.02, 2, 1.3, 0.075, 2, 1.333, 0.113, 2, 1.367, 0.127, 2, 1.4, 0.121, 2, 1.433, 0.104, 2, 1.467, 0.079, 2, 1.5, 0.05, 2, 1.533, 0.02, 2, 1.567, -0.009, 2, 1.6, -0.034, 2, 1.633, -0.051, 2, 1.667, -0.057, 2, 1.7, -0.055, 2, 1.733, -0.047, 2, 1.767, -0.036, 2, 1.8, -0.023, 2, 1.833, -0.009, 2, 1.867, 0.004, 2, 1.9, 0.015, 2, 1.933, 0.023, 2, 1.967, 0.026, 2, 2, 0.023, 2, 2.033, 0.013, 2, 2.067, -0.001, 2, 2.1, -0.02, 2, 2.133, -0.041, 2, 2.167, -0.063, 2, 2.2, -0.087, 2, 2.233, -0.109, 2, 2.267, -0.13, 2, 2.3, -0.149, 2, 2.333, -0.163, 2, 2.367, -0.172, 2, 2.4, -0.176, 2, 2.433, -0.124, 2, 2.467, 0.003, 2, 2.5, 0.171, 2, 2.533, 0.34, 2, 2.567, 0.466, 2, 2.6, 0.519, 2, 2.633, 0.462, 2, 2.667, 0.314, 2, 2.7, 0.103, 2, 2.733, -0.138, 2, 2.767, -0.379, 2, 2.8, -0.589, 2, 2.833, -0.738, 2, 2.867, -0.794, 2, 2.9, -0.739, 2, 2.933, -0.592, 2, 2.967, -0.379, 2, 3, -0.126, 2, 3.033, 0.134, 2, 3.067, 0.387, 2, 3.1, 0.6, 2, 3.133, 0.747, 2, 3.167, 0.802, 2, 3.2, 0.769, 2, 3.233, 0.681, 2, 3.267, 0.547, 2, 3.3, 0.384, 2, 3.333, 0.2, 2, 3.367, 0.012, 2, 3.4, -0.173, 2, 3.433, -0.335, 2, 3.467, -0.469, 2, 3.5, -0.557, 2, 3.533, -0.591, 2, 3.567, -0.577, 2, 3.6, -0.538, 2, 3.633, -0.477, 2, 3.667, -0.401, 2, 3.7, -0.311, 2, 3.733, -0.213, 2, 3.767, -0.112, 2, 3.8, -0.01, 2, 3.833, 0.088, 2, 3.867, 0.178, 2, 3.9, 0.254, 2, 3.933, 0.314, 2, 3.967, 0.353, 2, 4, 0.367, 2, 4.033, 0.354, 2, 4.067, 0.318, 2, 4.1, 0.263, 2, 4.133, 0.194, 2, 4.167, 0.117, 2, 4.2, 0.034, 2, 4.233, -0.049, 2, 4.267, -0.125, 2, 4.3, -0.195, 2, 4.333, -0.25, 2, 4.367, -0.285, 2, 4.4, -0.299, 2, 4.433, -0.286, 2, 4.467, -0.251, 2, 4.5, -0.198, 2, 4.533, -0.135, 2, 4.567, -0.067, 2, 4.6, 0.001, 2, 4.633, 0.064, 2, 4.667, 0.117, 2, 4.7, 0.152, 2, 4.733, 0.165, 2, 4.767, 0.157, 2, 4.8, 0.135, 2, 4.833, 0.103, 2, 4.867, 0.065, 2, 4.9, 0.026, 2, 4.933, -0.012, 2, 4.967, -0.044, 2, 5, -0.066, 2, 5.033, -0.074, 2, 5.067, -0.07, 2, 5.1, -0.06, 2, 5.133, -0.046, 2, 5.167, -0.029, 2, 5.2, -0.012, 2, 5.233, 0.005, 2, 5.267, 0.02, 2, 5.3, 0.03, 2, 5.333, 0.033, 2, 5.367, 0.031, 2, 5.4, 0.026, 2, 5.433, 0.018, 2, 5.467, 0.009, 2, 5.5, 0, 2, 5.533, -0.007, 2, 5.567, -0.013, 2, 5.6, -0.015, 2, 5.667, -0.014, 2, 5.7, -0.012, 2, 5.733, -0.008, 2, 5.767, -0.004, 2, 5.8, 0, 2, 5.833, 0.003, 2, 5.867, 0.006, 2, 5.9, 0.007, 2, 5.933, 0.006, 2, 5.967, 0.006, 2, 6, 0.004, 2, 6.033, 0.003, 2, 6.067, 0.001, 2, 6.1, -0.001, 2, 6.133, -0.002, 2, 6.167, -0.003, 2, 6.2, -0.003, 2, 6.233, -0.003, 2, 6.267, -0.003, 2, 6.3, -0.002, 2, 6.333, -0.001, 2, 6.367, 0, 2, 6.4, 0, 2, 6.433, 0.001, 2, 6.467, 0.001, 2, 6.5, 0.001, 2, 6.567, 0.001, 2, 6.6, 0.001, 2, 6.633, 0.001, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, 0, 2, 6.767, -0.001, 2, 6.867, -0.001, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0, 2, 7.033, 0, 2, 7.067, 0, 2, 7.167, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.333, 0, 2, 7.367, 0, 2, 7.4, 0, 2, 7.533, 0, 2, 7.667, 0, 2, 7.7, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.016, 2, 0.067, 0.053, 2, 0.1, 0.097, 2, 0.133, 0.134, 2, 0.167, 0.15, 2, 0.2, 0.13, 2, 0.233, 0.078, 2, 0.267, 0.002, 2, 0.3, -0.087, 2, 0.333, -0.18, 2, 0.367, -0.27, 2, 0.4, -0.345, 2, 0.433, -0.397, 2, 0.467, -0.417, 2, 0.5, -0.397, 2, 0.533, -0.343, 2, 0.567, -0.262, 2, 0.6, -0.163, 2, 0.633, -0.051, 2, 0.667, 0.062, 2, 0.7, 0.175, 2, 0.733, 0.273, 2, 0.767, 0.355, 2, 0.8, 0.408, 2, 0.833, 0.428, 2, 0.867, 0.41, 2, 0.9, 0.359, 2, 0.933, 0.283, 2, 0.967, 0.193, 2, 1, 0.095, 2, 1.033, -0.004, 2, 1.067, -0.094, 2, 1.1, -0.17, 2, 1.133, -0.221, 2, 1.167, -0.239, 2, 1.2, -0.229, 2, 1.233, -0.203, 2, 1.267, -0.163, 2, 1.3, -0.115, 2, 1.333, -0.063, 2, 1.367, -0.012, 2, 1.4, 0.036, 2, 1.433, 0.076, 2, 1.467, 0.102, 2, 1.5, 0.112, 2, 1.533, 0.108, 2, 1.567, 0.098, 2, 1.6, 0.082, 2, 1.633, 0.063, 2, 1.667, 0.041, 2, 1.7, 0.019, 2, 1.733, -0.003, 2, 1.767, -0.022, 2, 1.8, -0.038, 2, 1.833, -0.049, 2, 1.867, -0.053, 2, 1.9, -0.051, 2, 1.933, -0.045, 2, 1.967, -0.036, 2, 2, -0.025, 2, 2.033, -0.014, 2, 2.067, -0.002, 2, 2.1, 0.008, 2, 2.133, 0.017, 2, 2.167, 0.023, 2, 2.2, 0.025, 2, 2.233, 0.014, 2, 2.267, -0.012, 2, 2.3, -0.046, 2, 2.333, -0.081, 2, 2.367, -0.107, 2, 2.4, -0.118, 2, 2.433, -0.092, 2, 2.467, -0.025, 2, 2.5, 0.069, 2, 2.533, 0.168, 2, 2.567, 0.261, 2, 2.6, 0.328, 2, 2.633, 0.354, 2, 2.667, 0.314, 2, 2.7, 0.209, 2, 2.733, 0.06, 2, 2.767, -0.111, 2, 2.8, -0.281, 2, 2.833, -0.43, 2, 2.867, -0.535, 2, 2.9, -0.575, 2, 2.933, -0.542, 2, 2.967, -0.451, 2, 3, -0.314, 2, 3.033, -0.151, 2, 3.067, 0.027, 2, 3.1, 0.204, 2, 3.133, 0.367, 2, 3.167, 0.504, 2, 3.2, 0.595, 2, 3.233, 0.628, 2, 3.267, 0.6, 2, 3.3, 0.524, 2, 3.333, 0.409, 2, 3.367, 0.269, 2, 3.4, 0.11, 2, 3.433, -0.05, 2, 3.467, -0.209, 2, 3.5, -0.349, 2, 3.533, -0.464, 2, 3.567, -0.54, 2, 3.6, -0.568, 2, 3.633, -0.552, 2, 3.667, -0.503, 2, 3.7, -0.431, 2, 3.733, -0.339, 2, 3.767, -0.233, 2, 3.8, -0.119, 2, 3.833, -0.001, 2, 3.867, 0.113, 2, 3.9, 0.219, 2, 3.933, 0.311, 2, 3.967, 0.383, 2, 4, 0.432, 2, 4.033, 0.448, 2, 4.067, 0.433, 2, 4.1, 0.391, 2, 4.133, 0.327, 2, 4.167, 0.246, 2, 4.2, 0.157, 2, 4.233, 0.059, 2, 4.267, -0.038, 2, 4.3, -0.127, 2, 4.333, -0.208, 2, 4.367, -0.272, 2, 4.4, -0.314, 2, 4.433, -0.329, 2, 4.467, -0.315, 2, 4.5, -0.277, 2, 4.533, -0.219, 2, 4.567, -0.149, 2, 4.6, -0.074, 2, 4.633, 0.001, 2, 4.667, 0.071, 2, 4.7, 0.129, 2, 4.733, 0.167, 2, 4.767, 0.181, 2, 4.8, 0.175, 2, 4.833, 0.158, 2, 4.867, 0.132, 2, 4.9, 0.101, 2, 4.933, 0.066, 2, 4.967, 0.03, 2, 5, -0.006, 2, 5.033, -0.037, 2, 5.067, -0.063, 2, 5.1, -0.08, 2, 5.133, -0.086, 2, 5.167, -0.082, 2, 5.2, -0.073, 2, 5.233, -0.058, 2, 5.267, -0.041, 2, 5.3, -0.023, 2, 5.333, -0.004, 2, 5.367, 0.013, 2, 5.4, 0.027, 2, 5.433, 0.037, 2, 5.467, 0.04, 2, 5.5, 0.039, 2, 5.533, 0.035, 2, 5.567, 0.03, 2, 5.6, 0.023, 2, 5.633, 0.015, 2, 5.667, 0.007, 2, 5.7, -0.001, 2, 5.733, -0.008, 2, 5.767, -0.014, 2, 5.8, -0.018, 2, 5.833, -0.019, 2, 5.867, -0.018, 2, 5.9, -0.016, 2, 5.933, -0.013, 2, 5.967, -0.009, 2, 6, -0.005, 2, 6.033, -0.001, 2, 6.067, 0.003, 2, 6.1, 0.006, 2, 6.133, 0.008, 2, 6.167, 0.009, 2, 6.2, 0.009, 2, 6.233, 0.008, 2, 6.267, 0.006, 2, 6.3, 0.004, 2, 6.333, 0.002, 2, 6.367, 0, 2, 6.4, -0.001, 2, 6.433, -0.003, 2, 6.467, -0.004, 2, 6.5, -0.004, 2, 6.567, -0.004, 2, 6.6, -0.004, 2, 6.633, -0.003, 2, 6.667, -0.002, 2, 6.7, -0.001, 2, 6.733, 0, 2, 6.767, 0.001, 2, 6.8, 0.001, 2, 6.833, 0.002, 2, 6.867, 0.002, 2, 6.9, 0.002, 2, 6.933, 0.002, 2, 6.967, 0.001, 2, 7, 0.001, 2, 7.033, 0, 2, 7.067, 0, 2, 7.1, -0.001, 2, 7.133, -0.001, 2, 7.167, -0.001, 2, 7.267, -0.001, 2, 7.3, -0.001, 2, 7.333, 0, 2, 7.367, 0, 2, 7.4, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 7.5, 0, 2, 7.567, 0, 2, 7.6, 0, 2, 7.667, 0, 2, 7.7, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.8, 0, 2, 7.833, 0, 2, 7.9, 0, 2, 7.933, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0.031, 2, 0.067, 0.104, 2, 0.1, 0.192, 2, 0.133, 0.265, 2, 0.167, 0.296, 2, 0.2, 0.239, 2, 0.233, 0.091, 2, 0.267, -0.114, 2, 0.3, -0.331, 2, 0.333, -0.536, 2, 0.367, -0.683, 2, 0.4, -0.74, 2, 0.433, -0.701, 2, 0.467, -0.596, 2, 0.5, -0.436, 2, 0.533, -0.246, 2, 0.567, -0.039, 2, 0.6, 0.168, 2, 0.633, 0.358, 2, 0.667, 0.517, 2, 0.7, 0.623, 2, 0.733, 0.662, 2, 0.767, 0.627, 2, 0.8, 0.534, 2, 0.833, 0.4, 2, 0.867, 0.241, 2, 0.9, 0.076, 2, 0.933, -0.083, 2, 0.967, -0.218, 2, 1, -0.311, 2, 1.033, -0.345, 2, 1.067, -0.328, 2, 1.1, -0.282, 2, 1.133, -0.216, 2, 1.167, -0.138, 2, 1.2, -0.057, 2, 1.233, 0.021, 2, 1.267, 0.088, 2, 1.3, 0.133, 2, 1.333, 0.15, 2, 1.367, 0.141, 2, 1.4, 0.116, 2, 1.433, 0.082, 2, 1.467, 0.042, 2, 1.5, 0.002, 2, 1.533, -0.032, 2, 1.567, -0.057, 2, 1.6, -0.066, 2, 1.633, -0.062, 2, 1.667, -0.051, 2, 1.7, -0.036, 2, 1.733, -0.019, 2, 1.767, -0.001, 2, 1.8, 0.014, 2, 1.833, 0.025, 2, 1.867, 0.029, 2, 1.9, 0.027, 2, 1.933, 0.022, 2, 1.967, 0.016, 2, 2, 0.008, 2, 2.033, 0.001, 2, 2.067, -0.006, 2, 2.1, -0.011, 2, 2.133, -0.013, 2, 2.167, -0.012, 2, 2.2, -0.01, 2, 2.233, -0.007, 2, 2.267, -0.004, 2, 2.3, 0, 2, 2.333, 0.003, 2, 2.367, 0.005, 2, 2.4, 0.005, 2, 2.467, 0.005, 2, 2.5, 0.004, 2, 2.533, 0.003, 2, 2.567, 0.002, 2, 2.6, 0, 2, 2.633, -0.001, 2, 2.667, -0.002, 2, 2.7, -0.002, 2, 2.733, -0.002, 2, 2.767, -0.002, 2, 2.8, -0.001, 2, 2.833, 0, 2, 2.867, 0, 2, 2.9, 0.001, 2, 2.933, 0.001, 2, 3.033, 0.001, 2, 3.067, 0.001, 2, 3.1, 0, 2, 3.133, 0, 2, 3.167, 0, 2, 3.2, 0, 2, 3.233, 0, 2, 3.267, 0, 2, 3.333, 0, 2, 3.367, 0, 2, 3.4, 0, 2, 3.433, 0, 2, 3.467, 0, 2, 3.6, 0, 2, 3.667, 0, 2, 3.733, 0, 2, 3.8, 0, 2, 3.833, 0, 2, 3.9, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, -0.007, 2, 0.067, -0.026, 2, 0.1, -0.049, 2, 0.133, -0.067, 2, 0.167, -0.075, 2, 0.2, -0.067, 2, 0.233, -0.045, 2, 0.267, -0.012, 2, 0.3, 0.028, 2, 0.333, 0.071, 2, 0.367, 0.114, 2, 0.4, 0.154, 2, 0.433, 0.187, 2, 0.467, 0.209, 2, 0.5, 0.217, 2, 0.533, 0.206, 2, 0.567, 0.179, 2, 0.6, 0.136, 2, 0.633, 0.085, 2, 0.667, 0.026, 2, 0.7, -0.033, 2, 0.733, -0.091, 2, 0.767, -0.143, 2, 0.8, -0.185, 2, 0.833, -0.213, 2, 0.867, -0.223, 2, 0.9, -0.215, 2, 0.933, -0.193, 2, 0.967, -0.16, 2, 1, -0.12, 2, 1.033, -0.074, 2, 1.067, -0.028, 2, 1.1, 0.017, 2, 1.133, 0.058, 2, 1.167, 0.091, 2, 1.2, 0.112, 2, 1.233, 0.121, 2, 1.267, 0.116, 2, 1.3, 0.105, 2, 1.333, 0.088, 2, 1.367, 0.067, 2, 1.4, 0.043, 2, 1.433, 0.019, 2, 1.467, -0.004, 2, 1.5, -0.025, 2, 1.533, -0.043, 2, 1.567, -0.054, 2, 1.6, -0.058, 2, 1.633, -0.056, 2, 1.667, -0.051, 2, 1.7, -0.042, 2, 1.733, -0.032, 2, 1.767, -0.021, 2, 1.8, -0.009, 2, 1.833, 0.002, 2, 1.867, 0.012, 2, 1.9, 0.02, 2, 1.933, 0.026, 2, 1.967, 0.028, 2, 2, 0.027, 2, 2.033, 0.025, 2, 2.067, 0.021, 2, 2.1, 0.017, 2, 2.133, 0.012, 2, 2.167, 0.007, 2, 2.2, 0.002, 2, 2.233, -0.003, 2, 2.267, -0.007, 2, 2.3, -0.01, 2, 2.333, -0.012, 2, 2.367, -0.013, 2, 2.4, -0.013, 2, 2.433, -0.012, 2, 2.467, -0.01, 2, 2.5, -0.007, 2, 2.533, -0.005, 2, 2.567, -0.002, 2, 2.6, 0, 2, 2.633, 0.003, 2, 2.667, 0.005, 2, 2.7, 0.006, 2, 2.733, 0.006, 2, 2.767, 0.006, 2, 2.8, 0.006, 2, 2.833, 0.005, 2, 2.867, 0.004, 2, 2.9, 0.002, 2, 2.933, 0.001, 2, 2.967, 0, 2, 3, -0.001, 2, 3.033, -0.002, 2, 3.067, -0.003, 2, 3.1, -0.003, 2, 3.167, -0.003, 2, 3.2, -0.003, 2, 3.233, -0.002, 2, 3.267, -0.002, 2, 3.3, -0.001, 2, 3.333, 0, 2, 3.367, 0, 2, 3.4, 0.001, 2, 3.433, 0.001, 2, 3.467, 0.001, 2, 3.5, 0.002, 2, 3.533, 0.001, 2, 3.567, 0.001, 2, 3.6, 0.001, 2, 3.633, 0.001, 2, 3.667, 0, 2, 3.7, 0, 2, 3.733, 0, 2, 3.767, 0, 2, 3.8, -0.001, 2, 3.833, -0.001, 2, 3.933, -0.001, 2, 3.967, -0.001, 2, 4, 0, 2, 4.033, 0, 2, 4.067, 0, 2, 4.1, 0, 2, 4.133, 0, 2, 4.2, 0, 2, 4.233, 0, 2, 4.267, 0, 2, 4.367, 0, 2, 4.4, 0, 2, 4.467, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 4.567, 0, 2, 4.6, 0, 2, 4.633, 0, 2, 4.667, 0, 2, 4.7, 0, 2, 4.733, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 4.933, 0, 2, 4.967, 0, 2, 5, 0, 2, 5.133, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh0", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh0", "Segments": [0, 0, 2, 0.033, 0.264, 2, 0.067, 0.894, 2, 0.1, 1.662, 2, 0.133, 2.292, 2, 0.167, 2.556, 2, 0.2, 2.268, 2, 0.233, 1.497, 2, 0.267, 0.382, 2, 0.3, -0.94, 2, 0.333, -2.305, 2, 0.367, -3.627, 2, 0.4, -4.742, 2, 0.433, -5.513, 2, 0.467, -5.8, 2, 0.5, -5.537, 2, 0.533, -4.821, 2, 0.567, -3.743, 2, 0.6, -2.458, 2, 0.633, -1.06, 2, 0.667, 0.338, 2, 0.7, 1.623, 2, 0.733, 2.702, 2, 0.767, 3.417, 2, 0.8, 3.68, 2, 0.833, 3.507, 2, 0.867, 3.043, 2, 0.9, 2.37, 2, 0.933, 1.573, 2, 0.967, 0.75, 2, 1, -0.047, 2, 1.033, -0.719, 2, 1.067, -1.184, 2, 1.1, -1.357, 2, 1.133, -1.298, 2, 1.167, -1.139, 2, 1.2, -0.91, 2, 1.233, -0.638, 2, 1.267, -0.357, 2, 1.3, -0.085, 2, 1.333, 0.145, 2, 1.367, 0.304, 2, 1.4, 0.363, 2, 1.433, 0.347, 2, 1.467, 0.305, 2, 1.5, 0.243, 2, 1.533, 0.171, 2, 1.567, 0.095, 2, 1.6, 0.023, 2, 1.633, -0.039, 2, 1.667, -0.081, 2, 1.7, -0.097, 2, 1.733, -0.093, 2, 1.767, -0.081, 2, 1.8, -0.065, 2, 1.833, -0.046, 2, 1.867, -0.026, 2, 1.9, -0.006, 2, 1.933, 0.01, 2, 1.967, 0.022, 2, 2, 0.026, 2, 2.033, -0.016, 2, 2.067, -0.128, 2, 2.1, -0.302, 2, 2.133, -0.52, 2, 2.167, -0.761, 2, 2.2, -1.023, 2, 2.233, -1.284, 2, 2.267, -1.526, 2, 2.3, -1.743, 2, 2.333, -1.917, 2, 2.367, -2.029, 2, 2.4, -2.071, 2, 2.433, -1.496, 2, 2.467, -0.1, 2, 2.5, 1.756, 2, 2.533, 3.613, 2, 2.567, 5.009, 2, 2.6, 5.584, 2, 2.633, 5.134, 2, 2.667, 3.929, 2, 2.7, 2.184, 2, 2.733, 0.117, 2, 2.767, -2.019, 2, 2.8, -4.086, 2, 2.833, -5.83, 2, 2.867, -7.036, 2, 2.9, -7.485, 2, 2.933, -7.001, 2, 2.967, -5.704, 2, 3, -3.827, 2, 3.033, -1.603, 2, 3.067, 0.695, 2, 3.1, 2.92, 2, 3.133, 4.797, 2, 3.167, 6.094, 2, 3.2, 6.578, 2, 3.233, 6.39, 2, 3.267, 5.848, 2, 3.3, 5.042, 2, 3.333, 4.007, 2, 3.367, 2.816, 2, 3.4, 1.548, 2, 3.433, 0.216, 2, 3.467, -1.052, 2, 3.5, -2.243, 2, 3.533, -3.278, 2, 3.567, -4.084, 2, 3.6, -4.626, 2, 3.633, -4.814, 2, 3.667, -4.672, 2, 3.7, -4.264, 2, 3.733, -3.656, 2, 3.767, -2.877, 2, 3.8, -1.979, 2, 3.833, -1.024, 2, 3.867, -0.019, 2, 3.9, 0.936, 2, 3.933, 1.834, 2, 3.967, 2.613, 2, 4, 3.221, 2, 4.033, 3.629, 2, 4.067, 3.771, 2, 4.1, 3.627, 2, 4.133, 3.242, 2, 4.167, 2.655, 2, 4.2, 1.946, 2, 4.233, 1.138, 2, 4.267, 0.32, 2, 4.3, -0.487, 2, 4.333, -1.197, 2, 4.367, -1.784, 2, 4.4, -2.169, 2, 4.433, -2.313, 2, 4.467, -2.225, 2, 4.5, -1.986, 2, 4.533, -1.626, 2, 4.567, -1.196, 2, 4.6, -0.729, 2, 4.633, -0.262, 2, 4.667, 0.167, 2, 4.7, 0.528, 2, 4.733, 0.767, 2, 4.767, 0.855, 2, 4.8, 0.808, 2, 4.833, 0.686, 2, 4.867, 0.512, 2, 4.9, 0.314, 2, 4.933, 0.115, 2, 4.967, -0.058, 2, 5, -0.18, 2, 5.033, -0.227, 2, 5.067, -0.217, 2, 5.1, -0.19, 2, 5.133, -0.152, 2, 5.167, -0.106, 2, 5.2, -0.059, 2, 5.233, -0.014, 2, 5.267, 0.025, 2, 5.3, 0.051, 2, 5.333, 0.061, 2, 5.367, 0.059, 2, 5.4, 0.051, 2, 5.433, 0.041, 2, 5.467, 0.029, 2, 5.5, 0.016, 2, 5.533, 0.004, 2, 5.567, -0.007, 2, 5.6, -0.014, 2, 5.633, -0.017, 2, 5.667, -0.016, 2, 5.7, -0.014, 2, 5.733, -0.011, 2, 5.767, -0.008, 2, 5.8, -0.004, 2, 5.833, -0.001, 2, 5.867, 0.002, 2, 5.9, 0.004, 2, 5.933, 0.004, 2, 5.967, 0.004, 2, 6, 0.004, 2, 6.033, 0.003, 2, 6.067, 0.002, 2, 6.1, 0.001, 2, 6.133, 0, 2, 6.167, 0, 2, 6.2, -0.001, 2, 6.233, -0.001, 2, 6.267, -0.001, 2, 6.3, -0.001, 2, 6.333, -0.001, 2, 6.367, 0, 2, 6.4, 0, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.6, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.767, 0, 2, 6.833, 0, 2, 6.867, 0, 2, 6.933, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh0", "Segments": [0, 0, 2, 0.033, -0.213, 2, 0.067, -0.682, 2, 0.1, -1.15, 2, 0.133, -1.363, 2, 0.167, -0.979, 2, 0.2, -0.047, 2, 0.233, 1.193, 2, 0.267, 2.432, 2, 0.3, 3.365, 2, 0.333, 3.749, 2, 0.367, 3.477, 2, 0.4, 2.748, 2, 0.433, 1.694, 2, 0.467, 0.445, 2, 0.5, -0.846, 2, 0.533, -2.095, 2, 0.567, -3.15, 2, 0.6, -3.878, 2, 0.633, -4.15, 2, 0.667, -3.885, 2, 0.7, -3.174, 2, 0.733, -2.145, 2, 0.767, -0.926, 2, 0.8, 0.333, 2, 0.833, 1.552, 2, 0.867, 2.581, 2, 0.9, 3.291, 2, 0.933, 3.557, 2, 0.967, 3.361, 2, 1, 2.836, 2, 1.033, 2.076, 2, 1.067, 1.176, 2, 1.1, 0.246, 2, 1.133, -0.654, 2, 1.167, -1.413, 2, 1.2, -1.938, 2, 1.233, -2.134, 2, 1.267, -2.029, 2, 1.3, -1.748, 2, 1.333, -1.342, 2, 1.367, -0.861, 2, 1.4, -0.363, 2, 1.433, 0.118, 2, 1.467, 0.525, 2, 1.5, 0.806, 2, 1.533, 0.91, 2, 1.567, 0.868, 2, 1.6, 0.753, 2, 1.633, 0.587, 2, 1.667, 0.39, 2, 1.7, 0.187, 2, 1.733, -0.009, 2, 1.767, -0.175, 2, 1.8, -0.29, 2, 1.833, -0.333, 2, 1.867, -0.313, 2, 1.9, -0.263, 2, 1.933, -0.191, 2, 1.967, -0.109, 2, 2, -0.027, 2, 2.033, 0.044, 2, 2.067, 0.095, 2, 2.1, 0.114, 2, 2.133, 0.104, 2, 2.167, 0.08, 2, 2.2, 0.051, 2, 2.233, 0.027, 2, 2.267, 0.017, 2, 2.3, 0.335, 2, 2.333, 0.908, 2, 2.367, 1.226, 2, 2.4, 0.84, 2, 2.433, -0.096, 2, 2.467, -1.34, 2, 2.5, -2.585, 2, 2.533, -3.521, 2, 2.567, -3.906, 2, 2.6, -3.321, 2, 2.633, -1.811, 2, 2.667, 0.285, 2, 2.7, 2.507, 2, 2.733, 4.603, 2, 2.767, 6.113, 2, 2.8, 6.698, 2, 2.833, 6.073, 2, 2.867, 4.424, 2, 2.9, 2.094, 2, 2.933, -0.578, 2, 2.967, -3.25, 2, 3, -5.58, 2, 3.033, -7.229, 2, 3.067, -7.854, 2, 3.1, -7.394, 2, 3.133, -6.161, 2, 3.167, -4.378, 2, 3.2, -2.264, 2, 3.233, -0.08, 2, 3.267, 2.034, 2, 3.3, 3.817, 2, 3.333, 5.05, 2, 3.367, 5.51, 2, 3.4, 5.311, 2, 3.433, 4.782, 2, 3.467, 3.975, 2, 3.5, 2.999, 2, 3.533, 1.888, 2, 3.567, 0.764, 2, 3.6, -0.347, 2, 3.633, -1.323, 2, 3.667, -2.13, 2, 3.7, -2.659, 2, 3.733, -2.858, 2, 3.767, -2.802, 2, 3.8, -2.645, 2, 3.833, -2.401, 2, 3.867, -2.085, 2, 3.9, -1.71, 2, 3.933, -1.293, 2, 3.967, -0.846, 2, 4, -0.384, 2, 4.033, 0.077, 2, 4.067, 0.524, 2, 4.1, 0.942, 2, 4.133, 1.317, 2, 4.167, 1.633, 2, 4.2, 1.877, 2, 4.233, 2.034, 2, 4.267, 2.09, 2, 4.3, 1.974, 2, 4.333, 1.66, 2, 4.367, 1.186, 2, 4.4, 0.621, 2, 4.433, 0.007, 2, 4.467, -0.607, 2, 4.5, -1.171, 2, 4.533, -1.645, 2, 4.567, -1.959, 2, 4.6, -2.075, 2, 4.633, -1.959, 2, 4.667, -1.65, 2, 4.7, -1.203, 2, 4.733, -0.673, 2, 4.767, -0.126, 2, 4.8, 0.404, 2, 4.833, 0.851, 2, 4.867, 1.16, 2, 4.9, 1.275, 2, 4.933, 1.196, 2, 4.967, 0.988, 2, 5, 0.693, 2, 5.033, 0.355, 2, 5.067, 0.017, 2, 5.1, -0.277, 2, 5.133, -0.486, 2, 5.167, -0.565, 2, 5.2, -0.538, 2, 5.233, -0.467, 2, 5.267, -0.363, 2, 5.3, -0.24, 2, 5.333, -0.114, 2, 5.367, 0.009, 2, 5.4, 0.113, 2, 5.433, 0.184, 2, 5.467, 0.211, 2, 5.5, 0.201, 2, 5.533, 0.175, 2, 5.567, 0.137, 2, 5.6, 0.093, 2, 5.633, 0.046, 2, 5.667, 0.001, 2, 5.7, -0.036, 2, 5.733, -0.062, 2, 5.767, -0.072, 2, 5.8, -0.069, 2, 5.833, -0.06, 2, 5.867, -0.047, 2, 5.9, -0.032, 2, 5.933, -0.017, 2, 5.967, -0.001, 2, 6, 0.011, 2, 6.033, 0.02, 2, 6.067, 0.023, 2, 6.1, 0.022, 2, 6.133, 0.019, 2, 6.167, 0.014, 2, 6.2, 0.008, 2, 6.233, 0.002, 2, 6.267, -0.003, 2, 6.3, -0.006, 2, 6.333, -0.007, 2, 6.4, -0.007, 2, 6.433, -0.006, 2, 6.467, -0.004, 2, 6.5, -0.003, 2, 6.533, -0.001, 2, 6.567, 0.001, 2, 6.6, 0.002, 2, 6.633, 0.002, 2, 6.667, 0.002, 2, 6.7, 0.002, 2, 6.733, 0.002, 2, 6.767, 0.001, 2, 6.8, 0.001, 2, 6.833, 0, 2, 6.867, 0, 2, 6.9, -0.001, 2, 6.933, -0.001, 2, 7, -0.001, 2, 7.033, -0.001, 2, 7.067, 0, 2, 7.1, 0, 2, 7.133, 0, 2, 7.167, 0, 2, 7.2, 0, 2, 7.333, 0, 2, 7.4, 0, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.6, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh0", "Segments": [0, 0, 2, 0.033, -0.08, 2, 0.067, -0.274, 2, 0.1, -0.531, 2, 0.133, -0.788, 2, 0.167, -0.982, 2, 0.2, -1.062, 2, 0.233, -0.797, 2, 0.267, -0.115, 2, 0.3, 0.833, 2, 0.333, 1.837, 2, 0.367, 2.784, 2, 0.4, 3.467, 2, 0.433, 3.731, 2, 0.467, 3.417, 2, 0.5, 2.576, 2, 0.533, 1.358, 2, 0.567, -0.085, 2, 0.6, -1.576, 2, 0.633, -3.019, 2, 0.667, -4.237, 2, 0.7, -5.078, 2, 0.733, -5.393, 2, 0.767, -5.035, 2, 0.8, -4.077, 2, 0.833, -2.691, 2, 0.867, -1.049, 2, 0.9, 0.648, 2, 0.933, 2.291, 2, 0.967, 3.677, 2, 1, 4.635, 2, 1.033, 4.992, 2, 1.067, 4.705, 2, 1.1, 3.934, 2, 1.133, 2.82, 2, 1.167, 1.499, 2, 1.2, 0.134, 2, 1.233, -1.187, 2, 1.267, -2.301, 2, 1.3, -3.072, 2, 1.333, -3.359, 2, 1.367, -3.182, 2, 1.4, -2.708, 2, 1.433, -2.022, 2, 1.467, -1.208, 2, 1.5, -0.368, 2, 1.533, 0.445, 2, 1.567, 1.131, 2, 1.6, 1.606, 2, 1.633, 1.782, 2, 1.667, 1.693, 2, 1.7, 1.455, 2, 1.733, 1.109, 2, 1.767, 0.7, 2, 1.8, 0.277, 2, 1.833, -0.132, 2, 1.867, -0.478, 2, 1.9, -0.717, 2, 1.933, -0.806, 2, 1.967, -0.767, 2, 2, -0.662, 2, 2.033, -0.511, 2, 2.067, -0.332, 2, 2.1, -0.147, 2, 2.133, 0.032, 2, 2.167, 0.184, 2, 2.2, 0.288, 2, 2.233, 0.327, 2, 2.267, 0.301, 2, 2.3, 0.275, 2, 2.333, 0.351, 2, 2.367, 0.52, 2, 2.4, 0.688, 2, 2.433, 0.765, 2, 2.467, 0.445, 2, 2.5, -0.332, 2, 2.533, -1.364, 2, 2.567, -2.397, 2, 2.6, -3.173, 2, 2.633, -3.493, 2, 2.667, -2.921, 2, 2.7, -1.444, 2, 2.733, 0.606, 2, 2.767, 2.779, 2, 2.8, 4.829, 2, 2.833, 6.306, 2, 2.867, 6.878, 2, 2.9, 6.202, 2, 2.933, 4.417, 2, 2.967, 1.894, 2, 3, -0.998, 2, 3.033, -3.89, 2, 3.067, -6.412, 2, 3.1, -8.197, 2, 3.133, -8.874, 2, 3.167, -8.411, 2, 3.2, -7.154, 2, 3.233, -5.261, 2, 3.267, -3.003, 2, 3.3, -0.548, 2, 3.333, 1.907, 2, 3.367, 4.164, 2, 3.4, 6.058, 2, 3.433, 7.315, 2, 3.467, 7.777, 2, 3.5, 7.433, 2, 3.533, 6.497, 2, 3.567, 5.086, 2, 3.6, 3.404, 2, 3.633, 1.575, 2, 3.667, -0.254, 2, 3.7, -1.936, 2, 3.733, -3.347, 2, 3.767, -4.283, 2, 3.8, -4.627, 2, 3.833, -4.538, 2, 3.867, -4.285, 2, 3.9, -3.888, 2, 3.933, -3.383, 2, 3.967, -2.781, 2, 4, -2.125, 2, 4.033, -1.431, 2, 4.067, -0.725, 2, 4.1, -0.032, 2, 4.133, 0.624, 2, 4.167, 1.226, 2, 4.2, 1.731, 2, 4.233, 2.128, 2, 4.267, 2.381, 2, 4.3, 2.471, 2, 4.333, 2.347, 2, 4.367, 2.017, 2, 4.4, 1.514, 2, 4.433, 0.906, 2, 4.467, 0.214, 2, 4.5, -0.486, 2, 4.533, -1.178, 2, 4.567, -1.786, 2, 4.6, -2.289, 2, 4.633, -2.619, 2, 4.667, -2.743, 2, 4.7, -2.611, 2, 4.733, -2.255, 2, 4.767, -1.718, 2, 4.8, -1.078, 2, 4.833, -0.382, 2, 4.867, 0.314, 2, 4.9, 0.954, 2, 4.933, 1.491, 2, 4.967, 1.847, 2, 5, 1.978, 2, 5.033, 1.847, 2, 5.067, 1.501, 2, 5.1, 1.011, 2, 5.133, 0.45, 2, 5.167, -0.111, 2, 5.2, -0.6, 2, 5.233, -0.946, 2, 5.267, -1.078, 2, 5.3, -1.023, 2, 5.333, -0.878, 2, 5.367, -0.668, 2, 5.4, -0.419, 2, 5.433, -0.162, 2, 5.467, 0.087, 2, 5.5, 0.297, 2, 5.533, 0.442, 2, 5.567, 0.496, 2, 5.6, 0.472, 2, 5.633, 0.407, 2, 5.667, 0.314, 2, 5.7, 0.203, 2, 5.733, 0.089, 2, 5.767, -0.022, 2, 5.8, -0.115, 2, 5.833, -0.18, 2, 5.867, -0.204, 2, 5.9, -0.194, 2, 5.933, -0.168, 2, 5.967, -0.131, 2, 6, -0.086, 2, 6.033, -0.04, 2, 6.067, 0.004, 2, 6.1, 0.042, 2, 6.133, 0.068, 2, 6.167, 0.078, 2, 6.2, 0.074, 2, 6.233, 0.064, 2, 6.267, 0.05, 2, 6.3, 0.034, 2, 6.333, 0.016, 2, 6.367, -0.001, 2, 6.4, -0.015, 2, 6.433, -0.024, 2, 6.467, -0.028, 2, 6.5, -0.027, 2, 6.533, -0.023, 2, 6.567, -0.018, 2, 6.6, -0.012, 2, 6.633, -0.006, 2, 6.667, 0, 2, 6.7, 0.005, 2, 6.733, 0.008, 2, 6.767, 0.01, 2, 6.8, 0.009, 2, 6.833, 0.008, 2, 6.867, 0.006, 2, 6.9, 0.004, 2, 6.933, 0.002, 2, 6.967, 0, 2, 7, -0.002, 2, 7.033, -0.003, 2, 7.067, -0.003, 2, 7.1, -0.003, 2, 7.133, -0.003, 2, 7.167, -0.002, 2, 7.2, -0.001, 2, 7.233, 0, 2, 7.267, 0, 2, 7.3, 0.001, 2, 7.333, 0.001, 2, 7.4, 0.001, 2, 7.433, 0.001, 2, 7.467, 0.001, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.6, 0, 2, 7.633, 0, 2, 7.667, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.833, 0, 2, 7.867, 0, 2, 7.9, 0, 2, 7.933, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh0", "Segments": [0, 0.003, 2, 0.033, -0.036, 2, 0.067, -0.14, 2, 0.1, -0.286, 2, 0.133, -0.454, 2, 0.167, -0.622, 2, 0.2, -0.768, 2, 0.233, -0.872, 2, 0.267, -0.911, 2, 0.3, -0.705, 2, 0.333, -0.163, 2, 0.367, 0.604, 2, 0.4, 1.483, 2, 0.433, 2.362, 2, 0.467, 3.129, 2, 0.5, 3.671, 2, 0.533, 3.877, 2, 0.567, 3.434, 2, 0.6, 2.268, 2, 0.633, 0.62, 2, 0.667, -1.27, 2, 0.7, -3.16, 2, 0.733, -4.809, 2, 0.767, -5.975, 2, 0.8, -6.417, 2, 0.833, -6.054, 2, 0.867, -5.067, 2, 0.9, -3.58, 2, 0.933, -1.807, 2, 0.967, 0.121, 2, 1, 2.05, 2, 1.033, 3.823, 2, 1.067, 5.31, 2, 1.1, 6.297, 2, 1.133, 6.66, 2, 1.167, 6.26, 2, 1.2, 5.186, 2, 1.233, 3.632, 2, 1.267, 1.79, 2, 1.3, -0.112, 2, 1.333, -1.953, 2, 1.367, -3.507, 2, 1.4, -4.581, 2, 1.433, -4.982, 2, 1.467, -4.706, 2, 1.5, -3.966, 2, 1.533, -2.895, 2, 1.567, -1.626, 2, 1.6, -0.315, 2, 1.633, 0.955, 2, 1.667, 2.026, 2, 1.7, 2.766, 2, 1.733, 3.042, 2, 1.767, 2.882, 2, 1.8, 2.454, 2, 1.833, 1.834, 2, 1.867, 1.099, 2, 1.9, 0.341, 2, 1.933, -0.394, 2, 1.967, -1.014, 2, 2, -1.442, 2, 2.033, -1.602, 2, 2.067, -1.545, 2, 2.1, -1.393, 2, 2.133, -1.161, 2, 2.167, -0.88, 2, 2.2, -0.561, 2, 2.233, -0.237, 2, 2.267, 0.082, 2, 2.3, 0.363, 2, 2.333, 0.595, 2, 2.367, 0.747, 2, 2.4, 0.804, 2, 2.433, 0.687, 2, 2.467, 0.371, 2, 2.5, -0.107, 2, 2.533, -0.676, 2, 2.567, -1.295, 2, 2.6, -1.915, 2, 2.633, -2.484, 2, 2.667, -2.961, 2, 2.7, -3.278, 2, 2.733, -3.395, 2, 2.767, -2.796, 2, 2.8, -1.249, 2, 2.833, 0.897, 2, 2.867, 3.173, 2, 2.9, 5.319, 2, 2.933, 6.866, 2, 2.967, 7.465, 2, 3, 6.699, 2, 3.033, 4.681, 2, 3.067, 1.827, 2, 3.1, -1.444, 2, 3.133, -4.715, 2, 3.167, -7.569, 2, 3.2, -9.587, 2, 3.233, -10.353, 2, 3.267, -9.655, 2, 3.3, -7.785, 2, 3.333, -5.078, 2, 3.367, -1.871, 2, 3.4, 1.443, 2, 3.433, 4.65, 2, 3.467, 7.356, 2, 3.5, 9.227, 2, 3.533, 9.925, 2, 3.567, 9.526, 2, 3.6, 8.462, 2, 3.633, 6.842, 2, 3.667, 4.882, 2, 3.7, 2.65, 2, 3.733, 0.391, 2, 3.767, -1.84, 2, 3.8, -3.8, 2, 3.833, -5.421, 2, 3.867, -6.484, 2, 3.9, -6.883, 2, 3.933, -6.679, 2, 3.967, -6.133, 2, 4, -5.29, 2, 4.033, -4.231, 2, 4.067, -3.057, 2, 4.1, -1.784, 2, 4.133, -0.512, 2, 4.167, 0.662, 2, 4.2, 1.721, 2, 4.233, 2.565, 2, 4.267, 3.111, 2, 4.3, 3.314, 2, 4.333, 3.217, 2, 4.367, 2.943, 2, 4.4, 2.515, 2, 4.433, 1.984, 2, 4.467, 1.349, 2, 4.5, 0.663, 2, 4.533, -0.053, 2, 4.567, -0.768, 2, 4.6, -1.454, 2, 4.633, -2.089, 2, 4.667, -2.621, 2, 4.7, -3.048, 2, 4.733, -3.322, 2, 4.767, -3.42, 2, 4.8, -3.202, 2, 4.833, -2.621, 2, 4.867, -1.779, 2, 4.9, -0.782, 2, 4.933, 0.248, 2, 4.967, 1.245, 2, 5, 2.087, 2, 5.033, 2.669, 2, 5.067, 2.886, 2, 5.1, 2.723, 2, 5.133, 2.288, 2, 5.167, 1.659, 2, 5.2, 0.913, 2, 5.233, 0.142, 2, 5.267, -0.604, 2, 5.3, -1.234, 2, 5.333, -1.669, 2, 5.367, -1.831, 2, 5.4, -1.735, 2, 5.433, -1.475, 2, 5.467, -1.101, 2, 5.5, -0.656, 2, 5.533, -0.197, 2, 5.567, 0.247, 2, 5.6, 0.622, 2, 5.633, 0.881, 2, 5.667, 0.978, 2, 5.7, 0.928, 2, 5.733, 0.795, 2, 5.767, 0.603, 2, 5.8, 0.375, 2, 5.833, 0.14, 2, 5.867, -0.088, 2, 5.9, -0.281, 2, 5.933, -0.413, 2, 5.967, -0.463, 2, 6, -0.44, 2, 6.033, -0.379, 2, 6.067, -0.29, 2, 6.1, -0.185, 2, 6.133, -0.077, 2, 6.167, 0.028, 2, 6.2, 0.117, 2, 6.233, 0.178, 2, 6.267, 0.201, 2, 6.3, 0.191, 2, 6.333, 0.165, 2, 6.367, 0.127, 2, 6.4, 0.083, 2, 6.433, 0.037, 2, 6.467, -0.008, 2, 6.5, -0.046, 2, 6.533, -0.072, 2, 6.567, -0.082, 2, 6.6, -0.078, 2, 6.633, -0.067, 2, 6.667, -0.052, 2, 6.7, -0.034, 2, 6.733, -0.016, 2, 6.767, 0.002, 2, 6.8, 0.017, 2, 6.833, 0.028, 2, 6.867, 0.032, 2, 6.9, 0.03, 2, 6.933, 0.026, 2, 6.967, 0.02, 2, 7, 0.013, 2, 7.033, 0.006, 2, 7.067, 0, 2, 7.1, -0.006, 2, 7.133, -0.01, 2, 7.167, -0.012, 2, 7.2, -0.011, 2, 7.233, -0.01, 2, 7.267, -0.007, 2, 7.3, -0.005, 2, 7.333, -0.002, 2, 7.367, 0, 2, 7.4, 0.002, 2, 7.433, 0.004, 2, 7.467, 0.004, 2, 7.5, 0.004, 2, 7.533, 0.003, 2, 7.567, 0.002, 2, 7.6, 0.001, 2, 7.633, 0, 2, 7.667, -0.001, 2, 7.7, -0.001, 2, 7.733, -0.001, 2, 7.8, -0.001, 2, 7.833, -0.001, 2, 7.867, -0.001, 2, 7.9, 0, 2, 7.933, 0, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh1", "Segments": [0, 0, 2, 0.033, 0.264, 2, 0.067, 0.894, 2, 0.1, 1.662, 2, 0.133, 2.292, 2, 0.167, 2.556, 2, 0.2, 2.268, 2, 0.233, 1.497, 2, 0.267, 0.382, 2, 0.3, -0.94, 2, 0.333, -2.305, 2, 0.367, -3.627, 2, 0.4, -4.742, 2, 0.433, -5.513, 2, 0.467, -5.8, 2, 0.5, -5.537, 2, 0.533, -4.821, 2, 0.567, -3.743, 2, 0.6, -2.458, 2, 0.633, -1.06, 2, 0.667, 0.338, 2, 0.7, 1.623, 2, 0.733, 2.702, 2, 0.767, 3.417, 2, 0.8, 3.68, 2, 0.833, 3.507, 2, 0.867, 3.043, 2, 0.9, 2.37, 2, 0.933, 1.573, 2, 0.967, 0.75, 2, 1, -0.047, 2, 1.033, -0.719, 2, 1.067, -1.184, 2, 1.1, -1.357, 2, 1.133, -1.298, 2, 1.167, -1.139, 2, 1.2, -0.91, 2, 1.233, -0.638, 2, 1.267, -0.357, 2, 1.3, -0.085, 2, 1.333, 0.145, 2, 1.367, 0.304, 2, 1.4, 0.363, 2, 1.433, 0.347, 2, 1.467, 0.305, 2, 1.5, 0.243, 2, 1.533, 0.171, 2, 1.567, 0.095, 2, 1.6, 0.023, 2, 1.633, -0.039, 2, 1.667, -0.081, 2, 1.7, -0.097, 2, 1.733, -0.093, 2, 1.767, -0.081, 2, 1.8, -0.065, 2, 1.833, -0.046, 2, 1.867, -0.026, 2, 1.9, -0.006, 2, 1.933, 0.01, 2, 1.967, 0.022, 2, 2, 0.026, 2, 2.033, -0.016, 2, 2.067, -0.128, 2, 2.1, -0.302, 2, 2.133, -0.52, 2, 2.167, -0.761, 2, 2.2, -1.023, 2, 2.233, -1.284, 2, 2.267, -1.526, 2, 2.3, -1.743, 2, 2.333, -1.917, 2, 2.367, -2.029, 2, 2.4, -2.071, 2, 2.433, -1.496, 2, 2.467, -0.1, 2, 2.5, 1.756, 2, 2.533, 3.613, 2, 2.567, 5.009, 2, 2.6, 5.584, 2, 2.633, 5.134, 2, 2.667, 3.929, 2, 2.7, 2.184, 2, 2.733, 0.117, 2, 2.767, -2.019, 2, 2.8, -4.086, 2, 2.833, -5.83, 2, 2.867, -7.036, 2, 2.9, -7.485, 2, 2.933, -7.001, 2, 2.967, -5.704, 2, 3, -3.827, 2, 3.033, -1.603, 2, 3.067, 0.695, 2, 3.1, 2.92, 2, 3.133, 4.797, 2, 3.167, 6.094, 2, 3.2, 6.578, 2, 3.233, 6.39, 2, 3.267, 5.848, 2, 3.3, 5.042, 2, 3.333, 4.007, 2, 3.367, 2.816, 2, 3.4, 1.548, 2, 3.433, 0.216, 2, 3.467, -1.052, 2, 3.5, -2.243, 2, 3.533, -3.278, 2, 3.567, -4.084, 2, 3.6, -4.626, 2, 3.633, -4.814, 2, 3.667, -4.672, 2, 3.7, -4.264, 2, 3.733, -3.656, 2, 3.767, -2.877, 2, 3.8, -1.979, 2, 3.833, -1.024, 2, 3.867, -0.019, 2, 3.9, 0.936, 2, 3.933, 1.834, 2, 3.967, 2.613, 2, 4, 3.221, 2, 4.033, 3.629, 2, 4.067, 3.771, 2, 4.1, 3.627, 2, 4.133, 3.242, 2, 4.167, 2.655, 2, 4.2, 1.946, 2, 4.233, 1.138, 2, 4.267, 0.32, 2, 4.3, -0.487, 2, 4.333, -1.197, 2, 4.367, -1.784, 2, 4.4, -2.169, 2, 4.433, -2.313, 2, 4.467, -2.225, 2, 4.5, -1.986, 2, 4.533, -1.626, 2, 4.567, -1.196, 2, 4.6, -0.729, 2, 4.633, -0.262, 2, 4.667, 0.167, 2, 4.7, 0.528, 2, 4.733, 0.767, 2, 4.767, 0.855, 2, 4.8, 0.808, 2, 4.833, 0.686, 2, 4.867, 0.512, 2, 4.9, 0.314, 2, 4.933, 0.115, 2, 4.967, -0.058, 2, 5, -0.18, 2, 5.033, -0.227, 2, 5.067, -0.217, 2, 5.1, -0.19, 2, 5.133, -0.152, 2, 5.167, -0.106, 2, 5.2, -0.059, 2, 5.233, -0.014, 2, 5.267, 0.025, 2, 5.3, 0.051, 2, 5.333, 0.061, 2, 5.367, 0.059, 2, 5.4, 0.051, 2, 5.433, 0.041, 2, 5.467, 0.029, 2, 5.5, 0.016, 2, 5.533, 0.004, 2, 5.567, -0.007, 2, 5.6, -0.014, 2, 5.633, -0.017, 2, 5.667, -0.016, 2, 5.7, -0.014, 2, 5.733, -0.011, 2, 5.767, -0.008, 2, 5.8, -0.004, 2, 5.833, -0.001, 2, 5.867, 0.002, 2, 5.9, 0.004, 2, 5.933, 0.004, 2, 5.967, 0.004, 2, 6, 0.004, 2, 6.033, 0.003, 2, 6.067, 0.002, 2, 6.1, 0.001, 2, 6.133, 0, 2, 6.167, 0, 2, 6.2, -0.001, 2, 6.233, -0.001, 2, 6.267, -0.001, 2, 6.3, -0.001, 2, 6.333, -0.001, 2, 6.367, 0, 2, 6.4, 0, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.6, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.767, 0, 2, 6.833, 0, 2, 6.867, 0, 2, 6.933, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1", "Segments": [0, 0, 2, 0.033, -0.213, 2, 0.067, -0.682, 2, 0.1, -1.15, 2, 0.133, -1.363, 2, 0.167, -0.979, 2, 0.2, -0.047, 2, 0.233, 1.193, 2, 0.267, 2.432, 2, 0.3, 3.365, 2, 0.333, 3.749, 2, 0.367, 3.477, 2, 0.4, 2.748, 2, 0.433, 1.694, 2, 0.467, 0.445, 2, 0.5, -0.846, 2, 0.533, -2.095, 2, 0.567, -3.15, 2, 0.6, -3.878, 2, 0.633, -4.15, 2, 0.667, -3.885, 2, 0.7, -3.174, 2, 0.733, -2.145, 2, 0.767, -0.926, 2, 0.8, 0.333, 2, 0.833, 1.552, 2, 0.867, 2.581, 2, 0.9, 3.291, 2, 0.933, 3.557, 2, 0.967, 3.361, 2, 1, 2.836, 2, 1.033, 2.076, 2, 1.067, 1.176, 2, 1.1, 0.246, 2, 1.133, -0.654, 2, 1.167, -1.413, 2, 1.2, -1.938, 2, 1.233, -2.134, 2, 1.267, -2.029, 2, 1.3, -1.748, 2, 1.333, -1.342, 2, 1.367, -0.861, 2, 1.4, -0.363, 2, 1.433, 0.118, 2, 1.467, 0.525, 2, 1.5, 0.806, 2, 1.533, 0.91, 2, 1.567, 0.868, 2, 1.6, 0.753, 2, 1.633, 0.587, 2, 1.667, 0.39, 2, 1.7, 0.187, 2, 1.733, -0.009, 2, 1.767, -0.175, 2, 1.8, -0.29, 2, 1.833, -0.333, 2, 1.867, -0.313, 2, 1.9, -0.263, 2, 1.933, -0.191, 2, 1.967, -0.109, 2, 2, -0.027, 2, 2.033, 0.044, 2, 2.067, 0.095, 2, 2.1, 0.114, 2, 2.133, 0.104, 2, 2.167, 0.08, 2, 2.2, 0.051, 2, 2.233, 0.027, 2, 2.267, 0.017, 2, 2.3, 0.335, 2, 2.333, 0.908, 2, 2.367, 1.226, 2, 2.4, 0.84, 2, 2.433, -0.096, 2, 2.467, -1.34, 2, 2.5, -2.585, 2, 2.533, -3.521, 2, 2.567, -3.906, 2, 2.6, -3.321, 2, 2.633, -1.811, 2, 2.667, 0.285, 2, 2.7, 2.507, 2, 2.733, 4.603, 2, 2.767, 6.113, 2, 2.8, 6.698, 2, 2.833, 6.073, 2, 2.867, 4.424, 2, 2.9, 2.094, 2, 2.933, -0.578, 2, 2.967, -3.25, 2, 3, -5.58, 2, 3.033, -7.229, 2, 3.067, -7.854, 2, 3.1, -7.394, 2, 3.133, -6.161, 2, 3.167, -4.378, 2, 3.2, -2.264, 2, 3.233, -0.08, 2, 3.267, 2.034, 2, 3.3, 3.817, 2, 3.333, 5.05, 2, 3.367, 5.51, 2, 3.4, 5.311, 2, 3.433, 4.782, 2, 3.467, 3.975, 2, 3.5, 2.999, 2, 3.533, 1.888, 2, 3.567, 0.764, 2, 3.6, -0.347, 2, 3.633, -1.323, 2, 3.667, -2.13, 2, 3.7, -2.659, 2, 3.733, -2.858, 2, 3.767, -2.802, 2, 3.8, -2.645, 2, 3.833, -2.401, 2, 3.867, -2.085, 2, 3.9, -1.71, 2, 3.933, -1.293, 2, 3.967, -0.846, 2, 4, -0.384, 2, 4.033, 0.077, 2, 4.067, 0.524, 2, 4.1, 0.942, 2, 4.133, 1.317, 2, 4.167, 1.633, 2, 4.2, 1.877, 2, 4.233, 2.034, 2, 4.267, 2.09, 2, 4.3, 1.974, 2, 4.333, 1.66, 2, 4.367, 1.186, 2, 4.4, 0.621, 2, 4.433, 0.007, 2, 4.467, -0.607, 2, 4.5, -1.171, 2, 4.533, -1.645, 2, 4.567, -1.959, 2, 4.6, -2.075, 2, 4.633, -1.959, 2, 4.667, -1.65, 2, 4.7, -1.203, 2, 4.733, -0.673, 2, 4.767, -0.126, 2, 4.8, 0.404, 2, 4.833, 0.851, 2, 4.867, 1.16, 2, 4.9, 1.275, 2, 4.933, 1.196, 2, 4.967, 0.988, 2, 5, 0.693, 2, 5.033, 0.355, 2, 5.067, 0.017, 2, 5.1, -0.277, 2, 5.133, -0.486, 2, 5.167, -0.565, 2, 5.2, -0.538, 2, 5.233, -0.467, 2, 5.267, -0.363, 2, 5.3, -0.24, 2, 5.333, -0.114, 2, 5.367, 0.009, 2, 5.4, 0.113, 2, 5.433, 0.184, 2, 5.467, 0.211, 2, 5.5, 0.201, 2, 5.533, 0.175, 2, 5.567, 0.137, 2, 5.6, 0.093, 2, 5.633, 0.046, 2, 5.667, 0.001, 2, 5.7, -0.036, 2, 5.733, -0.062, 2, 5.767, -0.072, 2, 5.8, -0.069, 2, 5.833, -0.06, 2, 5.867, -0.047, 2, 5.9, -0.032, 2, 5.933, -0.017, 2, 5.967, -0.001, 2, 6, 0.011, 2, 6.033, 0.02, 2, 6.067, 0.023, 2, 6.1, 0.022, 2, 6.133, 0.019, 2, 6.167, 0.014, 2, 6.2, 0.008, 2, 6.233, 0.002, 2, 6.267, -0.003, 2, 6.3, -0.006, 2, 6.333, -0.007, 2, 6.4, -0.007, 2, 6.433, -0.006, 2, 6.467, -0.004, 2, 6.5, -0.003, 2, 6.533, -0.001, 2, 6.567, 0.001, 2, 6.6, 0.002, 2, 6.633, 0.002, 2, 6.667, 0.002, 2, 6.7, 0.002, 2, 6.733, 0.002, 2, 6.767, 0.001, 2, 6.8, 0.001, 2, 6.833, 0, 2, 6.867, 0, 2, 6.9, -0.001, 2, 6.933, -0.001, 2, 7, -0.001, 2, 7.033, -0.001, 2, 7.067, 0, 2, 7.1, 0, 2, 7.133, 0, 2, 7.167, 0, 2, 7.2, 0, 2, 7.333, 0, 2, 7.4, 0, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.6, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh1", "Segments": [0, 0, 2, 0.033, -0.08, 2, 0.067, -0.274, 2, 0.1, -0.531, 2, 0.133, -0.788, 2, 0.167, -0.982, 2, 0.2, -1.062, 2, 0.233, -0.797, 2, 0.267, -0.115, 2, 0.3, 0.833, 2, 0.333, 1.837, 2, 0.367, 2.784, 2, 0.4, 3.467, 2, 0.433, 3.731, 2, 0.467, 3.417, 2, 0.5, 2.576, 2, 0.533, 1.358, 2, 0.567, -0.085, 2, 0.6, -1.576, 2, 0.633, -3.019, 2, 0.667, -4.237, 2, 0.7, -5.078, 2, 0.733, -5.393, 2, 0.767, -5.035, 2, 0.8, -4.077, 2, 0.833, -2.691, 2, 0.867, -1.049, 2, 0.9, 0.648, 2, 0.933, 2.291, 2, 0.967, 3.677, 2, 1, 4.635, 2, 1.033, 4.992, 2, 1.067, 4.705, 2, 1.1, 3.934, 2, 1.133, 2.82, 2, 1.167, 1.499, 2, 1.2, 0.134, 2, 1.233, -1.187, 2, 1.267, -2.301, 2, 1.3, -3.072, 2, 1.333, -3.359, 2, 1.367, -3.182, 2, 1.4, -2.708, 2, 1.433, -2.022, 2, 1.467, -1.208, 2, 1.5, -0.368, 2, 1.533, 0.445, 2, 1.567, 1.131, 2, 1.6, 1.606, 2, 1.633, 1.782, 2, 1.667, 1.693, 2, 1.7, 1.455, 2, 1.733, 1.109, 2, 1.767, 0.7, 2, 1.8, 0.277, 2, 1.833, -0.132, 2, 1.867, -0.478, 2, 1.9, -0.717, 2, 1.933, -0.806, 2, 1.967, -0.767, 2, 2, -0.662, 2, 2.033, -0.511, 2, 2.067, -0.332, 2, 2.1, -0.147, 2, 2.133, 0.032, 2, 2.167, 0.184, 2, 2.2, 0.288, 2, 2.233, 0.327, 2, 2.267, 0.301, 2, 2.3, 0.275, 2, 2.333, 0.351, 2, 2.367, 0.52, 2, 2.4, 0.688, 2, 2.433, 0.765, 2, 2.467, 0.445, 2, 2.5, -0.332, 2, 2.533, -1.364, 2, 2.567, -2.397, 2, 2.6, -3.173, 2, 2.633, -3.493, 2, 2.667, -2.921, 2, 2.7, -1.444, 2, 2.733, 0.606, 2, 2.767, 2.779, 2, 2.8, 4.829, 2, 2.833, 6.306, 2, 2.867, 6.878, 2, 2.9, 6.202, 2, 2.933, 4.417, 2, 2.967, 1.894, 2, 3, -0.998, 2, 3.033, -3.89, 2, 3.067, -6.412, 2, 3.1, -8.197, 2, 3.133, -8.874, 2, 3.167, -8.411, 2, 3.2, -7.154, 2, 3.233, -5.261, 2, 3.267, -3.003, 2, 3.3, -0.548, 2, 3.333, 1.907, 2, 3.367, 4.164, 2, 3.4, 6.058, 2, 3.433, 7.315, 2, 3.467, 7.777, 2, 3.5, 7.433, 2, 3.533, 6.497, 2, 3.567, 5.086, 2, 3.6, 3.404, 2, 3.633, 1.575, 2, 3.667, -0.254, 2, 3.7, -1.936, 2, 3.733, -3.347, 2, 3.767, -4.283, 2, 3.8, -4.627, 2, 3.833, -4.538, 2, 3.867, -4.285, 2, 3.9, -3.888, 2, 3.933, -3.383, 2, 3.967, -2.781, 2, 4, -2.125, 2, 4.033, -1.431, 2, 4.067, -0.725, 2, 4.1, -0.032, 2, 4.133, 0.624, 2, 4.167, 1.226, 2, 4.2, 1.731, 2, 4.233, 2.128, 2, 4.267, 2.381, 2, 4.3, 2.471, 2, 4.333, 2.347, 2, 4.367, 2.017, 2, 4.4, 1.514, 2, 4.433, 0.906, 2, 4.467, 0.214, 2, 4.5, -0.486, 2, 4.533, -1.178, 2, 4.567, -1.786, 2, 4.6, -2.289, 2, 4.633, -2.619, 2, 4.667, -2.743, 2, 4.7, -2.611, 2, 4.733, -2.255, 2, 4.767, -1.718, 2, 4.8, -1.078, 2, 4.833, -0.382, 2, 4.867, 0.314, 2, 4.9, 0.954, 2, 4.933, 1.491, 2, 4.967, 1.847, 2, 5, 1.978, 2, 5.033, 1.847, 2, 5.067, 1.501, 2, 5.1, 1.011, 2, 5.133, 0.45, 2, 5.167, -0.111, 2, 5.2, -0.6, 2, 5.233, -0.946, 2, 5.267, -1.078, 2, 5.3, -1.023, 2, 5.333, -0.878, 2, 5.367, -0.668, 2, 5.4, -0.419, 2, 5.433, -0.162, 2, 5.467, 0.087, 2, 5.5, 0.297, 2, 5.533, 0.442, 2, 5.567, 0.496, 2, 5.6, 0.472, 2, 5.633, 0.407, 2, 5.667, 0.314, 2, 5.7, 0.203, 2, 5.733, 0.089, 2, 5.767, -0.022, 2, 5.8, -0.115, 2, 5.833, -0.18, 2, 5.867, -0.204, 2, 5.9, -0.194, 2, 5.933, -0.168, 2, 5.967, -0.131, 2, 6, -0.086, 2, 6.033, -0.04, 2, 6.067, 0.004, 2, 6.1, 0.042, 2, 6.133, 0.068, 2, 6.167, 0.078, 2, 6.2, 0.074, 2, 6.233, 0.064, 2, 6.267, 0.05, 2, 6.3, 0.034, 2, 6.333, 0.016, 2, 6.367, -0.001, 2, 6.4, -0.015, 2, 6.433, -0.024, 2, 6.467, -0.028, 2, 6.5, -0.027, 2, 6.533, -0.023, 2, 6.567, -0.018, 2, 6.6, -0.012, 2, 6.633, -0.006, 2, 6.667, 0, 2, 6.7, 0.005, 2, 6.733, 0.008, 2, 6.767, 0.01, 2, 6.8, 0.009, 2, 6.833, 0.008, 2, 6.867, 0.006, 2, 6.9, 0.004, 2, 6.933, 0.002, 2, 6.967, 0, 2, 7, -0.002, 2, 7.033, -0.003, 2, 7.067, -0.003, 2, 7.1, -0.003, 2, 7.133, -0.003, 2, 7.167, -0.002, 2, 7.2, -0.001, 2, 7.233, 0, 2, 7.267, 0, 2, 7.3, 0.001, 2, 7.333, 0.001, 2, 7.4, 0.001, 2, 7.433, 0.001, 2, 7.467, 0.001, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.6, 0, 2, 7.633, 0, 2, 7.667, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.833, 0, 2, 7.867, 0, 2, 7.9, 0, 2, 7.933, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh1", "Segments": [0, 0.003, 2, 0.033, -0.036, 2, 0.067, -0.14, 2, 0.1, -0.286, 2, 0.133, -0.454, 2, 0.167, -0.622, 2, 0.2, -0.768, 2, 0.233, -0.872, 2, 0.267, -0.911, 2, 0.3, -0.705, 2, 0.333, -0.163, 2, 0.367, 0.604, 2, 0.4, 1.483, 2, 0.433, 2.362, 2, 0.467, 3.129, 2, 0.5, 3.671, 2, 0.533, 3.877, 2, 0.567, 3.434, 2, 0.6, 2.268, 2, 0.633, 0.62, 2, 0.667, -1.27, 2, 0.7, -3.16, 2, 0.733, -4.809, 2, 0.767, -5.975, 2, 0.8, -6.417, 2, 0.833, -6.054, 2, 0.867, -5.067, 2, 0.9, -3.58, 2, 0.933, -1.807, 2, 0.967, 0.121, 2, 1, 2.05, 2, 1.033, 3.823, 2, 1.067, 5.31, 2, 1.1, 6.297, 2, 1.133, 6.66, 2, 1.167, 6.26, 2, 1.2, 5.186, 2, 1.233, 3.632, 2, 1.267, 1.79, 2, 1.3, -0.112, 2, 1.333, -1.953, 2, 1.367, -3.507, 2, 1.4, -4.581, 2, 1.433, -4.982, 2, 1.467, -4.706, 2, 1.5, -3.966, 2, 1.533, -2.895, 2, 1.567, -1.626, 2, 1.6, -0.315, 2, 1.633, 0.955, 2, 1.667, 2.026, 2, 1.7, 2.766, 2, 1.733, 3.042, 2, 1.767, 2.882, 2, 1.8, 2.454, 2, 1.833, 1.834, 2, 1.867, 1.099, 2, 1.9, 0.341, 2, 1.933, -0.394, 2, 1.967, -1.014, 2, 2, -1.442, 2, 2.033, -1.602, 2, 2.067, -1.545, 2, 2.1, -1.393, 2, 2.133, -1.161, 2, 2.167, -0.88, 2, 2.2, -0.561, 2, 2.233, -0.237, 2, 2.267, 0.082, 2, 2.3, 0.363, 2, 2.333, 0.595, 2, 2.367, 0.747, 2, 2.4, 0.804, 2, 2.433, 0.687, 2, 2.467, 0.371, 2, 2.5, -0.107, 2, 2.533, -0.676, 2, 2.567, -1.295, 2, 2.6, -1.915, 2, 2.633, -2.484, 2, 2.667, -2.961, 2, 2.7, -3.278, 2, 2.733, -3.395, 2, 2.767, -2.796, 2, 2.8, -1.249, 2, 2.833, 0.897, 2, 2.867, 3.173, 2, 2.9, 5.319, 2, 2.933, 6.866, 2, 2.967, 7.465, 2, 3, 6.699, 2, 3.033, 4.681, 2, 3.067, 1.827, 2, 3.1, -1.444, 2, 3.133, -4.715, 2, 3.167, -7.569, 2, 3.2, -9.587, 2, 3.233, -10.353, 2, 3.267, -9.655, 2, 3.3, -7.785, 2, 3.333, -5.078, 2, 3.367, -1.871, 2, 3.4, 1.443, 2, 3.433, 4.65, 2, 3.467, 7.356, 2, 3.5, 9.227, 2, 3.533, 9.925, 2, 3.567, 9.526, 2, 3.6, 8.462, 2, 3.633, 6.842, 2, 3.667, 4.882, 2, 3.7, 2.65, 2, 3.733, 0.391, 2, 3.767, -1.84, 2, 3.8, -3.8, 2, 3.833, -5.421, 2, 3.867, -6.484, 2, 3.9, -6.883, 2, 3.933, -6.679, 2, 3.967, -6.133, 2, 4, -5.29, 2, 4.033, -4.231, 2, 4.067, -3.057, 2, 4.1, -1.784, 2, 4.133, -0.512, 2, 4.167, 0.662, 2, 4.2, 1.721, 2, 4.233, 2.565, 2, 4.267, 3.111, 2, 4.3, 3.314, 2, 4.333, 3.217, 2, 4.367, 2.943, 2, 4.4, 2.515, 2, 4.433, 1.984, 2, 4.467, 1.349, 2, 4.5, 0.663, 2, 4.533, -0.053, 2, 4.567, -0.768, 2, 4.6, -1.454, 2, 4.633, -2.089, 2, 4.667, -2.621, 2, 4.7, -3.048, 2, 4.733, -3.322, 2, 4.767, -3.42, 2, 4.8, -3.202, 2, 4.833, -2.621, 2, 4.867, -1.779, 2, 4.9, -0.782, 2, 4.933, 0.248, 2, 4.967, 1.245, 2, 5, 2.087, 2, 5.033, 2.669, 2, 5.067, 2.886, 2, 5.1, 2.723, 2, 5.133, 2.288, 2, 5.167, 1.659, 2, 5.2, 0.913, 2, 5.233, 0.142, 2, 5.267, -0.604, 2, 5.3, -1.234, 2, 5.333, -1.669, 2, 5.367, -1.831, 2, 5.4, -1.735, 2, 5.433, -1.475, 2, 5.467, -1.101, 2, 5.5, -0.656, 2, 5.533, -0.197, 2, 5.567, 0.247, 2, 5.6, 0.622, 2, 5.633, 0.881, 2, 5.667, 0.978, 2, 5.7, 0.928, 2, 5.733, 0.795, 2, 5.767, 0.603, 2, 5.8, 0.375, 2, 5.833, 0.14, 2, 5.867, -0.088, 2, 5.9, -0.281, 2, 5.933, -0.413, 2, 5.967, -0.463, 2, 6, -0.44, 2, 6.033, -0.379, 2, 6.067, -0.29, 2, 6.1, -0.185, 2, 6.133, -0.077, 2, 6.167, 0.028, 2, 6.2, 0.117, 2, 6.233, 0.178, 2, 6.267, 0.201, 2, 6.3, 0.191, 2, 6.333, 0.165, 2, 6.367, 0.127, 2, 6.4, 0.083, 2, 6.433, 0.037, 2, 6.467, -0.008, 2, 6.5, -0.046, 2, 6.533, -0.072, 2, 6.567, -0.082, 2, 6.6, -0.078, 2, 6.633, -0.067, 2, 6.667, -0.052, 2, 6.7, -0.034, 2, 6.733, -0.016, 2, 6.767, 0.002, 2, 6.8, 0.017, 2, 6.833, 0.028, 2, 6.867, 0.032, 2, 6.9, 0.03, 2, 6.933, 0.026, 2, 6.967, 0.02, 2, 7, 0.013, 2, 7.033, 0.006, 2, 7.067, 0, 2, 7.1, -0.006, 2, 7.133, -0.01, 2, 7.167, -0.012, 2, 7.2, -0.011, 2, 7.233, -0.01, 2, 7.267, -0.007, 2, 7.3, -0.005, 2, 7.333, -0.002, 2, 7.367, 0, 2, 7.4, 0.002, 2, 7.433, 0.004, 2, 7.467, 0.004, 2, 7.5, 0.004, 2, 7.533, 0.003, 2, 7.567, 0.002, 2, 7.6, 0.001, 2, 7.633, 0, 2, 7.667, -0.001, 2, 7.7, -0.001, 2, 7.733, -0.001, 2, 7.8, -0.001, 2, 7.833, -0.001, 2, 7.867, -0.001, 2, 7.9, 0, 2, 7.933, 0, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh2", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh2", "Segments": [0, 0, 2, 0.033, 0.256, 2, 0.067, 0.866, 2, 0.1, 1.61, 2, 0.133, 2.22, 2, 0.167, 2.476, 2, 0.2, 2.238, 2, 0.233, 1.591, 2, 0.267, 0.616, 2, 0.3, -0.546, 2, 0.333, -1.81, 2, 0.367, -3.073, 2, 0.4, -4.235, 2, 0.433, -5.21, 2, 0.467, -5.857, 2, 0.5, -6.095, 2, 0.533, -5.818, 2, 0.567, -5.066, 2, 0.6, -3.932, 2, 0.633, -2.58, 2, 0.667, -1.11, 2, 0.7, 0.36, 2, 0.733, 1.711, 2, 0.767, 2.845, 2, 0.8, 3.597, 2, 0.833, 3.874, 2, 0.867, 3.73, 2, 0.9, 3.338, 2, 0.933, 2.748, 2, 0.967, 2.044, 2, 1, 1.279, 2, 1.033, 0.514, 2, 1.067, -0.19, 2, 1.1, -0.78, 2, 1.133, -1.172, 2, 1.167, -1.316, 2, 1.2, -1.27, 2, 1.233, -1.145, 2, 1.267, -0.957, 2, 1.3, -0.733, 2, 1.333, -0.489, 2, 1.367, -0.245, 2, 1.4, -0.02, 2, 1.433, 0.168, 2, 1.467, 0.293, 2, 1.5, 0.339, 2, 1.533, 0.327, 2, 1.567, 0.295, 2, 1.6, 0.246, 2, 1.633, 0.189, 2, 1.667, 0.126, 2, 1.7, 0.063, 2, 1.733, 0.006, 2, 1.767, -0.043, 2, 1.8, -0.075, 2, 1.833, -0.087, 2, 1.867, -0.084, 2, 1.9, -0.077, 2, 1.933, -0.067, 2, 1.967, -0.054, 2, 2, -0.04, 2, 2.033, -0.025, 2, 2.067, -0.01, 2, 2.1, 0.002, 2, 2.133, 0.013, 2, 2.167, 0.02, 2, 2.2, 0.022, 2, 2.233, -0.125, 2, 2.267, -0.484, 2, 2.3, -0.96, 2, 2.333, -1.437, 2, 2.367, -1.795, 2, 2.4, -1.943, 2, 2.433, -1.534, 2, 2.467, -0.48, 2, 2.5, 0.983, 2, 2.533, 2.535, 2, 2.567, 3.998, 2, 2.6, 5.052, 2, 2.633, 5.461, 2, 2.667, 4.901, 2, 2.7, 3.426, 2, 2.733, 1.34, 2, 2.767, -1.051, 2, 2.8, -3.442, 2, 2.833, -5.528, 2, 2.867, -7.003, 2, 2.9, -7.563, 2, 2.933, -7.161, 2, 2.967, -6.068, 2, 3, -4.422, 2, 3.033, -2.46, 2, 3.067, -0.326, 2, 3.1, 1.808, 2, 3.133, 3.77, 2, 3.167, 5.416, 2, 3.2, 6.508, 2, 3.233, 6.91, 2, 3.267, 6.702, 2, 3.3, 6.101, 2, 3.333, 5.207, 2, 3.367, 4.059, 2, 3.4, 2.739, 2, 3.433, 1.332, 2, 3.467, -0.145, 2, 3.5, -1.552, 2, 3.533, -2.873, 2, 3.567, -4.02, 2, 3.6, -4.914, 2, 3.633, -5.515, 2, 3.667, -5.724, 2, 3.7, -5.556, 2, 3.733, -5.072, 2, 3.767, -4.351, 2, 3.8, -3.428, 2, 3.833, -2.364, 2, 3.867, -1.231, 2, 3.9, -0.041, 2, 3.933, 1.091, 2, 3.967, 2.155, 2, 4, 3.079, 2, 4.033, 3.799, 2, 4.067, 4.283, 2, 4.1, 4.451, 2, 4.133, 4.285, 2, 4.167, 3.842, 2, 4.2, 3.167, 2, 4.233, 2.351, 2, 4.267, 1.421, 2, 4.3, 0.481, 2, 4.333, -0.449, 2, 4.367, -1.265, 2, 4.4, -1.94, 2, 4.433, -2.383, 2, 4.467, -2.549, 2, 4.5, -2.453, 2, 4.533, -2.194, 2, 4.567, -1.803, 2, 4.6, -1.337, 2, 4.633, -0.83, 2, 4.667, -0.324, 2, 4.7, 0.142, 2, 4.733, 0.533, 2, 4.767, 0.793, 2, 4.8, 0.888, 2, 4.833, 0.862, 2, 4.867, 0.791, 2, 4.9, 0.683, 2, 4.933, 0.553, 2, 4.967, 0.405, 2, 5, 0.255, 2, 5.033, 0.107, 2, 5.067, -0.023, 2, 5.1, -0.131, 2, 5.133, -0.201, 2, 5.167, -0.228, 2, 5.2, -0.22, 2, 5.233, -0.198, 2, 5.267, -0.166, 2, 5.3, -0.127, 2, 5.333, -0.084, 2, 5.367, -0.042, 2, 5.4, -0.003, 2, 5.433, 0.03, 2, 5.467, 0.051, 2, 5.5, 0.059, 2, 5.533, 0.057, 2, 5.567, 0.052, 2, 5.6, 0.043, 2, 5.633, 0.033, 2, 5.667, 0.022, 2, 5.7, 0.011, 2, 5.733, 0.001, 2, 5.767, -0.008, 2, 5.8, -0.013, 2, 5.833, -0.015, 2, 5.867, -0.015, 2, 5.9, -0.013, 2, 5.933, -0.011, 2, 5.967, -0.009, 2, 6, -0.006, 2, 6.033, -0.003, 2, 6.067, 0, 2, 6.1, 0.002, 2, 6.133, 0.003, 2, 6.167, 0.004, 2, 6.2, 0.004, 2, 6.233, 0.003, 2, 6.267, 0.003, 2, 6.3, 0.002, 2, 6.333, 0.002, 2, 6.367, 0.001, 2, 6.4, 0, 2, 6.433, 0, 2, 6.467, -0.001, 2, 6.5, -0.001, 2, 6.567, -0.001, 2, 6.6, -0.001, 2, 6.633, -0.001, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, 0, 2, 6.767, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 6.9, 0, 2, 6.967, 0, 2, 7.033, 0, 2, 7.133, 0, 2, 7.2, 0, 2, 7.233, 0, 2, 7.3, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh2", "Segments": [0, 0, 2, 0.033, -0.228, 2, 0.067, -0.73, 2, 0.1, -1.232, 2, 0.133, -1.46, 2, 0.167, -1.152, 2, 0.2, -0.359, 2, 0.233, 0.743, 2, 0.267, 1.912, 2, 0.3, 3.014, 2, 0.333, 3.807, 2, 0.367, 4.115, 2, 0.4, 3.852, 2, 0.433, 3.136, 2, 0.467, 2.059, 2, 0.5, 0.774, 2, 0.533, -0.624, 2, 0.567, -2.021, 2, 0.6, -3.306, 2, 0.633, -4.384, 2, 0.667, -5.099, 2, 0.7, -5.362, 2, 0.733, -5.091, 2, 0.767, -4.353, 2, 0.8, -3.242, 2, 0.833, -1.917, 2, 0.867, -0.476, 2, 0.9, 0.965, 2, 0.933, 2.29, 2, 0.967, 3.401, 2, 1, 4.139, 2, 1.033, 4.41, 2, 1.067, 4.18, 2, 1.1, 3.563, 2, 1.133, 2.67, 2, 1.167, 1.612, 2, 1.2, 0.52, 2, 1.233, -0.538, 2, 1.267, -1.431, 2, 1.3, -2.048, 2, 1.333, -2.278, 2, 1.367, -2.19, 2, 1.4, -1.95, 2, 1.433, -1.589, 2, 1.467, -1.158, 2, 1.5, -0.689, 2, 1.533, -0.221, 2, 1.567, 0.21, 2, 1.6, 0.571, 2, 1.633, 0.811, 2, 1.667, 0.9, 2, 1.7, 0.866, 2, 1.733, 0.774, 2, 1.767, 0.636, 2, 1.8, 0.472, 2, 1.833, 0.293, 2, 1.867, 0.114, 2, 1.9, -0.051, 2, 1.933, -0.189, 2, 1.967, -0.281, 2, 2, -0.314, 2, 2.033, -0.275, 2, 2.067, -0.171, 2, 2.1, -0.011, 2, 2.133, 0.182, 2, 2.167, 0.401, 2, 2.2, 0.623, 2, 2.233, 0.843, 2, 2.267, 1.036, 2, 2.3, 1.195, 2, 2.333, 1.3, 2, 2.367, 1.339, 2, 2.4, 0.929, 2, 2.433, -0.066, 2, 2.467, -1.389, 2, 2.5, -2.712, 2, 2.533, -3.707, 2, 2.567, -4.117, 2, 2.6, -3.631, 2, 2.633, -2.35, 2, 2.667, -0.539, 2, 2.7, 1.537, 2, 2.733, 3.613, 2, 2.767, 5.423, 2, 2.8, 6.704, 2, 2.833, 7.19, 2, 2.867, 6.508, 2, 2.9, 4.708, 2, 2.933, 2.164, 2, 2.967, -0.753, 2, 3, -3.669, 2, 3.033, -6.213, 2, 3.067, -8.013, 2, 3.1, -8.695, 2, 3.133, -8.326, 2, 3.167, -7.34, 2, 3.2, -5.838, 2, 3.233, -4.022, 2, 3.267, -1.954, 2, 3.3, 0.14, 2, 3.333, 2.208, 2, 3.367, 4.024, 2, 3.4, 5.526, 2, 3.433, 6.512, 2, 3.467, 6.881, 2, 3.5, 6.606, 2, 3.533, 5.871, 2, 3.567, 4.751, 2, 3.6, 3.397, 2, 3.633, 1.856, 2, 3.667, 0.295, 2, 3.7, -1.246, 2, 3.733, -2.6, 2, 3.767, -3.72, 2, 3.8, -4.455, 2, 3.833, -4.73, 2, 3.867, -4.609, 2, 3.9, -4.269, 2, 3.933, -3.739, 2, 3.967, -3.079, 2, 4, -2.29, 2, 4.033, -1.439, 2, 4.067, -0.551, 2, 4.1, 0.336, 2, 4.133, 1.188, 2, 4.167, 1.977, 2, 4.2, 2.636, 2, 4.233, 3.167, 2, 4.267, 3.506, 2, 4.3, 3.628, 2, 4.333, 3.474, 2, 4.367, 3.062, 2, 4.4, 2.435, 2, 4.433, 1.677, 2, 4.467, 0.814, 2, 4.5, -0.059, 2, 4.533, -0.923, 2, 4.567, -1.681, 2, 4.6, -2.307, 2, 4.633, -2.719, 2, 4.667, -2.873, 2, 4.7, -2.721, 2, 4.733, -2.313, 2, 4.767, -1.723, 2, 4.8, -1.024, 2, 4.833, -0.302, 2, 4.867, 0.397, 2, 4.9, 0.987, 2, 4.933, 1.394, 2, 4.967, 1.546, 2, 5, 1.486, 2, 5.033, 1.323, 2, 5.067, 1.077, 2, 5.1, 0.784, 2, 5.133, 0.466, 2, 5.167, 0.147, 2, 5.2, -0.146, 2, 5.233, -0.392, 2, 5.267, -0.555, 2, 5.3, -0.615, 2, 5.333, -0.592, 2, 5.367, -0.529, 2, 5.4, -0.435, 2, 5.433, -0.322, 2, 5.467, -0.2, 2, 5.5, -0.078, 2, 5.533, 0.035, 2, 5.567, 0.129, 2, 5.6, 0.192, 2, 5.633, 0.215, 2, 5.667, 0.207, 2, 5.7, 0.185, 2, 5.733, 0.153, 2, 5.767, 0.114, 2, 5.8, 0.072, 2, 5.833, 0.031, 2, 5.867, -0.008, 2, 5.9, -0.04, 2, 5.933, -0.062, 2, 5.967, -0.07, 2, 6, -0.067, 2, 6.033, -0.06, 2, 6.067, -0.05, 2, 6.1, -0.038, 2, 6.133, -0.024, 2, 6.167, -0.011, 2, 6.2, 0.002, 2, 6.233, 0.012, 2, 6.267, 0.019, 2, 6.3, 0.022, 2, 6.367, 0.021, 2, 6.4, 0.019, 2, 6.433, 0.015, 2, 6.467, 0.012, 2, 6.5, 0.008, 2, 6.533, 0.003, 2, 6.567, 0, 2, 6.6, -0.004, 2, 6.633, -0.006, 2, 6.667, -0.007, 2, 6.7, -0.006, 2, 6.733, -0.006, 2, 6.767, -0.005, 2, 6.8, -0.004, 2, 6.833, -0.002, 2, 6.867, -0.001, 2, 6.9, 0, 2, 6.933, 0.001, 2, 6.967, 0.002, 2, 7, 0.002, 2, 7.033, 0.002, 2, 7.067, 0.002, 2, 7.1, 0.001, 2, 7.133, 0.001, 2, 7.167, 0.001, 2, 7.2, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.3, -0.001, 2, 7.333, -0.001, 2, 7.367, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.6, 0, 2, 7.633, 0, 2, 7.667, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.8, 0, 2, 7.833, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh2", "Segments": [0, -0.002, 2, 0.033, -0.06, 2, 0.067, -0.21, 2, 0.1, -0.418, 2, 0.133, -0.639, 2, 0.167, -0.847, 2, 0.2, -0.997, 2, 0.233, -1.055, 2, 0.267, -0.837, 2, 0.3, -0.265, 2, 0.333, 0.544, 2, 0.367, 1.472, 2, 0.4, 2.4, 2, 0.433, 3.209, 2, 0.467, 3.782, 2, 0.5, 3.999, 2, 0.533, 3.645, 2, 0.567, 2.697, 2, 0.6, 1.326, 2, 0.633, -0.3, 2, 0.667, -1.979, 2, 0.7, -3.604, 2, 0.733, -4.976, 2, 0.767, -5.924, 2, 0.8, -6.277, 2, 0.833, -5.942, 2, 0.867, -5.029, 2, 0.9, -3.654, 2, 0.933, -2.015, 2, 0.967, -0.232, 2, 1, 1.55, 2, 1.033, 3.19, 2, 1.067, 4.565, 2, 1.1, 5.477, 2, 1.133, 5.813, 2, 1.167, 5.55, 2, 1.2, 4.837, 2, 1.233, 3.762, 2, 1.267, 2.48, 2, 1.3, 1.087, 2, 1.333, -0.307, 2, 1.367, -1.589, 2, 1.4, -2.664, 2, 1.433, -3.377, 2, 1.467, -3.64, 2, 1.5, -3.489, 2, 1.533, -3.079, 2, 1.567, -2.461, 2, 1.6, -1.724, 2, 1.633, -0.923, 2, 1.667, -0.122, 2, 1.7, 0.615, 2, 1.733, 1.233, 2, 1.767, 1.643, 2, 1.8, 1.794, 2, 1.833, 1.723, 2, 1.867, 1.53, 2, 1.9, 1.239, 2, 1.933, 0.892, 2, 1.967, 0.515, 2, 2, 0.138, 2, 2.033, -0.208, 2, 2.067, -0.499, 2, 2.1, -0.692, 2, 2.133, -0.763, 2, 2.167, -0.713, 2, 2.2, -0.577, 2, 2.233, -0.372, 2, 2.267, -0.127, 2, 2.3, 0.139, 2, 2.333, 0.405, 2, 2.367, 0.65, 2, 2.4, 0.855, 2, 2.433, 0.992, 2, 2.467, 1.042, 2, 2.5, 0.801, 2, 2.533, 0.18, 2, 2.567, -0.681, 2, 2.6, -1.595, 2, 2.633, -2.457, 2, 2.667, -3.078, 2, 2.7, -3.318, 2, 2.733, -2.75, 2, 2.767, -1.282, 2, 2.8, 0.754, 2, 2.833, 2.914, 2, 2.867, 4.95, 2, 2.9, 6.418, 2, 2.933, 6.986, 2, 2.967, 6.416, 2, 3, 4.887, 2, 3.033, 2.675, 2, 3.067, 0.054, 2, 3.1, -2.654, 2, 3.133, -5.275, 2, 3.167, -7.487, 2, 3.2, -9.016, 2, 3.233, -9.586, 2, 3.267, -9.059, 2, 3.3, -7.63, 2, 3.333, -5.475, 2, 3.367, -2.906, 2, 3.4, -0.113, 2, 3.433, 2.681, 2, 3.467, 5.249, 2, 3.5, 7.404, 2, 3.533, 8.834, 2, 3.567, 9.361, 2, 3.6, 8.968, 2, 3.633, 7.921, 2, 3.667, 6.326, 2, 3.7, 4.396, 2, 3.733, 2.199, 2, 3.767, -0.025, 2, 3.8, -2.222, 2, 3.833, -4.152, 2, 3.867, -5.747, 2, 3.9, -6.794, 2, 3.933, -7.186, 2, 3.967, -6.981, 2, 4, -6.391, 2, 4.033, -5.513, 2, 4.067, -4.386, 2, 4.1, -3.089, 2, 4.133, -1.708, 2, 4.167, -0.257, 2, 4.2, 1.124, 2, 4.233, 2.421, 2, 4.267, 3.548, 2, 4.3, 4.426, 2, 4.333, 5.016, 2, 4.367, 5.221, 2, 4.4, 5.035, 2, 4.433, 4.534, 2, 4.467, 3.76, 2, 4.5, 2.788, 2, 4.533, 1.711, 2, 4.567, 0.543, 2, 4.6, -0.624, 2, 4.633, -1.701, 2, 4.667, -2.673, 2, 4.7, -3.447, 2, 4.733, -3.948, 2, 4.767, -4.135, 2, 4.8, -3.949, 2, 4.833, -3.443, 2, 4.867, -2.682, 2, 4.9, -1.774, 2, 4.933, -0.787, 2, 4.967, 0.201, 2, 5, 1.108, 2, 5.033, 1.87, 2, 5.067, 2.375, 2, 5.1, 2.561, 2, 5.133, 2.455, 2, 5.167, 2.167, 2, 5.2, 1.734, 2, 5.233, 1.216, 2, 5.267, 0.654, 2, 5.3, 0.091, 2, 5.333, -0.426, 2, 5.367, -0.86, 2, 5.4, -1.148, 2, 5.433, -1.254, 2, 5.467, -1.204, 2, 5.5, -1.07, 2, 5.533, -0.867, 2, 5.567, -0.625, 2, 5.6, -0.362, 2, 5.633, -0.099, 2, 5.667, 0.143, 2, 5.7, 0.346, 2, 5.733, 0.48, 2, 5.767, 0.53, 2, 5.8, 0.509, 2, 5.833, 0.454, 2, 5.867, 0.371, 2, 5.9, 0.271, 2, 5.933, 0.163, 2, 5.967, 0.055, 2, 6, -0.044, 2, 6.033, -0.127, 2, 6.067, -0.183, 2, 6.1, -0.203, 2, 6.133, -0.196, 2, 6.167, -0.179, 2, 6.2, -0.152, 2, 6.233, -0.12, 2, 6.267, -0.083, 2, 6.3, -0.046, 2, 6.333, -0.01, 2, 6.367, 0.023, 2, 6.4, 0.049, 2, 6.433, 0.067, 2, 6.467, 0.073, 2, 6.5, 0.071, 2, 6.533, 0.063, 2, 6.567, 0.052, 2, 6.6, 0.039, 2, 6.633, 0.024, 2, 6.667, 0.009, 2, 6.7, -0.004, 2, 6.733, -0.015, 2, 6.767, -0.023, 2, 6.8, -0.025, 2, 6.833, -0.024, 2, 6.867, -0.022, 2, 6.9, -0.018, 2, 6.933, -0.013, 2, 6.967, -0.008, 2, 7, -0.003, 2, 7.033, 0.001, 2, 7.067, 0.005, 2, 7.1, 0.008, 2, 7.133, 0.009, 2, 7.167, 0.008, 2, 7.2, 0.007, 2, 7.233, 0.006, 2, 7.267, 0.005, 2, 7.3, 0.003, 2, 7.333, 0.001, 2, 7.367, 0, 2, 7.4, -0.002, 2, 7.433, -0.002, 2, 7.467, -0.003, 2, 7.5, -0.003, 2, 7.533, -0.002, 2, 7.567, -0.002, 2, 7.6, -0.001, 2, 7.633, -0.001, 2, 7.667, 0, 2, 7.7, 0, 2, 7.733, 0.001, 2, 7.767, 0.001, 2, 7.8, 0.001, 2, 7.833, 0.001, 2, 7.867, 0.001, 2, 7.9, 0.001, 2, 7.933, 0, 2, 7.967, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh2", "Segments": [0, -0.004, 2, 0.033, -0.034, 2, 0.067, -0.113, 2, 0.1, -0.228, 2, 0.133, -0.364, 2, 0.167, -0.505, 2, 0.2, -0.641, 2, 0.233, -0.756, 2, 0.267, -0.835, 2, 0.3, -0.865, 2, 0.333, -0.694, 2, 0.367, -0.238, 2, 0.4, 0.422, 2, 0.433, 1.205, 2, 0.467, 2.013, 2, 0.5, 2.796, 2, 0.533, 3.456, 2, 0.567, 3.913, 2, 0.6, 4.083, 2, 0.633, 3.768, 2, 0.667, 2.911, 2, 0.7, 1.62, 2, 0.733, 0.08, 2, 0.767, -1.593, 2, 0.8, -3.267, 2, 0.833, -4.806, 2, 0.867, -6.098, 2, 0.9, -6.954, 2, 0.933, -7.27, 2, 0.967, -6.859, 2, 1, -5.742, 2, 1.033, -4.06, 2, 1.067, -2.054, 2, 1.1, 0.128, 2, 1.133, 2.309, 2, 1.167, 4.315, 2, 1.2, 5.998, 2, 1.233, 7.114, 2, 1.267, 7.525, 2, 1.3, 7.165, 2, 1.333, 6.188, 2, 1.367, 4.715, 2, 1.4, 2.959, 2, 1.433, 1.05, 2, 1.467, -0.859, 2, 1.5, -2.615, 2, 1.533, -4.088, 2, 1.567, -5.065, 2, 1.6, -5.425, 2, 1.633, -5.187, 2, 1.667, -4.542, 2, 1.7, -3.57, 2, 1.733, -2.412, 2, 1.767, -1.151, 2, 1.8, 0.109, 2, 1.833, 1.268, 2, 1.867, 2.24, 2, 1.9, 2.885, 2, 1.933, 3.122, 2, 1.967, 2.993, 2, 2, 2.641, 2, 2.033, 2.11, 2, 2.067, 1.477, 2, 2.1, 0.79, 2, 2.133, 0.102, 2, 2.167, -0.531, 2, 2.2, -1.061, 2, 2.233, -1.413, 2, 2.267, -1.543, 2, 2.3, -1.434, 2, 2.333, -1.146, 2, 2.367, -0.738, 2, 2.4, -0.271, 2, 2.433, 0.196, 2, 2.467, 0.603, 2, 2.5, 0.891, 2, 2.533, 1.001, 2, 2.567, 0.842, 2, 2.6, 0.423, 2, 2.633, -0.169, 2, 2.667, -0.848, 2, 2.7, -1.527, 2, 2.733, -2.12, 2, 2.767, -2.539, 2, 2.8, -2.698, 2, 2.833, -2.284, 2, 2.867, -1.194, 2, 2.9, 0.348, 2, 2.933, 2.115, 2, 2.967, 3.882, 2, 3, 5.423, 2, 3.033, 6.514, 2, 3.067, 6.927, 2, 3.1, 6.172, 2, 3.133, 4.182, 2, 3.167, 1.368, 2, 3.2, -1.858, 2, 3.233, -5.085, 2, 3.267, -7.899, 2, 3.3, -9.889, 2, 3.333, -10.644, 2, 3.367, -10.024, 2, 3.4, -8.339, 2, 3.433, -5.8, 2, 3.467, -2.773, 2, 3.5, 0.519, 2, 3.533, 3.811, 2, 3.567, 6.838, 2, 3.6, 9.377, 2, 3.633, 11.062, 2, 3.667, 11.682, 2, 3.7, 11.168, 2, 3.733, 9.799, 2, 3.767, 7.711, 2, 3.8, 5.187, 2, 3.833, 2.313, 2, 3.867, -0.597, 2, 3.9, -3.471, 2, 3.933, -5.995, 2, 3.967, -8.083, 2, 4, -9.452, 2, 4.033, -9.966, 2, 4.067, -9.677, 2, 4.1, -8.846, 2, 4.133, -7.609, 2, 4.167, -6.022, 2, 4.2, -4.195, 2, 4.233, -2.25, 2, 4.267, -0.206, 2, 4.3, 1.739, 2, 4.333, 3.566, 2, 4.367, 5.153, 2, 4.4, 6.39, 2, 4.433, 7.221, 2, 4.467, 7.51, 2, 4.5, 7.243, 2, 4.533, 6.528, 2, 4.567, 5.422, 2, 4.6, 4.034, 2, 4.633, 2.497, 2, 4.667, 0.829, 2, 4.7, -0.839, 2, 4.733, -2.376, 2, 4.767, -3.764, 2, 4.8, -4.87, 2, 4.833, -5.585, 2, 4.867, -5.852, 2, 4.9, -5.579, 2, 4.933, -4.839, 2, 4.967, -3.722, 2, 5, -2.392, 2, 5.033, -0.944, 2, 5.067, 0.503, 2, 5.1, 1.833, 2, 5.133, 2.95, 2, 5.167, 3.69, 2, 5.2, 3.963, 2, 5.233, 3.816, 2, 5.267, 3.423, 2, 5.3, 2.824, 2, 5.333, 2.099, 2, 5.367, 1.275, 2, 5.4, 0.44, 2, 5.433, -0.385, 2, 5.467, -1.11, 2, 5.5, -1.709, 2, 5.533, -2.102, 2, 5.567, -2.249, 2, 5.6, -2.156, 2, 5.633, -1.903, 2, 5.667, -1.522, 2, 5.7, -1.068, 2, 5.733, -0.575, 2, 5.767, -0.081, 2, 5.8, 0.373, 2, 5.833, 0.754, 2, 5.867, 1.007, 2, 5.9, 1.1, 2, 5.933, 1.056, 2, 5.967, 0.936, 2, 6, 0.757, 2, 6.033, 0.542, 2, 6.067, 0.309, 2, 6.1, 0.076, 2, 6.133, -0.138, 2, 6.167, -0.318, 2, 6.2, -0.437, 2, 6.233, -0.481, 2, 6.267, -0.465, 2, 6.3, -0.423, 2, 6.333, -0.357, 2, 6.367, -0.279, 2, 6.4, -0.189, 2, 6.433, -0.098, 2, 6.467, -0.008, 2, 6.5, 0.071, 2, 6.533, 0.136, 2, 6.567, 0.178, 2, 6.6, 0.195, 2, 6.633, 0.187, 2, 6.667, 0.167, 2, 6.7, 0.136, 2, 6.733, 0.099, 2, 6.767, 0.06, 2, 6.8, 0.02, 2, 6.833, -0.017, 2, 6.867, -0.047, 2, 6.9, -0.068, 2, 6.933, -0.075, 2, 6.967, -0.072, 2, 7, -0.065, 2, 7.033, -0.053, 2, 7.067, -0.039, 2, 7.1, -0.024, 2, 7.133, -0.009, 2, 7.167, 0.005, 2, 7.2, 0.017, 2, 7.233, 0.025, 2, 7.267, 0.028, 2, 7.3, 0.027, 2, 7.333, 0.024, 2, 7.367, 0.02, 2, 7.4, 0.014, 2, 7.433, 0.009, 2, 7.467, 0.003, 2, 7.5, -0.002, 2, 7.533, -0.006, 2, 7.567, -0.009, 2, 7.6, -0.01, 2, 7.633, -0.009, 2, 7.667, -0.008, 2, 7.7, -0.007, 2, 7.733, -0.005, 2, 7.767, -0.003, 2, 7.8, -0.001, 2, 7.833, 0, 2, 7.867, 0.002, 2, 7.9, 0.003, 2, 7.933, 0.003, 2, 8, 0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh3", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh3", "Segments": [0, 0, 2, 0.033, 0.229, 2, 0.067, 0.775, 2, 0.1, 1.441, 2, 0.133, 1.987, 2, 0.167, 2.215, 2, 0.2, 1.96, 2, 0.233, 1.275, 2, 0.267, 0.284, 2, 0.3, -0.891, 2, 0.333, -2.104, 2, 0.367, -3.279, 2, 0.4, -4.27, 2, 0.433, -4.955, 2, 0.467, -5.211, 2, 0.5, -4.974, 2, 0.533, -4.332, 2, 0.567, -3.365, 2, 0.6, -2.212, 2, 0.633, -0.957, 2, 0.667, 0.297, 2, 0.7, 1.45, 2, 0.733, 2.418, 2, 0.767, 3.06, 2, 0.8, 3.296, 2, 0.833, 3.142, 2, 0.867, 2.728, 2, 0.9, 2.13, 2, 0.933, 1.421, 2, 0.967, 0.688, 2, 1, -0.021, 2, 1.033, -0.62, 2, 1.067, -1.033, 2, 1.1, -1.188, 2, 1.133, -1.146, 2, 1.167, -1.032, 2, 1.2, -0.861, 2, 1.233, -0.657, 2, 1.267, -0.435, 2, 1.3, -0.213, 2, 1.333, -0.009, 2, 1.367, 0.162, 2, 1.4, 0.275, 2, 1.433, 0.317, 2, 1.467, 0.303, 2, 1.5, 0.266, 2, 1.533, 0.213, 2, 1.567, 0.149, 2, 1.6, 0.083, 2, 1.633, 0.02, 2, 1.667, -0.034, 2, 1.7, -0.071, 2, 1.733, -0.085, 2, 1.767, -0.081, 2, 1.8, -0.071, 2, 1.833, -0.057, 2, 1.867, -0.04, 2, 1.9, -0.022, 2, 1.933, -0.005, 2, 1.967, 0.009, 2, 2, 0.019, 2, 2.033, 0.023, 2, 2.067, -0.02, 2, 2.1, -0.134, 2, 2.133, -0.308, 2, 2.167, -0.519, 2, 2.2, -0.759, 2, 2.233, -1.001, 2, 2.267, -1.241, 2, 2.3, -1.451, 2, 2.333, -1.626, 2, 2.367, -1.74, 2, 2.4, -1.783, 2, 2.433, -1.415, 2, 2.467, -0.468, 2, 2.5, 0.847, 2, 2.533, 2.242, 2, 2.567, 3.557, 2, 2.6, 4.504, 2, 2.633, 4.872, 2, 2.667, 4.369, 2, 2.7, 3.045, 2, 2.733, 1.172, 2, 2.767, -0.975, 2, 2.8, -3.122, 2, 2.833, -4.995, 2, 2.867, -6.319, 2, 2.9, -6.822, 2, 2.933, -6.38, 2, 2.967, -5.198, 2, 3, -3.486, 2, 3.033, -1.458, 2, 3.067, 0.637, 2, 3.1, 2.665, 2, 3.133, 4.377, 2, 3.167, 5.56, 2, 3.2, 6.001, 2, 3.233, 5.828, 2, 3.267, 5.331, 2, 3.3, 4.591, 2, 3.333, 3.642, 2, 3.367, 2.549, 2, 3.4, 1.386, 2, 3.433, 0.163, 2, 3.467, -1.001, 2, 3.5, -2.093, 2, 3.533, -3.043, 2, 3.567, -3.783, 2, 3.6, -4.28, 2, 3.633, -4.452, 2, 3.667, -4.338, 2, 3.7, -4.018, 2, 3.733, -3.519, 2, 3.767, -2.898, 2, 3.8, -2.156, 2, 3.833, -1.355, 2, 3.867, -0.519, 2, 3.9, 0.316, 2, 3.933, 1.117, 2, 3.967, 1.859, 2, 4, 2.48, 2, 4.033, 2.979, 2, 4.067, 3.299, 2, 4.1, 3.413, 2, 4.133, 3.261, 2, 4.167, 2.849, 2, 4.2, 2.226, 2, 4.233, 1.485, 2, 4.267, 0.678, 2, 4.3, -0.128, 2, 4.333, -0.87, 2, 4.367, -1.492, 2, 4.4, -1.905, 2, 4.433, -2.057, 2, 4.467, -1.979, 2, 4.5, -1.766, 2, 4.533, -1.444, 2, 4.567, -1.061, 2, 4.6, -0.645, 2, 4.633, -0.228, 2, 4.667, 0.155, 2, 4.7, 0.476, 2, 4.733, 0.689, 2, 4.767, 0.768, 2, 4.8, 0.734, 2, 4.833, 0.645, 2, 4.867, 0.515, 2, 4.9, 0.361, 2, 4.933, 0.202, 2, 4.967, 0.049, 2, 5, -0.081, 2, 5.033, -0.171, 2, 5.067, -0.204, 2, 5.1, -0.195, 2, 5.133, -0.171, 2, 5.167, -0.137, 2, 5.2, -0.096, 2, 5.233, -0.054, 2, 5.267, -0.013, 2, 5.3, 0.021, 2, 5.333, 0.045, 2, 5.367, 0.054, 2, 5.4, 0.052, 2, 5.433, 0.046, 2, 5.467, 0.036, 2, 5.5, 0.026, 2, 5.533, 0.014, 2, 5.567, 0.004, 2, 5.6, -0.006, 2, 5.633, -0.012, 2, 5.667, -0.014, 2, 5.7, -0.014, 2, 5.733, -0.012, 2, 5.767, -0.01, 2, 5.8, -0.007, 2, 5.833, -0.004, 2, 5.867, -0.001, 2, 5.9, 0.002, 2, 5.933, 0.003, 2, 5.967, 0.004, 2, 6.033, 0.004, 2, 6.067, 0.003, 2, 6.1, 0.002, 2, 6.133, 0.001, 2, 6.167, 0.001, 2, 6.2, 0, 2, 6.233, -0.001, 2, 6.267, -0.001, 2, 6.333, -0.001, 2, 6.367, -0.001, 2, 6.4, -0.001, 2, 6.433, 0, 2, 6.467, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.567, 0, 2, 6.633, 0, 2, 6.7, 0, 2, 6.767, 0, 2, 6.833, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 7, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh3", "Segments": [0, 0, 2, 0.033, -0.19, 2, 0.067, -0.609, 2, 0.1, -1.028, 2, 0.133, -1.218, 2, 0.167, -0.966, 2, 0.2, -0.315, 2, 0.233, 0.589, 2, 0.267, 1.547, 2, 0.3, 2.451, 2, 0.333, 3.102, 2, 0.367, 3.355, 2, 0.4, 3.106, 2, 0.433, 2.438, 2, 0.467, 1.472, 2, 0.5, 0.328, 2, 0.533, -0.855, 2, 0.567, -1.999, 2, 0.6, -2.965, 2, 0.633, -3.632, 2, 0.667, -3.881, 2, 0.7, -3.631, 2, 0.733, -2.96, 2, 0.767, -1.989, 2, 0.8, -0.838, 2, 0.833, 0.35, 2, 0.867, 1.501, 2, 0.9, 2.472, 2, 0.933, 3.143, 2, 0.967, 3.393, 2, 1, 3.209, 2, 1.033, 2.716, 2, 1.067, 2.002, 2, 1.1, 1.156, 2, 1.133, 0.282, 2, 1.167, -0.564, 2, 1.2, -1.278, 2, 1.233, -1.772, 2, 1.267, -1.956, 2, 1.3, -1.86, 2, 1.333, -1.605, 2, 1.367, -1.235, 2, 1.4, -0.796, 2, 1.433, -0.343, 2, 1.467, 0.096, 2, 1.5, 0.466, 2, 1.533, 0.722, 2, 1.567, 0.817, 2, 1.6, 0.779, 2, 1.633, 0.676, 2, 1.667, 0.527, 2, 1.7, 0.35, 2, 1.733, 0.168, 2, 1.767, -0.008, 2, 1.8, -0.157, 2, 1.833, -0.26, 2, 1.867, -0.298, 2, 1.9, -0.285, 2, 1.933, -0.248, 2, 1.967, -0.194, 2, 2, -0.131, 2, 2.033, -0.066, 2, 2.067, -0.003, 2, 2.1, 0.051, 2, 2.133, 0.088, 2, 2.167, 0.101, 2, 2.2, 0.088, 2, 2.233, 0.064, 2, 2.267, 0.051, 2, 2.3, 0.323, 2, 2.333, 0.814, 2, 2.367, 1.086, 2, 2.4, 0.734, 2, 2.433, -0.119, 2, 2.467, -1.254, 2, 2.5, -2.389, 2, 2.533, -3.243, 2, 2.567, -3.594, 2, 2.6, -3.049, 2, 2.633, -1.641, 2, 2.667, 0.313, 2, 2.7, 2.384, 2, 2.733, 4.338, 2, 2.767, 5.745, 2, 2.8, 6.291, 2, 2.833, 5.704, 2, 2.867, 4.158, 2, 2.9, 1.972, 2, 2.933, -0.533, 2, 2.967, -3.039, 2, 3, -5.225, 2, 3.033, -6.771, 2, 3.067, -7.357, 2, 3.1, -6.919, 2, 3.133, -5.744, 2, 3.167, -4.044, 2, 3.2, -2.029, 2, 3.233, 0.052, 2, 3.267, 2.067, 2, 3.3, 3.767, 2, 3.333, 4.942, 2, 3.367, 5.381, 2, 3.4, 5.183, 2, 3.433, 4.657, 2, 3.467, 3.854, 2, 3.5, 2.884, 2, 3.533, 1.779, 2, 3.567, 0.66, 2, 3.6, -0.445, 2, 3.633, -1.416, 2, 3.667, -2.218, 2, 3.7, -2.745, 2, 3.733, -2.942, 2, 3.767, -2.886, 2, 3.8, -2.729, 2, 3.833, -2.483, 2, 3.867, -2.166, 2, 3.9, -1.789, 2, 3.933, -1.37, 2, 3.967, -0.921, 2, 4, -0.457, 2, 4.033, 0.006, 2, 4.067, 0.455, 2, 4.1, 0.875, 2, 4.133, 1.251, 2, 4.167, 1.569, 2, 4.2, 1.814, 2, 4.233, 1.972, 2, 4.267, 2.028, 2, 4.3, 1.917, 2, 4.333, 1.616, 2, 4.367, 1.162, 2, 4.4, 0.621, 2, 4.433, 0.032, 2, 4.467, -0.556, 2, 4.5, -1.097, 2, 4.533, -1.551, 2, 4.567, -1.852, 2, 4.6, -1.963, 2, 4.633, -1.855, 2, 4.667, -1.564, 2, 4.7, -1.143, 2, 4.733, -0.644, 2, 4.767, -0.129, 2, 4.8, 0.37, 2, 4.833, 0.79, 2, 4.867, 1.081, 2, 4.9, 1.19, 2, 4.933, 1.131, 2, 4.967, 0.974, 2, 5, 0.747, 2, 5.033, 0.478, 2, 5.067, 0.2, 2, 5.1, -0.069, 2, 5.133, -0.296, 2, 5.167, -0.453, 2, 5.2, -0.511, 2, 5.233, -0.487, 2, 5.267, -0.423, 2, 5.3, -0.329, 2, 5.333, -0.219, 2, 5.367, -0.104, 2, 5.4, 0.006, 2, 5.433, 0.1, 2, 5.467, 0.164, 2, 5.5, 0.188, 2, 5.533, 0.18, 2, 5.567, 0.156, 2, 5.6, 0.123, 2, 5.633, 0.083, 2, 5.667, 0.041, 2, 5.7, 0.002, 2, 5.733, -0.032, 2, 5.767, -0.055, 2, 5.8, -0.064, 2, 5.833, -0.061, 2, 5.867, -0.053, 2, 5.9, -0.042, 2, 5.933, -0.029, 2, 5.967, -0.015, 2, 6, -0.001, 2, 6.033, 0.01, 2, 6.067, 0.018, 2, 6.1, 0.021, 2, 6.133, 0.02, 2, 6.167, 0.017, 2, 6.2, 0.014, 2, 6.233, 0.009, 2, 6.267, 0.005, 2, 6.3, 0.001, 2, 6.333, -0.003, 2, 6.367, -0.006, 2, 6.4, -0.006, 2, 6.433, -0.006, 2, 6.467, -0.005, 2, 6.5, -0.004, 2, 6.533, -0.003, 2, 6.567, -0.002, 2, 6.6, 0, 2, 6.633, 0.001, 2, 6.667, 0.002, 2, 6.7, 0.002, 2, 6.767, 0.002, 2, 6.8, 0.002, 2, 6.833, 0.001, 2, 6.867, 0.001, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, -0.001, 2, 7.067, -0.001, 2, 7.1, 0, 2, 7.133, 0, 2, 7.167, 0, 2, 7.2, 0, 2, 7.233, 0, 2, 7.3, 0, 2, 7.333, 0, 2, 7.367, 0, 2, 7.4, 0, 2, 7.467, 0, 2, 7.633, 0, 2, 7.667, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh3", "Segments": [0, 0.001, 2, 0.033, -0.069, 2, 0.067, -0.238, 2, 0.1, -0.462, 2, 0.133, -0.687, 2, 0.167, -0.856, 2, 0.2, -0.925, 2, 0.233, -0.742, 2, 0.267, -0.257, 2, 0.3, 0.427, 2, 0.333, 1.212, 2, 0.367, 1.996, 2, 0.4, 2.681, 2, 0.433, 3.165, 2, 0.467, 3.349, 2, 0.5, 3.061, 2, 0.533, 2.29, 2, 0.567, 1.175, 2, 0.6, -0.147, 2, 0.633, -1.512, 2, 0.667, -2.834, 2, 0.7, -3.949, 2, 0.733, -4.72, 2, 0.767, -5.007, 2, 0.8, -4.671, 2, 0.833, -3.769, 2, 0.867, -2.465, 2, 0.9, -0.919, 2, 0.933, 0.678, 2, 0.967, 2.223, 2, 1, 3.528, 2, 1.033, 4.429, 2, 1.067, 4.766, 2, 1.1, 4.493, 2, 1.133, 3.761, 2, 1.167, 2.702, 2, 1.2, 1.447, 2, 1.233, 0.151, 2, 1.267, -1.104, 2, 1.3, -2.163, 2, 1.333, -2.895, 2, 1.367, -3.168, 2, 1.4, -3.002, 2, 1.433, -2.558, 2, 1.467, -1.916, 2, 1.5, -1.156, 2, 1.533, -0.37, 2, 1.567, 0.391, 2, 1.6, 1.033, 2, 1.633, 1.476, 2, 1.667, 1.642, 2, 1.7, 1.56, 2, 1.733, 1.342, 2, 1.767, 1.025, 2, 1.8, 0.65, 2, 1.833, 0.263, 2, 1.867, -0.112, 2, 1.9, -0.429, 2, 1.933, -0.647, 2, 1.967, -0.729, 2, 2, -0.707, 2, 2.033, -0.647, 2, 2.067, -0.554, 2, 2.1, -0.437, 2, 2.133, -0.298, 2, 2.167, -0.147, 2, 2.2, 0.01, 2, 2.233, 0.166, 2, 2.267, 0.317, 2, 2.3, 0.456, 2, 2.333, 0.573, 2, 2.367, 0.666, 2, 2.4, 0.726, 2, 2.433, 0.748, 2, 2.467, 0.536, 2, 2.5, -0.01, 2, 2.533, -0.768, 2, 2.567, -1.572, 2, 2.6, -2.33, 2, 2.633, -2.877, 2, 2.667, -3.088, 2, 2.7, -2.564, 2, 2.733, -1.212, 2, 2.767, 0.665, 2, 2.8, 2.654, 2, 2.833, 4.531, 2, 2.867, 5.883, 2, 2.9, 6.407, 2, 2.933, 5.765, 2, 2.967, 4.07, 2, 3, 1.674, 2, 3.033, -1.072, 2, 3.067, -3.819, 2, 3.1, -6.215, 2, 3.133, -7.909, 2, 3.167, -8.552, 2, 3.2, -7.994, 2, 3.233, -6.501, 2, 3.267, -4.339, 2, 3.3, -1.777, 2, 3.333, 0.87, 2, 3.367, 3.432, 2, 3.4, 5.594, 2, 3.433, 7.088, 2, 3.467, 7.646, 2, 3.5, 7.347, 2, 3.533, 6.551, 2, 3.567, 5.337, 2, 3.6, 3.868, 2, 3.633, 2.197, 2, 3.667, 0.505, 2, 3.7, -1.166, 2, 3.733, -2.635, 2, 3.767, -3.848, 2, 3.8, -4.645, 2, 3.833, -4.944, 2, 3.867, -4.834, 2, 3.9, -4.529, 2, 3.933, -4.052, 2, 3.967, -3.459, 2, 4, -2.75, 2, 4.033, -1.985, 2, 4.067, -1.187, 2, 4.1, -0.389, 2, 4.133, 0.376, 2, 4.167, 1.085, 2, 4.2, 1.678, 2, 4.233, 2.155, 2, 4.267, 2.46, 2, 4.3, 2.57, 2, 4.333, 2.466, 2, 4.367, 2.189, 2, 4.4, 1.761, 2, 4.433, 1.224, 2, 4.467, 0.628, 2, 4.5, -0.017, 2, 4.533, -0.663, 2, 4.567, -1.258, 2, 4.6, -1.796, 2, 4.633, -2.224, 2, 4.667, -2.501, 2, 4.7, -2.604, 2, 4.733, -2.451, 2, 4.767, -2.039, 2, 4.8, -1.443, 2, 4.833, -0.737, 2, 4.867, -0.008, 2, 4.9, 0.698, 2, 4.933, 1.294, 2, 4.967, 1.705, 2, 5, 1.859, 2, 5.033, 1.761, 2, 5.067, 1.498, 2, 5.1, 1.118, 2, 5.133, 0.668, 2, 5.167, 0.202, 2, 5.2, -0.248, 2, 5.233, -0.628, 2, 5.267, -0.891, 2, 5.3, -0.989, 2, 5.333, -0.94, 2, 5.367, -0.808, 2, 5.4, -0.617, 2, 5.433, -0.39, 2, 5.467, -0.156, 2, 5.5, 0.07, 2, 5.533, 0.261, 2, 5.567, 0.393, 2, 5.6, 0.443, 2, 5.633, 0.425, 2, 5.667, 0.378, 2, 5.7, 0.307, 2, 5.733, 0.223, 2, 5.767, 0.131, 2, 5.8, 0.039, 2, 5.833, -0.045, 2, 5.867, -0.116, 2, 5.9, -0.163, 2, 5.933, -0.18, 2, 5.967, -0.172, 2, 6, -0.149, 2, 6.033, -0.116, 2, 6.067, -0.076, 2, 6.1, -0.036, 2, 6.133, 0.004, 2, 6.167, 0.037, 2, 6.2, 0.06, 2, 6.233, 0.068, 2, 6.267, 0.065, 2, 6.3, 0.057, 2, 6.333, 0.044, 2, 6.367, 0.029, 2, 6.4, 0.014, 2, 6.433, 0, 2, 6.467, -0.013, 2, 6.5, -0.021, 2, 6.533, -0.025, 2, 6.567, -0.023, 2, 6.6, -0.02, 2, 6.633, -0.016, 2, 6.667, -0.011, 2, 6.7, -0.005, 2, 6.733, 0, 2, 6.767, 0.004, 2, 6.8, 0.007, 2, 6.833, 0.009, 2, 6.867, 0.008, 2, 6.9, 0.007, 2, 6.933, 0.006, 2, 6.967, 0.004, 2, 7, 0.002, 2, 7.033, 0, 2, 7.067, -0.001, 2, 7.1, -0.002, 2, 7.133, -0.003, 2, 7.167, -0.003, 2, 7.2, -0.002, 2, 7.233, -0.002, 2, 7.267, -0.001, 2, 7.3, -0.001, 2, 7.333, 0, 2, 7.367, 0, 2, 7.4, 0.001, 2, 7.433, 0.001, 2, 7.5, 0.001, 2, 7.533, 0.001, 2, 7.567, 0, 2, 7.6, 0, 2, 7.633, 0, 2, 7.667, 0, 2, 7.7, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.833, 0, 2, 7.867, 0, 2, 7.933, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh3", "Segments": [0, 0.004, 2, 0.033, -0.03, 2, 0.067, -0.118, 2, 0.1, -0.244, 2, 0.133, -0.388, 2, 0.167, -0.531, 2, 0.2, -0.657, 2, 0.233, -0.746, 2, 0.267, -0.779, 2, 0.3, -0.596, 2, 0.333, -0.112, 2, 0.367, 0.572, 2, 0.4, 1.357, 2, 0.433, 2.141, 2, 0.467, 2.825, 2, 0.5, 3.309, 2, 0.533, 3.493, 2, 0.567, 3.164, 2, 0.6, 2.283, 2, 0.633, 1.008, 2, 0.667, -0.503, 2, 0.7, -2.065, 2, 0.733, -3.576, 2, 0.767, -4.851, 2, 0.8, -5.732, 2, 0.833, -6.061, 2, 0.867, -5.715, 2, 0.9, -4.776, 2, 0.933, -3.361, 2, 0.967, -1.673, 2, 1, 0.161, 2, 1.033, 1.996, 2, 1.067, 3.683, 2, 1.1, 5.099, 2, 1.133, 6.038, 2, 1.167, 6.384, 2, 1.2, 5.999, 2, 1.233, 4.97, 2, 1.267, 3.48, 2, 1.3, 1.714, 2, 1.333, -0.11, 2, 1.367, -1.876, 2, 1.4, -3.366, 2, 1.433, -4.395, 2, 1.467, -4.779, 2, 1.5, -4.517, 2, 1.533, -3.813, 2, 1.567, -2.795, 2, 1.6, -1.589, 2, 1.633, -0.342, 2, 1.667, 0.864, 2, 1.7, 1.882, 2, 1.733, 2.586, 2, 1.767, 2.849, 2, 1.8, 2.729, 2, 1.833, 2.403, 2, 1.867, 1.912, 2, 1.9, 1.327, 2, 1.933, 0.69, 2, 1.967, 0.054, 2, 2, -0.531, 2, 2.033, -1.022, 2, 2.067, -1.348, 2, 2.1, -1.468, 2, 2.133, -1.411, 2, 2.167, -1.258, 2, 2.2, -1.026, 2, 2.233, -0.745, 2, 2.267, -0.425, 2, 2.3, -0.101, 2, 2.333, 0.219, 2, 2.367, 0.5, 2, 2.4, 0.732, 2, 2.433, 0.885, 2, 2.467, 0.942, 2, 2.5, 0.77, 2, 2.533, 0.318, 2, 2.567, -0.321, 2, 2.6, -1.053, 2, 2.633, -1.786, 2, 2.667, -2.425, 2, 2.7, -2.877, 2, 2.733, -3.048, 2, 2.767, -2.623, 2, 2.8, -1.502, 2, 2.833, 0.082, 2, 2.867, 1.899, 2, 2.9, 3.716, 2, 2.933, 5.3, 2, 2.967, 6.421, 2, 3, 6.846, 2, 3.033, 6.121, 2, 3.067, 4.208, 2, 3.1, 1.504, 2, 3.133, -1.596, 2, 3.167, -4.696, 2, 3.2, -7.4, 2, 3.233, -9.313, 2, 3.267, -10.038, 2, 3.3, -9.344, 2, 3.333, -7.483, 2, 3.367, -4.79, 2, 3.4, -1.599, 2, 3.433, 1.697, 2, 3.467, 4.888, 2, 3.5, 7.581, 2, 3.533, 9.442, 2, 3.567, 10.136, 2, 3.6, 9.717, 2, 3.633, 8.6, 2, 3.667, 6.898, 2, 3.7, 4.839, 2, 3.733, 2.495, 2, 3.767, 0.122, 2, 3.8, -2.222, 2, 3.833, -4.28, 2, 3.867, -5.983, 2, 3.9, -7.1, 2, 3.933, -7.519, 2, 3.967, -7.29, 2, 4, -6.676, 2, 4.033, -5.729, 2, 4.067, -4.539, 2, 4.1, -3.221, 2, 4.133, -1.792, 2, 4.167, -0.362, 2, 4.2, 0.956, 2, 4.233, 2.146, 2, 4.267, 3.093, 2, 4.3, 3.707, 2, 4.333, 3.935, 2, 4.367, 3.815, 2, 4.4, 3.469, 2, 4.433, 2.954, 2, 4.467, 2.293, 2, 4.5, 1.532, 2, 4.533, 0.721, 2, 4.567, -0.13, 2, 4.6, -0.94, 2, 4.633, -1.701, 2, 4.667, -2.362, 2, 4.7, -2.878, 2, 4.733, -3.224, 2, 4.767, -3.344, 2, 4.8, -3.175, 2, 4.833, -2.716, 2, 4.867, -2.025, 2, 4.9, -1.201, 2, 4.933, -0.305, 2, 4.967, 0.591, 2, 5, 1.415, 2, 5.033, 2.106, 2, 5.067, 2.565, 2, 5.1, 2.734, 2, 5.133, 2.581, 2, 5.167, 2.173, 2, 5.2, 1.583, 2, 5.233, 0.883, 2, 5.267, 0.159, 2, 5.3, -0.541, 2, 5.333, -1.131, 2, 5.367, -1.539, 2, 5.4, -1.692, 2, 5.433, -1.62, 2, 5.467, -1.424, 2, 5.5, -1.13, 2, 5.533, -0.779, 2, 5.567, -0.397, 2, 5.6, -0.015, 2, 5.633, 0.336, 2, 5.667, 0.63, 2, 5.7, 0.825, 2, 5.733, 0.897, 2, 5.767, 0.852, 2, 5.8, 0.731, 2, 5.833, 0.555, 2, 5.867, 0.347, 2, 5.9, 0.131, 2, 5.933, -0.077, 2, 5.967, -0.253, 2, 6, -0.374, 2, 6.033, -0.419, 2, 6.067, -0.399, 2, 6.1, -0.344, 2, 6.133, -0.264, 2, 6.167, -0.169, 2, 6.2, -0.071, 2, 6.233, 0.023, 2, 6.267, 0.103, 2, 6.3, 0.158, 2, 6.333, 0.179, 2, 6.367, 0.17, 2, 6.4, 0.147, 2, 6.433, 0.114, 2, 6.467, 0.074, 2, 6.5, 0.033, 2, 6.533, -0.006, 2, 6.567, -0.04, 2, 6.6, -0.063, 2, 6.633, -0.071, 2, 6.667, -0.068, 2, 6.7, -0.059, 2, 6.733, -0.046, 2, 6.767, -0.03, 2, 6.8, -0.014, 2, 6.833, 0.001, 2, 6.867, 0.015, 2, 6.9, 0.024, 2, 6.933, 0.027, 2, 6.967, 0.026, 2, 7, 0.023, 2, 7.033, 0.019, 2, 7.067, 0.014, 2, 7.1, 0.009, 2, 7.133, 0.003, 2, 7.167, -0.002, 2, 7.2, -0.006, 2, 7.233, -0.009, 2, 7.267, -0.01, 2, 7.3, -0.01, 2, 7.333, -0.008, 2, 7.367, -0.006, 2, 7.4, -0.004, 2, 7.433, -0.002, 2, 7.467, 0, 2, 7.5, 0.002, 2, 7.533, 0.003, 2, 7.567, 0.004, 2, 7.6, 0.003, 2, 7.633, 0.003, 2, 7.667, 0.002, 2, 7.7, 0.001, 2, 7.733, 0, 2, 7.767, 0, 2, 7.8, -0.001, 2, 7.833, -0.001, 2, 7.933, -0.001, 2, 7.967, -0.001, 2, 8, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh46", "Segments": [0, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh46", "Segments": [0, 0, 2, 0.033, 0.168, 2, 0.067, 0.57, 2, 0.1, 1.059, 2, 0.133, 1.461, 2, 0.167, 1.629, 2, 0.2, 1.465, 2, 0.233, 1.018, 2, 0.267, 0.346, 2, 0.3, -0.456, 2, 0.333, -1.328, 2, 0.367, -2.2, 2, 0.4, -3.002, 2, 0.433, -3.675, 2, 0.467, -4.121, 2, 0.5, -4.285, 2, 0.533, -4.122, 2, 0.567, -3.686, 2, 0.6, -3.022, 2, 0.633, -2.218, 2, 0.667, -1.304, 2, 0.7, -0.378, 2, 0.733, 0.537, 2, 0.767, 1.34, 2, 0.8, 2.004, 2, 0.833, 2.44, 2, 0.867, 2.603, 2, 0.9, 2.523, 2, 0.933, 2.308, 2, 0.967, 1.981, 2, 1, 1.585, 2, 1.033, 1.135, 2, 1.067, 0.679, 2, 1.1, 0.228, 2, 1.133, -0.168, 2, 1.167, -0.495, 2, 1.2, -0.709, 2, 1.233, -0.79, 2, 1.267, -0.767, 2, 1.3, -0.704, 2, 1.333, -0.61, 2, 1.367, -0.495, 2, 1.4, -0.365, 2, 1.433, -0.232, 2, 1.467, -0.102, 2, 1.5, 0.013, 2, 1.533, 0.107, 2, 1.567, 0.17, 2, 1.6, 0.193, 2, 1.633, 0.188, 2, 1.667, 0.175, 2, 1.7, 0.155, 2, 1.733, 0.13, 2, 1.767, 0.103, 2, 1.8, 0.073, 2, 1.833, 0.043, 2, 1.867, 0.015, 2, 1.9, -0.01, 2, 1.933, -0.03, 2, 1.967, -0.043, 2, 2, -0.048, 2, 2.033, -0.045, 2, 2.067, -0.039, 2, 2.1, -0.031, 2, 2.133, -0.021, 2, 2.167, -0.012, 2, 2.2, -0.003, 2, 2.233, 0.003, 2, 2.267, 0.005, 2, 2.3, -0.19, 2, 2.333, -0.619, 2, 2.367, -1.048, 2, 2.4, -1.243, 2, 2.433, -0.978, 2, 2.467, -0.294, 2, 2.5, 0.655, 2, 2.533, 1.662, 2, 2.567, 2.611, 2, 2.6, 3.295, 2, 2.633, 3.56, 2, 2.667, 3.264, 2, 2.7, 2.47, 2, 2.733, 1.321, 2, 2.767, -0.04, 2, 2.8, -1.446, 2, 2.833, -2.807, 2, 2.867, -3.955, 2, 2.9, -4.749, 2, 2.933, -5.045, 2, 2.967, -4.774, 2, 3, -4.039, 2, 3.033, -2.93, 2, 3.067, -1.608, 2, 3.1, -0.171, 2, 3.133, 1.266, 2, 3.167, 2.588, 2, 3.2, 3.696, 2, 3.233, 4.432, 2, 3.267, 4.703, 2, 3.3, 4.556, 2, 3.333, 4.132, 2, 3.367, 3.501, 2, 3.4, 2.692, 2, 3.433, 1.76, 2, 3.467, 0.768, 2, 3.5, -0.275, 2, 3.533, -1.267, 2, 3.567, -2.199, 2, 3.6, -3.008, 2, 3.633, -3.639, 2, 3.667, -4.063, 2, 3.7, -4.21, 2, 3.733, -4.084, 2, 3.767, -3.724, 2, 3.8, -3.187, 2, 3.833, -2.498, 2, 3.867, -1.704, 2, 3.9, -0.86, 2, 3.933, 0.027, 2, 3.967, 0.872, 2, 4, 1.665, 2, 4.033, 2.354, 2, 4.067, 2.891, 2, 4.1, 3.252, 2, 4.133, 3.377, 2, 4.167, 3.255, 2, 4.2, 2.928, 2, 4.233, 2.429, 2, 4.267, 1.826, 2, 4.3, 1.14, 2, 4.333, 0.445, 2, 4.367, -0.241, 2, 4.4, -0.844, 2, 4.433, -1.342, 2, 4.467, -1.669, 2, 4.5, -1.792, 2, 4.533, -1.736, 2, 4.567, -1.587, 2, 4.6, -1.36, 2, 4.633, -1.085, 2, 4.667, -0.773, 2, 4.7, -0.456, 2, 4.733, -0.144, 2, 4.767, 0.131, 2, 4.8, 0.358, 2, 4.833, 0.507, 2, 4.867, 0.563, 2, 4.9, 0.549, 2, 4.933, 0.511, 2, 4.967, 0.453, 2, 5, 0.381, 2, 5.033, 0.3, 2, 5.067, 0.212, 2, 5.1, 0.125, 2, 5.133, 0.044, 2, 5.167, -0.029, 2, 5.2, -0.087, 2, 5.233, -0.124, 2, 5.267, -0.138, 2, 5.3, -0.134, 2, 5.333, -0.123, 2, 5.367, -0.107, 2, 5.4, -0.087, 2, 5.433, -0.064, 2, 5.467, -0.04, 2, 5.5, -0.018, 2, 5.533, 0.002, 2, 5.567, 0.019, 2, 5.6, 0.03, 2, 5.633, 0.034, 2, 5.667, 0.033, 2, 5.7, 0.03, 2, 5.733, 0.026, 2, 5.767, 0.021, 2, 5.8, 0.016, 2, 5.833, 0.01, 2, 5.867, 0.004, 2, 5.9, -0.001, 2, 5.933, -0.005, 2, 5.967, -0.007, 2, 6, -0.008, 2, 6.067, -0.008, 2, 6.1, -0.007, 2, 6.133, -0.006, 2, 6.167, -0.005, 2, 6.2, -0.004, 2, 6.233, -0.002, 2, 6.267, -0.001, 2, 6.3, 0, 2, 6.333, 0.001, 2, 6.367, 0.002, 2, 6.4, 0.002, 2, 6.433, 0.002, 2, 6.467, 0.002, 2, 6.5, 0.002, 2, 6.533, 0.001, 2, 6.567, 0.001, 2, 6.6, 0, 2, 6.633, 0, 2, 6.667, 0, 2, 6.7, 0, 2, 6.733, 0, 2, 6.867, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0, 2, 7.067, 0, 2, 7.1, 0, 2, 7.2, 0, 2, 7.267, 0, 2, 7.3, 0, 2, 7.333, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh46", "Segments": [0, 0, 2, 0.033, -0.105, 2, 0.067, -0.357, 2, 0.1, -0.664, 2, 0.133, -0.915, 2, 0.167, -1.021, 2, 0.2, -0.799, 2, 0.233, -0.226, 2, 0.267, 0.57, 2, 0.3, 1.413, 2, 0.333, 2.208, 2, 0.367, 2.781, 2, 0.4, 3.003, 2, 0.433, 2.805, 2, 0.467, 2.267, 2, 0.5, 1.457, 2, 0.533, 0.491, 2, 0.567, -0.56, 2, 0.6, -1.61, 2, 0.633, -2.577, 2, 0.667, -3.387, 2, 0.7, -3.925, 2, 0.733, -4.123, 2, 0.767, -3.922, 2, 0.8, -3.378, 2, 0.833, -2.558, 2, 0.867, -1.58, 2, 0.9, -0.517, 2, 0.933, 0.547, 2, 0.967, 1.524, 2, 1, 2.345, 2, 1.033, 2.889, 2, 1.067, 3.089, 2, 1.1, 2.982, 2, 1.133, 2.697, 2, 1.167, 2.262, 2, 1.2, 1.736, 2, 1.233, 1.137, 2, 1.267, 0.531, 2, 1.3, -0.067, 2, 1.333, -0.593, 2, 1.367, -1.028, 2, 1.4, -1.314, 2, 1.433, -1.421, 2, 1.467, -1.374, 2, 1.5, -1.252, 2, 1.533, -1.064, 2, 1.567, -0.837, 2, 1.6, -0.579, 2, 1.633, -0.318, 2, 1.667, -0.06, 2, 1.7, 0.167, 2, 1.733, 0.354, 2, 1.767, 0.477, 2, 1.8, 0.523, 2, 1.833, 0.507, 2, 1.867, 0.463, 2, 1.9, 0.396, 2, 1.933, 0.315, 2, 1.967, 0.222, 2, 2, 0.129, 2, 2.033, 0.036, 2, 2.067, -0.045, 2, 2.1, -0.112, 2, 2.133, -0.156, 2, 2.167, -0.172, 2, 2.2, -0.114, 2, 2.233, 0.036, 2, 2.267, 0.244, 2, 2.3, 0.465, 2, 2.333, 0.673, 2, 2.367, 0.823, 2, 2.4, 0.881, 2, 2.433, 0.604, 2, 2.467, -0.067, 2, 2.5, -0.96, 2, 2.533, -1.853, 2, 2.567, -2.525, 2, 2.6, -2.801, 2, 2.633, -2.471, 2, 2.667, -1.6, 2, 2.7, -0.368, 2, 2.733, 1.044, 2, 2.767, 2.455, 2, 2.8, 3.687, 2, 2.833, 4.558, 2, 2.867, 4.889, 2, 2.9, 4.516, 2, 2.933, 3.518, 2, 2.967, 2.073, 2, 3, 0.362, 2, 3.033, -1.407, 2, 3.067, -3.119, 2, 3.1, -4.563, 2, 3.133, -5.562, 2, 3.167, -5.934, 2, 3.2, -5.667, 2, 3.233, -4.957, 2, 3.267, -3.873, 2, 3.3, -2.563, 2, 3.333, -1.071, 2, 3.367, 0.439, 2, 3.4, 1.931, 2, 3.433, 3.242, 2, 3.467, 4.325, 2, 3.5, 5.036, 2, 3.533, 5.303, 2, 3.567, 5.114, 2, 3.6, 4.607, 2, 3.633, 3.825, 2, 3.667, 2.843, 2, 3.7, 1.755, 2, 3.733, 0.575, 2, 3.767, -0.605, 2, 3.8, -1.694, 2, 3.833, -2.676, 2, 3.867, -3.458, 2, 3.9, -3.965, 2, 3.933, -4.154, 2, 3.967, -4.026, 2, 4, -3.661, 2, 4.033, -3.116, 2, 4.067, -2.418, 2, 4.1, -1.614, 2, 4.133, -0.757, 2, 4.167, 0.142, 2, 4.2, 0.998, 2, 4.233, 1.802, 2, 4.267, 2.501, 2, 4.3, 3.045, 2, 4.333, 3.411, 2, 4.367, 3.538, 2, 4.4, 3.374, 2, 4.433, 2.928, 2, 4.467, 2.256, 2, 4.5, 1.454, 2, 4.533, 0.583, 2, 4.567, -0.288, 2, 4.6, -1.089, 2, 4.633, -1.762, 2, 4.667, -2.208, 2, 4.7, -2.372, 2, 4.733, -2.29, 2, 4.767, -2.072, 2, 4.8, -1.74, 2, 4.833, -1.338, 2, 4.867, -0.881, 2, 4.9, -0.418, 2, 4.933, 0.04, 2, 4.967, 0.442, 2, 5, 0.774, 2, 5.033, 0.992, 2, 5.067, 1.074, 2, 5.1, 1.039, 2, 5.133, 0.946, 2, 5.167, 0.805, 2, 5.2, 0.635, 2, 5.233, 0.44, 2, 5.267, 0.244, 2, 5.3, 0.049, 2, 5.333, -0.121, 2, 5.367, -0.262, 2, 5.4, -0.355, 2, 5.433, -0.39, 2, 5.467, -0.377, 2, 5.5, -0.345, 2, 5.533, -0.295, 2, 5.567, -0.235, 2, 5.6, -0.166, 2, 5.633, -0.097, 2, 5.667, -0.028, 2, 5.7, 0.032, 2, 5.733, 0.082, 2, 5.767, 0.114, 2, 5.8, 0.127, 2, 5.833, 0.123, 2, 5.867, 0.114, 2, 5.9, 0.101, 2, 5.933, 0.083, 2, 5.967, 0.064, 2, 6, 0.044, 2, 6.033, 0.023, 2, 6.067, 0.004, 2, 6.1, -0.013, 2, 6.133, -0.027, 2, 6.167, -0.036, 2, 6.2, -0.039, 2, 6.233, -0.038, 2, 6.267, -0.035, 2, 6.3, -0.03, 2, 6.333, -0.024, 2, 6.367, -0.017, 2, 6.4, -0.01, 2, 6.433, -0.004, 2, 6.467, 0.002, 2, 6.5, 0.007, 2, 6.533, 0.01, 2, 6.567, 0.011, 2, 6.6, 0.011, 2, 6.633, 0.01, 2, 6.667, 0.009, 2, 6.7, 0.007, 2, 6.733, 0.005, 2, 6.767, 0.003, 2, 6.8, 0.001, 2, 6.833, -0.001, 2, 6.867, -0.002, 2, 6.9, -0.003, 2, 6.933, -0.003, 2, 7, -0.003, 2, 7.033, -0.003, 2, 7.067, -0.002, 2, 7.1, -0.002, 2, 7.133, -0.001, 2, 7.167, -0.001, 2, 7.2, 0, 2, 7.233, 0, 2, 7.267, 0.001, 2, 7.3, 0.001, 2, 7.4, 0.001, 2, 7.433, 0.001, 2, 7.467, 0.001, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.6, 0, 2, 7.633, 0, 2, 7.7, 0, 2, 7.767, 0, 2, 7.833, 0, 2, 7.867, 0, 2, 7.9, 0, 2, 7.933, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh46", "Segments": [0, 0.003, 2, 0.033, -0.036, 2, 0.067, -0.138, 2, 0.1, -0.279, 2, 0.133, -0.428, 2, 0.167, -0.569, 2, 0.2, -0.67, 2, 0.233, -0.71, 2, 0.267, -0.589, 2, 0.3, -0.264, 2, 0.333, 0.206, 2, 0.367, 0.762, 2, 0.4, 1.337, 2, 0.433, 1.894, 2, 0.467, 2.363, 2, 0.5, 2.688, 2, 0.533, 2.809, 2, 0.567, 2.606, 2, 0.6, 2.056, 2, 0.633, 1.227, 2, 0.667, 0.238, 2, 0.7, -0.837, 2, 0.733, -1.912, 2, 0.767, -2.9, 2, 0.8, -3.729, 2, 0.833, -4.28, 2, 0.867, -4.482, 2, 0.9, -4.283, 2, 0.933, -3.752, 2, 0.967, -2.943, 2, 1, -1.964, 2, 1.033, -0.85, 2, 1.067, 0.278, 2, 1.1, 1.392, 2, 1.133, 2.371, 2, 1.167, 3.18, 2, 1.2, 3.711, 2, 1.233, 3.91, 2, 1.267, 3.764, 2, 1.3, 3.373, 2, 1.333, 2.778, 2, 1.367, 2.059, 2, 1.4, 1.239, 2, 1.433, 0.41, 2, 1.467, -0.41, 2, 1.5, -1.129, 2, 1.533, -1.724, 2, 1.567, -2.115, 2, 1.6, -2.261, 2, 1.633, -2.183, 2, 1.667, -1.974, 2, 1.7, -1.655, 2, 1.733, -1.27, 2, 1.767, -0.831, 2, 1.8, -0.386, 2, 1.833, 0.052, 2, 1.867, 0.438, 2, 1.9, 0.757, 2, 1.933, 0.966, 2, 1.967, 1.044, 2, 2, 1.004, 2, 2.033, 0.897, 2, 2.067, 0.734, 2, 2.1, 0.54, 2, 2.133, 0.33, 2, 2.167, 0.119, 2, 2.2, -0.075, 2, 2.233, -0.237, 2, 2.267, -0.345, 2, 2.3, -0.385, 2, 2.333, -0.317, 2, 2.367, -0.157, 2, 2.4, 0.038, 2, 2.433, 0.199, 2, 2.467, 0.266, 2, 2.5, 0.142, 2, 2.533, -0.179, 2, 2.567, -0.624, 2, 2.6, -1.096, 2, 2.633, -1.542, 2, 2.667, -1.863, 2, 2.7, -1.987, 2, 2.733, -1.763, 2, 2.767, -1.163, 2, 2.8, -0.296, 2, 2.833, 0.733, 2, 2.867, 1.795, 2, 2.9, 2.823, 2, 2.933, 3.691, 2, 2.967, 4.291, 2, 3, 4.514, 2, 3.033, 4.143, 2, 3.067, 3.146, 2, 3.1, 1.704, 2, 3.133, -0.004, 2, 3.167, -1.77, 2, 3.2, -3.478, 2, 3.233, -4.92, 2, 3.267, -5.917, 2, 3.3, -6.289, 2, 3.333, -5.983, 2, 3.367, -5.169, 2, 3.4, -3.928, 2, 3.433, -2.427, 2, 3.467, -0.718, 2, 3.5, 1.012, 2, 3.533, 2.721, 2, 3.567, 4.222, 2, 3.6, 5.463, 2, 3.633, 6.277, 2, 3.667, 6.582, 2, 3.7, 6.335, 2, 3.733, 5.673, 2, 3.767, 4.649, 2, 3.8, 3.364, 2, 3.833, 1.94, 2, 3.867, 0.396, 2, 3.9, -1.148, 2, 3.933, -2.572, 2, 3.967, -3.857, 2, 4, -4.881, 2, 4.033, -5.543, 2, 4.067, -5.791, 2, 4.1, -5.578, 2, 4.133, -5.009, 2, 4.167, -4.13, 2, 4.2, -3.025, 2, 4.233, -1.802, 2, 4.267, -0.475, 2, 4.3, 0.851, 2, 4.333, 2.075, 2, 4.367, 3.179, 2, 4.4, 4.058, 2, 4.433, 4.628, 2, 4.467, 4.84, 2, 4.5, 4.676, 2, 4.533, 4.235, 2, 4.567, 3.555, 2, 4.6, 2.7, 2, 4.633, 1.753, 2, 4.667, 0.727, 2, 4.7, -0.3, 2, 4.733, -1.247, 2, 4.767, -2.101, 2, 4.8, -2.782, 2, 4.833, -3.222, 2, 4.867, -3.386, 2, 4.9, -3.263, 2, 4.933, -2.932, 2, 4.967, -2.428, 2, 5, -1.819, 2, 5.033, -1.125, 2, 5.067, -0.423, 2, 5.1, 0.271, 2, 5.133, 0.88, 2, 5.167, 1.384, 2, 5.2, 1.714, 2, 5.233, 1.838, 2, 5.267, 1.775, 2, 5.3, 1.607, 2, 5.333, 1.35, 2, 5.367, 1.04, 2, 5.4, 0.687, 2, 5.433, 0.33, 2, 5.467, -0.023, 2, 5.5, -0.333, 2, 5.533, -0.589, 2, 5.567, -0.758, 2, 5.6, -0.821, 2, 5.633, -0.794, 2, 5.667, -0.721, 2, 5.7, -0.611, 2, 5.733, -0.478, 2, 5.767, -0.326, 2, 5.8, -0.173, 2, 5.833, -0.021, 2, 5.867, 0.112, 2, 5.9, 0.222, 2, 5.933, 0.295, 2, 5.967, 0.322, 2, 6, 0.313, 2, 6.033, 0.29, 2, 6.067, 0.253, 2, 6.1, 0.208, 2, 6.133, 0.158, 2, 6.167, 0.103, 2, 6.2, 0.049, 2, 6.233, -0.002, 2, 6.267, -0.047, 2, 6.3, -0.083, 2, 6.333, -0.106, 2, 6.367, -0.115, 2, 6.4, -0.112, 2, 6.433, -0.102, 2, 6.467, -0.087, 2, 6.5, -0.069, 2, 6.533, -0.048, 2, 6.567, -0.028, 2, 6.6, -0.007, 2, 6.633, 0.011, 2, 6.667, 0.026, 2, 6.7, 0.036, 2, 6.733, 0.039, 2, 6.767, 0.038, 2, 6.8, 0.035, 2, 6.833, 0.03, 2, 6.867, 0.024, 2, 6.9, 0.017, 2, 6.933, 0.01, 2, 6.967, 0.003, 2, 7, -0.003, 2, 7.033, -0.008, 2, 7.067, -0.012, 2, 7.1, -0.013, 2, 7.133, -0.012, 2, 7.167, -0.011, 2, 7.2, -0.01, 2, 7.233, -0.008, 2, 7.267, -0.006, 2, 7.3, -0.003, 2, 7.333, -0.001, 2, 7.367, 0.001, 2, 7.4, 0.003, 2, 7.433, 0.004, 2, 7.467, 0.004, 2, 7.533, 0.004, 2, 7.567, 0.003, 2, 7.6, 0.003, 2, 7.633, 0.002, 2, 7.667, 0.001, 2, 7.7, 0.001, 2, 7.733, 0, 2, 7.767, -0.001, 2, 7.8, -0.001, 2, 7.833, -0.001, 2, 7.933, -0.001, 2, 7.967, -0.001, 2, 8, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh46", "Segments": [0, 0, 2, 0.033, 0.004, 2, 0.067, -0.021, 2, 0.1, -0.089, 2, 0.133, -0.188, 2, 0.167, -0.304, 2, 0.2, -0.425, 2, 0.233, -0.541, 2, 0.267, -0.64, 2, 0.3, -0.708, 2, 0.333, -0.733, 2, 0.367, -0.61, 2, 0.4, -0.278, 2, 0.433, 0.224, 2, 0.467, 0.822, 2, 0.5, 1.472, 2, 0.533, 2.122, 2, 0.567, 2.72, 2, 0.6, 3.221, 2, 0.633, 3.554, 2, 0.667, 3.676, 2, 0.7, 3.389, 2, 0.733, 2.61, 2, 0.767, 1.435, 2, 0.8, 0.035, 2, 0.833, -1.488, 2, 0.867, -3.011, 2, 0.9, -4.411, 2, 0.933, -5.586, 2, 0.967, -6.366, 2, 1, -6.653, 2, 1.033, -6.339, 2, 1.067, -5.503, 2, 1.1, -4.228, 2, 1.133, -2.687, 2, 1.167, -0.932, 2, 1.2, 0.845, 2, 1.233, 2.6, 2, 1.267, 4.141, 2, 1.3, 5.416, 2, 1.333, 6.252, 2, 1.367, 6.566, 2, 1.4, 6.304, 2, 1.433, 5.607, 2, 1.467, 4.544, 2, 1.5, 3.258, 2, 1.533, 1.794, 2, 1.567, 0.312, 2, 1.6, -1.152, 2, 1.633, -2.438, 2, 1.667, -3.501, 2, 1.7, -4.199, 2, 1.733, -4.46, 2, 1.767, -4.298, 2, 1.8, -3.864, 2, 1.833, -3.203, 2, 1.867, -2.403, 2, 1.9, -1.492, 2, 1.933, -0.571, 2, 1.967, 0.34, 2, 2, 1.139, 2, 2.033, 1.801, 2, 2.067, 2.234, 2, 2.1, 2.397, 2, 2.133, 2.32, 2, 2.167, 2.115, 2, 2.2, 1.802, 2, 2.233, 1.423, 2, 2.267, 0.992, 2, 2.3, 0.555, 2, 2.333, 0.124, 2, 2.367, -0.254, 2, 2.4, -0.568, 2, 2.433, -0.773, 2, 2.467, -0.85, 2, 2.5, -0.792, 2, 2.533, -0.663, 2, 2.567, -0.534, 2, 2.6, -0.476, 2, 2.633, -0.58, 2, 2.667, -0.832, 2, 2.7, -1.168, 2, 2.733, -1.503, 2, 2.767, -1.756, 2, 2.8, -1.86, 2, 2.833, -1.601, 2, 2.867, -0.906, 2, 2.9, 0.099, 2, 2.933, 1.29, 2, 2.967, 2.52, 2, 3, 3.711, 2, 3.033, 4.715, 2, 3.067, 5.41, 2, 3.1, 5.669, 2, 3.133, 5.259, 2, 3.167, 4.146, 2, 3.2, 2.469, 2, 3.233, 0.47, 2, 3.267, -1.704, 2, 3.3, -3.878, 2, 3.333, -5.878, 2, 3.367, -7.555, 2, 3.4, -8.668, 2, 3.433, -9.078, 2, 3.467, -8.611, 2, 3.5, -7.366, 2, 3.533, -5.469, 2, 3.567, -3.175, 2, 3.6, -0.563, 2, 3.633, 2.081, 2, 3.667, 4.693, 2, 3.7, 6.987, 2, 3.733, 8.884, 2, 3.767, 10.128, 2, 3.8, 10.595, 2, 3.833, 10.102, 2, 3.867, 8.789, 2, 3.9, 6.787, 2, 3.933, 4.366, 2, 3.967, 1.61, 2, 4, -1.18, 2, 4.033, -3.936, 2, 4.067, -6.357, 2, 4.1, -8.359, 2, 4.133, -9.672, 2, 4.167, -10.165, 2, 4.2, -9.853, 2, 4.233, -8.955, 2, 4.267, -7.62, 2, 4.3, -5.906, 2, 4.333, -3.933, 2, 4.367, -1.833, 2, 4.4, 0.374, 2, 4.433, 2.475, 2, 4.467, 4.448, 2, 4.5, 6.161, 2, 4.533, 7.497, 2, 4.567, 8.394, 2, 4.6, 8.706, 2, 4.633, 8.404, 2, 4.667, 7.593, 2, 4.7, 6.342, 2, 4.733, 4.77, 2, 4.767, 3.029, 2, 4.8, 1.141, 2, 4.833, -0.747, 2, 4.867, -2.488, 2, 4.9, -4.06, 2, 4.933, -5.311, 2, 4.967, -6.122, 2, 5, -6.424, 2, 5.033, -6.178, 2, 5.067, -5.523, 2, 5.1, -4.526, 2, 5.133, -3.318, 2, 5.167, -1.944, 2, 5.2, -0.553, 2, 5.233, 0.821, 2, 5.267, 2.028, 2, 5.3, 3.026, 2, 5.333, 3.681, 2, 5.367, 3.926, 2, 5.4, 3.786, 2, 5.433, 3.411, 2, 5.467, 2.84, 2, 5.5, 2.149, 2, 5.533, 1.363, 2, 5.567, 0.567, 2, 5.6, -0.219, 2, 5.633, -0.91, 2, 5.667, -1.481, 2, 5.7, -1.856, 2, 5.733, -1.996, 2, 5.767, -1.938, 2, 5.8, -1.783, 2, 5.833, -1.543, 2, 5.867, -1.242, 2, 5.9, -0.908, 2, 5.933, -0.546, 2, 5.967, -0.184, 2, 6, 0.15, 2, 6.033, 0.451, 2, 6.067, 0.691, 2, 6.1, 0.846, 2, 6.133, 0.904, 2, 6.167, 0.874, 2, 6.2, 0.793, 2, 6.233, 0.67, 2, 6.267, 0.522, 2, 6.3, 0.352, 2, 6.333, 0.181, 2, 6.367, 0.012, 2, 6.4, -0.137, 2, 6.433, -0.26, 2, 6.467, -0.341, 2, 6.5, -0.371, 2, 6.533, -0.361, 2, 6.567, -0.333, 2, 6.6, -0.291, 2, 6.633, -0.238, 2, 6.667, -0.179, 2, 6.7, -0.115, 2, 6.733, -0.052, 2, 6.767, 0.007, 2, 6.8, 0.06, 2, 6.833, 0.103, 2, 6.867, 0.13, 2, 6.9, 0.14, 2, 6.933, 0.136, 2, 6.967, 0.124, 2, 7, 0.105, 2, 7.033, 0.083, 2, 7.067, 0.057, 2, 7.1, 0.032, 2, 7.133, 0.006, 2, 7.167, -0.016, 2, 7.2, -0.034, 2, 7.233, -0.047, 2, 7.267, -0.051, 2, 7.3, -0.049, 2, 7.333, -0.045, 2, 7.367, -0.038, 2, 7.4, -0.03, 2, 7.433, -0.021, 2, 7.467, -0.012, 2, 7.5, -0.003, 2, 7.533, 0.005, 2, 7.567, 0.012, 2, 7.6, 0.016, 2, 7.633, 0.018, 2, 7.667, 0.017, 2, 7.7, 0.016, 2, 7.733, 0.014, 2, 7.767, 0.011, 2, 7.8, 0.009, 2, 7.833, 0.006, 2, 7.867, 0.003, 2, 7.9, 0, 2, 7.933, -0.002, 2, 7.967, -0.004, 2, 8, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh46", "Segments": [0, -0.083, 2, 0.033, -0.073, 2, 0.067, -0.052, 2, 0.1, -0.031, 2, 0.133, -0.021, 2, 0.167, -0.041, 2, 0.2, -0.094, 2, 0.233, -0.17, 2, 0.267, -0.26, 2, 0.3, -0.353, 2, 0.333, -0.443, 2, 0.367, -0.519, 2, 0.4, -0.572, 2, 0.433, -0.591, 2, 0.467, -0.473, 2, 0.5, -0.151, 2, 0.533, 0.333, 2, 0.567, 0.911, 2, 0.6, 1.539, 2, 0.633, 2.167, 2, 0.667, 2.745, 2, 0.7, 3.229, 2, 0.733, 3.551, 2, 0.767, 3.669, 2, 0.8, 3.407, 2, 0.833, 2.709, 2, 0.867, 1.644, 2, 0.9, 0.357, 2, 0.933, -1.109, 2, 0.967, -2.592, 2, 1, -4.058, 2, 1.033, -5.345, 2, 1.067, -6.409, 2, 1.1, -7.108, 2, 1.133, -7.37, 2, 1.167, -7.002, 2, 1.2, -6.022, 2, 1.233, -4.529, 2, 1.267, -2.722, 2, 1.3, -0.666, 2, 1.333, 1.416, 2, 1.367, 3.473, 2, 1.4, 5.279, 2, 1.433, 6.773, 2, 1.467, 7.753, 2, 1.5, 8.12, 2, 1.533, 7.835, 2, 1.567, 7.068, 2, 1.6, 5.885, 2, 1.633, 4.399, 2, 1.667, 2.753, 2, 1.7, 0.968, 2, 1.733, -0.817, 2, 1.767, -2.464, 2, 1.8, -3.95, 2, 1.833, -5.133, 2, 1.867, -5.899, 2, 1.9, -6.185, 2, 1.933, -5.947, 2, 1.967, -5.313, 2, 2, -4.346, 2, 2.033, -3.177, 2, 2.067, -1.847, 2, 2.1, -0.499, 2, 2.133, 0.831, 2, 2.167, 2, 2, 2.2, 2.967, 2, 2.233, 3.601, 2, 2.267, 3.839, 2, 2.3, 3.728, 2, 2.333, 3.431, 2, 2.367, 2.973, 2, 2.4, 2.397, 2, 2.433, 1.759, 2, 2.467, 1.068, 2, 2.5, 0.376, 2, 2.533, -0.262, 2, 2.567, -0.837, 2, 2.6, -1.296, 2, 2.633, -1.593, 2, 2.667, -1.703, 2, 2.7, -1.681, 2, 2.733, -1.633, 2, 2.767, -1.584, 2, 2.8, -1.562, 2, 2.833, -1.577, 2, 2.867, -1.436, 2, 2.9, -1.056, 2, 2.933, -0.469, 2, 2.967, 0.267, 2, 3, 1.083, 2, 3.033, 1.968, 2, 3.067, 2.853, 2, 3.1, 3.669, 2, 3.133, 4.405, 2, 3.167, 4.992, 2, 3.2, 5.372, 2, 3.233, 5.513, 2, 3.267, 5.09, 2, 3.3, 3.939, 2, 3.333, 2.206, 2, 3.367, 0.139, 2, 3.4, -2.109, 2, 3.433, -4.356, 2, 3.467, -6.423, 2, 3.5, -8.157, 2, 3.533, -9.307, 2, 3.567, -9.731, 2, 3.6, -9.116, 2, 3.633, -7.447, 2, 3.667, -4.932, 2, 3.7, -1.934, 2, 3.733, 1.327, 2, 3.767, 4.587, 2, 3.8, 7.586, 2, 3.833, 10.101, 2, 3.867, 11.77, 2, 3.9, 12.384, 2, 3.933, 11.88, 2, 3.967, 10.527, 2, 4, 8.438, 2, 4.033, 5.815, 2, 4.067, 2.909, 2, 4.1, -0.242, 2, 4.133, -3.394, 2, 4.167, -6.3, 2, 4.2, -8.923, 2, 4.233, -11.012, 2, 4.267, -12.364, 2, 4.3, -12.869, 2, 4.333, -12.384, 2, 4.367, -11.084, 2, 4.4, -9.077, 2, 4.433, -6.556, 2, 4.467, -3.763, 2, 4.5, -0.734, 2, 4.533, 2.295, 2, 4.567, 5.088, 2, 4.6, 7.609, 2, 4.633, 9.616, 2, 4.667, 10.916, 2, 4.7, 11.401, 2, 4.733, 10.997, 2, 4.767, 9.913, 2, 4.8, 8.239, 2, 4.833, 6.137, 2, 4.867, 3.809, 2, 4.9, 1.283, 2, 4.933, -1.242, 2, 4.967, -3.57, 2, 5, -5.672, 2, 5.033, -7.346, 2, 5.067, -8.43, 2, 5.1, -8.834, 2, 5.133, -8.54, 2, 5.167, -7.751, 2, 5.2, -6.532, 2, 5.233, -5.001, 2, 5.267, -3.306, 2, 5.3, -1.467, 2, 5.333, 0.372, 2, 5.367, 2.068, 2, 5.4, 3.598, 2, 5.433, 4.817, 2, 5.467, 5.606, 2, 5.5, 5.9, 2, 5.533, 5.715, 2, 5.567, 5.219, 2, 5.6, 4.452, 2, 5.633, 3.489, 2, 5.667, 2.422, 2, 5.7, 1.265, 2, 5.733, 0.108, 2, 5.767, -0.959, 2, 5.8, -1.922, 2, 5.833, -2.688, 2, 5.867, -3.185, 2, 5.9, -3.37, 2, 5.933, -3.25, 2, 5.967, -2.929, 2, 6, -2.44, 2, 6.033, -1.848, 2, 6.067, -1.175, 2, 6.1, -0.493, 2, 6.133, 0.18, 2, 6.167, 0.771, 2, 6.2, 1.26, 2, 6.233, 1.581, 2, 6.267, 1.702, 2, 6.3, 1.652, 2, 6.333, 1.52, 2, 6.367, 1.315, 2, 6.4, 1.058, 2, 6.433, 0.773, 2, 6.467, 0.465, 2, 6.5, 0.156, 2, 6.533, -0.129, 2, 6.567, -0.386, 2, 6.6, -0.59, 2, 6.633, -0.723, 2, 6.667, -0.772, 2, 6.7, -0.746, 2, 6.733, -0.676, 2, 6.767, -0.57, 2, 6.8, -0.442, 2, 6.833, -0.296, 2, 6.867, -0.148, 2, 6.9, -0.003, 2, 6.933, 0.126, 2, 6.967, 0.232, 2, 7, 0.301, 2, 7.033, 0.327, 2, 7.067, 0.316, 2, 7.1, 0.288, 2, 7.133, 0.244, 2, 7.167, 0.191, 2, 7.2, 0.13, 2, 7.233, 0.069, 2, 7.267, 0.008, 2, 7.3, -0.045, 2, 7.333, -0.089, 2, 7.367, -0.118, 2, 7.4, -0.129, 2, 7.433, -0.125, 2, 7.467, -0.116, 2, 7.5, -0.101, 2, 7.533, -0.082, 2, 7.567, -0.062, 2, 7.6, -0.04, 2, 7.633, -0.018, 2, 7.667, 0.003, 2, 7.7, 0.021, 2, 7.733, 0.036, 2, 7.767, 0.045, 2, 7.8, 0.049, 2, 7.833, 0.047, 2, 7.867, 0.043, 2, 7.9, 0.037, 2, 7.933, 0.029, 2, 7.967, 0.02, 2, 8, 0.011]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh46", "Segments": [0, -0.213, 2, 0.033, -0.246, 2, 0.067, -0.279, 2, 0.1, -0.27, 2, 0.133, -0.246, 2, 0.167, -0.213, 2, 0.2, -0.177, 2, 0.233, -0.144, 2, 0.267, -0.12, 2, 0.3, -0.111, 2, 0.333, -0.137, 2, 0.367, -0.203, 2, 0.4, -0.296, 2, 0.433, -0.394, 2, 0.467, -0.486, 2, 0.5, -0.553, 2, 0.533, -0.578, 2, 0.567, -0.435, 2, 0.6, -0.046, 2, 0.633, 0.541, 2, 0.667, 1.241, 2, 0.7, 2.002, 2, 0.733, 2.762, 2, 0.767, 3.462, 2, 0.8, 4.049, 2, 0.833, 4.438, 2, 0.867, 4.582, 2, 0.9, 4.234, 2, 0.933, 3.306, 2, 0.967, 1.892, 2, 1, 0.182, 2, 1.033, -1.764, 2, 1.067, -3.735, 2, 1.1, -5.682, 2, 1.133, -7.392, 2, 1.167, -8.806, 2, 1.2, -9.733, 2, 1.233, -10.081, 2, 1.267, -9.634, 2, 1.3, -8.435, 2, 1.333, -6.583, 2, 1.367, -4.257, 2, 1.4, -1.68, 2, 1.433, 1.114, 2, 1.467, 3.908, 2, 1.5, 6.484, 2, 1.533, 8.81, 2, 1.567, 10.662, 2, 1.6, 11.861, 2, 1.633, 12.309, 2, 1.667, 11.856, 2, 1.7, 10.641, 2, 1.733, 8.765, 2, 1.767, 6.408, 2, 1.8, 3.798, 2, 1.833, 0.968, 2, 1.867, -1.863, 2, 1.9, -4.473, 2, 1.933, -6.829, 2, 1.967, -8.706, 2, 2, -9.92, 2, 2.033, -10.373, 2, 2.067, -9.958, 2, 2.1, -8.852, 2, 2.133, -7.165, 2, 2.167, -5.125, 2, 2.2, -2.802, 2, 2.233, -0.451, 2, 2.267, 1.872, 2, 2.3, 3.912, 2, 2.333, 5.598, 2, 2.367, 6.705, 2, 2.4, 7.12, 2, 2.433, 6.896, 2, 2.467, 6.294, 2, 2.5, 5.364, 2, 2.533, 4.197, 2, 2.567, 2.903, 2, 2.6, 1.501, 2, 2.633, 0.098, 2, 2.667, -1.195, 2, 2.7, -2.362, 2, 2.733, -3.292, 2, 2.767, -3.894, 2, 2.8, -4.118, 2, 2.833, -3.99, 2, 2.867, -3.629, 2, 2.9, -3.067, 2, 2.933, -2.338, 2, 2.967, -1.476, 2, 3, -0.513, 2, 3.033, 0.516, 2, 3.067, 1.579, 2, 3.1, 2.641, 2, 3.133, 3.671, 2, 3.167, 4.633, 2, 3.2, 5.495, 2, 3.233, 6.224, 2, 3.267, 6.786, 2, 3.3, 7.148, 2, 3.333, 7.276, 2, 3.367, 6.708, 2, 3.4, 5.165, 2, 3.433, 2.839, 2, 3.467, 0.067, 2, 3.5, -2.947, 2, 3.533, -5.961, 2, 3.567, -8.733, 2, 3.6, -11.059, 2, 3.633, -12.602, 2, 3.667, -13.17, 2, 3.7, -12.434, 2, 3.733, -10.473, 2, 3.767, -7.485, 2, 3.8, -3.87, 2, 3.833, 0.245, 2, 3.867, 4.41, 2, 3.9, 8.525, 2, 3.933, 12.14, 2, 3.967, 15.128, 2, 4, 17.089, 2, 4.033, 17.825, 2, 4.067, 17.086, 2, 4.1, 15.104, 2, 4.133, 12.043, 2, 4.167, 8.199, 2, 4.2, 3.941, 2, 4.233, -0.677, 2, 4.267, -5.295, 2, 4.3, -9.553, 2, 4.333, -13.397, 2, 4.367, -16.458, 2, 4.4, -18.44, 2, 4.433, -19.179, 2, 4.467, -18.438, 2, 4.5, -16.451, 2, 4.533, -13.383, 2, 4.567, -9.529, 2, 4.6, -5.26, 2, 4.633, -0.631, 2, 4.667, 3.999, 2, 4.7, 8.268, 2, 4.733, 12.121, 2, 4.767, 15.19, 2, 4.8, 17.176, 2, 4.833, 17.917, 2, 4.867, 17.267, 2, 4.9, 15.523, 2, 4.933, 12.829, 2, 4.967, 9.446, 2, 5, 5.699, 2, 5.033, 1.635, 2, 5.067, -2.429, 2, 5.1, -6.176, 2, 5.133, -9.559, 2, 5.167, -12.253, 2, 5.2, -13.997, 2, 5.233, -14.648, 2, 5.267, -14.145, 2, 5.3, -12.799, 2, 5.333, -10.719, 2, 5.367, -8.108, 2, 5.4, -5.215, 2, 5.433, -2.078, 2, 5.467, 1.059, 2, 5.5, 3.952, 2, 5.533, 6.564, 2, 5.567, 8.643, 2, 5.6, 9.99, 2, 5.633, 10.492, 2, 5.667, 10.15, 2, 5.7, 9.235, 2, 5.733, 7.822, 2, 5.767, 6.046, 2, 5.8, 4.08, 2, 5.833, 1.947, 2, 5.867, -0.186, 2, 5.9, -2.152, 2, 5.933, -3.927, 2, 5.967, -5.341, 2, 6, -6.256, 2, 6.033, -6.598, 2, 6.067, -6.355, 2, 6.1, -5.707, 2, 6.133, -4.72, 2, 6.167, -3.526, 2, 6.2, -2.166, 2, 6.233, -0.79, 2, 6.267, 0.57, 2, 6.3, 1.764, 2, 6.333, 2.751, 2, 6.367, 3.399, 2, 6.4, 3.642, 2, 6.433, 3.532, 2, 6.467, 3.238, 2, 6.5, 2.785, 2, 6.533, 2.215, 2, 6.567, 1.584, 2, 6.6, 0.899, 2, 6.633, 0.215, 2, 6.667, -0.416, 2, 6.7, -0.986, 2, 6.733, -1.44, 2, 6.767, -1.734, 2, 6.8, -1.843, 2, 6.833, -1.779, 2, 6.867, -1.609, 2, 6.9, -1.35, 2, 6.933, -1.037, 2, 6.967, -0.68, 2, 7, -0.319, 2, 7.033, 0.037, 2, 7.067, 0.351, 2, 7.1, 0.61, 2, 7.133, 0.78, 2, 7.167, 0.844, 2, 7.2, 0.819, 2, 7.233, 0.755, 2, 7.267, 0.655, 2, 7.3, 0.529, 2, 7.333, 0.39, 2, 7.367, 0.239, 2, 7.4, 0.088, 2, 7.433, -0.051, 2, 7.467, -0.176, 2, 7.5, -0.276, 2, 7.533, -0.341, 2, 7.567, -0.365, 2, 7.6, -0.353, 2, 7.633, -0.32, 2, 7.667, -0.271, 2, 7.7, -0.211, 2, 7.733, -0.143, 2, 7.767, -0.074, 2, 7.8, -0.006, 2, 7.833, 0.054, 2, 7.867, 0.103, 2, 7.9, 0.136, 2, 7.933, 0.148, 2, 7.967, 0.144, 2, 8, 0.133]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh0_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh1_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh2_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh3_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh46_Skinning", "Segments": [0, 1, 0, 8, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 8, 1]}]}