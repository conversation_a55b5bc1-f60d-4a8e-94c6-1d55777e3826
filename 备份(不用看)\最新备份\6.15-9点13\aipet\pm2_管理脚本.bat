@echo off
chcp 65001 > nul
echo ================================
echo     AI桌宠 PM2 管理脚本
echo ================================
echo.

:menu
echo 请选择操作：
echo [1] 启动 AI桌宠
echo [2] 停止 AI桌宠  
echo [3] 重启 AI桌宠
echo [4] 查看状态
echo [5] 查看日志
echo [6] 设置开机自启
echo [7] 取消开机自启
echo [8] 退出
echo.
set /p choice=请输入选项 (1-8): 

if "%choice%"=="1" goto start_app
if "%choice%"=="2" goto stop_app
if "%choice%"=="3" goto restart_app
if "%choice%"=="4" goto status_app
if "%choice%"=="5" goto logs_app
if "%choice%"=="6" goto setup_startup
if "%choice%"=="7" goto remove_startup
if "%choice%"=="8" goto exit_script
goto invalid

:start_app
echo.
echo 正在启动 AI桌宠...
if not exist "logs" mkdir logs
pm2 start ecosystem.config.js
echo.
pause
goto menu

:stop_app
echo.
echo 正在停止 AI桌宠...
pm2 stop ai_桌宠
echo.
pause
goto menu

:restart_app
echo.
echo 正在重启 AI桌宠...
pm2 restart ai_桌宠
echo.
pause
goto menu

:status_app
echo.
echo AI桌宠 运行状态：
pm2 list
echo.
pause
goto menu

:logs_app
echo.
echo 查看 AI桌宠 日志 (按 Ctrl+C 退出):
pm2 logs ai_桌宠
echo.
pause
goto menu

:setup_startup
echo.
echo 设置开机自启需要管理员权限...
echo 1. 请以管理员身份运行命令提示符
echo 2. 执行: pm2-startup install
echo 3. 然后以普通权限执行: pm2 save
echo.
echo 是否现在执行保存操作? (Y/N)
set /p save_choice=
if /i "%save_choice%"=="Y" (
    pm2 save
    echo 配置已保存！
) else (
    echo 请记得手动执行 pm2 save
)
echo.
pause
goto menu

:remove_startup
echo.
echo 取消开机自启需要管理员权限...
echo 请以管理员身份执行: pm2-startup uninstall
echo.
pause
goto menu

:invalid
echo.
echo 无效选项，请重新选择！
echo.
pause
goto menu

:exit_script
echo.
echo 感谢使用！
exit /b 0