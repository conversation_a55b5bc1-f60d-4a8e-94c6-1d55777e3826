#!/usr/bin/env python3
"""
测试表情选择器功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton
from aipet.ui.input_area import ModernInputArea

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试表情选择器")
        self.setGeometry(100, 100, 400, 300)
        
        layout = QVBoxLayout(self)
        
        # 创建输入区域
        self.input_area = ModernInputArea(self)
        layout.addWidget(self.input_area)
        
        # 创建测试按钮
        test_button = QPushButton("测试表情选择器")
        test_button.clicked.connect(self.test_emoji_picker)
        layout.addWidget(test_button)
    
    def test_emoji_picker(self):
        """测试表情选择器"""
        try:
            self.input_area.show_emoji_picker()
            print("✓ 表情选择器功能正常")
        except Exception as e:
            print(f"✗ 表情选择器出错: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("测试窗口已启动，点击按钮测试表情选择器功能")
    
    sys.exit(app.exec_())
