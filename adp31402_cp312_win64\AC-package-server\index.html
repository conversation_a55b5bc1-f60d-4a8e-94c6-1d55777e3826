<!DOCTYPE html>
<html>
	<head lang="en">
		<meta charset="utf-8">
		<title>Yoooooo!</title>
	</head>
	<body>
		<canvas id="cavsElem" width="400" height="200">
			Please Update your webbrowser
		</canvas>
		<script>
			/* 绘制圆脸 */
			//获得画布并上下文对象 
			var context = document.getElementById('cavsElem').getContext('2d');
			context.beginPath(); //开始路径
			context.arc(100, 100, 100, 0, 2 * Math.PI, true); //绘制圆形，true为逆时针
			context.closePath(); // 关闭路径
			context.fillStyle = 'yellow'; //设置填充颜色
			context.fill(); //填充
			/* 绘制小嘴 */
			context.beginPath(); //开始路径
			context.strokeStyle = "#fff"; //设置描边颜色
			context.lineWidth = 5;//设置线的粗细
			context.arc(100, 130, 20, Math.PI / 6, 5 * Math.PI / 6, false); //绘制弧形，false为顺时针
			// context.closePath();
			context.stroke(); //描边
			/* 绘制左眼 */
			context.beginPath(); //开始路径
			context.arc(50, 75, 20, 0, 2 * Math.PI, true); //绘制圆形，true为逆时针
			context.closePath(); // 关闭路径
			context.fillStyle = 'white'; //设置填充颜色
			context.fill(); //填充
			
			/* 绘制右眼 */
			context.beginPath(); //开始路径
			context.arc(150, 75, 18, 0, 2 * Math.PI, true); //绘制圆形，true为逆时针
			context.closePath(); // 关闭路径
			context.fillStyle = 'white'; //设置填充颜色
			context.fill(); //填充
			context.stroke(); //描边
		</script>
		<h1>It seems that you might enter <a href="https://github.com/grass-tech/Agentic-AI-Desktop-Pet">Ai Desktop Pet</a> API web page by mistake...</h1>
		<p>If you are using my software, I'll congratulate you find the easter egg.</p>
		<ul>
        <li>http://adp.nekocode.top/ai/free/generate.php</li>
        </ul>
        <p>Guess what it is used to  ;)</p>
	</body>
</html>
