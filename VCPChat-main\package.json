{"name": "vcp-chat-desktop", "version": "1.0.0", "description": "一个为VCP服务器打造的AI聊天桌面客户端", "main": "main.js", "scripts": {"postinstall": "electron-rebuild -f -w selection-hook", "start": "electron .", "pack": "electron-rebuild -f -w selection-hook && electron-builder --dir", "dist": "electron-rebuild -f -w selection-hook && electron-builder"}, "keywords": ["VCP", "AI", "Cha<PERSON>", "Desktop", "Electron"], "author": "莱恩 (由小吉辅助生成)", "license": "MIT", "devDependencies": {"@electron/rebuild": "^3.7.2", "electron": "^36.0.0", "electron-builder": "^26.0.12"}, "dependencies": {"clipboard-event": "^1.6.0", "fs-extra": "^11.2.0", "mammoth": "^1.7.0", "marked": "^12.0.0", "node-global-key-listener": "^0.3.0", "pdf-parse": "^1.1.1", "selection-hook": "^0.9.23", "sharp": "^0.34.2", "ws": "^8.14.2"}, "build": {"appId": "com.vcp.chatdesktop", "productName": "VCP聊天客户端", "files": ["main.js", "preload.js", "renderer.js", "main.html", "image-viewer.html", "text-viewer.html", "style.css", "assets/", "modules/**/*", "node_modules/**/*"], "directories": {"buildResources": "assets"}, "mac": {"category": "public.app-category.productivity", "icon": "assets/icon.icns"}, "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets"}}}