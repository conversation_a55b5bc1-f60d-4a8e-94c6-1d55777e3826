{"schema_version": "1.0", "default_service_id": "edge_tts_online", "current_service_id": "gag_gpt_sovits", "services": [{"id": "local_vits", "name": "本地 VITS 服务", "type": "local_http_vits", "config_file": "configs/tts_local_vits_config.json", "enabled": true, "description": "本地运行的VITS TTS服务"}, {"id": "hf_space_genshin", "name": "在线 VITS (原神/崩坏)", "type": "huggingface_space_vits", "config_file": "configs/tts_hf_genshin_config.json", "enabled": false, "description": "Hugging Face Space 托管的原神角色语音服务"}, {"id": "edge_tts_online", "name": "微软 Edge TTS (在线)", "type": "edge_tts_python_lib", "config_file": "configs/tts_edge_online_config.json", "enabled": true, "description": "使用微软Edge在线服务进行高质量语音合成"}, {"id": "gag_gpt_sovits", "name": "GAG (GPT-SoVITS)", "type": "gpt_sovits", "config_file": "configs/tts_service_gag.json", "enabled": true, "description": "本地运行的GPT-SoVITS服务，支持模型切换"}], "global_settings": {"max_temp_files": 10, "audio_cache_size": 50, "request_timeout": 30, "max_retries": 3, "enable_right_click_stop": true, "enable_auto_stop_on_new_message": true, "show_stop_confirmation": false, "tts_enabled": true}}