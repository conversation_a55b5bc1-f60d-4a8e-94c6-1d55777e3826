# main.py 重构计划

### 1. 目标 (Goal)

当前 `aipet/main.py` 文件已超过3200行，集成了应用主控制器、工具类、HTTP服务和大量配置，导致其结构臃肿，难以维护。

本次重构的目标是将其彻底拆分，将不同的职责分离到各自独立的模块中。这将使 `main.py` 回归其本质：**作为应用程序的入口点，清晰地负责组装和启动各个核心组件**，从而显著提高代码库的模块化、可读性和可维护性。

### 2. 新的文件结构 (Proposed File Structure)

我们将在 `aipet` 目录下创建新的 `core` 和 `services` 子目录，用于存放拆分后的核心逻辑和服务。

**重构后:**
```
aipet/
├── main.py                # 应用入口 (大幅简化)
├── core/
│   ├── __init__.py
│   ├── app_controller.py    # 存放 AppController 主类
│   ├── tts_manager.py       # 存放所有TTS相关的逻辑 (合成、播放、管理)
│   ├── asr_manager.py       # 存放所有ASR相关的逻辑
│   ├── data_manager.py      # 存放用户数据、话题管理和持久化逻辑
│   └── resource_manager.py  # 存放 TempFileManager, resource_path, 路径常量等
├── services/
│   ├── __init__.py
│   └── live2d_api_server.py # 存放 Live2D 的 HTTP API 服务
└── ... (其他现有文件)
```

### 3. 详细重构步骤 (Detailed Refactoring Steps)

我们将继续采纳**增量、持续验证**的重构策略。每完成一个阶段的迁移，我们都会停下来，等待您启动程序进行验证。只有在您确认功能正常后，我们才会进入下一阶段。

**更新后的重构策略:**
根据我们的讨论，我们调整了原有的重构顺序。我们认识到 `AppController` 过于庞大，直接迁移风险较高。因此，我们将采用"由内而外"的策略：首先将 `AppController` 内部的各项独立功能（如TTS、ASR、数据管理等）逐一抽离到新的管理类中，使 `AppController` 不断"瘦身"。当其核心逻辑变得清晰且足够精简后，最后再将这个"骨架"`AppController` 迁移到自己的模块中。

**迁移清单与进度 (Migration Checklist & Progress)**

**阶段 0: 基础设置 (Foundation Setup)**
- [x] 1. 创建新目录: `aipet/core/` 和 `aipet/services/`
- [x] 2. 创建空的Python文件:
    - [x] `aipet/core/__init__.py`
    - [x] `aipet/core/app_controller.py`
    - [x] `aipet/core/tts_manager.py`
    - [x] `aipet/core/asr_manager.py`
    - [x] `aipet/core/data_manager.py`
    - [x] `aipet/core/resource_manager.py`
    - [x] `aipet/services/__init__.py`
    - [x] `aipet/services/live2d_api_server.py`
- **验证点**: 确认文件和目录结构已按预期创建。 (已完成)

**阶段 1: 迁移资源和工具函数**
- [x] 1. 移动 `TempFileManager` 类、`resource_path` 函数和 `configure_ffmpeg_path` 函数到 `aipet/core/resource_manager.py`。
- [x] 2. 移动所有路径和配置相关的常量 (如 `IMAGE_CACHE_DIR`, `API_BASE_URL` 等) 到 `resource_manager.py`。
- [x] 3. 在 `main.py` 中更新导入，从 `core.resource_manager` 导入所需内容。
- **验证点**: 请您启动应用，确认临时文件管理、资源路径查找等基础功能正常。 (已由您确认)

**阶段 2: 抽离TTS管理逻辑 (原阶段3)**
- [ ] 1. 创建一个新的 `TTSManager` 类在 `aipet/core/tts_manager.py` 中。
- [ ] 2. 从 `AppController` 中剪切所有与TTS功能紧密相关的方法（例如 `_synthesize_speech`, `_play_audio_data`, `_tts_worker`, `set_tts_service`, `get_available_tts_services` 等）并粘贴到 `TTSManager` 中。
- [ ] 3. `AppController` 将在其 `__init__` 方法中创建一个 `TTSManager` 的实例，并将自身（`self`）传入，以便 `TTSManager` 可以回调主控制器。
- [ ] 4. 将 `AppController` 中对这些TTS方法的调用改为通过 `self.tts_manager` 实例调用。
- **验证点**: 请您启动应用，全面测试所有TTS相关功能，包括切换服务、更换声音、测试发音以及对话中的自动朗读。

**阶段 3: 抽离ASR管理逻辑 (原阶段4)**
- [ ] 1. 创建一个新的 `ASRManager` 类在 `aipet/core/asr_manager.py` 中。
- [ ] 2. 将所有ASR相关方法（如 `toggle_asr_listening`, `_asr_listen_loop_target`, `_handle_recognized_text_from_asr` 等）从 `AppController` 移动到 `ASRManager`。
- [ ] 3. `AppController` 创建并持有一个 `ASRManager` 实例。
- **验证点**: 请您启动应用，测试语音识别输入功能是否正常。

**阶段 4: 抽离数据和话题管理 (原阶段5)**
- [ ] 1. 创建一个新的 `DataManager` 类在 `aipet/core/data_manager.py` 中。
- [ ] 2. 将所有与用户数据、配置和话题历史记录管理相关的逻辑（例如 `_load_user_data`, `_save_user_data`, `_create_new_topic`, `delete_topic` 等）移入 `DataManager`。
- [ ] 3. `AppController` 创建并持有一个 `DataManager` 实例。
- **验证点**: 请您启动应用，确认历史对话记录能被正确加载，话题切换、新建和删除功能正常，并且应用关闭时数据能被正确保存。

**阶段 5: 迁移Live2D API服务器 (原阶段6)**
- [ ] 1. 将 `Live2DAPIHandler` 内部类和 `_start_http_server` 方法整体移动到 `aipet/services/live2d_api_server.py`。
- [ ] 2. 在新文件中，导出一个 `start_server(app_controller_instance)` 函数。
- [ ] 3. `AppController` 在初始化时调用此 `start_server` 函数来启动服务。
- **验证点**: 请您启动应用，确认Live2D的API（例如通过浏览器访问 `http://localhost:26666/`）依然可以正常工作并能触发模型动作。

**阶段 6: 迁移 `AppController` 骨架 (原阶段2)**
- [ ] 1. 此时 `AppController` 已经被大幅简化，只剩下核心的UI协调和信号连接逻辑。
- [ ] 2. 将这个精简后的 `AppController` 类的完整定义移动到 `aipet/core/app_controller.py`。
- [ ] 3. `main.py` 中将只保留应用的启动逻辑（创建 `QApplication`，实例化 `AppController`，然后调用 `run()`）。
- [ ] 4. 在 `app_controller.py` 中添加所有必要的导入语句以解决依赖关系。
- **验证点**: 请您启动应用，确认主窗口 (`ChatWindow`) 和 Live2D 角色能够正常显示，并且基本的窗口拖动、关闭等交互正常。

**最终阶段: 清理 `main.py`**
- [ ] 1. 移除所有已迁移的类的源代码和函数。
- [ ] 2. 整理和优化文件顶部的导入语句。
- [ ] 3. 最终 `main.py` 将变得非常简洁，主要职责就是设置环境、实例化并运行 `AppController`。
- **验证点**: 请您启动应用程序，进行一次完整的回归测试，确保所有功能完美如初。 