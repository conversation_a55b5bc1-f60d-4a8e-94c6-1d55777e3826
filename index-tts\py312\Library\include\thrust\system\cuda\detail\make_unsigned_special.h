/*
 *  Copyright 2019 NVIDIA Corporation
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

#pragma once

#include <thrust/detail/config.h>

#if defined(_CCCL_IMPLICIT_SYSTEM_HEADER_GCC)
#  pragma GCC system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_CLANG)
#  pragma clang system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_MSVC)
#  pragma system_header
#endif // no system header

THRUST_NAMESPACE_BEGIN
namespace cuda_cub {

namespace detail {

    template<typename Size>
    struct make_unsigned_special;

    template<>
    struct make_unsigned_special<int> { typedef unsigned int type; };

    // this is special, because CUDA's atomicAdd doesn't have an overload
    // for unsigned long, for some godforsaken reason
    template<>
    struct make_unsigned_special<long> { typedef unsigned long long type; };

    template<>
    struct make_unsigned_special<long long> { typedef unsigned long long type; };

}
}
THRUST_NAMESPACE_END

