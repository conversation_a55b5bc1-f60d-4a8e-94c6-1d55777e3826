/* style.css */
@font-face {
    font-family: 'Maven Pro ExtraBold'; /* Corrected font name with space */
    src: url('./assets/font/MavenPro-ExtraBold.ttf') format('truetype');
    font-weight: 800;
    font-style: normal;
}

:root {
    /* --- 默认暗色主题 (墨色系) --- */
    --primary-bg-dark: #1c1c1e;      /* 深墨灰 - 近黑 */
    --secondary-bg-dark: #28282c;    /* 略浅的墨灰 */
    --tertiary-bg-dark: #121212;     /* 聊天区背景 - 纯黑に近い */
    --accent-bg-dark: #3a3a3e;       /* 悬停/选中背景 - 中墨灰 */
    --primary-text-dark: #e0e0e0;    /* 主要文字 - 浅灰 */
    --secondary-text-dark: #a0a0a0;  /* 次要/标题文字 - 中灰 */
    --highlight-text-dark: #6fa8dc;  /* 高亮文字 - 柔和蓝 (点缀色) */
    --border-color-dark: #3a3a3e;    /* 边框颜色 */
    --user-bubble-bg-dark: rgba(56, 120, 173, 0.75);  /* 用户气泡 - 柔和蓝 (点缀色) - 带透明度 */
    --assistant-bubble-bg-dark: rgba(47, 47, 51, 0.75);/* AI气泡 - 深墨灰 - 带透明度 */
    --button-bg-dark: #48484c;       /* 普通按钮背景 - 中墨灰 */
    --button-hover-bg-dark: #58585e; /* 按钮悬停 - 略亮的墨灰 */
    --danger-color-dark: #e57373;    /* 危险操作 - 柔和红 */
    --danger-hover-bg-dark: #ef5350; /* 危险操作悬停 - 略深的柔和红 */
    --input-bg-dark: #222225;        /* 输入框背景 */
    --tool-bubble-bg-dark: #3a3a3e;   /* VCP工具调用气泡背景 */
    --tool-bubble-border-dark: #58585e;
    --notification-bg-dark: #2f2f33;
    --notification-header-bg-dark: #303034; /* 比 secondary-bg-dark (#28282c) 略深 */
    --notification-border-dark: #48484c;
    --scrollbar-track-dark: rgba(40, 40, 40, 0.5);
    --scrollbar-thumb-dark: rgba(100, 100, 100, 0.6);
    --scrollbar-thumb-hover-dark: rgba(120, 120, 120, 0.8);
    --quoted-text-dark: #FFB74D; /* 深色模式引用文本 - 柔和橙色 */

    /* --- 亮色主题 --- */
    --primary-bg-light: #f4f6f8;      /* 非常浅的灰蓝色 */
    --secondary-bg-light: #ffffff;    /* 白色 */
    --tertiary-bg-light: #e9edf0;     /* 聊天区背景，浅灰 */
    --accent-bg-light: #e0e6eb;       /* 悬停/选中背景 */
    --primary-text-light: #2c3e50;    /* 主要文字 - 深灰蓝 */
    --secondary-text-light: #5a6f80;  /* 次要/标题文字 - 中灰蓝 */
    --highlight-text-light: #3498db;  /* 高亮文字 - 清爽蓝 */
    --border-color-light: #e0e6eb;    /* 边框颜色 - 调整为更浅的颜色，与 accent-bg-light 相似 */
    --user-bubble-bg-light: rgba(52, 152, 219, 0.7);  /* 用户气泡 - 清爽蓝 - 带透明度 */
    --assistant-bubble-bg-light: rgba(232, 244, 248, 0.7);/* AI气泡 - 非常浅的蓝 - 带透明度 */
    --button-bg-light: #3498db;       /* 普通按钮背景 - 清爽蓝 */
    --button-hover-bg-light: #2980b9; /* 按钮悬停 - 深一点的蓝 */
    --danger-color-light: #e74c3c;    /* 危险操作 - 红色 */
    --danger-hover-bg-light: #c0392b; /* 危险操作悬停 - 深红 */
    --input-bg-light: #ffffff;        /* 输入框背景 - 白色 */
    --tool-bubble-bg-light: #e0e6eb;   /* VCP工具调用气泡背景 - 浅灰 */
    --tool-bubble-border-light: #b8c0c8;
    --notification-bg-light: #e8f4f8;
    --notification-header-bg-light: #f0f0f0; /* 比 secondary-bg-light (#ffffff) 略深 */
    --notification-border-light: #3498db;
    --scrollbar-track-light: rgba(200, 200, 200, 0.5);
    --scrollbar-thumb-light: rgba(150, 150, 150, 0.6);
    --scrollbar-thumb-hover-light: rgba(120, 120, 120, 0.8);
    --quoted-text-light: #007bff; /* 浅色模式引用文本 - 蓝色 */

    /* --- 通用/当前主题变量 --- */
    /* 默认使用暗色主题变量 */
    --primary-bg: var(--primary-bg-dark);
    --secondary-bg: var(--secondary-bg-dark);
    --tertiary-bg: var(--tertiary-bg-dark);
    --accent-bg: var(--accent-bg-dark);
    --primary-text: var(--primary-text-dark);
    --secondary-text: var(--secondary-text-dark);
    --highlight-text: var(--highlight-text-dark);
    --border-color: var(--border-color-dark);
    --user-bubble-bg: var(--user-bubble-bg-dark);
    --assistant-bubble-bg: var(--assistant-bubble-bg-dark);
    --button-bg: var(--button-bg-dark);
    --button-hover-bg: var(--button-hover-bg-dark);
    --danger-color: var(--danger-color-dark);
    --danger-hover-bg: var(--danger-hover-bg-dark);
    --input-bg: var(--input-bg-dark);
    --tool-bubble-bg: var(--tool-bubble-bg-dark);
    --tool-bubble-border: var(--tool-bubble-border-dark);
    --notification-bg: var(--notification-bg-dark);
    --notification-header-bg: var(--notification-header-bg-dark);
    --notification-border: var(--notification-border-dark);
    --scrollbar-track: var(--scrollbar-track-dark);
    --scrollbar-thumb: var(--scrollbar-thumb-dark);
    --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-dark);
    --quoted-text: var(--quoted-text-dark); /* 新增：引用文本颜色 */

    /* RGB versions for semi-transparent backgrounds */
    --rgb-secondary-bg-dark: 40, 40, 44;
    --rgb-secondary-bg-light: 255, 255, 255;
    /* --- 在这里添加以下两行 --- */
    --shimmer-color-transparent: rgba(224, 224, 224, 0.6); /* 基于您的主文字颜色 #e0e0e0 */
    --shimmer-color-highlight: #ffffff; /* 暗色模式下，用纯白作为高光最合适 */
}

/* --- 亮色主题应用 --- */
body.light-theme {
    --primary-bg: var(--primary-bg-light);
    --secondary-bg: var(--secondary-bg-light);
    --tertiary-bg: var(--tertiary-bg-light);
    --accent-bg: var(--accent-bg-light);
    --primary-text: var(--primary-text-light);
    --secondary-text: var(--secondary-text-light);
    --highlight-text: var(--highlight-text-light);
    --border-color: var(--border-color-light);
    --user-bubble-bg: var(--user-bubble-bg-light);
    --assistant-bubble-bg: var(--assistant-bubble-bg-light);
    --button-bg: var(--button-bg-light);
    --button-hover-bg: var(--button-hover-bg-light);
    --danger-color: var(--danger-color-light);
    --danger-hover-bg: var(--danger-hover-bg-light);
    --input-bg: var(--input-bg-light);
    --tool-bubble-bg: var(--tool-bubble-bg-light);
    --tool-bubble-border: var(--tool-bubble-border-light);
    --notification-bg: var(--notification-bg-light);
    --notification-header-bg: var(--notification-header-bg-light);
    --notification-border: var(--notification-border-light);
    --scrollbar-track: var(--scrollbar-track-light);
    --scrollbar-thumb: var(--scrollbar-thumb-light);
    --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-light);
    --quoted-text: var(--quoted-text-light); /* 新增：引用文本颜色 */
    /* --- 在这里添加以下两行 --- */
    --shimmer-color-transparent: rgba(44, 62, 80, 0.3); /* 基于您的亮色主文字 #2c3e50 - 降低透明度以增强对比 */
    --shimmer-color-highlight: #000000; /* 亮色模式下，用纯黑作为高光有强烈的对比感 */
}

body.light-theme .main-content {
    background-image: url('assets/light.jpeg');
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    margin: 0;
    padding: 0;
    background-color: var(--primary-bg);
    color: var(--primary-text);
    display: flex;
    flex-direction: column; /* Changed to column to stack title-bar and container */
    height: 100vh;
    overflow: hidden;
    font-size: 15px;
    -webkit-font-smoothing: antialiased; /* 提升字体渲染清晰度 */
    -moz-osx-font-smoothing: grayscale; /* 提升字体渲染清晰度 (Firefox) */
    text-rendering: optimizeLegibility; /* 优化文本渲染 */
}

/* --- Custom Title Bar --- */
.title-bar {
    height: 30px; /* Adjust as needed */
    background-color: var(--secondary-bg);
    color: var(--primary-text);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px 0 10px; /* Reduced right padding slightly */
    /* border-bottom: 1px solid var(--border-color); */
    -webkit-app-region: drag;
    flex-shrink: 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    box-sizing: border-box; /* Added */
}

.title-bar-text {
    font-size: 0.9em;
    margin-left: 0px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    min-width: 0; /* Allow shrinking */
    margin-right: 10px; /* Add some space between text and controls */
}

.title-bar-logo {
   height: 20px; /* Adjust based on title bar height */
   margin-right: 8px;
   vertical-align: middle; /* Align with text */
}

.title-bar-controls {
    display: flex;
    align-items: center;
    -webkit-app-region: no-drag;
    flex-shrink: 0; /* Crucial: Do not allow this container to shrink */
    height: 100%; /* Ensure controls container fills title bar height */
    min-width: 120px; /* Ensure enough space for 4 buttons (4 * 30px) */
    flex-wrap: nowrap; /* Force buttons to stay on one line */
}

.title-bar-button {
    background: transparent;
    border: none;
    color: var(--secondary-text);
    padding: 0;
    width: 30px; /* Standard width for window controls */
    height: 30px; /* Match title-bar height explicitly */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    box-sizing: border-box;
    flex-shrink: 0; /* Buttons themselves should not shrink */
}

.title-bar-button svg {
    width: 10px;
    height: 10px;
    fill: currentColor;
}

.title-bar-button:hover {
    background-color: var(--accent-bg);
    color: var(--highlight-text);
}

.title-bar-button.close-button:hover {
    background-color: var(--danger-color);
    color: white;
}


.container {
    display: flex;
    width: 100%;
    height: calc(100vh - 30px); /* Adjust based on title-bar height */
    margin-top: 30px; /* Offset for the fixed title bar */
    overflow: hidden; /* Prevent scrollbars on body due to resizers */
}

/* --- Resizers --- */
.resizer {
    background-color: var(--secondary-bg); 
    width: 5px;
    cursor: col-resize;
    flex-shrink: 0;
    z-index: 100; 
    transition: background-color 0.2s ease; 
    pointer-events: auto; 
}
.resizer:hover {
    background-color: var(--user-bubble-bg);
}


/* --- Sidebar (Agent List) --- */
.sidebar {
    width: 260px; /* Default width */
    min-width: 180px; 
    max-width: 600px; 
    background-color: var(--secondary-bg);
    padding: 15px 15px 15px 17px; 
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    transition: width 0.3s cubic-bezier(0.25, 0.1, 0.25, 1); 
    overflow-x: hidden; 
    flex-shrink: 0; 
}
 
/* Sidebar Tabs */
.sidebar-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-tab-button {
    flex-grow: 1;
    padding: 10px 5px;
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent; /* For active indicator */
    color: var(--secondary-text);
    cursor: pointer;
    font-size: 0.95em;
    text-align: center;
    transition: background-color 0.2s ease, color 0.2s ease, border-bottom-color 0.2s ease;
    margin-bottom: -1px; /* Align with bottom border of container */
}

.sidebar-tab-button:hover {
    background-color: var(--accent-bg);
    color: var(--highlight-text);
}

.sidebar-tab-button.active {
    color: var(--highlight-text);
    border-bottom-color: var(--user-bubble-bg);
    font-weight: 500;
}

.sidebar-tab-content {
    display: flex;
    flex-direction: column;
    flex-grow: 0; 
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transform: translateY(10px);
    pointer-events: none;
    transition: opacity 0.25s ease-out,
                transform 0.25s ease-out,
                max-height 0.35s cubic-bezier(0.25, 0.1, 0.25, 1),
                padding-top 0.35s cubic-bezier(0.25, 0.1, 0.25, 1),
                padding-bottom 0.35s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.sidebar-tab-content:not(.active) {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    border-top-width: 0 !important; 
    border-bottom-width: 0 !important;
}

.sidebar-tab-content.active {
    opacity: 1;
    max-height: 100vh; 
    transform: translateY(0);
    overflow-y: auto;
    pointer-events: auto;
    overflow-x: hidden;
    flex-grow: 1; 
}
/* End Sidebar Tabs */

.sidebar h2 {
    color: var(--secondary-text);
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 1.4em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.settings-header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px; 
    padding-bottom: 10px; 
    border-bottom: 1px solid var(--border-color); 
}

.settings-header-bar h2 {
    margin: 0; 
    padding: 0; 
    border-bottom: none; 
    text-align: left; 
    flex-grow: 1; 
}

.settings-header-bar .global-settings-btn {
    width: auto; 
    padding: 8px 15px; 
    font-size: 0.9em; 
    margin-left: 15px; 
}

.agent-list, .topic-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    overflow-x: hidden; 
    flex-grow: 1;
}
 
.agent-list li, .topic-list .topic-item {
    padding: 10px 12px;
    margin-bottom: 6px;
    border-radius: 8px; 
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.2s ease, transform 0.1s ease;
}

.agent-list li:hover, .topic-list .topic-item:hover {
    background-color: var(--accent-bg);
    transform: translateX(2px);
}
.agent-list li.active { 
    background-color: var(--user-bubble-bg);
    color: white;
    font-weight: 500;
}
.agent-list li.active .agent-name {
    color: white;
}
.agent-list li.active img.avatar {
    border-color: var(--highlight-text);
}

.agent-list img.avatar, .topic-list .topic-item img.avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
    border: 2px solid var(--button-bg);
}

.agent-list .agent-name, .topic-list .topic-item .agent-name {
    font-weight: 400;
    font-size: 1.05em;
    color: var(--primary-text);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1; 
}

.topic-list .topic-item .message-count {
    margin-left: auto; 
    padding: 3px 8px;
    background-color: var(--accent-bg);
    color: var(--highlight-text);
    border-radius: 10px;
    font-size: 0.85em; 
    min-width: 20px; 
    text-align: center;
    font-weight: bold; 
    font-family: "Arial Rounded MT Bold", "Helvetica Rounded", Arial, sans-serif; 
}

#tabContentTopics p { 
    padding: 15px;
    text-align: center;
    color: var(--secondary-text);
}


.sidebar-actions {
    margin-top: auto;
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: row; /* Changed to row for horizontal buttons */
    gap: 8px;
    justify-content: space-between; /* Distribute space between buttons */
}
.sidebar-button {
    background-color: var(--button-bg);
    color: var(--primary-text);
    border: 1px solid transparent;
    padding: 10px;
    width: 100%;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.95em;
    transition: background-color 0.2s ease;
    text-align: center;
}

.sidebar-button.small-button {
    flex: 1; /* Make buttons take equal width */
    min-width: 100px; /* Ensure a minimum width */
    max-width: 140px; /* Increase max-width slightly */
    flex-shrink: 0;
}
.sidebar-button:hover {
    background-color: var(--button-hover-bg);
    border-color: var(--button-hover-bg);
}

body.light-theme .sidebar-button {
    color: #ffffff; 
    border: 1px solid var(--button-bg);
}

body.light-theme .sidebar-button:hover {
    color: #ffffff;
    border-color: var(--button-hover-bg);
}

/* --- Main Content (Chat Area) --- */
.main-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--tertiary-bg); 
    background-image: url('assets/dark.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    overflow: hidden; 
}

.chat-header {
    padding: 12px 20px;
    background-color: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 36px; 
    flex-shrink: 0; 
}
.chat-header h3 {
    margin: 0;
    font-size: 1.2em;
    color: var(--highlight-text);
    font-weight: 500;
}
.chat-actions .header-button {
    background: transparent;
    border: 1px solid var(--button-bg);
    color: var(--secondary-text);
    padding: 0 10px; 
    height: 32px; 
    border-radius: 8px; 
    cursor: pointer;
    margin-left: 8px; 
    font-size: 0.9em;
    transition: background-color 0.2s, border-color 0.2s;
    display: inline-flex; 
    align-items: center; 
    justify-content: center; 
    box-sizing: border-box; 
}
.chat-actions .header-button svg {
    stroke: currentColor; 
    width: 18px; 
    height: 18px;
}
.chat-actions .header-button#themeToggleBtn {
    padding: 0 10px; 
    position: relative;
    top: 3.4px; 
}

.chat-actions .header-button#toggleNotificationsBtn {
    padding: 0 10px; 
    position: relative;
    top: 3.4px; 
}

.theme-icon {
    width: 26px; 
    height: 26px; 
}

#sun-icon {
    stroke: #FFD700; 
    fill: #FFD700;   
}

#moon-icon {
    stroke: #6495ED; 
    fill: #6495ED;   
}

.chat-actions .header-button:hover {
    background-color: var(--button-hover-bg); 
    border-color: var(--button-hover-bg);
    color: var(--primary-text); 
}

button#devButton.header-button {
    background-color: var(--button-bg); 
    color: var(--secondary-text); 
    border: 1px solid var(--button-bg); 
    padding: 0 10px; 
    height: 32px; 
    border-radius: 8px; 
    cursor: pointer;
    font-size: 0.9em; 
    transition: background-color 0.2s, border-color 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin-left: 8px; 
    width: auto;
}

button#devButton.header-button:hover {
    background-color: var(--button-hover-bg);
    border-color: var(--button-hover-bg);
    color: var(--primary-text); 
}

body.light-theme button#devButton.header-button {
    color: #ffffff; 
    border: 1px solid var(--button-bg-light); 
}

body.light-theme button#devButton.header-button:hover {
    color: #ffffff; 
    border-color: var(--button-hover-bg-light); 
}

body.light-theme .chat-actions .header-button {
    color: var(--button-bg);
}
body.light-theme .chat-actions .header-button:hover {
    color: #ffffff;
}


.chat-messages-container {
    flex-grow: 1;
    overflow-y: auto; 
    display: flex; 
    flex-direction: column-reverse; 
}

.chat-messages {
    padding: 15px 20px;
    display: flex;
    flex-direction: column; 
}

/* --- Message Item QQ Style --- */
.message-item {
    margin-bottom: 18px; /* Increased margin for QQ style */
    max-width: 100%; 
    display: flex;
    align-items: flex-start; 
    gap: 10px; 
    position: relative; 
}

.message-item.user {
    flex-direction: row-reverse; 
}

.message-item.assistant {
    flex-direction: row; 
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0; 
    /* border: 1px solid var(--border-color); */ /* Original static border */
    border: 2px solid transparent; /* Default transparent border, thickness can be adjusted */
    transition: border-color 0.3s ease; /* Smooth transition if color changes */
    margin-top: 0px;
}

.name-time-block {
    display: flex;
    flex-direction: column;
    margin-top: 0; 
    line-height: 1.3;
}

.message-item.user .name-time-block {
    align-items: flex-end;
    /* margin-right: 5px; */ /* Removed as bubble is now below */
}

.message-item.assistant .name-time-block {
    align-items: flex-start;
    /* No specific margin needed here by default */
}

/* New wrapper for name/time and bubble column */
.details-and-bubble-wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px; /* Small gap between name/time block and the bubble */
    flex-grow: 1; /* Allow this wrapper to take available horizontal space */
    min-width: 0; /* Fixes potential overflow issues with flex children */
}

/* Align content within the wrapper based on user/assistant */
.message-item.user .details-and-bubble-wrapper {
    align-items: flex-end; /* Aligns name/time and bubble to the right */
}

.message-item.assistant .details-and-bubble-wrapper {
    align-items: flex-start; /* Aligns name/time and bubble to the left */
}

.message-item .sender-name {
    /* font-weight: 500; */ /* Replaced by more specific bold below */
    font-weight: bold; /* Make all sender names bold */
    margin-bottom: 2px;
    font-size: 0.85em;
}

.message-item.assistant .sender-name {
    color: var(--highlight-text); 
}
.message-item.user .sender-name {
    color: var(--secondary-text); 
    /* opacity: 0.9; */ /* Remove opacity to make it more solid and highlighted */
}

.message-item .message-timestamp {
    font-size: 0.7em; 
    color: var(--secondary-text);
    opacity: 0.8;
}

/* .md-content is the actual bubble */
.message-item .md-content {
    padding: 10px 15px;
    border-radius: 10px; 
    max-width: 70%; 
    word-wrap: break-word; 
    line-height: 1.5; 
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24); 
    position: relative; 
    backdrop-filter: blur(8px); /* Keep if desired */
    -webkit-backdrop-filter: blur(8px); /* Keep if desired */
}

.message-item.user .md-content {
    background-color: var(--user-bubble-bg);
    color: var(--primary-text); 
    border-bottom-right-radius: 4px; 
}
body.light-theme .message-item.user .md-content {
    background-color: var(--user-bubble-bg-light);
    color: #ffffff; 
}

.message-item.assistant .md-content {
    background-color: var(--assistant-bubble-bg);
    color: var(--primary-text); 
    border-bottom-left-radius: 4px; 
}
body.light-theme .message-item.assistant .md-content {
    background-color: var(--assistant-bubble-bg-light);
    color: var(--primary-text-light); 
    border: 1px solid #d0d8e0; 
}

/* System messages special layout */
.message-item.system.system-message-layout {
    justify-content: center; 
    padding: 8px 12px;
    border-radius: 10px;
    background-color: var(--accent-bg); 
    color: var(--secondary-text);
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    /* Undo flex for system messages if they are simple */
    display: block; 
}
.message-item.system.system-message-layout .md-content {
    width: 100%;
    max-width: 100%; /* Override bubble max-width for system */
    box-shadow: none;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background-color: transparent;
    padding: 0;
}

/* Welcome bubble for initial "no item selected" message */
.message-item.system.welcome-bubble {
    background-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.65); /* Default to dark theme's secondary bg with opacity */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.3);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    color: var(--primary-text);
    padding: 8px 20px; /* Minimal vertical padding */
    border-radius: 12px;
    max-width: 500px;
    margin: 30px auto;
    display: block; /* Changed from inline-block, parent is already block and centered */
    text-align: center;
}

body.light-theme .message-item.system.welcome-bubble {
    background-color: rgba(var(--rgb-secondary-bg-light, 255, 255, 255), 0.7);
    border: 1px solid rgba(var(--rgb-secondary-bg-light, 255, 255, 255), 0.4);
    color: var(--primary-text-light);
}

.message-item.system.welcome-bubble p {
    margin: 0; /* Ensure no vertical margin on the paragraph itself */
    padding: 0; /* Ensure no vertical padding on the paragraph itself */
    line-height: 1.2; /* Reset to a more standard single line height */
    font-size: 1em;
}
/* Removed p:last-child rule as it's no longer necessary */


.message-item.thinking .md-content .thinking-indicator,
.message-item.streaming .md-content .thinking-indicator {
    display: inline-block; 
    padding: 5px 0; 
}

.message-controls {
    position: absolute;
    top: 2px;
    /* Adjust right/left based on user/assistant */
    display: none; 
    z-index: 10;
}
.message-item.user .message-controls {
    left: 5px; /* For user messages if bubble is on the left */
}
.message-item.assistant .message-controls {
    right: 5px;
}

.message-item:hover .message-controls {
    display: flex; 
}

.message-edit-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: var(--primary-text);
    padding: 3px 6px; 
    font-size: 0.8em;
    border-radius: 5px; 
    cursor: pointer;
    margin-left: 5px;
}
.message-edit-btn:hover {
    background: rgba(255,255,255,0.2);
}


.message-item .md-content img, 
.message-item .md-content video, 
.message-item .md-content audio {
    max-width: 100%;
    border-radius: 8px;
    margin-top: 8px;
    display: block;
}
.message-item .md-content pre { 
    background-color: rgba(0,0,0,0.2);
    padding: 10px;
    border-radius: 6px;
    white-space: pre-wrap; 
    word-break: break-all;   
    overflow-x: auto; 
    font-size: 0.9em;
    margin-top: 8px;
    border: 1px solid var(--border-color);
}
/* Attachments inside the bubble */
.message-item .md-content .message-attachments {
    display: flex;
    flex-wrap: wrap; 
    gap: 8px; 
    margin-top: 8px;
}

.topic-timestamp-bubble {
    display: none; 
    text-align: center;
    padding: 3px 12px; 
    font-size: 0.75em; 
    color: var(--secondary-text);
    background-color: var(--accent-bg); 
    border-radius: 12px; 
    margin: 6px auto 8px auto; 
    max-width: fit-content; 
    box-shadow: 0 1px 2px rgba(0,0,0,0.1); 
    opacity: 0.8; 
}


.vcp-tool-request-bubble { /* This is for the old style, might conflict or be replaced by new .vcp-tool-use-bubble */
    background-color: var(--tool-bubble-bg);
    border: 1px solid var(--tool-bubble-border);
    border-radius: 8px;
    padding: 10px 15px;
    margin-top: 10px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.9em;
    color: #c5c8c6; 
    white-space: pre-wrap; 
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}
.vcp-tool-request-bubble strong { 
    color: #81a2be; 
}
.vcp-tool-request-bubble span.vcp-param-value { 
    color: #b5bd68; 
}


.chat-input-area {
    padding-top: 8px; 
    padding-right: 15px;
    padding-bottom: 12px; 
    padding-left: 15px;
    background-color: var(--secondary-bg);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: flex-end; 
    flex-wrap: wrap; 
    flex-shrink: 0; /* Prevent input area from shrinking */
}

.attachment-preview-area .file-preview { /* This is the old attachment preview style in input area */
    background-color: var(--accent-bg);
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 0.8em;
    display: flex;
    align-items: center;
}
.attachment-preview-area .file-preview button {
    background: none;
    border: none;
    color: var(--danger-color);
    margin-left: 5px;
    cursor: pointer;
    padding: 0;
    font-size: 1.1em;
}


#messageInput {
    flex-grow: 1;
    padding-top: 8px; 
    padding-right: 12px;
    padding-bottom: 10px;
    padding-left: 12px;
    border: 1px solid var(--border-color);
    border-radius: 20px; 
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 1em;
    resize: none; 
    margin-right: 10px;
    max-height: 150px; 
    overflow-y: auto;
    line-height: 1.4;
}
#messageInput:focus {
    outline: none;
    border-color: var(--user-bubble-bg);
    box-shadow: 0 0 0 2px rgba(61, 90, 128, 0.3);
}

#sendMessageBtn, #attachFileBtn {
    background-color: var(--button-bg); 
    border: none;
    border-radius: 50%; 
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    padding: 0;
    margin-left: 8px;
}
#sendMessageBtn svg, #attachFileBtn svg {
    width: 24px; 
    height: 24px; 
    stroke: var(--secondary-text); 
    fill: var(--secondary-text);   
}
body.light-theme #sendMessageBtn svg, body.light-theme #attachFileBtn svg {
    stroke: #ffffff; 
    fill: #ffffff; 
}

#sendMessageBtn:hover, #attachFileBtn:hover {
    background-color: var(--button-hover-bg);
}

/* --- Notifications Sidebar --- */
.notifications-sidebar {
    width: 300px; /* Default width */
    min-width: 220px; /* Adjusted min-width */
    max-width: 600px; /* Added max-width */
    background-color: var(--secondary-bg);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    padding: 0; 
    transition: width 0.3s cubic-bezier(0.25, 0.1, 0.25, 1), 
                min-width 0.3s cubic-bezier(0.25, 0.1, 0.25, 1), 
                padding 0.3s cubic-bezier(0.25, 0.1, 0.25, 1),
                opacity 0.3s ease-out, 
                transform 0.3s ease-out;
    overflow: hidden; 
    flex-shrink: 0; /* Prevent shrinking */
}

.notifications-sidebar:not(.active) {
    width: 0 !important; 
    min-width: 0 !important; 
    padding-left: 0 !important; 
    padding-right: 0 !important; 
    border-left-width: 0 !important; 
    opacity: 0;
    transform: translateX(100%); /* Slide out to the right */
}

.notifications-header {
    padding: 10px 15px; 
    background-color: var(--notification-header-bg); 
    display: flex;
    justify-content: space-between; 
    align-items: center;
    border-bottom: none; 
    flex-shrink: 0; /* Prevent header from shrinking */
}

.notification-header-actions {
    display: flex;
    align-items: center;
    gap: 8px; 
}

.datetime-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start; 
}

#clearNotificationsBtn, .notifications-header .header-button { 
    background: transparent;
    border: 1px solid var(--button-bg);
    color: var(--secondary-text);
    font-size: 0.8em;
    padding: 4px 8px;
    border-radius: 6px; 
    cursor: pointer;
    display: inline-flex; 
    align-items: center;
    justify-content: center;
    height: 30px; 
    box-sizing: border-box;
}
#clearNotificationsBtn:hover, .notifications-header .header-button:hover {
    background-color: var(--button-hover-bg);
    color: var(--primary-text);
    border-color: var(--button-hover-bg);
}
body.light-theme #clearNotificationsBtn, body.light-theme .notifications-header .header-button {
    color: var(--button-bg);
}
body.light-theme #clearNotificationsBtn:hover, body.light-theme .notifications-header .header-button:hover {
    color: #ffffff;
}

.notifications-header .header-button#openAdminPanelBtn {
    padding: 0 8px; 
}
.notifications-header .header-button svg {
    stroke: currentColor;
    width: 18px; 
    height: 18px; 
}

.notifications-status {
    padding: 8px 15px;
    font-size: 0.85em;
    background-color: var(--tertiary-bg);
    color: var(--secondary-text);
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    flex-shrink: 0; /* Prevent status bar from shrinking */
}
.notifications-status.status-open { background-color: #2e7d32; color: white;}
.notifications-status.status-closed { background-color: #c62828; color: white;}
.notifications-status.status-error { background-color: #b71c1c; color: white;}
.notifications-status.status-connecting { background-color: #f9a825; color: black;}


.notifications-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    flex-grow: 1;
}
.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9em;
    background-color: var(--notification-bg);
    color: var(--primary-text); 
}
.notification-item:last-child {
    border-bottom: none;
}
.notification-item strong {
    color: var(--highlight-text);
    display: block;
    margin-bottom: 4px;
}
.notification-item pre { 
    background-color: rgba(0,0,0,0.2);
    padding: 6px;
    border-radius: 4px;
    font-size: 0.85em;
    margin-top: 5px;
    max-height: 100px;
    overflow: auto;
    white-space: pre-wrap;
    word-break: break-all;
}
.notification-timestamp {
    font-size: 0.75em;
    color: var(--secondary-text);
    opacity: 0.7;
    display: block;
    text-align: right;
    margin-top: 5px;
}
 
.notes-section {
    padding: 10px 15px;
    display: flex;
    justify-content: flex-end; 
    align-items: center;
    background-color: var(--secondary-bg); 
    padding-top: 10px; 
    padding-bottom: 10px;
    flex-shrink: 0; /* Prevent notes section from shrinking */
}

.notifications-sidebar > .section-divider { 
    border: none;
    margin: 0; 
    width: 100%;
    box-sizing: border-box; 
    padding: 0 15px; 
}

.notes-section #openTranslatorBtn,
.notes-section #openNotesBtn {
    background-color: var(--button-bg);
    color: var(--secondary-text);
    border: 1px solid var(--button-bg);
    padding: 0 10px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.2s, border-color 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin-left: 8px;
    width: auto;
}

.notes-section #openTranslatorBtn:hover,
.notes-section #openNotesBtn:hover {
    background-color: var(--button-hover-bg);
    border-color: var(--button-hover-bg);
    color: var(--primary-text);
}

.notes-section #openTranslatorBtn svg,
.notes-section #openNotesBtn svg {
    stroke: currentColor;
    width: 18px;
    height: 18px;
}

body.light-theme .notes-section #openTranslatorBtn,
body.light-theme .notes-section #openNotesBtn {
    color: #ffffff;
    border: 1px solid var(--button-bg-light);
}

body.light-theme .notes-section #openTranslatorBtn:hover,
body.light-theme .notes-section #openNotesBtn:hover {
    color: #ffffff;
    border-color: var(--button-hover-bg-light);
}

/* --- Modals --- */
.modal {
    display: none; 
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.6);
    align-items: center;
    justify-content: center;
}
.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--secondary-bg) !important; /* Ensure this overrides for cropper */
    margin: auto;
    padding: 25px 30px;
    border: 1px solid var(--border-color);
    border-radius: 12px; 
    width: 90%;
    max-width: 550px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    position: relative;
    color: var(--primary-text);
}

#avatarCropperModal .modal-content { /* Specific styles for cropper modal if needed */
    color: var(--primary-text);
}
.modal-content h2 {
    margin-top: 0;
    color: var(--highlight-text);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}
.modal-content label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--secondary-text);
}
.modal-content input[type="text"],
.modal-content input[type="url"],
.modal-content input[type="password"],
.modal-content input[type="number"],
.modal-content textarea,
.modal-content select {
    width: calc(100% - 20px);
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 8px; 
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 1em;
}
.modal-content input[type="file"] {
    margin-bottom: 15px;
}
.modal-content textarea {
    min-height: 80px;
    resize: vertical;
}
.modal-content button[type="submit"], .modal-content .danger-button {
    background-color: var(--user-bubble-bg);
    color: #ffffff;
    padding: 10px 18px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s;
}
.modal-content button[type="submit"]:hover {
    background-color: var(--button-hover-bg);
}

.button-primary {
    background-color: var(--user-bubble-bg);
    color: #ffffff;
    padding: 10px 18px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s;
}

.button-primary:hover {
    background-color: var(--button-hover-bg);
}

body.light-theme .button-primary {
    background-color: var(--user-bubble-bg-light);
    color: #ffffff;
}

body.light-theme .button-primary:hover {
    background-color: var(--button-hover-bg-light);
}

.button-secondary {
    background-color: var(--user-bubble-bg); /* Same as primary */
    color: #ffffff; /* Same as primary */
    padding: 10px 18px;
    border: none; /* Same as primary */
    border-radius: 8px; /* Same as primary */
    cursor: pointer; /* Same as primary */
    font-size: 1em; /* Same as primary */
    transition: background-color 0.2s; /* Same as primary */
}

.button-secondary:hover {
    background-color: var(--button-hover-bg); /* Same as primary */
}

body.light-theme .button-secondary {
    background-color: var(--user-bubble-bg-light); /* Same as primary */
    color: #ffffff; /* Same as primary */
}

body.light-theme .button-secondary:hover {
    background-color: var(--button-hover-bg-light); /* Same as primary */
}
body.light-theme .modal-content button[type="submit"] {
    color: #ffffff;
}

.modal-content .danger-button {
    background-color: var(--danger-color);
    color: #ffffff; 
}
.modal-content .danger-button:hover {
    background-color: var(--danger-hover-bg);
}
body.light-theme .modal-content .danger-button {
    color: #ffffff;
}
.form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px; /* 为按钮之间添加间距 */
}


.close-button {
    color: #aaa;
    position: absolute;
    top: 0px;
    right: 8px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}
.close-button:hover,
.close-button:focus {
    color: var(--highlight-text);
    text-decoration: none;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
    border-radius: 4px;
}
::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
}

.sidebar .agent-list::-webkit-scrollbar,
.sidebar .topic-list::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.sidebar .agent-list::-webkit-scrollbar-thumb,
.sidebar .topic-list::-webkit-scrollbar-thumb {
    background-color: transparent; 
    border-radius: 4px;
    transition: background-color 0.3s ease-in-out;
}

.sidebar .agent-list::-webkit-scrollbar-track,
.sidebar .topic-list::-webkit-scrollbar-track {
    background-color: transparent; 
    border-radius: 4px;
    transition: background-color 0.3s ease-in-out;
}

.sidebar .agent-list:hover::-webkit-scrollbar-thumb,
.sidebar .topic-list:hover::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb); 
}

.sidebar .agent-list:hover::-webkit-scrollbar-track,
.sidebar .topic-list:hover::-webkit-scrollbar-track {
    background-color: var(--scrollbar-track); 
}

.sidebar .agent-list:hover::-webkit-scrollbar-thumb:hover,
.sidebar .topic-list:hover::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover);
}

/* Markdown specific styling for chat messages */
.message-item .md-content p { margin: 0 0 0.5em 0; }
.message-item .md-content h1, .message-item .md-content h2, .message-item .md-content h3 { margin-top: 0.8em; margin-bottom: 0.4em; color: var(--highlight-text); }
.message-item .md-content ul, .message-item .md-content ol { margin-left: 20px; padding-left: 0; }
.message-item .md-content blockquote { border-left: 3px solid var(--user-bubble-bg); margin-left: 0; padding-left: 1em; color: var(--secondary-text); }
.message-item .md-content code { background-color: rgba(0,0,0,0.2); padding: 0.2em 0.4em; border-radius: 3px; font-size: 0.9em; }
.message-item .md-content pre code { padding: 0; background: none; } 

.message-edit-textarea {
    /* width: 500px;  No longer fixed width */
    min-width: 250px; 
    min-height: 50px;
    padding: 8px;
    margin-top: 5px;
    margin-bottom: 5px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-family: inherit;
    font-size: 0.95em;
    resize: both; 
    max-width: 100%; 
    box-sizing: border-box;
    /* Will be a flex child of .message-item.message-item-editing */
    flex-grow: 1;
}

.message-edit-controls {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 5px;
    /* Will be a flex child or sibling of textarea */
    width: 100%; /* if it's a block below textarea */
}
.message-edit-controls button {
    padding: 5px 10px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 0.9em;
    background-color: var(--button-bg);
    color: var(--primary-text); 
    transition: background-color 0.2s ease;
}
.message-edit-controls button:hover {
    background-color: var(--button-hover-bg);
}
body.light-theme .message-edit-controls button {
    color: #ffffff; 
}

/* Editing mode for QQ Style */
.message-item.message-item-editing {
    flex-direction: column; /* Stack textarea and controls vertically */
    align-items: stretch; /* Make children (textarea, controls) take full width */
}
.message-item.message-item-editing .chat-avatar,
.message-item.message-item-editing .name-time-block {
    display: none; /* Hide avatar and name/time block when editing */
}
.message-item.message-item-editing .md-content {
    display: none; /* Hide the original bubble content */
}

/* Ensure the details-and-bubble-wrapper is also hidden when editing, if it exists */
.message-item.message-item-editing .details-and-bubble-wrapper {
    display: none;
}


.context-menu {
    position: fixed;
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    padding: 5px 0;
    z-index: 1001; 
    min-width: 150px;
}
.context-menu-item {
    padding: 8px 15px;
    color: var(--primary-text);
    cursor: pointer;
    font-size: 0.9em;
}
.context-menu-item:hover {
    background-color: var(--accent-bg);
    color: var(--highlight-text);
}
.context-menu-item.danger-text {
    color: var(--danger-color);
}
.context-menu-item.danger-text:hover {
    background-color: var(--danger-hover-bg);
    color: white; 
}


@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        position: absolute; 
        z-index: 100;
        transform: translateX(-100%); 
        transition: transform 0.3s ease-in-out;
    }
    .sidebar.open {
        transform: translateX(0);
    }
    .main-content {
        width: 100%;
    }
    .notifications-sidebar {
        display: none; 
    }
    /* .message-item {
        max-width: 90%; // This might conflict with flex based .md-content max-width
    } */
    .message-item .md-content {
        max-width: 85%; /* Adjust bubble width for smaller screens */
    }
    .modal-content {
        width: 95%;
        padding: 20px;
    }
}

#tabContentSettings {
    padding: 15px;
    box-sizing: border-box;
}

#agentSettingsContainer {
    width: 100%;
    box-sizing: border-box;
}

#agentSettingsContainer h3 { 
    color: var(--highlight-text);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}


#agentSettingsForm {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 12px; 
}

#agentSettingsForm > div { 
    display: flex;
    flex-direction: column; 
    gap: 6px; 
}

#agentSettingsForm label {
    display: block;
    font-weight: 500;
    color: var(--secondary-text);
    font-size: 0.9em;
}

#agentSettingsForm input[type="text"],
#agentSettingsForm input[type="url"],
#agentSettingsForm input[type="password"],
#agentSettingsForm input[type="number"],
#agentSettingsForm input[type="file"],
#agentSettingsForm textarea,
#agentSettingsForm select {
    width: 100%;
    box-sizing: border-box;
    padding: 9px 10px;
    margin-bottom: 0; 
    border-radius: 8px; 
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 0.95em;
}
#agentSettingsForm input[type="file"] {
    padding: 5px; 
}


#agentSettingsForm textarea {
    min-height: 80px;
    resize: vertical;
}

#agentSettingsForm .form-actions {
    margin-top: 15px; 
    display: flex;
    flex-direction: column; /* Make buttons stack vertically */
    justify-content: flex-start;
    gap: 10px;
}

#agentSettingsForm .form-actions button {
    padding: 10px 18px; 
    font-size: 1em;    
    border-radius: 8px; 
    border: none;       
    cursor: pointer;    
    transition: background-color 0.2s; 
    color: #ffffff; 
}

#agentSettingsForm .form-actions button[type="submit"] {
    background-color: var(--user-bubble-bg); 
}
#agentSettingsForm .form-actions button[type="submit"]:hover {
    background-color: var(--button-hover-bg);
}

#agentSettingsForm .form-actions .danger-button {
    background-color: var(--danger-color);
}
#agentSettingsForm .form-actions .danger-button:hover {
    background-color: var(--danger-hover-bg);
}

body.light-theme #agentSettingsForm .form-actions button[type="submit"] {
    background-color: var(--user-bubble-bg-light); 
}
body.light-theme #agentSettingsForm .form-actions button[type="submit"]:hover {
    background-color: var(--button-hover-bg-light);
}
body.light-theme #agentSettingsForm .form-actions .danger-button {
    background-color: var(--danger-color-light); 
}
body.light-theme #agentSettingsForm .form-actions .danger-button:hover {
    background-color: var(--danger-hover-bg-light);
}

#selectAgentPromptForSettings {
    text-align: center;
    padding: 20px;
    color: var(--secondary-text);
}

/* --- Group Settings Specific Styles --- */
#groupSettingsContainer {
    width: 100%;
    box-sizing: border-box;
    /* Similar to #agentSettingsContainer if needed, or rely on parent #tabContentSettings padding */
}

#groupSettingsContainer h3 { /* Assuming a title like agent settings */
    color: var(--highlight-text);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

#groupSettingsForm {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

#groupSettingsForm .form-group { /* Common class for form sections */
    display: flex;
    flex-direction: column;
    gap: 6px;
}

#groupSettingsForm .form-group-inline { /* For checkbox and label on same line */
    display: flex;
    align-items: center;
    gap: 8px; /* Space between checkbox and label */
}

#groupSettingsForm .form-group-inline label {
    margin-bottom: 0; /* Remove bottom margin from label */
    display: inline-flex; /* Allow label to be inline with checkbox */
    align-items: center;
    cursor: pointer;
}

#groupSettingsForm label {
    display: block;
    font-weight: 500;
    color: var(--secondary-text);
    font-size: 0.9em;
}

#groupSettingsForm input[type="text"],
#groupSettingsForm input[type="file"],
#groupSettingsForm textarea,
#groupSettingsForm select {
    width: 100%;
    box-sizing: border-box;
    padding: 9px 10px;
    margin-bottom: 0;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 0.95em;
}
#groupSettingsForm input[type="file"] {
    padding: 5px;
}

#groupSettingsForm textarea {
    min-height: 60px; /* Slightly smaller for group prompts */
    resize: vertical;
}

#groupSettingsForm .form-actions {
    margin-top: 15px;
    display: flex;
    flex-direction: column; /* Make buttons stack vertically */
    justify-content: flex-start;
    gap: 10px;
}

#groupSettingsForm .form-actions button {
    padding: 10px 18px;
    font-size: 1em;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #ffffff;
    flex: 1; /* Make buttons take equal width */
    text-align: center; /* Center text within the button */
}

#groupSettingsForm .form-actions button[type="submit"] {
    background-color: var(--user-bubble-bg);
}
#groupSettingsForm .form-actions button[type="submit"]:hover {
    background-color: var(--button-hover-bg);
}

#groupSettingsForm .form-actions #deleteGroupBtn {
    background-color: var(--danger-color);
}
#groupSettingsForm .form-actions #deleteGroupBtn:hover {
    background-color: var(--danger-hover-bg);
}

body.light-theme #groupSettingsForm .form-actions button[type="submit"] {
    background-color: var(--user-bubble-bg-light);
}
body.light-theme #groupSettingsForm .form-actions button[type="submit"]:hover {
    background-color: var(--button-hover-bg-light);
}
body.light-theme #groupSettingsForm .form-actions #deleteGroupBtn {
    background-color: var(--danger-color-light);
}
body.light-theme #groupSettingsForm .form-actions #deleteGroupBtn:hover {
    background-color: var(--danger-hover-bg-light);
}


.group-members-list-container {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px;
    background-color: var(--input-bg);
}

.group-member-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
}

.group-member-item input[type="checkbox"] {
    margin-right: 8px;
    width: 16px; /* Custom size for checkbox */
    height: 16px;
    accent-color: var(--highlight-text); /* Color the checkbox itself */
}

.group-member-item label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal; /* Override general label style if needed */
    color: var(--primary-text);
    font-size: 0.95em;
}

.group-member-item .avatar-small {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
    object-fit: cover;
}

#memberTagsContainer {
    /* Styles for the container of all tag inputs, if needed */
}

#memberTagsInputs {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.member-tag-input-item {
    display: flex;
    flex-direction: column; /* Stack label and input */
    gap: 4px;
}
.member-tag-input-item label {
    font-size: 0.85em;
    color: var(--secondary-text);
}
.member-tag-input-item input[type="text"] {
    font-size: 0.9em;
    padding: 6px 8px;
}

#groupSettingsForm img#groupAvatarPreview {
    border: 2px solid var(--button-bg) !important;
    border-radius: 8px;
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px;
}


/* End Group Settings Specific Styles */


@keyframes vcp-shimmer-bg {
    0% { background-position: 150% 0; }
    100% { background-position: -150% 0; }
}

.notification-item {
    position: relative;
    background-color: var(--notification-bg);
    color: var(--primary-text);
    padding: 10px 15px;
    padding-right: 40px;
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
    margin: 0;
    font-size: 0.9em;
    box-shadow: none;
    opacity: 0;
    transform: translateX(100%);
    transition: opacity 0.5s ease, transform 0.5s ease;
    width: 100%;
    box-sizing: border-box;
    cursor: pointer;
    overflow: hidden;

    background-image: linear-gradient(
        110deg,
        var(--notification-bg) 0%,
        var(--notification-bg) 40%,
        var(--accent-bg) 50%,
        var(--notification-bg) 60%,
        var(--notification-bg) 100%
    );
    background-size: 250% 100%;
    animation: vcp-shimmer-bg 7s linear infinite;
}

.notification-item.visible {
    opacity: 1;
    transform: translateX(0);
}

.notification-item:last-child {
    border-bottom: 1px solid var(--border-color);
}

.notifications-list > .notification-item:last-of-type {
}

.notification-item strong {
    color: var(--highlight-text);
    display: block;
    margin-bottom: 4px;
    position: relative;
    z-index: 1;
}

.notification-item .notification-content {
    position: relative;
    z-index: 1;
}

.notification-item pre {
    background-color: rgba(0,0,0,0.2);
    padding: 6px;
    border-radius: 4px;
    font-size: 0.85em;
    margin-top: 5px;
    max-height: 100px;
    overflow: auto;
    white-space: pre-wrap;
    word-break: break-all;
    position: relative;
    z-index: 1;
}

.notification-copy-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255,255,255,0.1);
    color: var(--primary-text);
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 4px;
    padding: 2px 5px;
    font-size: 0.8em;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s, background-color 0.2s;
    z-index: 2;
}
.notification-copy-btn:hover {
    opacity: 1;
    background: rgba(255,255,255,0.2);
}

.notification-timestamp {
    font-size: 0.75em;
    color: #444444;
    opacity: 0.9;
    display: block;
    text-align: right;
    margin-top: 5px;
    position: relative;
    z-index: 1;
}

body.light-theme .notification-item {
    color: #000000;
}

.context-menu-item.regenerate-text {
    color: #28a745;
}

.context-menu-item.regenerate-text:hover {
    color: #1e7e34;
    background-color: var(--accent-bg);
}

.attachment-preview-area {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    order: -1; /* To appear above the input field */
    margin-bottom: 8px;
    padding: 0; /* Keep padding at 0, as items themselves have padding */
}

.attachment-preview-item {
    background-color: var(--accent-bg);
    color: var(--primary-text);
    border-radius: 8px; /* Rounded rectangle, not a pill */
    padding: 6px 10px; /* Adjusted padding */
    display: flex;
    align-items: center;
    font-size: 0.9em;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease-in-out;
    cursor: default;
    /* position: relative; -- only if remove button is absolute */
    max-width: 200px; /* Prevent items from becoming too wide */
}

.attachment-preview-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.attachment-thumbnail-image {
    width: 32px; /* Thumbnail size */
    height: 32px;
    object-fit: cover;
    border-radius: 4px; /* Slightly rounded corners for the image */
    margin-right: 8px;
    border: 1px solid var(--border-color); /* Optional border for the image */
}

.file-preview-icon {
    margin-right: 8px;
    font-size: 1.2em; /* Slightly larger icon for non-images */
    width: 32px; /* Match thumbnail width for alignment */
    height: 32px; /* Match thumbnail height */
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-preview-name {
    display: inline; /* Make filename visible */
    flex-grow: 1; /* Allow name to take available space */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 8px; /* Space before remove button */
    line-height: 1.2; /* Ensure text is vertically centered well */
}

.file-preview-remove-btn {
    /* Removed absolute positioning, now part of flex flow */
    background: transparent;
    border: none;
    color: var(--secondary-text); /* Muted color */
    padding: 0; /* Reset padding */
    margin-left: auto; /* Pushes button to the right within the flex container */
    cursor: pointer;
    font-size: 1.3em; /* Larger 'x' */
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
    align-self: center; /* Vertically align with other items */
    width: 20px; /* Give some clickable area */
    height: 20px;
}

.file-preview-remove-btn:hover {
    color: var(--danger-color); /* Highlight on hover */
}

/* This class was unused and can be removed or ignored */
/* .attachment-preview-area .file-preview {
    display: none;
} */

.message-attachment-image-thumbnail {
    max-width: 150px;
    max-height: 150px;
    border-radius: 8px;
    cursor: pointer;
    object-fit: cover;
    margin: 5px;
    border: 2px solid var(--button-bg);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.message-attachment-image-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.topics-header {
    display: flex;
    flex-direction: column;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
}

.topics-header h2 {
    margin-top: 5px;
    margin-bottom: 10px;
    color: var(--secondary-text);
    font-size: 1.4em;
    border-bottom: none;
    padding-bottom: 0;
}

.topic-search-container {
    display: flex;
    width: 100%;
    gap: 8px;
    padding: 0 15px;
    box-sizing: border-box;
}

.topic-search-input {
    flex-grow: 1;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 1em;
    box-sizing: border-box;
    max-width: calc(100% - 8px);
    min-width: 0;
}

.topic-search-input::placeholder {
    color: var(--secondary-text);
    opacity: 0.7;
}

.topic-search-input:focus {
    outline: none;
    border-color: var(--user-bubble-bg);
    box-shadow: 0 0 0 2px rgba(61, 90, 128, 0.3);
}

.digital-clock {
    font-size: 2.0em;
    color: var(--highlight-text);
    font-family: 'Maven Pro ExtraBold', 'Arial Black', sans-serif;
    font-weight: 800;
    letter-spacing: 1px;
    line-height: 1;
    margin-bottom: -1px;
    text-align: left;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.date-display {
    font-size: 0.7em;
    color: var(--secondary-text);
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1;
    text-align: left;
    margin-top: 0px;
    position: relative;
    top: -1px;
    left: 3px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@keyframes blinkColon {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.2; }
}

.digital-clock .colon {
    animation: blinkColon 2s infinite;
    position: relative;
}

.message-item .md-content .quoted-text {
    color: var(--quoted-text);
}
@keyframes st-colon-blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.2; }
}

@keyframes st-soft-circular-ripple-effect {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.7;
    }
    80% {
        transform: translate(-50%, -50%) scale(18);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
}

.topic-item.active-topic-glowing {
    position: relative;
    overflow: hidden;
}

.topic-item.active-topic-glowing::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 10px;
    height: 10px;
    background-image: radial-gradient(
        circle,
        rgba(190, 210, 240, 0.45) 0%,
        rgba(190, 210, 240, 0.3) 40%,
        rgba(190, 210, 240, 0) 70%
    );
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    animation: st-soft-circular-ripple-effect 3.8s ease-out infinite;
    z-index: 0;
    pointer-events: none;
}
body.light-theme .topic-item.active-topic-glowing::before {
    background-image: radial-gradient(
        circle,
        rgba(65, 144, 213, 0.041) 0%,
        rgba(56, 131, 196, 0.358) 40%,
        rgba(14, 78, 134, 0) 70%
    ) !important;
}
input[type="file"] {
    font-size: 0;
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    width: auto;
    display: inline-block;
    vertical-align: middle; /* 保持垂直居中对齐 */
    position: relative; /* 启用相对定位 */
    top: 8px; /* 向下微调3像素 */
}

input[type="file"]::-webkit-file-upload-button,
input[type="file"]::file-selector-button {
    background-color: var(--button-bg);
    color: var(--primary-text);
    border: 1px solid var(--button-bg);
    padding: 10px 15px; /* 恢复垂直内边距 */
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.95em;
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
    font-family: inherit;
}

input[type="file"]::-webkit-file-upload-button:hover,
input[type="file"]::file-selector-button:hover {
    background-color: var(--button-hover-bg);
    border-color: var(--button-hover-bg);
}

body.light-theme input[type="file"]::-webkit-file-upload-button,
body.light-theme input[type="file"]::file-selector-button {
    color: #ffffff;
}

body.light-theme input[type="file"]::-webkit-file-upload-button:hover,
body.light-theme input[type="file"]::file-selector-button:hover {
    color: #ffffff;
}
#agentSettingsForm img.avatar-preview,
#agentSettingsContainer img[alt="Avatar Preview"],
#agentSettingsForm div > img[src*="blob:"],
#agentSettingsForm div > img[data-preview-for],
#agentSettingsForm img[id*="AvatarPreview"],
#agentSettingsForm img[class*="AvatarPreview"] {
    border: 2px solid var(--button-bg) !important;
    border-radius: 8px;
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px;
}

#agentSettingsForm > div > img:only-child {
    border: 2px solid var(--button-bg) !important;
    border-radius: 8px;
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px;
}
#agentSettingsForm .form-actions button.error-feedback {
    background-color: var(--danger-color) !important;
    color: white !important;
}

#agentSettingsForm .form-actions button.error-feedback:hover {
    background-color: var(--danger-hover-bg) !important;
}

#floating-toast-notifications-container {
    position: fixed;
    top: 40px;
    right: 20px;
    width: 320px;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.floating-toast-notification {
    background-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.85);
    color: var(--primary-text);
    padding: 12px 15px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
    font-size: 0.9em;
    position: relative;
    opacity: 0;
    transform: translateX(100%);
    transition: opacity 0.4s ease-out, transform 0.4s ease-out, background-color 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    background-image: linear-gradient(
        110deg,
        transparent 0%,
        transparent 35%,
        var(--accent-bg) 50%,
        transparent 65%,
        transparent 100%
    );
    background-size: 300% 100%;
    animation: vcp-shimmer-bg 5s linear infinite;
}

body.dark-theme .floating-toast-notification {
    background-color: rgba(40, 40, 44, 0.85);
}

body.light-theme .floating-toast-notification {
    background-color: rgba(240, 244, 248, 0.85);
    color: var(--primary-text-light);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}


.floating-toast-notification.visible {
    opacity: 1;
    transform: translateX(0);
}

.floating-toast-notification.exiting {
    opacity: 0;
    transform: translateX(110%);
}


.floating-toast-notification strong {
    color: var(--highlight-text);
    display: block;
    margin-bottom: 5px;
    font-size: 1.05em;
}

.floating-toast-notification .notification-content p {
    margin: 0;
    line-height: 1.4;
}
.floating-toast-notification .notification-content pre {
    background-color: rgba(0,0,0,0.1);
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    margin-top: 6px;
    max-height: 80px;
    overflow: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

.floating-toast-notification .notification-timestamp {
    font-size: 0.75em;
    color: var(--secondary-text);
    opacity: 0.8;
    display: block;
    text-align: right;
    margin-top: 8px;
}

/* @tag 高亮样式 */
.highlighted-tag {
    color: var(--highlight-text); /* 使用主题的高亮颜色 */
    background-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.2); /* 轻微背景，暗色主题兼容 */
    padding: 0.1em 0.3em;
    border-radius: 4px;
    font-weight: 500;
}

body.light-theme .highlighted-tag {
    color: var(--highlight-text-light);
    background-color: rgba(var(--rgb-secondary-bg-light, 255, 255, 255), 0.3); /* 轻微背景，亮色主题兼容 */
}

/* 新增样式：使表单组内的元素水平排列 */
.form-group-inline {
    display: flex;
    flex-direction: row !important; /* 确保水平排列 */
    align-items: center;
    justify-content: flex-start; /* 将内容靠左对齐 */
    gap: 10px; /* 调整元素之间的间距 */
}

.form-group-inline label {
    margin-bottom: 0; /* 移除标签的底部外边距 */
}

/* 新增样式：使用户头像表单组内的元素水平排列 */
.form-group-inline-avatar {
    display: flex;
    align-items: center;
    gap: 10px; /* 调整元素之间的间距 */
}

.form-group-inline-avatar label {
    margin-bottom: 0; /* 移除标签的底部外边距 */
}


/*
 * =======================================================
 * 小吉的魔法时间：流式文字动态“流光”效果 (Text Shimmer Effect)
 * =======================================================
 */

/* 1. 定义一个名为 "textShimmer" 的动画过程 */
@keyframes textShimmer {
  from {
    /* 动画开始时，背景渐变的位置在最右侧 */
    background-position: 200% center;
  }
  to {
    /* 动画结束时，背景渐变的位置移动到最左侧，形成流动效果 */
    background-position: -200% center;
  }
}

/* 2. 将此效果应用到正在流式输出的消息内容上 */
/* 这个选择器精确地指向了正在流式输出(有.streaming类)的消息项(message-item)中的内容区域(md-content) */
.message-item.streaming .md-content {
  /* 创建一个从透明到高亮再到透明的线性渐变作为背景 */
  background-image: linear-gradient(
    to right,
    var(--shimmer-color-transparent) 20%,
    var(--shimmer-color-highlight) 50%,
    var(--shimmer-color-transparent) 80%
  );
  
  /* 关键魔法#1：将背景的范围裁切成文字的形状 */
  background-clip: text;
  -webkit-background-clip: text; /* 兼容Webkit内核浏览器 */

  /* 关键魔法#2：将文字本身的颜色设为透明，这样才能“透”出后面的背景渐变 */
  color: transparent;
  
  /* 将背景尺寸扩大，以便渐变效果有足够的空间进行移动 */
  background-size: 200% auto;
  
  /* 应用我们之前定义的动画 */
  animation: textShimmer 2.5s linear infinite; /* 设置动画速度 */
}

/* --- Invite Agent Buttons --- */
.invite-agent-buttons-container {
    display: grid;
    /* 一行尽可能多地显示按钮，每个按钮最小宽度约100px，最大占据1fr空间 */
    /* 如果希望严格一行3个，可以使用: grid-template-columns: repeat(3, 1fr); */
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px; /* 按钮之间的间距 */
    padding: 10px 15px; /* 容器的内边距，与 notifications-list 保持一致 */
    /* border-top: 1px solid var(--border-color); /* 在main.html中已通过style添加 */
    /* margin-top: 10px; /* 在main.html中已通过style添加 */
    background-color: var(--secondary-bg); /* 与通知区域背景协调 */
}

.invite-agent-button {
    display: flex;
    align-items: center;
    padding: 6px 10px; /* 较小的内边距 */
    border: 1px solid var(--button-border-color, var(--border-color)); /* 尝试使用按钮边框变量，否则用通用边框 */
    background-color: var(--button-bg);
    color: var(--primary-text); /* 使用主题的主文字颜色 */
    border-radius: 6px; /* 与其他小按钮类似的圆角 */
    cursor: pointer;
    font-size: 0.85em; /* 较小的字体 */
    text-align: left;
    overflow: hidden; /* 防止内容溢出 */
    transition: background-color 0.2s ease, border-color 0.2s ease, transform 0.1s ease;
    min-width: 0; /* 允许按钮在grid中收缩 */
}

.invite-agent-button:hover {
    background-color: var(--button-hover-bg);
    border-color: var(--button-hover-bg);
    transform: translateY(-1px); /* 轻微上浮效果 */
}

.invite-agent-button img {
    width: 20px; /* 小头像 */
    height: 20px;
    border-radius: 50%;
    margin-right: 7px; /* 头像和文字间距 */
    object-fit: cover;
    flex-shrink: 0; /* 防止头像被压缩 */
}

.invite-agent-button span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; /* 名称过长时显示省略号 */
    flex-grow: 1; /* 允许文字占据剩余空间 */
}

/* 亮色主题下的特定调整 (如果需要) */
body.light-theme .invite-agent-button {
    border-color: var(--border-color-light);
    background-color: var(--secondary-bg-light); /* 使用更浅的背景以融入亮色主题 */
    color: var(--primary-text-light);
}

body.light-theme .invite-agent-button:hover {
    background-color: var(--accent-bg-light);
    border-color: var(--accent-bg-light);
}