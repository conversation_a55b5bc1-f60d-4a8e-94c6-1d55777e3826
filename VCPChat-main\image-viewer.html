<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片预览</title>
    <style>
        body { 
            margin: 0; 
            background-color: #2c2c2e; /* Dark background for better contrast */
            display: flex; 
            justify-content: center; 
            align-items: center; 
            height: 100vh; 
            overflow: hidden; 
            font-family: sans-serif;
            color: #f0f0f0;
        }
        img {
            max-width: 98vw; /* Slightly less than 100 to avoid scrollbars due to potential padding/border */
            max-height: 90vh; /* Adjusted to make space for buttons */
            object-fit: contain;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            border-radius: 4px;
            display: block; /* Helps with centering and layout */
            margin-bottom: 15px; /* Space between image and buttons */
            transition: transform 0.1s ease-out; /* Smooth scaling */
            cursor: grab; /* Indicate draggable when zoomed */
        }
        .error-message {
            padding: 20px;
            background-color: #444;
            border-radius: 5px;
            font-size: 1.2em;
        }
        .controls {
            position: absolute;
            bottom: 20px; /* Position at the bottom */
            left: 50%;
            transform: translateX(-50%);
            /* display: flex; will be set by JS after image load */
            gap: 15px; /* Space between buttons */
            padding: 10px;
            background-color: rgba(44, 44, 46, 0.8); /* Semi-transparent background */
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
            pointer-events: none; /* Prevent interaction when hidden */
        }
        .controls.active {
            opacity: 1;
            visibility: visible;
            pointer-events: auto; /* Allow interaction when visible */
        }
        .control-button {
            background-color: #555;
            color: #fff;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
            transition: background-color 0.2s ease;
        }
        .control-button:hover {
            background-color: #666;
        }
        .control-button svg {
            width: 18px;
            height: 18px;
            fill: currentColor;
        }
    </style>
</head>
<body>
    <img id="viewerImage" src="" alt="图片加载中或加载失败..." style="display:none;"/>
    <div class="controls" id="imageControls" style="display:none;">
        <button id="copyButton" class="control-button">
            <svg viewBox="0 0 24 24"><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"></path></svg>
            复制
        </button>
        <button id="downloadButton" class="control-button">
            <svg viewBox="0 0 24 24"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"></path></svg>
            下载
        </button>
    </div>
    <div id="errorMessage" class="error-message" style="display:none;">无法加载图片。</div>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const params = new URLSearchParams(window.location.search);
            const imageUrl = params.get('src');
            const imageTitle = params.get('title') || '图片预览';
            
            document.title = decodeURIComponent(imageTitle);
            const imgElement = document.getElementById('viewerImage');
            const errorDiv = document.getElementById('errorMessage');
            const imageControls = document.getElementById('imageControls');
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');

            let currentScale = 1;
            const minScale = 0.2;
            const maxScale = 5;
            let isDragging = false;
            let startX, startY, initialX, initialY;
            let imgInitialX = 0; // To store image's current translation
            let imgInitialY = 0;

            if (imageUrl) {
                const decodedImageUrl = decodeURIComponent(imageUrl);
                console.log('Image Viewer: Loading image -', decodedImageUrl);
                imgElement.src = decodedImageUrl;
                imgElement.alt = decodeURIComponent(imageTitle);
                // imgElement.style.display = 'block'; // Visibility handled by onload/onerror

                imgElement.onload = () => {
                    console.log('Image Viewer: Image loaded successfully.');
                    imgElement.style.display = 'block';
                    imageControls.style.display = 'flex'; // Set display so it can become visible
                    errorDiv.style.display = 'none';

                    // Add hover listeners for showing/hiding controls
                    imgElement.addEventListener('mouseenter', () => {
                        imageControls.classList.add('active');
                    });
                    imgElement.addEventListener('mouseleave', () => {
                        // Check if mouse is over the controls themselves before hiding
                        setTimeout(() => { // Timeout to allow moving to controls
                            if (!imageControls.matches(':hover')) {
                                imageControls.classList.remove('active');
                            }
                        }, 50); // Small delay
                    });
                    // Keep controls visible if mouse moves onto them
                    imageControls.addEventListener('mouseenter', () => {
                        imageControls.classList.add('active');
                    });
                    imageControls.addEventListener('mouseleave', () => {
                        imageControls.classList.remove('active');
                    });

                    // Zoom functionality
                    imgElement.addEventListener('wheel', (event) => {
                        if (event.ctrlKey) {
                            event.preventDefault(); // Prevent page zoom

                            const rect = imgElement.getBoundingClientRect();
                            const mouseX = event.clientX - rect.left; // Mouse X relative to image
                            const mouseY = event.clientY - rect.top;  // Mouse Y relative to image

                            // Set transform origin to mouse position
                            imgElement.style.transformOrigin = `${mouseX}px ${mouseY}px`;

                            const scaleAmount = 0.1;
                            if (event.deltaY < 0) { // Zoom in
                                currentScale = Math.min(maxScale, currentScale + scaleAmount);
                            } else { // Zoom out
                                currentScale = Math.max(minScale, currentScale - scaleAmount);
                            }
                            
                            // Apply scale and keep current translation
                            imgElement.style.transform = `translate(${imgInitialX}px, ${imgInitialY}px) scale(${currentScale})`;

                            if (currentScale > 1) {
                                imgElement.style.cursor = 'grab';
                            } else {
                                imgElement.style.cursor = 'default';
                                // Reset translation if scaled back to 1 or less
                                imgInitialX = 0;
                                imgInitialY = 0;
                                imgElement.style.transform = `translate(0px, 0px) scale(${currentScale})`;
                            }
                        }
                    }, { passive: false }); // passive: false to allow preventDefault

                    // Drag functionality
                    imgElement.addEventListener('mousedown', (event) => {
                        if (event.button === 0 && currentScale > 1) { // Only on left click and when zoomed
                            isDragging = true;
                            startX = event.clientX;
                            startY = event.clientY;
                            // No need to store initial translation here, as imgInitialX/Y are already current
                            imgElement.style.cursor = 'grabbing';
                            event.preventDefault(); // Prevent text selection or other default drag behaviors
                        }
                    });

                    document.addEventListener('mousemove', (event) => {
                        if (isDragging) {
                            const dx = event.clientX - startX;
                            const dy = event.clientY - startY;
                            
                            // Update the image's translation based on drag
                            // The new translation is the initial translation plus the drag distance
                            const newTranslateX = imgInitialX + dx;
                            const newTranslateY = imgInitialY + dy;
                            
                            imgElement.style.transform = `translate(${newTranslateX}px, ${newTranslateY}px) scale(${currentScale})`;
                        }
                    });

                    document.addEventListener('mouseup', (event) => {
                        if (event.button === 0 && isDragging) {
                            isDragging = false;
                            if (currentScale > 1) {
                                imgElement.style.cursor = 'grab';
                            } else {
                                imgElement.style.cursor = 'default';
                            }
                            // Persist the new translation
                            const currentTransform = new WebKitCSSMatrix(window.getComputedStyle(imgElement).transform);
                            imgInitialX = currentTransform.m41; // e value for X translation
                            imgInitialY = currentTransform.m42; // f value for Y translation
                        }
                    });
                     // Also stop dragging if mouse leaves the window
                    document.addEventListener('mouseleave', () => {
                       if (isDragging) {
                           isDragging = false;
                           if (currentScale > 1) {
                               imgElement.style.cursor = 'grab';
                           } else {
                               imgElement.style.cursor = 'default';
                           }
                           const currentTransform = new WebKitCSSMatrix(window.getComputedStyle(imgElement).transform);
                           imgInitialX = currentTransform.m41;
                           imgInitialY = currentTransform.m42;
                       }
                   });

                };
                imgElement.onerror = () => {
                    console.error('Image Viewer: Error loading image -', decodedImageUrl);
                    imgElement.style.display = 'none';
                    imageControls.style.display = 'none'; // Keep controls hidden on error
                    errorDiv.textContent = `无法加载图片: ${decodeURIComponent(imageTitle)}`;
                    errorDiv.style.display = 'block';
                };

            } else {
                console.error('Image Viewer: No image URL provided.');
                imgElement.style.display = 'none';
                imageControls.style.display = 'none';
                errorDiv.textContent = '未提供图片URL。';
                errorDiv.style.display = 'block';
            }

            copyButton.addEventListener('click', async () => {
                if (!imgElement.src || imgElement.src === window.location.href) {
                    console.warn('Image Viewer: No valid image to copy.');
                    // Optionally, briefly change button text to indicate issue
                    const originalText = copyButton.innerHTML;
                    copyButton.innerHTML = '<svg viewBox="0 0 24 24"><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"></path></svg> 无效图片';
                    setTimeout(() => {
                        copyButton.innerHTML = originalText;
                    }, 2000);
                    return;
                }
                const originalButtonText = copyButton.innerHTML; // Store original content with SVG
                try {
                    const response = await fetch(imgElement.src);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    let blob = await response.blob();
                    let finalBlobType = blob.type || 'image/png';

                    if (blob.type === 'image/jpeg' || blob.type === 'image/jpg') {
                        console.log('Image Viewer: Converting JPEG to PNG for clipboard.');
                        try {
                            const imageBitmap = await createImageBitmap(blob);
                            const canvas = document.createElement('canvas');
                            canvas.width = imageBitmap.width;
                            canvas.height = imageBitmap.height;
                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(imageBitmap, 0, 0);
                            blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
                            finalBlobType = 'image/png';
                            console.log('Image Viewer: Conversion to PNG successful.');
                        } catch (conversionError) {
                            console.error('Image Viewer: Failed to convert JPEG to PNG -', conversionError);
                            // Fallback to original blob if conversion fails, though it might still fail on write
                        }
                    }

                    const item = new ClipboardItem({ [finalBlobType]: blob });
                    await navigator.clipboard.write([item]);
                    console.log('Image copied to clipboard as', finalBlobType);
                    copyButton.innerHTML = '<svg viewBox="0 0 24 24" style="width:18px; height:18px; fill:currentColor;"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"></path></svg> 已复制'; // Checkmark SVG + Text
                    setTimeout(() => {
                        copyButton.innerHTML = originalButtonText;
                    }, 2000);
                } catch (err) {
                    console.error('Image Viewer: Failed to copy image -', err);
                    copyButton.innerHTML = '<svg viewBox="0 0 24 24"><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"></path></svg> 复制失败';
                     setTimeout(() => {
                        copyButton.innerHTML = originalButtonText;
                    }, 2000);
                }
            });

            downloadButton.addEventListener('click', () => {
                if (!imgElement.src || imgElement.src === window.location.href) {
                    console.warn('Image Viewer: No valid image to download.');
                    return;
                }
                try {
                    const link = document.createElement('a');
                    link.href = imgElement.src;
                    
                    let filename = decodeURIComponent(imageTitle) || 'downloaded_image';
                    const urlFilename = imgElement.src.substring(imgElement.src.lastIndexOf('/') + 1).split('?')[0]; // Remove query params
                    const urlExtensionMatch = urlFilename.match(/\.(jpe?g|png|gif|webp|svg)$/i);
                    const titleExtensionMatch = filename.match(/\.(jpe?g|png|gif|webp|svg)$/i);

                    if (!titleExtensionMatch && urlExtensionMatch) {
                        // Ensure filename doesn't already somehow end with the extension from URL (e.g. title was "image.jpg" and url is "other.jpg")
                        const baseFilename = filename.replace(/\.[^/.]+$/, ""); // remove existing extension if any
                        if (baseFilename + urlExtensionMatch[0] !== filename) {
                           filename = baseFilename + urlExtensionMatch[0];
                        } else if (!filename.endsWith(urlExtensionMatch[0])) {
                           filename += urlExtensionMatch[0];
                        }
                    } else if (!titleExtensionMatch && !urlExtensionMatch) {
                         if (!/\.[a-z0-9]{3,4}$/i.test(filename)) { // check for 3 or 4 char extension
                            filename += '.png'; // Default extension
                         }
                    }
                    
                    link.download = filename.replace(/[<>:"/\\|?*]+/g, '_').replace(/\s+/g, '_'); // Sanitize and replace spaces

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    console.log('Image download initiated for:', link.download);
                } catch (err) {
                    console.error('Image Viewer: Failed to initiate download -', err);
                }
            });

        });

        // Add keyboard listener for Escape key to close the window
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>