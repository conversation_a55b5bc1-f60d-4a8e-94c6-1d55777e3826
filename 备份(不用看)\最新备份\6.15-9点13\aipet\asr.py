# 语音识别模块 - 简化版本
import json
import wave
import pyaudio
import numpy as np
from funasr_onnx import SenseVoiceSmall
import torch
import os
import threading
import re
import time
import traceback
import tempfile
import shutil
import warnings

# 抑制相关的警告信息
warnings.filterwarnings("ignore", category=UserWarning, module="librosa")
warnings.filterwarnings("ignore", category=UserWarning, module="audioread")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="librosa")
warnings.filterwarnings("ignore", message="PySoundFile failed. Trying audioread instead.")

# 设置环境变量来抑制某些音频库的警告
os.environ['PYTHONWARNINGS'] = 'ignore::UserWarning:librosa'

# 确保 onnxruntime 的正确导入
try:
    import onnxruntime as ort
    from onnxruntime import SessionOptions
except ImportError as e:
    print(f"警告: onnxruntime 导入失败: {e}")

# 获取asr.py文件所在的目录
_ASR_MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
_DATA_DIR = os.path.join(_ASR_MODULE_DIR, "data")

# 设置默认值
mic_num = None
FORMAT = pyaudio.paInt16
CHANNELS, RATE, CHUNK = 1, 16000, 512
p, stream, asr_model, vad_model = None, None, None, None

# 缓存路径
cache_path = os.path.join(_DATA_DIR, "cache", "cache_record.wav")
os.makedirs(os.path.dirname(cache_path), exist_ok=True)

# VAD 相关常量
VAD_THRESHOLD = 0.5
VAD_SILENCE_TIMEOUT_SECONDS = 1.5
VAD_SILENCE_CHUNKS = int(VAD_SILENCE_TIMEOUT_SECONDS * RATE / CHUNK)
LEADING_SILENCE_SECONDS = 0.5
LEADING_SILENCE_CHUNKS = int(LEADING_SILENCE_SECONDS * RATE / CHUNK)

# 用于主应用控制ASR流程的事件
asr_continue_event = None

def set_asr_continue_event(event):
    """设置ASR继续事件"""
    global asr_continue_event
    asr_continue_event = event

def load_vad_model():
    """简化的 VAD 模型加载"""
    global vad_model
    if vad_model is None:
        print("正在加载语音检测模型...")
        try:
            # 直接使用 torch.hub 加载，让它自动处理缓存
            model_tuple = torch.hub.load(
                repo_or_dir='snakers4/silero-vad',
                model='silero_vad',
                trust_repo=True
            )
            
            # silero_vad 返回的是一个 tuple，第一个元素是模型
            if isinstance(model_tuple, tuple):
                vad_model = model_tuple[0]
            else:
                vad_model = model_tuple
                
            print("✓ 语音检测模型加载完成")
            
            # 简单的预热测试
            dummy_tensor = torch.zeros(CHUNK, dtype=torch.float32)
            _ = vad_model(dummy_tensor, RATE)
            return vad_model
            
        except Exception as e:
            print(f"✗ 语音检测模型加载失败: {e}")
            return None
    return vad_model

def _clean_sensevoice_text(text):
    """清理SenseVoice模型输出的特殊标记"""
    if not text:
        return ""
    
    # 移除SenseVoice的特殊标记，如 <|zh|>, <|SAD|>, <|Speech|>, <|woitn|> 等
    import re
    # 匹配形如 <|...| > 的标记
    cleaned_text = re.sub(r'<\|[^|]*\|>', '', text)
    
    # 移除多余空格
    cleaned_text = ' '.join(cleaned_text.split())
    
    return cleaned_text.strip()

def recognize_audio(audiodata):
    """简化的语音识别函数"""
    global asr_model
    if not audiodata:
        print("没有有效的音频数据进行识别。")
        return ""
        
    if asr_model is None:
        print("正在加载语音识别模型...")
        model_path = os.path.join(_DATA_DIR, "model", "sensevoice-small-onnx-quant")
        
        try:
            if not os.path.isdir(model_path):
                raise FileNotFoundError(f"ASR模型目录未找到: {model_path}")
              
            # 检查是否需要使用临时目录（中文路径问题）
            try:
                model_path.encode('ascii')
                # 路径只包含ASCII字符，直接加载
                asr_model = SenseVoiceSmall(model_path, batch_size=1, quantize=True)
                print("✓ 语音识别模型加载完成")
            except UnicodeEncodeError:
                # 路径包含非ASCII字符，使用临时目录
                print("检测到中文路径，正在处理...")
                temp_dir = tempfile.mkdtemp(prefix="sensevoice_")
                temp_model_path = os.path.join(temp_dir, "sensevoice-small-onnx-quant")
                
                shutil.copytree(model_path, temp_model_path)
                asr_model = SenseVoiceSmall(temp_model_path, batch_size=1, quantize=True)
                print("✓ 语音识别模型加载完成（临时目录）")
                
        except Exception as e:
            print(f"✗ 语音识别模型加载失败: {e}")
            asr_model = None
            return "ASR模型加载失败"

    if asr_model is None:
        return "ASR模型未加载"

    try:
        # 确保音频数据格式正确
        if isinstance(audiodata, bytes):
            # 保存音频数据到临时文件，确保格式正确
            with open(cache_path, "wb") as f:
                f.write(audiodata)
        else:
            print(f"警告：音频数据格式不正确: {type(audiodata)}")
            return ""
        
        # 验证音频文件是否有效
        try:
            with wave.open(cache_path, 'rb') as wf:
                if wf.getnchannels() != CHANNELS or wf.getframerate() != RATE:
                    print(f"警告：音频格式不匹配 - 通道数: {wf.getnchannels()}, 采样率: {wf.getframerate()}")
        except Exception as e:
            print(f"音频文件验证失败: {e}")
            return ""
        
        # 抑制FunASR的输出
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            # 进行语音识别
            res = asr_model(cache_path, language="zh", use_itn=False)
        
        # 调试：打印返回结果的结构
        print(f"ASR结果类型: {type(res)}")
        print(f"ASR结果内容: {res}")
        
        if res and len(res) > 0:
            # 检查第一个元素的类型
            first_item = res[0]
            print(f"第一个元素类型: {type(first_item)}")
            print(f"第一个元素内容: {first_item}")
            
            # 根据不同的数据类型处理
            if isinstance(first_item, dict) and 'text' in first_item:
                text = first_item['text']
                # 清理SenseVoice的特殊标记
                text = _clean_sensevoice_text(text)
                print(f"识别结果（清理后）: {text}")
                return text.strip() if text else ""
            elif isinstance(first_item, str):
                # 如果直接是字符串，清理SenseVoice的特殊标记
                text = _clean_sensevoice_text(first_item)
                print(f"识别结果（清理后）: {text}")
                return text.strip() if text else ""
            elif hasattr(first_item, 'text'):
                # 如果是对象有text属性
                text = getattr(first_item, 'text', '')
                text = _clean_sensevoice_text(text)
                print(f"识别结果（清理后）: {text}")
                return text.strip() if text else ""
            else:
                print(f"未知的ASR结果格式: {first_item}")
                return ""
        else:
            print("ASR未返回结果")
            return ""
            
    except Exception as e:
        print(f"语音识别过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return ""

def start_audio_stream():
    """开始音频流"""
    global stream, p
    
    # 延迟初始化 PyAudio, 避免与torch.hub加载冲突
    if p is None:
        try:
            p = pyaudio.PyAudio()
            print("✓ PyAudio 初始化成功")
        except Exception as e:
            print(f"✗ PyAudio 初始化失败: {e}")
            p = None
            return

    if stream is None:
        try:
            stream = p.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                frames_per_buffer=CHUNK,
                input_device_index=mic_num
            )
            print("✓ 音频流启动成功")
        except Exception as e:
            print(f"✗ 音频流启动失败: {e}")
            stream = None

def stop_audio_stream():
    """停止音频流"""
    global stream
    if stream is not None:
        try:
            stream.stop_stream()
            stream.close()
            stream = None
            print("✓ 音频流已停止")
        except Exception as e:
            print(f"停止音频流时出错: {e}")

def record_with_vad():
    """使用VAD进行录音"""
    if vad_model is None:
        print("VAD模型未加载")
        return None
    
    # 确保音频流已启动
    if stream is None:
        print("音频流未启动，正在启动...")
        start_audio_stream()
        if stream is None:
            print("✗ 无法启动音频流")
            return None

    # 等待asr_continue_event被设置后才开始录音
    if asr_continue_event:
        print("等待ASR continue事件...")
        if not asr_continue_event.is_set():
            print("ASR continue事件未设置，等待中...")
            return None

    print("开始录音...")
    frames = []
    silence_chunks = 0
    leading_silence_buffer = []
    
    try:
        while True:
            # 在录音过程中，允许外部停止
            if asr_continue_event and not asr_continue_event.is_set():
                print("录音过程中检测到continue事件被清除，停止录音")
                break
                
            data = stream.read(CHUNK, exception_on_overflow=False)
            audio_chunk = np.frombuffer(data, dtype=np.int16).astype(np.float32) / 32768.0
            
            # VAD检测
            speech_prob = vad_model(torch.from_numpy(audio_chunk), RATE).item()
            
            if speech_prob > VAD_THRESHOLD:
                # 检测到语音
                if len(frames) == 0:
                    # 第一次检测到语音，添加前导静音
                    frames.extend(leading_silence_buffer)
                frames.append(data)
                silence_chunks = 0
            else:
                # 静音
                if len(frames) > 0:
                    frames.append(data)
                    silence_chunks += 1
                    if silence_chunks >= VAD_SILENCE_CHUNKS:
                        break
                else:
                    # 保持前导静音缓冲
                    leading_silence_buffer.append(data)
                    if len(leading_silence_buffer) > LEADING_SILENCE_CHUNKS:
                        leading_silence_buffer.pop(0)
        
        if frames:
            print(f"录音完成，共 {len(frames)} 块音频数据")
            # 转换为标准wav格式
            import io
            wav_buffer = io.BytesIO()
            
            # 使用标准的wav格式参数
            try:
                with wave.open(wav_buffer, 'wb') as wf:
                    wf.setnchannels(CHANNELS)  # 单声道
                    wf.setsampwidth(p.get_sample_size(FORMAT))  # 16位
                    wf.setframerate(RATE)  # 16000Hz
                    
                    # 合并所有音频帧
                    audio_data = b''.join(frames)
                    wf.writeframes(audio_data)
                
                wav_data = wav_buffer.getvalue()
                print(f"生成WAV文件，大小: {len(wav_data)} 字节")
                
                # 验证生成的音频文件
                wav_buffer.seek(0)
                with wave.open(wav_buffer, 'rb') as wf_verify:
                    print(f"音频验证 - 通道: {wf_verify.getnchannels()}, 采样率: {wf_verify.getframerate()}, 位深: {wf_verify.getsampwidth()*8}bit")
                
                return wav_data
                
            except Exception as e:
                print(f"音频格式转换失败: {e}")
                return None
        else:
            print("未检测到有效语音")
            return None
            
    except Exception as e:
        print(f"录音过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None

# 导出的主要函数
def cleanup_asr():
    """清理ASR相关资源"""
    global stream, asr_model, vad_model
    
    try:
        # 停止音频流
        stop_audio_stream()
        
        # 清理模型
        asr_model = None
        vad_model = None
        
        # 清理缓存文件
        if os.path.exists(cache_path):
            try:
                os.remove(cache_path)
                print("✓ 清理缓存文件完成")
            except Exception as e:
                print(f"清理缓存文件失败: {e}")
        
        print("✓ ASR资源清理完成")
        
    except Exception as e:
        print(f"ASR资源清理失败: {e}")

def get_asr_status():
    """获取ASR状态"""
    status = {
        'vad_loaded': vad_model is not None,
        'asr_loaded': asr_model is not None,
        'stream_active': stream is not None and not stream.is_stopped(),
        'cache_exists': os.path.exists(cache_path)
    }
    return status

def cleanup_pyaudio():
    """清理 PyAudio 资源"""
    global p
    stop_audio_stream()
    if p:
        try:
            p.terminate()
            p = None
            print("✓ PyAudio 资源已清理")
        except Exception as e:
            print(f"清理 PyAudio 时出错: {e}")

def main():
    """用于独立测试的函数"""
    # ... existing code ...

__all__ = [
    'load_vad_model',
    'recognize_audio', 
    'start_audio_stream',
    'stop_audio_stream',
    'record_with_vad',
    'set_asr_continue_event',
    'cleanup_asr',
    'get_asr_status',
    'cleanup_pyaudio'
]