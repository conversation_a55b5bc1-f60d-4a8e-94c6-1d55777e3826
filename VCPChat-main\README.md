# VCP 聊天客户端

这是一个为 VCP (Variable & Command Protocol) 服务器打造的 AI 聊天桌面客户端。

*请立刻在全局设置里设置用户名，以避免众多依赖功能报错！*

后端链接：https://github.com/lioensky/VCPToolBox

## 主要功能

*   **VCP 服务器集成**: 客户端的核心聊天功能依赖于 VCP 服务器。它通过 HTTP(S) 与 VCP 服务器通信，发送用户消息并接收 AI 的响应，支持流式传输以实现实时交互。
*   **VCP 工具调用**: 支持 VCP 服务器定义的工具调用。
*   **VCP 日记渲染**: 能够渲染和显示 VCP 日记内容。
*   **Agent 管理**:
    *   创建、删除和配置多个 AI Agent。
    *   为每个 Agent 设置名称、系统提示、模型参数（如温度、上下文Token限制、最大输出Token）。
    *   管理 Agent 的头像。
    *   支持每个 Agent 拥有多个独立的聊天话题 (Topics)，包括话题的创建、删除、重命名和排序。
    *   支持 Agent 列表的自定义排序。
*   **群聊模式 (Agent Groups)**:
    *   允许多个已配置的 Agent 在同一个聊天会话中进行协作或角色扮演。
    *   支持创建、配置和管理 Agent 群组，包括设置群组名称、头像。
    *   每个群组可以包含多个从现有 Agent 列表中选择的成员。
    *   **发言模式**:
        *   **顺序发言 (`sequential`)**: 成员按预定顺序轮流发言（当前实现为按成员列表顺序，每次一个，具体高级轮换逻辑可后续增强）。
        *   **自然随机 (`naturerandom`)**: 根据用户输入中的 `@角色名`、`@角色标签` 或消息内容中与成员预设标签匹配的关键词，来智能决定哪些成员响应。此模式还带有一定的随机性，并可能在没有明确触发时选择一个保底发言者。
        *   **邀约模式 (`inviteonly`)**:根据用户点击Agent的按钮来决定谁来发言。
    *   **群组设定 (`groupPrompt`)**: 可以为整个群聊定义一个共同的背景、规则或系统级指令，影响群内所有 Agent 的行为。
    *   **发言邀请 (`invitePrompt`)**:
        *   这是一个模板字符串，用于在群聊中由系统（或协调者Agent）提示特定 Agent 发言。
        *   模板中应使用 `{{VCPChatAgentName}}` 作为占位符，系统在实际邀请时会自动将其替换为目标 Agent 的名称。
        *   **默认 `invitePrompt` 示例**：`现在轮到你{{VCPChatAgentName}}发言了。系统已经为大家添加[xxx的发言：]这样的标记头，以用于区分不同发言来自谁。大家不用自己再输出自己的发言标记头，讨论时不要讨论这个标记头系统，专注正常聊天即可。`
        *   这个提示旨在引导 Agent 自然地开始其回合，同时告知它们关于发言标记的规则。
    *   **发言标记系统**:
        *   为了在包含多个 Agent 和用户的群聊中清晰地标识每一条消息的来源，系统会自动在每条消息（无论是用户还是 Agent 的）前添加发言者标记，格式通常为 `[发言者名称的发言]: 实际消息内容`。
        *   **重要提示**：用户和配置的 Agent 在聊天时**无需手动输入或模仿**这些标记头。Agent 的系统提示和 `invitePrompt` 也应引导它们专注于对话内容，而不是讨论或生成这些标记。
    *   群组同样支持独立的话题管理，包括话题的创建、删除、重命名和排序。
*   **聊天界面**:
    *   提供用户友好的聊天界面进行 AI 交互。
    *   支持 Markdown 渲染聊天消息，包括代码块高亮。
    *   强大的文件处理能力：
        *   支持通过文件选择器、粘贴（文件路径或图片数据）、拖放操作添加附件。
        *   能够从剪贴板读取和粘贴图片，并直接在聊天中发送。
        *   支持将过长的文本粘贴内容自动保存为文本文件附件。
    *   内置图片查看器，方便在独立窗口预览聊天中的图片，支持复制和外部打开。
    *   **阅读模式**: 对 AI 发送的长内容提供沉浸式阅读体验。
        *   支持 Markdown、代码块语法高亮 (Highlight.js) 和 LaTeX 公式渲染 (KaTeX)。
        *   提供代码块内编辑和一键复制功能。
        *   支持对整个阅读内容进行编辑或快速分享到笔记模块。
        *   提供自定义上下文菜单（复制、剪切、删除、编辑全文、复制全文）。
    *   聊天分支功能，可以基于现有对话创建新的聊天分支。
*   **笔记模块**:
    *   独立的笔记管理窗口，方便记录和整理信息。
    *   支持创建、读取、更新和删除 TXT 格式的笔记。
    *   笔记编辑器支持 Markdown 语法，并提供实时预览功能（包括代码高亮和 LaTeX）。
    *   支持在笔记中粘贴图片，图片将作为附件保存并自动插入 Markdown 链接。
    *   提供笔记搜索功能，快速定位所需内容。
    *   自动保存机制，防止笔记内容丢失。
    *   支持从聊天消息或其他应用内容“分享到笔记”，快速创建新笔记。
*   **数据存储**:
    *   聊天记录、Agent 配置、笔记内容和附件等数据安全地存储在项目内的 `AppData` 目录中。
*   **VCPLog 集成**:
    *   通过 WebSocket 连接到 VCPLog 服务，实时接收和显示来自 VCP 服务器的日志信息，方便调试和监控。
*   **自定义设置**:
    *   允许用户配置应用程序的一些基本设置，如用户名、VCP 服务器地址、VCPLog 服务地址等。
    *   服务器地址为 `http://yourip:6005/v1/chat/completions`，通知地址通常为 `ws://yourip:6005`。Https则对应wss。
*   **窗口与交互**:
    *   自定义窗口框架和控制按钮（最小化、最大化/还原、关闭）。
    *   提供全局快捷键，例如 `Control+Shift+I` 快速打开开发者工具。
    *   打开外部链接前进行安全检查，提升安全性。
*   **划词小助手 (Selection Assistant)**:
    *   **全局文本监听**: 在设置中启用后，可在任何应用程序中通过鼠标划选文本来激活。
    *   **悬浮动作条**: 划选文本后，会在鼠标附近出现一个悬浮工具条，提供快捷操作按钮（如翻译、总结、解释、搜索）。
    *   **调用内部 Agent**: 所有快捷操作都会调用在设置中预先指定的 VCP Agent 来执行，充分复用现有 AI 能力。
    *   **独立对话窗口**: 点击快捷操作后，会弹出一个独立的、轻量的聊天窗口，显示该 Agent 对划选文本的处理过程和结果。
    *   **无缝体验**: 整个过程无需离开当前工作窗口，实现了高效的即时信息处理。

## 技术栈

*   **Electron**: 用于构建跨平台的桌面应用程序。
*   **Node.js**: 作为后端运行环境。
*   **HTML, CSS, JavaScript**: 构建用户界面。
*   **核心依赖库**:
    *   `fs-extra`: 用于增强的文件系统操作。
    *   `marked`: 用于 Markdown 解析和渲染。
    *   `ws`: 用于 WebSocket 通信 (VCPLog)。
    *   `pdf-parse`: 用于解析 PDF 文件内容以提取文本。
    *   `mammoth`: 用于解析 DOCX 文件内容以提取文本。
*   **前端特性支持**:
    *   `highlight.js`: 用于代码块的语法高亮（通过 CDN 或本地集成）。
    *   `KaTeX`: 用于 LaTeX 数学公式的渲染（通过 CDN 或本地集成）。

## 示例截图

以下是一些客户端界面的示例截图：

![示例图1](assets/E1.jpg)
![示例图2](assets/E2.jpg)
![示例图3](assets/E3.jpg)
![示例图4](assets/E4.jpg)
![示例图5](assets/E6.jpg)
![示例图6](assets/E5.jpg)

## 客户端职责

本客户端主要负责提供与 VCP-AI 进行聊天的用户界面、窗口渲染、Agent 管理、笔记管理、本地数据存储以及与 VCP 服务器和 VCPLog 服务的通信。大部分核心 AI 处理逻辑和工具执行由连接的 VCP 服务器实现。
