from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QLabel, QFrame, QVBoxLayout, QPushButton, 
                             QScrollArea, QGraphicsOpacityEffect, QGraphicsDropShadowEffect)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QEvent, QPoint, pyqtSignal, QRect
from PyQt5.QtGui import QColor, QPalette

class TypingIndicator(QWidget):
    """打字指示器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_animation()
        
    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(50)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # AI头像
        avatar_label = QLabel("🤖")
        avatar_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                background-color: rgba(0, 122, 255, 0.1);
                border-radius: 16px;
                padding: 6px;
                margin-right: 10px;
            }
        """)
        
        # 打字文本
        typing_label = QLabel("AI正在输入")
        typing_label.setStyleSheet("""
            QLabel {
                color: rgba(0, 122, 255, 0.8);
                font-size: 13px;
                font-weight: 500;
            }
        """)
        
        # 动画点
        self.dots_container = QWidget()
        dots_layout = QHBoxLayout(self.dots_container)
        dots_layout.setContentsMargins(0, 0, 0, 0)
        dots_layout.setSpacing(4)
        
        self.dots = []
        for i in range(3):
            dot = QLabel("●")
            dot.setStyleSheet("""
                QLabel {
                    color: #007AFF;
                    font-size: 16px;
                }
            """)
            self.dots.append(dot)
            dots_layout.addWidget(dot)
        
        layout.addWidget(avatar_label)
        layout.addWidget(typing_label)
        layout.addWidget(self.dots_container)
        layout.addStretch()
        
    def setup_animation(self):
        """设置动画"""
        self.animations = []
        
        for i, dot in enumerate(self.dots):
            effect = QGraphicsOpacityEffect()
            dot.setGraphicsEffect(effect)
            
            animation = QPropertyAnimation(effect, b"opacity")
            animation.setDuration(600)
            animation.setStartValue(0.3)
            animation.setEndValue(1.0)
            animation.setLoopCount(-1)  # 无限循环
            animation.setEasingCurve(QEasingCurve.InOutSine)
            
            # 错开动画时间
            QTimer.singleShot(i * 200, animation.start)
            self.animations.append(animation)
    
    def start_animation(self):
        """开始动画"""
        for animation in self.animations:
            if animation.state() != QPropertyAnimation.Running:
                animation.start()
    
    def stop_animation(self):
        """停止动画"""
        for animation in self.animations:
            animation.stop()

class ModernButton(QPushButton):
    """带动画和样式的现代化按钮"""
    
    def __init__(self, text: str = "", icon: str = "", parent=None):
        super().__init__(parent)
        self._text_content = text
        self._icon_content = icon
        self.update_button_text()
        
        self.setCursor(Qt.PointingHandCursor)
        self.setup_animations()
        
        # 默认样式
        self.set_style_type("primary")

    def update_button_text(self):
        """根据图标和文本更新按钮内容"""
        display_text = f"{self._icon_content} {self._text_content}".strip()
        self.setText(display_text)
        
    def setup_animations(self):
        """设置动画效果"""
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(150)
        self.scale_animation.setEasingCurve(QEasingCurve.OutBack)
        
    def set_style_type(self, style_type: str, theme_manager=None):
        """
        设置按钮的样式类型 (例如: 'primary', 'secondary', 'icon')
        如果提供了 theme_manager，将使用主题颜色。
        """
        # 基础样式
        base_style = """
            QPushButton {{
                border: none;
                border-radius: {{radius}};
                padding: {{v_padding}} {{h_padding}};
                font-size: 14px;
                font-weight: 500;
                background-color: {{bg_color}};
                color: {{text_color}};
            }}
            QPushButton:hover {{
                background-color: {{bg_hover}};
            }}
            QPushButton:pressed {{
                background-color: {{bg_pressed}};
            }}
            QPushButton:disabled {{
                background-color: {{bg_color_disabled}};
                color: {{text_color_disabled}};
            }}
        """
        
        # 默认颜色 (如果 theme_manager 不可用)
        default_colors = {
            "primary": "#007AFF", "primary_dark": "#0051D5", "text_inverse": "#FFFFFF",
            "secondary_bg": "#E5E5EA", "secondary_hover": "#D1D1D6", "text_primary": "#000000",
            "text_secondary": "#8A8A8E",
            "disabled_bg": "#B0B0B0", "disabled_text": "#FFFFFF"
        }

        def get_color(name):
            if theme_manager:
                # 尝试从 theme_manager 获取，如果失败则回退到默认值
                return theme_manager.get_color(name) or default_colors.get(name)
            return default_colors.get(name)

        styles = {
            "primary": {
                "radius": "18px", "v_padding": "10px", "h_padding": "20px",
                "bg_color": get_color("primary"), 
                "text_color": get_color("text_inverse"),
                "bg_hover": get_color("primary_dark"), 
                "bg_pressed": get_color("primary_dark"),
                "bg_color_disabled": get_color("disabled_bg"),
                "text_color_disabled": get_color("disabled_text")
            },
            "secondary": {
                "radius": "18px", "v_padding": "10px", "h_padding": "20px",
                "bg_color": get_color("secondary_bg"), 
                "text_color": get_color("text_primary"),
                "bg_hover": get_color("secondary_hover"), 
                "bg_pressed": get_color("secondary_hover"),
                "bg_color_disabled": get_color("disabled_bg"),
                "text_color_disabled": get_color("disabled_text")
            },
            "icon": {
                "radius": "18px", "v_padding": "8px", "h_padding": "8px",
                "bg_color": "transparent", 
                "text_color": get_color("text_secondary"),
                "bg_hover": get_color("secondary_bg"), 
                "bg_pressed": get_color("secondary_hover"),
                "bg_color_disabled": "transparent",
                "text_color_disabled": get_color("disabled_text")
            }
        }
        
        style_config = styles.get(style_type, styles["primary"])
        self.setStyleSheet(base_style.format(**style_config))

    def enterEvent(self, event):
        """鼠标进入事件"""
        self.scale_animation.stop()
        start_rect = self.geometry()
        end_rect = start_rect.adjusted(-2, -2, 2, 2)
        self.scale_animation.setStartValue(start_rect)
        self.scale_animation.setEndValue(end_rect)
        self.scale_animation.start()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.scale_animation.stop()
        start_rect = self.geometry()
        end_rect = start_rect.adjusted(2, 2, -2, -2)
        self.scale_animation.setStartValue(start_rect)
        self.scale_animation.setEndValue(end_rect)
        self.scale_animation.start()
        super().leaveEvent(event)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        self.scale_animation.stop()
        start_rect = self.geometry()
        # 调整为更小的尺寸
        end_rect = QRect(
            start_rect.x() + 1, 
            start_rect.y() + 1,
            start_rect.width() - 2,
            start_rect.height() - 2
        )
        self.scale_animation.setStartValue(start_rect)
        self.scale_animation.setEndValue(end_rect)
        self.scale_animation.start()
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件，恢复到悬停状态"""
        self.scale_animation.stop()
        start_rect = self.geometry()
        # 恢复到悬停尺寸 (比原始大)
        end_rect = QRect(
            start_rect.x() - 3,
            start_rect.y() - 3,
            start_rect.width() + 6,
            start_rect.height() + 6
        )
        self.scale_animation.setStartValue(start_rect)
        self.scale_animation.setEndValue(end_rect)
        self.scale_animation.start()
        super().mouseReleaseEvent(event)

class SmartScrollArea(QScrollArea):
    """能判断是否在底部的智能滚动区域"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_at_bottom = True
        self.setup_ui()
        self.verticalScrollBar().rangeChanged.connect(self._on_range_changed)
        self.verticalScrollBar().valueChanged.connect(self._on_scroll_changed)

    def setup_ui(self):
        """设置UI"""
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setFrameShape(QFrame.NoFrame)

    def _on_range_changed(self, min_val, max_val):
        """当内容范围变化时（例如添加新消息）"""
        if self.is_at_bottom:
            self.scroll_to_bottom(animated=True)

    def _on_scroll_changed(self, value):
        """当滚动条位置变化时"""
        scroll_bar = self.verticalScrollBar()
        self.is_at_bottom = (value >= scroll_bar.maximum())

    def scroll_to_bottom(self, animated: bool = True):
        """滚动到底部"""
        scroll_bar = self.verticalScrollBar()
        if animated:
            self.scroll_animation = QPropertyAnimation(scroll_bar, b"value")
            self.scroll_animation.setDuration(400)
            self.scroll_animation.setStartValue(scroll_bar.value())
            self.scroll_animation.setEndValue(scroll_bar.maximum())
            self.scroll_animation.setEasingCurve(QEasingCurve.OutCubic)
            self.scroll_animation.start()
        else:
            scroll_bar.setValue(scroll_bar.maximum())

    def enable_scroll_animation(self):
        """
        (此方法可能不再需要，因为动画直接在scroll_to_bottom中创建)
        但保留以防未来需要更复杂的动画控制
        """
        pass 