from PyQt5.QtWidgets import QWidget, QGraphicsOpacityEffect
from PyQt5.QtCore import QPropertyAnimation, QEasingCurve, QPoint

class AnimationManager:
    """动画管理器"""
    
    @staticmethod
    def create_fade_in(widget: QWidget, duration: int = 300) -> QPropertyAnimation:
        """创建淡入动画"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation
    
    @staticmethod
    def create_slide_in(widget: QWidget, direction: str = "bottom", duration: int = 400) -> QPropertyAnimation:
        """创建滑入动画"""
        start_pos = widget.pos()
        
        if direction == "bottom":
            end_pos = start_pos
            start_pos = QPoint(start_pos.x(), start_pos.y() + 50)
        elif direction == "right":
            end_pos = start_pos  
            start_pos = QPoint(start_pos.x() + 50, start_pos.y())
        else:
            end_pos = start_pos
        
        widget.move(start_pos)
        
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(end_pos)
        animation.setEasingCurve(QEasingCurve.OutBack)
        return animation
    
    @staticmethod
    def create_scale_in(widget: QWidget, duration: int = 200) -> QPropertyAnimation:
        """创建缩放动画"""
        # 这里需要自定义属性来实现缩放，PyQt5中比较复杂
        # 简化版本，使用透明度代替
        return AnimationManager.create_fade_in(widget, duration) 