import os
import tempfile
import uuid
import logging
from typing import Optional

from PyQt5.QtWidgets import (QWidget, QTextEdit, QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
                             QFileDialog, QDialog, QShortcut, QScrollArea, QPushButton, QApplication, QMessageBox)
from PyQt5.QtCore import pyqtSignal, QMimeData, Qt, QEvent, QPropertyAnimation, QEasingCurve, QUrl
from PyQt5.QtGui import QKeySequence, QTextCursor, QImageReader

from .widgets import ModernButton

logger = logging.getLogger(__name__)

class DroppableTextEdit(QTextEdit):
    """
    自定义QTextEdit，用于处理文件拖拽，插入清理后的文件路径。
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True) # 确保接受拖拽

    def canInsertFromMimeData(self, source: QMimeData) -> bool:
        # 我们主要关心包含URL的拖拽数据，但也允许默认的文本粘贴
        if source.hasUrls():
            return True
        return super().canInsertFromMimeData(source)

    def insertFromMimeData(self, source: QMimeData):
        if source.hasUrls():
            urls = source.urls()
            if urls:
                q_url = urls[0]
                cleaned_path = ""

                # 优先使用 toLocalFile()，它通常能正确处理本地文件路径
                if q_url.isLocalFile():
                    cleaned_path = q_url.toLocalFile()
                
                # 如果 toLocalFile() 的结果为空，或仍然像一个完整的URI (以 'file:' 开头)，
                # 则尝试从URL的 path() 组件解析，并进行更手动的清理。
                # 同时，确保即使 toLocalFile() 返回了内容，如果它不符合预期，也进行清理。
                if not cleaned_path or cleaned_path.lower().startswith("file:"):
                    path_component = q_url.path()
                    # 对于Windows路径，q_url.path() 可能返回 "/E:/folder/file.txt"
                    # 我们需要移除开头的斜杠
                    if len(path_component) > 1 and path_component.startswith('/') and \
                       path_component[1].isalpha() and path_component[2] == ':':
                        cleaned_path = path_component[1:]
                    else:
                        cleaned_path = path_component # 对于非此类Windows路径或Unix路径
                
                # 进一步清理任何残留的 "file://" 或 "file:///" 前缀
                # 转换为小写进行不区分大小写的检查，但从原始路径中切片
                temp_lower_path = cleaned_path.lower()
                if temp_lower_path.startswith("file:///"):
                    cleaned_path = cleaned_path[len("file:///"):]
                elif temp_lower_path.startswith("file://"):
                    cleaned_path = cleaned_path[len("file://"):]
                
                # 将所有反斜杠替换为正斜杠
                if cleaned_path:
                    cleaned_path = cleaned_path.replace('\\\\', '/') # Python中反斜杠需要转义

                # 解码可能存在的URL编码字符 (例如 %20 代表空格)
                if cleaned_path:
                    try:
                        # QUrl.fromPercentEncoding 需要 QByteArray
                        path_bytes = cleaned_path.encode('utf-8', 'surrogatepass')
                        decoded_bytes = QUrl.fromPercentEncoding(path_bytes)
                        cleaned_path = decoded_bytes.decode('utf-8', 'replace')
                    except Exception as e:
                        logger.warning(f"路径 '{cleaned_path}' 百分号解码失败: {e}")
                
                # 插入清理后的路径文本
                if cleaned_path: # 确保有内容可插
                    self.insertPlainText(cleaned_path)
                    
                    # ----- 触发附件设置逻辑 -----
                    # 假设 DroppableTextEdit 的父级是 ModernInputArea
                    # 如果 parent 是 ModernInputArea 实例，则调用其方法设置附件
                    parent_widget = self.parent()
                    if isinstance(parent_widget, ModernInputArea):
                        # 判断是图片还是普通附件
                        img_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']
                        if any(cleaned_path.lower().endswith(ext) for ext in img_extensions):
                            parent_widget.set_image_attachment(cleaned_path)
                        else:
                            parent_widget.set_file_attachment(cleaned_path)
                    else:
                        logger.warning("DroppableTextEdit的父组件不是ModernInputArea，无法自动设置附件。")
                    return # 已处理

        # 如果不是我们处理的URL或处理失败，则执行默认行为（例如粘贴普通文本）
        super().insertFromMimeData(source)


class ModernInputArea(QWidget):
    """现代化输入区域"""
    
    sendMessage = pyqtSignal(str, str, str)  # text, image_path, attachment_path
    voiceRecordingToggled = pyqtSignal(bool)
    gameCompanionModeToggled = pyqtSignal(bool)  # 游戏伴侣模式切换信号
    
    def __init__(self, parent=None, theme_manager=None, app_controller=None): # <--- 增加 app_controller 参数
        super().__init__(parent)
        self.theme_manager = theme_manager
        self.app_controller = app_controller # <--- 保存 app_controller 引用
        self.selected_image_path = None
        self.selected_attachment_path = None
        self.is_recording = False
        
        # 输入历史
        self.input_history = []
        self.history_index = -1
        self.max_history = 50
        
        self.setup_ui()
        self.setup_shortcuts()
        
    def setup_ui(self):
        """设置UI"""
        # 添加输入历史缓存 (优化建议 2)
        self.history_cache = {}

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 20)
        layout.setSpacing(12)
        
        # 文件预览区域
        self.preview_area = self.create_preview_area()
        layout.addWidget(self.preview_area)
        
        # 工具栏
        self.toolbar = self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # 输入区域
        self.input_container = self.create_input_container()
        layout.addWidget(self.input_container)
        
        # 应用样式
        self.apply_styling()
    
    def create_preview_area(self) -> QWidget:
        """创建文件预览区域"""
        container = QWidget()
        container.hide()  # 默认隐藏
        
        layout = QHBoxLayout(container)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(10)
        
        # 预览标签
        self.preview_icon = QLabel()
        self.preview_icon.setFixedSize(32, 32)
        self.preview_icon.setAlignment(Qt.AlignCenter)
        
        self.preview_text = QLabel()
        self.preview_text.setWordWrap(True)
        
        # 移除按钮
        self.remove_preview_btn = ModernButton("✕")
        self.remove_preview_btn.setFixedSize(24, 24)
        self.remove_preview_btn.clicked.connect(self.clear_attachments)
        
        layout.addWidget(self.preview_icon)
        layout.addWidget(self.preview_text, 1)
        layout.addWidget(self.remove_preview_btn)
        
        return container
    
    def create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 工具按钮配置
        tools = [
            ("📎", "附件", self.select_attachment, "attachment"),
            ("🖼️", "图片", self.select_image, "image"),
            ("🎤", "语音输入", self.toggle_asr_button_action, "asr_voice_input"),
            ("😊", "表情", self.show_emoji_picker, "emoji"),
            ("📸", "截图", self.take_screenshot, "screenshot"),
            ("🗣️", "语音设置", self._show_tts_settings_dialog, "tts_settings"),
            ("🎮", "游戏伴侣", self.toggle_game_companion_mode, "game_companion"),
            ("✨", "划词助手", self.toggle_selection_assistant, "selection_assistant"),  # 新增
        ]
        
        self.tool_buttons = {}
        for icon, tooltip, callback, key in tools:
            btn = ModernButton(icon=icon)
            btn.setFixedSize(36, 36)
            btn.setToolTip(tooltip)
            btn.clicked.connect(callback)
            
            if self.theme_manager:
                btn.set_style_type("tool", self.theme_manager)
            
            layout.addWidget(btn)
            self.tool_buttons[key] = btn
        
        layout.addStretch()
        
        # 模型选择器
        self.model_selector = self.create_model_selector()
        layout.addWidget(self.model_selector)
        
        # 游戏伴侣按钮特殊配置
        self.game_companion_button = self.tool_buttons.get("game_companion")
        if self.game_companion_button:
            self.game_companion_button.setCheckable(True)  # 设为可切换按钮

        # 划词助手按钮特殊配置
        self.selection_assistant_button = self.tool_buttons.get("selection_assistant")
        if self.selection_assistant_button:
            self.selection_assistant_button.setCheckable(True)  # 设为可切换按钮
            self.selection_assistant_button.setToolTip("划词助手 (点击启用)")

        return toolbar

    def _show_tts_settings_dialog(self):
        """显示TTS设置对话框。"""
        if self.app_controller and hasattr(self.app_controller, '_open_tts_settings_dialog'):
            self.app_controller._open_tts_settings_dialog()
        else:
            logger.error("ModernInputArea: AppController不可用或缺少 _open_tts_settings_dialog 方法。")
            QMessageBox.critical(self, "错误", "无法访问核心控制器或功能已过时，无法打开语音设置。")
    
    def create_model_selector(self) -> QComboBox:
        """创建模型选择器"""
        selector = QComboBox()
        selector.setMinimumWidth(120)
        selector.setToolTip("选择AI模型")
        
        # 从app_controller获取可用模型
        if hasattr(self.parent(), 'app_controller') and self.parent().app_controller:
            models = getattr(self.parent().app_controller, 'available_models', [])
            current_model = getattr(self.parent().app_controller.data_manager, 'api_model_name', '')
            
            for model in models:
                selector.addItem(model)
                
            # 设置当前模型
            if current_model in models:
                selector.setCurrentText(current_model)
        
        return selector
    
    def create_input_container(self) -> QWidget:
        """创建输入容器"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)
        
        # 输入框
        self.input_field = DroppableTextEdit(self) # <--- 使用 DroppableTextEdit
        self.input_field.setPlaceholderText("输入消息... (Enter发送, Shift+Enter换行)") # 更新提示文本
        self.input_field.setMaximumHeight(120)
        self.input_field.setMinimumHeight(44)
        self.input_field.textChanged.connect(self.on_text_changed)
        self.input_field.installEventFilter(self)
        
        # 发送按钮
        self.send_button = ModernButton(icon="➤")
        self.send_button.setFixedSize(44, 44)
        self.send_button.setToolTip("发送消息 (Ctrl+Enter)")
        self.send_button.clicked.connect(self.send_message)
        self.send_button.setEnabled(False)
        
        if self.theme_manager:
            self.send_button.set_style_type("primary", self.theme_manager)
        
        layout.addWidget(self.input_field, 1)
        layout.addWidget(self.send_button)
        
        return container
    
    def setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+Enter 发送 (现在由 eventFilter 处理 Enter，此快捷键可以移除或保留作为备用)
        # send_shortcut = QShortcut(QKeySequence("Ctrl+Return"), self) # 移除 Ctrl+Enter 快捷键
        # send_shortcut.activated.connect(self.send_message)
        
        # Ctrl+Shift+V 粘贴图片
        paste_image_shortcut = QShortcut(QKeySequence("Ctrl+Shift+V"), self)
        paste_image_shortcut.activated.connect(self.paste_image_from_clipboard)
        
        # Ctrl+L 清空输入
        clear_shortcut = QShortcut(QKeySequence("Ctrl+L"), self)
        clear_shortcut.activated.connect(self.clear_input)
    
    def eventFilter(self, obj, event):
        if obj is self.input_field and event.type() == QEvent.KeyPress:
            if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                if event.modifiers() == Qt.NoModifier:
                    # 检查输入框是否有内容，避免发送空消息 (如果send_message本身不处理)
                    if self.input_field.toPlainText().strip():
                        self.send_message()
                    return True  # 事件已处理，阻止默认换行
                elif event.modifiers() == Qt.ShiftModifier:
                    # Shift+Enter: 允许默认行为 (插入新行)
                    return super().eventFilter(obj, event) # 或者直接 return False
            
            # 处理历史记录导航 (上箭头和下箭头)
            if event.key() == Qt.Key_Up and (event.modifiers() == Qt.NoModifier or event.modifiers() == Qt.ShiftModifier) : # Shift+Up也导航
                self.navigate_history_up()
                return True # 事件已处理
            elif event.key() == Qt.Key_Down and (event.modifiers() == Qt.NoModifier or event.modifiers() == Qt.ShiftModifier): # Shift+Down也导航
                self.navigate_history_down()
                return True # 事件已处理

        return super().eventFilter(obj, event)

    def apply_styling(self):
        """应用样式"""
        if not self.theme_manager:
            return
        
        theme = self.theme_manager.get_current_theme()
        
        # 主容器样式
        self.setStyleSheet(f"""
            ModernInputArea {{
                background-color: {theme['window_bg']};
                border-radius: 20px;
            }}
        """)
        
        # 预览区域样式
        self.preview_area.setStyleSheet(f"""
            QWidget {{
                background-color: {theme['bubble_system_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 12px;
            }}
            QLabel {{
                color: {theme['text_primary']};
                font-size: 12px;
            }}
        """)
        
        # 输入框样式
        self.input_field.setStyleSheet(f"""
            QTextEdit {{
                background-color: {theme['input_bg']};
                border: 2px solid transparent;
                border-radius: 22px;
                padding: 12px 16px;
                font-size: 14px;
                color: {theme['text_primary']};
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
                selection-background-color: {theme['primary']};
            }}
            QTextEdit:focus {{
                border-color: {theme['primary']};
            }}
            QTextEdit QScrollBar:vertical {{
                background-color: transparent;
                width: 8px;
                border-radius: 4px;
            }}
            QTextEdit QScrollBar::handle:vertical {{
                background-color: {theme['text_tertiary']};
                border-radius: 4px;
                min-height: 20px;
            }}
        """)
        
        # 模型选择器样式
        self.model_selector.setStyleSheet(f"""
            QComboBox {{
                background-color: {theme['input_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 15px;
                padding: 6px 12px;
                font-size: 12px;
                color: {theme['text_primary']};
                min-width: 100px;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {theme['text_secondary']};
                margin-right: 5px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {theme['window_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                selection-background-color: {theme['primary']};
                color: {theme['text_primary']};
            }}
        """)
    
    def eventFilter(self, obj, event):
        """事件过滤器，实现Enter发送，Shift+Enter换行"""
        if obj is self.input_field and event.type() == QEvent.KeyPress:
            key = event.key()
            modifiers = event.modifiers()

            if key == Qt.Key_Return or key == Qt.Key_Enter:
                if modifiers == Qt.NoModifier: # 单独Enter键
                    if self.input_field.toPlainText().strip(): # 确保有内容才发送
                        self.send_message()
                    event.accept() # 接受事件
                    return True  # 事件已处理，阻止默认的换行行为
                elif modifiers == Qt.ShiftModifier: # Shift + Enter
                    # 允许默认行为 (插入新行)
                    # 不调用 event.accept()，也不返回 True，让父类处理或QTextEdit自行处理
                    return super().eventFilter(obj, event)
            
            # 上下箭头浏览历史 (保留原有逻辑)
            elif key == Qt.Key_Up and (modifiers == Qt.NoModifier or modifiers == Qt.ShiftModifier):
                self.browse_history(-1)
                event.accept()
                return True
            elif key == Qt.Key_Down and (modifiers == Qt.NoModifier or modifiers == Qt.ShiftModifier):
                self.browse_history(1)
                event.accept()
                return True
        
        return super().eventFilter(obj, event) # 其他事件交由父类处理
    
    def on_text_changed(self):
        """文本改变时的处理"""
        text = self.input_field.toPlainText().strip()
        
        # 动态调整高度
        self.adjust_input_height()
        
        # 更新发送按钮状态
        has_content = bool(text) or bool(self.selected_image_path) or bool(self.selected_attachment_path)
        self.send_button.setEnabled(has_content)
        
        # 重置历史浏览索引
        self.history_index = -1
    
    def adjust_input_height(self):
        """动态调整输入框高度"""
        doc = self.input_field.document()
        height = doc.size().height() + 24  # 加上padding
        height = max(44, min(120, height))
        
        # 平滑高度过渡
        if hasattr(self, 'height_animation'):
            self.height_animation.stop()
        
        self.height_animation = QPropertyAnimation(self.input_field, b"minimumHeight")
        self.height_animation.setDuration(150)
        self.height_animation.setStartValue(self.input_field.minimumHeight())
        self.height_animation.setEndValue(int(height))
        self.height_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.height_animation.start()
    
    def browse_history(self, direction: int):
        """浏览输入历史 - 使用缓存优化"""
        if not self.input_history:
            return
            
        # 使用缓存避免重复计算 (优化建议 2)
        # 注意：这里的缓存逻辑可能过于简单，如果历史记录很多，缓存会很大
        # 并且缓存键可能不是最优的，因为索引会变。
        # 一个更简单的优化可能是只缓存上一次和下一次的结果，或者完全移除这个缓存，
        # 因为浏览历史的操作频率通常不高，计算成本也相对较低。
        # 暂时按照建议实现，但标记为潜在问题。
        
        # 尝试从缓存获取
        # cache_key = f"{self.history_index}_{direction}" # 这个 key 在索引变化后会失效
        # 更可靠的 key 可能是目标索引
        target_index = self.history_index + direction
        target_index = max(-1, min(len(self.input_history) - 1, target_index))
        cache_key = str(target_index) # 使用目标索引作为 key

        if cache_key in self.history_cache:
            text = self.history_cache[cache_key]
            self.input_field.setText(text)
            # 将光标移到末尾
            cursor = self.input_field.textCursor()
            cursor.movePosition(QTextCursor.End)
            self.input_field.setTextCursor(cursor)
            self.history_index = target_index # 更新当前索引
            return

        # --- 原有计算逻辑 ---
        # 保存当前输入（如果是第一次浏览历史）
        if self.history_index == -1:
            current_text = self.input_field.toPlainText().strip()
            if current_text:
                self.current_input = current_text
        
        # 更新索引
        self.history_index += direction
        self.history_index = max(-1, min(len(self.input_history) - 1, self.history_index))
        
        # 设置文本
        if self.history_index == -1:
            # 恢复当前输入
            text = getattr(self, 'current_input', '')
        else:
            text = self.input_history[-(self.history_index + 1)] # 注意：这里用的是更新前的 history_index
        
        # --- 计算结束 ---

        # 缓存结果
        self.history_cache[cache_key] = text

        self.input_field.setText(text)
        self.history_index = target_index # 更新当前索引

        # 将光标移到末尾
        cursor = self.input_field.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.input_field.setTextCursor(cursor)
    
    def send_message(self):
        """发送消息"""
        text = self.input_field.toPlainText().strip()
        
        if not text and not self.selected_image_path and not self.selected_attachment_path:
            return
        
        # 添加到历史记录
        if text and (not self.input_history or self.input_history[-1] != text):
            self.input_history.append(text)
            if len(self.input_history) > self.max_history:
                self.input_history.pop(0)
        
        # 发出信号
        self.sendMessage.emit(text, self.selected_image_path or "", self.selected_attachment_path or "")
        
        # 清空输入
        self.clear_input()
    
    def clear_input(self):
        """清空输入"""
        self.input_field.clear()
        self.clear_attachments()
        self.history_index = -1
    
    def select_image(self):
        """选择图片"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择图片",
            "",
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp *.webp);;所有文件 (*.*)"
        )
        
        if file_path:
            self.set_image_attachment(file_path)
    
    def select_attachment(self):
        """选择附件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择附件", 
            "",
            "所有文件 (*.*)"
        )
        
        if file_path:
            self.set_file_attachment(file_path)
    
    def set_image_attachment(self, file_path: str):
        """设置图片附件"""
        self.selected_image_path = file_path
        self.selected_attachment_path = None  # 清除其他附件
        self.update_preview()
    
    def set_file_attachment(self, file_path: str):
        """设置文件附件"""
        self.selected_attachment_path = file_path
        self.selected_image_path = None  # 清除图片
        self.update_preview()
    
    def update_preview(self):
        """更新预览"""
        if self.selected_image_path:
            self.preview_icon.setText("🖼️")
            filename = os.path.basename(self.selected_image_path)
            self.preview_text.setText(f"图片: {filename}")
            self.preview_area.show()
        elif self.selected_attachment_path:
            self.preview_icon.setText("📎")
            filename = os.path.basename(self.selected_attachment_path)
            self.preview_text.setText(f"附件: {filename}")
            self.preview_area.show()
        else:
            self.preview_area.hide()
        
        # 更新发送按钮状态
        self.on_text_changed()
    
    def clear_attachments(self):
        """清除附件"""
        self.selected_image_path = None
        self.selected_attachment_path = None
        self.preview_area.hide()
        self.on_text_changed()
    
    def toggle_voice_recording(self):
        """切换语音录制"""
        self.is_recording = not self.is_recording
        
        btn = self.tool_buttons.get("voice")
        if btn:
            if self.is_recording:
                btn.setText("⏹️")
                btn.setToolTip("停止录音")
            else:
                btn.setText("🎤")
                btn.setToolTip("开始录音")
        
        self.voiceRecordingToggled.emit(self.is_recording)

    def toggle_asr_button_action(self):
        """ASR语音输入按钮点击处理"""
        if self.app_controller and hasattr(self.app_controller, 'toggle_asr_listening'):
            # AppController 会处理状态切换和UI更新信号的发射
            self.app_controller.toggle_asr_listening()
        else:
            logger.warning("ModernInputArea: AppController不可用，无法切换ASR。")
    
    def show_emoji_picker(self):
        """显示表情选择器"""
        emojis = [
            "😊", "😂", "🤣", "😍", "😘", "😗", "😙", "😚", "🙂", "🤗",
            "🤔", "😐", "😑", "😶", "🙄", "😏", "😣", "😥", "😮", "🤐",
            "😯", "😪", "😫", "🥱", "😴", "😌", "😛", "😜", "😝", "🤤",
            "👍", "👎", "👌", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉",
            "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔",
            "💯", "💢", "💥", "💫", "💦", "💨", "🕳️", "💭", "🗯️", "💬"
        ]
        
        # 创建表情选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择表情")
        dialog.setModal(True)
        dialog.resize(300, 200)
        
        layout = QVBoxLayout(dialog)
        
        # 表情网格
        scroll_area = QScrollArea()
        emoji_widget = QWidget()
        emoji_layout = QHBoxLayout(emoji_widget)
        emoji_layout.setSpacing(5)
        
        # 每行10个表情
        row_layout = None
        for i, emoji in enumerate(emojis):
            if i % 10 == 0:
                if row_layout:
                    emoji_layout.addLayout(row_layout)
                row_layout = QVBoxLayout()
            
            btn = QPushButton(emoji)
            btn.setFixedSize(30, 30)
            btn.clicked.connect(lambda checked, e=emoji: self.insert_emoji(e, dialog))
            row_layout.addWidget(btn)
        
        if row_layout:
            emoji_layout.addLayout(row_layout)
        
        scroll_area.setWidget(emoji_widget)
        layout.addWidget(scroll_area)
        
        dialog.exec_()
    
    def insert_emoji(self, emoji: str, dialog: QDialog):
        """插入表情"""
        cursor = self.input_field.textCursor()
        cursor.insertText(emoji)
        self.input_field.setFocus()
        dialog.accept()
    
    def take_screenshot(self):
        """截图功能"""
        print("DEBUG: ModernInputArea.take_screenshot() 方法被调用了！") # <--- 调试打印1
        
        parent_widget = self.parent() # ModernInputArea 的父组件应该是 ChatWindow
        # 在 ModernInputArea 的 __init__ 中，app_controller 已经作为参数传入并保存为 self.app_controller
        # 因此，我们应该直接使用 self.app_controller
        
        if hasattr(self, 'app_controller') and self.app_controller is not None:
            print(f"DEBUG: ModernInputArea 有 app_controller 实例: {type(self.app_controller)}")
            if hasattr(self.app_controller, 'trigger_screenshot_capture'):
                print("DEBUG: app_controller 有 trigger_screenshot_capture 方法，准备调用。")
                self.app_controller.trigger_screenshot_capture()
            else:
                print("ERROR: app_controller 实例上没有找到 trigger_screenshot_capture 方法！")
        else:
            print("ERROR: ModernInputArea 实例上没有找到 app_controller (self.app_controller is None or missing)！")
            # 作为备选，检查父组件，但这不应该是主要逻辑
            if parent_widget:
                print(f"DEBUG: ModernInputArea 的父组件是: {type(parent_widget)}")
                app_controller_instance_on_parent = getattr(parent_widget, 'app_controller', None)
                if app_controller_instance_on_parent:
                    print(f"DEBUG: 在父组件上找到了 app_controller 实例: {type(app_controller_instance_on_parent)}")
                    if hasattr(app_controller_instance_on_parent, 'trigger_screenshot_capture'):
                        print("DEBUG: 父组件的 app_controller 有 trigger_screenshot_capture 方法，准备调用。")
                        app_controller_instance_on_parent.trigger_screenshot_capture()
                    else:
                        print("ERROR: 父组件的 app_controller 实例上没有找到 trigger_screenshot_capture 方法！")
                else:
                    print("ERROR: 在父组件上也没有找到 app_controller 实例！")
            else:
                print("ERROR: ModernInputArea 没有父组件！这不正常。")
    
    def toggle_game_companion_mode(self):
        """切换游戏伴侣模式"""
        if not self.game_companion_button:
            return
            
        is_checked = self.game_companion_button.isChecked()
        if is_checked:
            self.game_companion_button.setText("🛑")  # 按下时显示停止图标
            self.game_companion_button.setToolTip("停止游戏伴侣模式")
        else:
            self.game_companion_button.setText("🎮")  # 弹起时显示游戏手柄图标
            self.game_companion_button.setToolTip("开启游戏伴侣模式")
        
        # 发送信号到ChatWindow
        self.gameCompanionModeToggled.emit(is_checked)
    
    def paste_image_from_clipboard(self):
        """从剪贴板粘贴图片"""
        clipboard = QApplication.clipboard()
        mime_data = clipboard.mimeData()
        
        if mime_data.hasImage():
            image = clipboard.image()
            if not image.isNull():
                # 保存到临时文件
                temp_path = tempfile.mktemp(suffix='.png')
                if image.save(temp_path):
                    self.set_image_attachment(temp_path)
        elif mime_data.hasUrls():
            # 处理文件拖拽
            urls = mime_data.urls()
            if urls:
                q_url = urls[0]
                file_path = ""

                # 尝试多种方式获取和清理路径
                if q_url.isLocalFile(): # 首先检查是否是本地文件URL
                    local_file_attempt = q_url.toLocalFile()
                    # toLocalFile() 在Windows上应该返回类似 E:/path/file.txt 或 E:\path\file.txt
                    # 它不应该再包含 "file:///" 前缀
                    file_path = local_file_attempt
                
                # 如果 toLocalFile() 结果为空或仍然像一个URI，尝试从path()组件获取
                if not file_path or file_path.lower().startswith("file:"):
                    path_component = q_url.path() # 可能返回 /E:/path/file.txt
                    if len(path_component) > 1 and path_component.startswith('/') and path_component[1].isalpha() and path_component[2] == ':':
                        # 移除Windows路径前多余的斜杠, e.g., /E:/ -> E:/
                        file_path = path_component[1:]
                    else:
                        file_path = path_component
                
                # 再次积极清理任何残留的 "file:" 前缀 (转换为小写以进行不区分大小写的检查)
                # 确保即时前面的逻辑未能完全清除，这里也能处理
                temp_lower_path = file_path.lower()
                if temp_lower_path.startswith("file:///"):
                    file_path = file_path[len("file:///"):]
                elif temp_lower_path.startswith("file://"):
                    file_path = file_path[len("file://"):]

                # 将所有反斜杠替换为正斜杠
                if file_path: # 确保 file_path 非空
                    file_path = file_path.replace('\\', '/')
                
                # 解码可能存在的URL编码字符 (如 %20 代表空格)
                if file_path:
                    try:
                        # QUrl.fromPercentEncoding 需要 QByteArray
                        file_path_bytes = file_path.encode('utf-8', 'surrogatepass') # 编码为字节，处理可能存在的孤立代理对
                        decoded_path_bytes = QUrl.fromPercentEncoding(file_path_bytes)
                        file_path = decoded_path_bytes.decode('utf-8', 'replace') # 解码回字符串，替换无法解码的字符
                    except Exception as e:
                        logger.warning(f"Error during percent decoding of path '{file_path}': {e}")


                if file_path and os.path.exists(file_path):
                    # 判断是否为图片
                    img_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']
                    if any(file_path.lower().endswith(ext) for ext in img_extensions):
                        self.set_image_attachment(file_path)
                    else:
                        self.set_file_attachment(file_path)

    def __del__(self):
        """析构函数 - 确保资源清理 (优化建议 8)"""
        if hasattr(self, 'adjust_timer'): # 检查是否有 adjust_timer 属性
            try:
                self.adjust_timer.stop()
                # self.adjust_timer.deleteLater() # QTimer 通常不需要手动 deleteLater，会被父对象管理
                logger.debug("Adjust timer stopped in ModernInputArea.__del__")
            except Exception as e:
                logger.warning(f"Error stopping adjust_timer in ModernInputArea.__del__: {e}")
        # 清理其他可能需要释放的资源，例如 history_cache
        self.history_cache.clear()
        logger.debug("ModernInputArea resources cleaned up.")

    def toggle_selection_assistant(self):
        """切换划词助手状态"""
        if self.app_controller and hasattr(self.app_controller, 'toggle_selection_assistant'):
            success = self.app_controller.toggle_selection_assistant()

            # 更新按钮状态
            selection_button = self.tool_buttons.get("selection_assistant")
            if selection_button:
                is_enabled = self.app_controller.is_selection_assistant_enabled()
                selection_button.setCheckable(True)
                selection_button.setChecked(is_enabled)

                # 更新按钮样式
                if is_enabled:
                    selection_button.setStyleSheet(
                        selection_button.styleSheet() +
                        "background-color: #4CAF50; color: white;"
                    )
                    selection_button.setToolTip("划词助手 (已启用)")
                else:
                    # 重置样式
                    if self.theme_manager:
                        selection_button.set_style_type("tool", self.theme_manager)
                    selection_button.setToolTip("划词助手 (已禁用)")
        else:
            logger.warning("ModernInputArea: AppController不可用，无法切换划词助手。")
            QMessageBox.warning(self, "警告", "划词助手功能不可用")
