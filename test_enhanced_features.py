#!/usr/bin/env python3
"""
测试增强功能脚本
用于验证划词助手和输入增强功能是否正常工作
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt, QTimer

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_selection_assistant():
    """测试划词助手功能"""
    print("=" * 50)
    print("测试划词助手功能")
    print("=" * 50)
    
    try:
        from aipet.modules.selection_assistant import SelectionAssistant
        
        # 创建一个模拟的 app_controller
        class MockAppController:
            def send_message(self, message):
                print(f"✓ 收到划词助手消息: {message[:100]}...")
        
        mock_controller = MockAppController()
        assistant = SelectionAssistant(mock_controller)
        
        print("✓ 划词助手模块导入成功")
        print("✓ 划词助手实例创建成功")
        
        # 测试启动和停止
        assistant.start()
        print("✓ 划词助手启动成功")
        
        time.sleep(1)
        
        assistant.stop()
        print("✓ 划词助手停止成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 划词助手测试失败: {e}")
        return False

def test_input_enhancer():
    """测试输入增强功能"""
    print("=" * 50)
    print("测试输入增强功能")
    print("=" * 50)
    
    try:
        from aipet.modules.input_enhancer import InputEnhancer
        from PyQt5.QtWidgets import QTextEdit
        
        # 创建一个模拟的输入区域
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        text_edit = QTextEdit()
        
        # 创建一个模拟的输入区域对象
        class MockInputArea:
            def __init__(self):
                self.input_field = text_edit
            
            def apply_styling(self):
                pass
        
        mock_input_area = MockInputArea()
        enhancer = InputEnhancer(mock_input_area)
        
        print("✓ 输入增强器模块导入成功")
        print("✓ 输入增强器实例创建成功")
        
        # 测试设置增强功能
        enhancer.setup_enhanced_input()
        print("✓ 输入增强功能设置成功")
        
        # 测试文件信息获取
        current_file = __file__
        file_info = enhancer.get_file_info(current_file)
        if file_info:
            print(f"✓ 文件信息获取成功: {file_info['name']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 输入增强器测试失败: {e}")
        return False

def test_integration():
    """测试集成功能"""
    print("=" * 50)
    print("测试主程序集成")
    print("=" * 50)
    
    try:
        # 测试主程序导入
        from aipet.main import AppController
        print("✓ 主程序模块导入成功")
        
        # 检查是否有增强功能相关的属性
        import inspect
        methods = [method for method in dir(AppController) if 'selection' in method.lower()]
        if methods:
            print(f"✓ 发现划词助手相关方法: {methods}")
        
        return True
        
    except Exception as e:
        print(f"✗ 主程序集成测试失败: {e}")
        return False

class TestWindow(QWidget):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AiPet 增强功能测试")
        self.setGeometry(100, 100, 600, 400)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("AiPet 增强功能测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 说明
        info = QLabel("""
        这个测试程序验证以下功能：
        1. 划词助手模块是否正常导入和工作
        2. 输入增强器是否正常导入和工作
        3. 主程序是否正确集成了新功能
        
        请查看控制台输出了解详细测试结果。
        """)
        info.setStyleSheet("background: #f0f0f0; padding: 15px; border-radius: 8px; margin: 10px;")
        layout.addWidget(info)
        
        # 测试文本区域
        self.test_text = QTextEdit()
        self.test_text.setPlainText("""
        这是一个测试文本区域。
        
        如果您的主程序正在运行，您可以：
        1. 选择这段文字测试划词助手功能
        2. 拖拽文件到主程序的输入框测试输入增强功能
        3. 在主程序的工具栏中找到"✨"按钮来启用/禁用划词助手
        
        测试步骤：
        1. 启动主程序 (python -m aipet.main)
        2. 在聊天窗口的工具栏中点击"✨"按钮启用划词助手
        3. 选择任意文本，应该会出现悬浮工具条
        4. 尝试拖拽文件到输入框，应该有智能处理
        """)
        layout.addWidget(self.test_text)
        
        # 测试按钮
        test_btn = QPushButton("运行功能测试")
        test_btn.clicked.connect(self.run_tests)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(test_btn)
        
        # 结果显示
        self.result_label = QLabel("点击按钮开始测试...")
        self.result_label.setStyleSheet("margin: 10px; font-size: 12px;")
        layout.addWidget(self.result_label)
    
    def run_tests(self):
        """运行所有测试"""
        self.result_label.setText("正在运行测试...")
        
        results = []
        
        # 运行测试
        results.append(("划词助手", test_selection_assistant()))
        results.append(("输入增强器", test_input_enhancer()))
        results.append(("主程序集成", test_integration()))
        
        # 显示结果
        success_count = sum(1 for _, success in results if success)
        total_count = len(results)
        
        result_text = f"测试完成: {success_count}/{total_count} 项通过\n\n"
        for name, success in results:
            status = "✓ 通过" if success else "✗ 失败"
            result_text += f"{name}: {status}\n"
        
        if success_count == total_count:
            result_text += "\n🎉 所有测试通过！增强功能已成功集成。"
        else:
            result_text += f"\n⚠️ 有 {total_count - success_count} 项测试失败，请检查控制台输出。"
        
        self.result_label.setText(result_text)

def main():
    """主函数"""
    print("AiPet 增强功能测试程序")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 自动运行一次测试
    QTimer.singleShot(1000, window.run_tests)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
